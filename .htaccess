# Library Management System .htaccess file - MINIMAL VERSION FOR TESTING

# Prevent directory listing
Options -Indexes

# Allow all access for testing
<RequireAll>
    Require all granted
</RequireAll>

# Enable URL rewriting - TEMPORARILY DISABLED FOR TESTING
<IfModule mod_rewrite.c>
    RewriteEngine Off
    # RewriteBase /Library/lms/

    # Redirect to HTTPS (uncomment in production)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Exclude admin, members, books, loans, reports directories from rewrite rules
    # RewriteCond %{REQUEST_URI} !^/Library/lms/admin/
    # RewriteCond %{REQUEST_URI} !^/Library/lms/members/
    # RewriteCond %{REQUEST_URI} !^/Library/lms/books/
    # RewriteCond %{REQUEST_URI} !^/Library/lms/loans/
    # RewriteCond %{REQUEST_URI} !^/Library/lms/reports/
    # RewriteCond %{REQUEST_URI} !^/Library/lms/api/
    # RewriteCond %{REQUEST_URI} !^/Library/lms/auth/
    # RewriteCond %{REQUEST_URI} !^/Library/lms/assets/
    # RewriteCond %{REQUEST_URI} !^/Library/lms/uploads/
    # RewriteCond %{REQUEST_URI} !^/Library/lms/includes/

    # Exclude specific PHP files that should be directly accessible
    # RewriteCond %{REQUEST_URI} !^/Library/lms/register\.php$
    # RewriteCond %{REQUEST_URI} !^/Library/lms/login\.php$
    # RewriteCond %{REQUEST_URI} !^/Library/lms/home\.php$
    # RewriteCond %{REQUEST_URI} !^/Library/lms/catalog\.php$
    # RewriteCond %{REQUEST_URI} !^/Library/lms/about\.php$
    # RewriteCond %{REQUEST_URI} !^/Library/lms/contact\.php$
    # RewriteCond %{REQUEST_URI} !^/Library/lms/terms\.php$

    # Redirect to index.php if file doesn't exist
    # RewriteCond %{REQUEST_FILENAME} !-f
    # RewriteCond %{REQUEST_FILENAME} !-d
    # RewriteRule ^(.*)$ index.php [QSA,L]
</IfModule>

# PHP settings - Compatible with PHP 7.x and 8.x
<IfModule mod_php.c>
    # Disable showing PHP errors to users
    php_flag display_errors Off

    # Increase security
    php_flag register_globals Off

    # Set maximum upload file size
    php_value upload_max_filesize 10M
    php_value post_max_size 10M

    # Set session timeout
    php_value session.gc_maxlifetime 3600
</IfModule>

# Add proper MIME types
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType image/svg+xml .svg
</IfModule>

# Enable browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# Compress files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css application/javascript application/json
</IfModule>
