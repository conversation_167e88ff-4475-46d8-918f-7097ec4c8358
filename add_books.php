<?php
/**
 * Add Books Script
 * This script adds a collection of books to the library database
 */

// Database connection
require_once 'config/database.php';
$database = new Database();
$db = $database->getConnection();

// Function to add a book
function addBook($db, $isbn, $title, $author, $category, $publication_year, $publisher,
                $quantity, $available_quantity, $shelf_location, $description) {

    // Check if book already exists
    $check_query = "SELECT id FROM books WHERE isbn = :isbn";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':isbn', $isbn);
    $check_stmt->execute();

    if ($check_stmt->rowCount() > 0) {
        return "Book with ISBN $isbn already exists in the database.";
    }

    // Current date for created_at field
    $created_at = date('Y-m-d H:i:s');

    // Insert the book
    $query = "INSERT INTO books (isbn, title, author, category, publication_year, publisher,
              quantity, available_quantity, shelf_location, description, created_at)
              VALUES (:isbn, :title, :author, :category, :publication_year, :publisher,
              :quantity, :available_quantity, :shelf_location, :description, :created_at)";

    $stmt = $db->prepare($query);
    $stmt->bindParam(':isbn', $isbn);
    $stmt->bindParam(':title', $title);
    $stmt->bindParam(':author', $author);
    $stmt->bindParam(':category', $category);
    $stmt->bindParam(':publication_year', $publication_year);
    $stmt->bindParam(':publisher', $publisher);
    $stmt->bindParam(':quantity', $quantity);
    $stmt->bindParam(':available_quantity', $available_quantity);
    $stmt->bindParam(':shelf_location', $shelf_location);
    $stmt->bindParam(':description', $description);
    $stmt->bindParam(':created_at', $created_at);

    if ($stmt->execute()) {
        return "Book '$title' added successfully.";
    } else {
        return "Error adding book '$title': " . print_r($stmt->errorInfo(), true);
    }
}

// Books to add - Part 1
$books = [
    // Fiction Books
    [
        'isbn' => '978-0061120084',
        'title' => 'To Kill a Mockingbird',
        'author' => 'Harper Lee',
        'category' => 'Fiction, Classic',
        'publication_year' => 1960,
        'publisher' => 'J. B. Lippincott & Co.',
        'quantity' => 5,
        'available_quantity' => 5,
        'shelf_location' => 'FIC-001',
        'description' => 'A powerful story of racial injustice and moral growth in the American South.'
    ],
    [
        'isbn' => '978-0451524935',
        'title' => '1984',
        'author' => 'George Orwell',
        'category' => 'Fiction, Dystopian',
        'publication_year' => 1949,
        'publisher' => 'Secker & Warburg',
        'quantity' => 4,
        'available_quantity' => 4,
        'shelf_location' => 'FIC-002',
        'description' => 'A dystopian novel about totalitarianism and surveillance.'
    ],
    [
        'isbn' => '978-0743273565',
        'title' => 'The Great Gatsby',
        'author' => 'F. Scott Fitzgerald',
        'category' => 'Fiction, Classic',
        'publication_year' => 1925,
        'publisher' => 'Charles Scribner\'s Sons',
        'quantity' => 3,
        'available_quantity' => 3,
        'shelf_location' => 'FIC-003',
        'description' => 'A tale of wealth, love, and the American Dream in the 1920s.'
    ],
    [
        'isbn' => '978-0141439518',
        'title' => 'Pride and Prejudice',
        'author' => 'Jane Austen',
        'category' => 'Fiction, Romance',
        'publication_year' => 1813,
        'publisher' => 'T. Egerton',
        'quantity' => 6,
        'available_quantity' => 6,
        'shelf_location' => 'FIC-004',
        'description' => 'A romantic novel about Elizabeth Bennet and Mr. Darcy.'
    ],
    [
        'isbn' => '978-0307474278',
        'title' => 'The Da Vinci Code',
        'author' => 'Dan Brown',
        'category' => 'Fiction, Thriller',
        'publication_year' => 2003,
        'publisher' => 'Doubleday',
        'quantity' => 7,
        'available_quantity' => 7,
        'shelf_location' => 'FIC-005',
        'description' => 'A mystery thriller involving art, religion, and secret societies.'
    ],
    [
        'isbn' => '978-0316769488',
        'title' => 'The Catcher in the Rye',
        'author' => 'J.D. Salinger',
        'category' => 'Fiction, Coming-of-Age',
        'publication_year' => 1951,
        'publisher' => 'Little, Brown and Company',
        'quantity' => 4,
        'available_quantity' => 4,
        'shelf_location' => 'FIC-006',
        'description' => 'A novel about teenage rebellion and alienation.'
    ],

    // Non-Fiction Books
    [
        'isbn' => '978-0062315007',
        'title' => 'The Alchemist',
        'author' => 'Paulo Coelho',
        'category' => 'Non-Fiction, Self-Help',
        'publication_year' => 1988,
        'publisher' => 'HarperOne',
        'quantity' => 4,
        'available_quantity' => 4,
        'shelf_location' => 'NF-001',
        'description' => 'A philosophical book about following one\'s dreams.'
    ],
    [
        'isbn' => '978-1451673319',
        'title' => 'Steve Jobs',
        'author' => 'Walter Isaacson',
        'category' => 'Non-Fiction, Biography',
        'publication_year' => 2011,
        'publisher' => 'Simon & Schuster',
        'quantity' => 3,
        'available_quantity' => 3,
        'shelf_location' => 'NF-002',
        'description' => 'The authorized biography of Apple\'s co-founder.'
    ],
    [
        'isbn' => '978-0062457714',
        'title' => 'The Subtle Art of Not Giving a F*ck',
        'author' => 'Mark Manson',
        'category' => 'Non-Fiction, Self-Help',
        'publication_year' => 2016,
        'publisher' => 'HarperOne',
        'quantity' => 5,
        'available_quantity' => 5,
        'shelf_location' => 'NF-003',
        'description' => 'A counterintuitive approach to living a good life.'
    ],
    [
        'isbn' => '978-0143126560',
        'title' => 'Sapiens: A Brief History of Humankind',
        'author' => 'Yuval Noah Harari',
        'category' => 'Non-Fiction, History',
        'publication_year' => 2011,
        'publisher' => 'Harper',
        'quantity' => 4,
        'available_quantity' => 4,
        'shelf_location' => 'NF-004',
        'description' => 'Explores the history of Homo sapiens from evolution to modern society.'
    ],
    [
        'isbn' => '978-0066620992',
        'title' => 'Good to Great',
        'author' => 'Jim Collins',
        'category' => 'Non-Fiction, Business',
        'publication_year' => 2001,
        'publisher' => 'HarperBusiness',
        'quantity' => 3,
        'available_quantity' => 3,
        'shelf_location' => 'NF-005',
        'description' => 'Analyzes how companies transition from good to great.'
    ],

    // Science Fiction & Fantasy
    [
        'isbn' => '978-0553418026',
        'title' => 'The Martian',
        'author' => 'Andy Weir',
        'category' => 'Science Fiction',
        'publication_year' => 2011,
        'publisher' => 'Crown',
        'quantity' => 4,
        'available_quantity' => 4,
        'shelf_location' => 'SF-001',
        'description' => 'An astronaut stranded on Mars fights for survival.'
    ],
    [
        'isbn' => '978-0553103540',
        'title' => 'A Game of Thrones',
        'author' => 'George R. R. Martin',
        'category' => 'Fantasy',
        'publication_year' => 1996,
        'publisher' => 'Bantam Books',
        'quantity' => 5,
        'available_quantity' => 5,
        'shelf_location' => 'SF-002',
        'description' => 'A Game of Thrones begins in the frozen lands beyond the Wall, where the sworn brothers of the Night’s Watch stumble upon an ancient terror—the White Walkers, creatures of ice and death long thought to be mere legend. The encounter leaves only corpses and dread in its wake, a warning of the darkness gathering in the north. Meanwhile, in the sunlit halls of Winterfell, Lord Eddard Stark receives his old friend, King Robert Baratheon, who rides north to offer Ned the position of Hand of the King. Reluctantly, Ned accepts, leaving his home to navigate the vipers’ nest of King’s Landing, where whispers of treachery coil around the throne. His wife, Catelyn, clings to a secret letter from her sister, Lysa, accusing the powerful Lannisters of murder—a claim that seems all too real when their young son, Bran, is thrown from a tower after glimpsing Queen Cersei in the arms of her twin brother, Jaime.

As Ned uncovers the truth—that Cersei’s golden-haired children are bastards born of incest, with no rightful claim to the throne—the realm teeters on the edge of chaos. King Robert dies in a hunting accident, orchestrated by Cersei, and his cruel son Joffrey seizes power. Ned’s honor proves his undoing; betrayed and imprisoned, he confesses to treason in a desperate bid to save his daughters, only for Joffrey to sever his head before a screaming crowd. The North rises in rebellion, led by Ned’s eldest son, Robb, declared King in the North by his bannermen.

Across the Narrow Sea, another claimant stirs. Daenerys Targaryen, the last daughter of a fallen dynasty, is sold into marriage to the fearsome Dothraki warlord Khal Drogo by her scheming brother, Viserys. Stripped of everything, Daenerys finds strength in fire and blood, hatching three ancient dragon eggs in the ashes of her husband’s funeral pyre. And at the edge of the world, Ned’s bastard son, Jon Snow, takes the black oath of the Night’s Watch, standing guard on the Wall as winter—and something far worse—gathers in the frozen dark.

The game of thrones has begun, and the players—stark in their ambitions, their loves, and their betrayals—march toward a war that will consume the realm. But beyond the petty squabbles of kings and queens, an older, colder enemy stirs, waiting for the world to forget its name. Winter is coming. And with it, the long night.'
    ],
    [
        'isbn' => '978-0765326355',
        'title' => 'The Way of Kings',
        'author' => 'Brandon Sanderson',
        'category' => 'Fantasy',
        'publication_year' => 2010,
        'publisher' => 'Tor Books',
        'quantity' => 3,
        'available_quantity' => 3,
        'shelf_location' => 'SF-003',
        'description' => 'The first book in the Stormlight Archive series.'
    ],
    [
        'isbn' => '978-0547928227',
        'title' => 'The Hobbit',
        'author' => 'J.R.R. Tolkien',
        'category' => 'Fantasy',
        'publication_year' => 1937,
        'publisher' => 'Allen & Unwin',
        'quantity' => 6,
        'available_quantity' => 6,
        'shelf_location' => 'SF-004',
        'description' => 'A fantasy adventure about Bilbo Baggins\' journey.'
    ]
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Books - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Add Books to Library</h1>

        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Adding Books...</h5>
                <div class="mt-3">
                    <?php
                    $success_count = 0;
                    $error_count = 0;

                    foreach ($books as $book) {
                        $result = addBook(
                            $db,
                            $book['isbn'],
                            $book['title'],
                            $book['author'],
                            $book['category'],
                            $book['publication_year'],
                            $book['publisher'],
                            $book['quantity'],
                            $book['available_quantity'],
                            $book['shelf_location'],
                            $book['description']
                        );

                        echo "<p>";
                        if (strpos($result, 'successfully') !== false) {
                            echo "<span class='text-success'>✓ </span>";
                            $success_count++;
                        } else {
                            echo "<span class='text-warning'>⚠ </span>";
                            $error_count++;
                        }
                        echo $result . "</p>";
                    }
                    ?>

                    <div class="alert <?php echo $error_count > 0 ? 'alert-warning' : 'alert-success'; ?> mt-3">
                        <strong>Summary:</strong> Added <?php echo $success_count; ?> books successfully.
                        <?php if ($error_count > 0): ?>
                            <?php echo $error_count; ?> books could not be added (likely already exist).
                        <?php endif; ?>
                    </div>

                    <div class="mt-3">
                        <a href="add_books_part2.php" class="btn btn-primary">Add More Books (Part 2)</a>
                        <a href="books/index.php" class="btn btn-secondary">View All Books</a>
                        <a href="admin/dashboard.php" class="btn btn-outline-secondary">Back to Dashboard</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
