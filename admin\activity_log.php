<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$records_per_page = 20;
$offset = ($page - 1) * $records_per_page;

// Filtering
$action_filter = isset($_GET['action']) ? $_GET['action'] : '';
$user_filter = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';

// Build query
$query = "SELECT al.*, u.username 
          FROM activity_log al
          LEFT JOIN users u ON al.user_id = u.id
          WHERE 1=1";
$params = [];

if (!empty($action_filter)) {
    $query .= " AND al.action = :action";
    $params[':action'] = $action_filter;
}

if ($user_filter > 0) {
    $query .= " AND al.user_id = :user_id";
    $params[':user_id'] = $user_filter;
}

if (!empty($date_from)) {
    $query .= " AND DATE(al.timestamp) >= :date_from";
    $params[':date_from'] = $date_from;
}

if (!empty($date_to)) {
    $query .= " AND DATE(al.timestamp) <= :date_to";
    $params[':date_to'] = $date_to;
}

// Count total records for pagination
$count_query = str_replace("al.*, u.username", "COUNT(*) as total", $query);
$stmt = $db->prepare($count_query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$total_records = $stmt->fetch()['total'];
$total_pages = ceil($total_records / $records_per_page);

// Get activities with pagination
$query .= " ORDER BY al.timestamp DESC LIMIT :offset, :limit";
$stmt = $db->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':limit', $records_per_page, PDO::PARAM_INT);
$stmt->execute();
$activities = $stmt->fetchAll();

// Get all users for filter dropdown
$query = "SELECT id, username FROM users ORDER BY username";
$stmt = $db->prepare($query);
$stmt->execute();
$users = $stmt->fetchAll();

// Get all action types for filter dropdown
$query = "SELECT DISTINCT action FROM activity_log ORDER BY action";
$stmt = $db->prepare($query);
$stmt->execute();
$actions = $stmt->fetchAll();

// Log view activity
logActivity($db, 'view', 'Viewed activity log');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activity Log - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Activity Log</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button id="darkModeToggle" class="btn btn-sm btn-outline-secondary" title="Toggle Dark Mode">
                            <i id="darkModeIcon" class="bi bi-moon"></i>
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-funnel me-2"></i> Filter Activities</h5>
                    </div>
                    <div class="card-body">
                        <form method="get" action="" class="row g-3">
                            <div class="col-md-3">
                                <label for="action" class="form-label">Action Type</label>
                                <select name="action" id="action" class="form-select">
                                    <option value="">All Actions</option>
                                    <?php foreach ($actions as $action): ?>
                                        <option value="<?php echo h($action['action']); ?>" <?php echo $action_filter === $action['action'] ? 'selected' : ''; ?>>
                                            <?php echo h(ucfirst($action['action'])); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="user_id" class="form-label">User</label>
                                <select name="user_id" id="user_id" class="form-select">
                                    <option value="0">All Users</option>
                                    <?php foreach ($users as $user): ?>
                                        <option value="<?php echo h($user['id']); ?>" <?php echo $user_filter === (int)$user['id'] ? 'selected' : ''; ?>>
                                            <?php echo h($user['username']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">Date From</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo h($date_from); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="date_to" class="form-label">Date To</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo h($date_to); ?>">
                            </div>
                            <div class="col-12 text-end">
                                <a href="activity_log.php" class="btn btn-secondary me-2">Reset</a>
                                <button type="submit" class="btn btn-primary">Apply Filters</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Activity Log Table -->
                <div class="card mb-4">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i> Activity Log</h5>
                        <span class="badge bg-primary"><?php echo $total_records; ?> activities</span>
                    </div>
                    <div class="card-body">
                        <?php if (empty($activities)): ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i> No activities found matching your criteria.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date & Time</th>
                                            <th>User</th>
                                            <th>Action</th>
                                            <th>Description</th>
                                            <th>IP Address</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($activities as $activity): ?>
                                            <tr>
                                                <td><?php echo formatDateTime($activity['timestamp']); ?></td>
                                                <td><?php echo h($activity['username'] ?? 'System'); ?></td>
                                                <td>
                                                    <?php if ($activity['action'] === 'login'): ?>
                                                        <span class="badge bg-success">Login</span>
                                                    <?php elseif ($activity['action'] === 'logout'): ?>
                                                        <span class="badge bg-secondary">Logout</span>
                                                    <?php elseif ($activity['action'] === 'add'): ?>
                                                        <span class="badge bg-primary">Add</span>
                                                    <?php elseif ($activity['action'] === 'edit'): ?>
                                                        <span class="badge bg-warning text-dark">Edit</span>
                                                    <?php elseif ($activity['action'] === 'delete'): ?>
                                                        <span class="badge bg-danger">Delete</span>
                                                    <?php elseif ($activity['action'] === 'view'): ?>
                                                        <span class="badge bg-info text-dark">View</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-dark"><?php echo h(ucfirst($activity['action'])); ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo h($activity['description']); ?></td>
                                                <td><?php echo h($activity['ip_address']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                                <nav aria-label="Activity log pagination">
                                    <ul class="pagination justify-content-center mt-4">
                                        <?php if ($page > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=1<?php echo !empty($action_filter) ? '&action=' . h($action_filter) : ''; ?><?php echo $user_filter > 0 ? '&user_id=' . $user_filter : ''; ?><?php echo !empty($date_from) ? '&date_from=' . h($date_from) : ''; ?><?php echo !empty($date_to) ? '&date_to=' . h($date_to) : ''; ?>" aria-label="First">
                                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                                </a>
                                            </li>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($action_filter) ? '&action=' . h($action_filter) : ''; ?><?php echo $user_filter > 0 ? '&user_id=' . $user_filter : ''; ?><?php echo !empty($date_from) ? '&date_from=' . h($date_from) : ''; ?><?php echo !empty($date_to) ? '&date_to=' . h($date_to) : ''; ?>" aria-label="Previous">
                                                    <span aria-hidden="true">&laquo;</span>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                        
                                        <?php
                                        $start_page = max(1, $page - 2);
                                        $end_page = min($total_pages, $page + 2);
                                        
                                        for ($i = $start_page; $i <= $end_page; $i++):
                                        ?>
                                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($action_filter) ? '&action=' . h($action_filter) : ''; ?><?php echo $user_filter > 0 ? '&user_id=' . $user_filter : ''; ?><?php echo !empty($date_from) ? '&date_from=' . h($date_from) : ''; ?><?php echo !empty($date_to) ? '&date_to=' . h($date_to) : ''; ?>">
                                                    <?php echo $i; ?>
                                                </a>
                                            </li>
                                        <?php endfor; ?>
                                        
                                        <?php if ($page < $total_pages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($action_filter) ? '&action=' . h($action_filter) : ''; ?><?php echo $user_filter > 0 ? '&user_id=' . $user_filter : ''; ?><?php echo !empty($date_from) ? '&date_from=' . h($date_from) : ''; ?><?php echo !empty($date_to) ? '&date_to=' . h($date_to) : ''; ?>" aria-label="Next">
                                                    <span aria-hidden="true">&raquo;</span>
                                                </a>
                                            </li>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $total_pages; ?><?php echo !empty($action_filter) ? '&action=' . h($action_filter) : ''; ?><?php echo $user_filter > 0 ? '&user_id=' . $user_filter : ''; ?><?php echo !empty($date_from) ? '&date_from=' . h($date_from) : ''; ?><?php echo !empty($date_to) ? '&date_to=' . h($date_to) : ''; ?>" aria-label="Last">
                                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo url('assets/js/dark-mode.js'); ?>"></script>
</body>
</html>
