<?php
/**
 * Activity Logs Viewer
 * Monitor and track all system activities and user actions
 */

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../login.php');
    exit;
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get filter parameters
$filter_type = isset($_GET['type']) ? $_GET['type'] : '';
$filter_user = isset($_GET['user']) ? $_GET['user'] : '';
$filter_date = isset($_GET['date']) ? $_GET['date'] : '';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Build query conditions
$conditions = [];
$params = [];

if (!empty($filter_type)) {
    $conditions[] = "activity_type = :type";
    $params[':type'] = $filter_type;
}

if (!empty($filter_user)) {
    $conditions[] = "user_id = :user_id";
    $params[':user_id'] = $filter_user;
}

if (!empty($filter_date)) {
    $conditions[] = "DATE(created_at) = :date";
    $params[':date'] = $filter_date;
}

if (!empty($search)) {
    $conditions[] = "(description LIKE :search OR activity_type LIKE :search)";
    $params[':search'] = "%$search%";
}

$where_clause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";

// Get activities with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 25;
$offset = ($page - 1) * $per_page;

// Get total count
$count_query = "SELECT COUNT(*) as total FROM activity_logs $where_clause";
$count_stmt = $db->prepare($count_query);
foreach ($params as $key => $value) {
    $count_stmt->bindValue($key, $value);
}
$count_stmt->execute();
$total_activities = $count_stmt->fetch()['total'];
$total_pages = ceil($total_activities / $per_page);

// Get activities
$query = "SELECT al.*, u.username, u.full_name 
          FROM activity_logs al
          LEFT JOIN users u ON al.user_id = u.id
          $where_clause
          ORDER BY al.created_at DESC
          LIMIT :offset, :per_page";
$stmt = $db->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
$stmt->bindParam(':per_page', $per_page, PDO::PARAM_INT);
$stmt->execute();
$activities = $stmt->fetchAll();

// Get activity types for filter
$types_query = "SELECT DISTINCT activity_type FROM activity_logs ORDER BY activity_type";
$types_stmt = $db->prepare($types_query);
$types_stmt->execute();
$activity_types = $types_stmt->fetchAll(PDO::FETCH_COLUMN);

// Get users for filter
$users_query = "SELECT DISTINCT u.id, u.username, u.full_name 
                FROM activity_logs al 
                JOIN users u ON al.user_id = u.id 
                ORDER BY u.username";
$users_stmt = $db->prepare($users_query);
$users_stmt->execute();
$users = $users_stmt->fetchAll();

// Get activity statistics
$stats_query = "SELECT 
                  COUNT(*) as total_activities,
                  COUNT(DISTINCT user_id) as active_users,
                  COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_activities,
                  COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as week_activities
                FROM activity_logs";
$stats_stmt = $db->prepare($stats_query);
$stats_stmt->execute();
$stats = $stats_stmt->fetch();

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function getActivityIcon($type) {
    switch ($type) {
        case 'login': return 'bi-box-arrow-in-right text-success';
        case 'logout': return 'bi-box-arrow-right text-secondary';
        case 'book_add': return 'bi-book-fill text-primary';
        case 'book_edit': return 'bi-pencil text-warning';
        case 'book_delete': return 'bi-trash text-danger';
        case 'member_add': return 'bi-person-plus text-success';
        case 'member_edit': return 'bi-person-gear text-warning';
        case 'loan_issue': return 'bi-arrow-right text-info';
        case 'loan_return': return 'bi-arrow-left text-success';
        case 'checkout': return 'bi-check-circle text-primary';
        case 'fine_add': return 'bi-currency-dollar text-warning';
        case 'settings': return 'bi-gear text-secondary';
        case 'backup': return 'bi-download text-info';
        default: return 'bi-activity text-muted';
    }
}

function getActivityColor($type) {
    switch ($type) {
        case 'login': return 'success';
        case 'logout': return 'secondary';
        case 'book_add': case 'member_add': return 'primary';
        case 'book_edit': case 'member_edit': return 'warning';
        case 'book_delete': return 'danger';
        case 'loan_issue': case 'checkout': return 'info';
        case 'loan_return': return 'success';
        default: return 'light';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activity Logs - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .stats-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.2s ease-in-out;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .activity-item {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .activity-item:hover {
            background-color: #f8f9fa;
            border-left-color: #0d6efd;
        }
        .activity-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #f8f9fa;
        }
        .filter-card {
            background-color: #f8f9fa;
            border: none;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-activity me-2"></i>Activity Logs
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportLogs()">
                                <i class="bi bi-download me-1"></i> Export Logs
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearOldLogs()">
                                <i class="bi bi-trash me-1"></i> Clear Old Logs
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Total Activities</h6>
                                        <h3 class="mb-0"><?php echo number_format($stats['total_activities']); ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-activity fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Active Users</h6>
                                        <h3 class="mb-0"><?php echo $stats['active_users']; ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-people fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Today's Activities</h6>
                                        <h3 class="mb-0"><?php echo $stats['today_activities']; ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-calendar-day fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">This Week</h6>
                                        <h3 class="mb-0"><?php echo $stats['week_activities']; ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-calendar-week fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card filter-card mb-4">
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-3">
                                <label for="type" class="form-label">Activity Type</label>
                                <select class="form-select" id="type" name="type">
                                    <option value="">All Types</option>
                                    <?php foreach ($activity_types as $type): ?>
                                        <option value="<?php echo h($type); ?>" <?php echo $filter_type === $type ? 'selected' : ''; ?>>
                                            <?php echo h(ucfirst(str_replace('_', ' ', $type))); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="user" class="form-label">User</label>
                                <select class="form-select" id="user" name="user">
                                    <option value="">All Users</option>
                                    <?php foreach ($users as $user): ?>
                                        <option value="<?php echo $user['id']; ?>" <?php echo $filter_user == $user['id'] ? 'selected' : ''; ?>>
                                            <?php echo h($user['username']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="date" class="form-label">Date</label>
                                <input type="date" class="form-control" id="date" name="date" value="<?php echo h($filter_date); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" placeholder="Search activities..." value="<?php echo h($search); ?>">
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                        <?php if (!empty($filter_type) || !empty($filter_user) || !empty($filter_date) || !empty($search)): ?>
                            <div class="mt-3">
                                <a href="activity_logs.php" class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-x me-1"></i> Clear Filters
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Activity List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>Activity Timeline
                            <span class="badge bg-secondary"><?php echo number_format($total_activities); ?> total</span>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($activities)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-activity fs-1 text-muted"></i>
                                <p class="text-muted mt-3">No activities found</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($activities as $activity): ?>
                                <div class="activity-item p-3 border-bottom">
                                    <div class="d-flex align-items-start">
                                        <div class="activity-icon me-3">
                                            <i class="bi <?php echo getActivityIcon($activity['activity_type']); ?> fs-5"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <span class="badge bg-<?php echo getActivityColor($activity['activity_type']); ?> me-2">
                                                            <?php echo h(ucfirst(str_replace('_', ' ', $activity['activity_type']))); ?>
                                                        </span>
                                                        <?php echo h($activity['description']); ?>
                                                    </h6>
                                                    <p class="mb-1 text-muted">
                                                        <i class="bi bi-person me-1"></i>
                                                        <?php echo h($activity['username'] ?? 'System'); ?>
                                                        <?php if ($activity['full_name']): ?>
                                                            (<?php echo h($activity['full_name']); ?>)
                                                        <?php endif; ?>
                                                    </p>
                                                    <?php if ($activity['ip_address']): ?>
                                                        <small class="text-muted">
                                                            <i class="bi bi-geo-alt me-1"></i>IP: <?php echo h($activity['ip_address']); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="text-end">
                                                    <small class="text-muted">
                                                        <i class="bi bi-clock me-1"></i>
                                                        <?php echo date('M j, Y g:i A', strtotime($activity['created_at'])); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Activity logs pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&type=<?php echo urlencode($filter_type); ?>&user=<?php echo urlencode($filter_user); ?>&date=<?php echo urlencode($filter_date); ?>&search=<?php echo urlencode($search); ?>">Previous</a>
                            </li>
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&type=<?php echo urlencode($filter_type); ?>&user=<?php echo urlencode($filter_user); ?>&date=<?php echo urlencode($filter_date); ?>&search=<?php echo urlencode($search); ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>
                            <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&type=<?php echo urlencode($filter_type); ?>&user=<?php echo urlencode($filter_user); ?>&date=<?php echo urlencode($filter_date); ?>&search=<?php echo urlencode($search); ?>">Next</a>
                            </li>
                        </ul>
                    </nav>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function exportLogs() {
            alert('Log export feature coming soon!');
        }

        function clearOldLogs() {
            if (confirm('Are you sure you want to clear logs older than 30 days? This action cannot be undone.')) {
                alert('Clear old logs feature coming soon!');
            }
        }

        // Auto-refresh every 60 seconds
        setInterval(function() {
            if (!document.querySelector('input:focus') && !document.querySelector('select:focus')) {
                location.reload();
            }
        }, 60000);
    </script>
</body>
</html>
