<?php
/**
 * Simple test script to debug the internal server error
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type
header('Content-Type: application/json');

try {
    // Basic test
    echo json_encode([
        'success' => true,
        'message' => 'Simple test working',
        'timestamp' => date('Y-m-d H:i:s'),
        'php_version' => phpversion(),
        'current_dir' => __DIR__
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
