<?php
/**
 * Clean version of database diagnostic script
 */

// Clean output buffer
if (ob_get_level()) {
    ob_clean();
}

// Set content type first
header('Content-Type: application/json');

// Enable error reporting but don't display
error_reporting(E_ALL);
ini_set('display_errors', 0);

try {
    // Include database configuration
    $config_path = dirname(dirname(__DIR__)) . '/config/database.php';
    
    if (!file_exists($config_path)) {
        throw new Exception("Database config file not found");
    }
    
    require_once $config_path;
    
    // Test database connection
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Failed to get database connection");
    }
    
    // Test basic query
    $stmt = $db->query("SELECT COUNT(*) as count FROM members");
    $members_count = $stmt->fetch()['count'];
    
    $stmt = $db->query("SELECT COUNT(*) as count FROM books");
    $books_count = $stmt->fetch()['count'];
    
    $stmt = $db->query("SELECT COUNT(*) as count FROM book_loans");
    $loans_count = $stmt->fetch()['count'];
    
    $stmt = $db->query("SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status = 'borrowed'");
    $active_loans = $stmt->fetch()['count'];
    
    // Success response
    $response = [
        'success' => true,
        'message' => 'All database tests passed',
        'results' => [
            'members_count' => (int)$members_count,
            'books_count' => (int)$books_count,
            'loans_count' => (int)$loans_count,
            'active_loans' => (int)$active_loans
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response);
}

// Ensure clean exit
exit;
