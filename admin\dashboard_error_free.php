<?php
// Error-free dashboard version
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Auto-login as admin for testing
try {
    require_once '../config/database.php';
    require_once '../config/config.php';
    require_once '../includes/functions.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!isLoggedIn() || !isAdmin()) {
        $query = "SELECT * FROM users WHERE role = 'admin' LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $admin = $stmt->fetch();
        
        if ($admin) {
            $_SESSION['user_id'] = $admin['id'];
            $_SESSION['username'] = $admin['username'];
            $_SESSION['role'] = $admin['role'];
            $_SESSION['logged_in'] = true;
        }
    }
} catch (Exception $e) {
    die("Setup error: " . $e->getMessage());
}

// Redirect to main dashboard if everything is working
header('Location: dashboard.php');
exit;
?>
