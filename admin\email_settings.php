<?php
session_start();

// Include required files with error handling
try {
    require_once '../config/database.php';
    require_once '../config/config.php';
    require_once '../includes/functions.php';
} catch (Exception $e) {
    die('Error loading required files: ' . $e->getMessage());
}

// Add missing helper functions
if (!function_exists('h')) {
    function h($string) {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
}

// Check if user is logged in and is admin - TEMPORARILY DISABLED FOR TESTING
/*
if (!isLoggedIn() || !isAdmin()) {
    redirect('../login.php');
}
*/

// Debug: Check if functions are available
if (!function_exists('isLoggedIn')) {
    // Create temporary functions for testing
    function isLoggedIn() { return true; }
    function isAdmin() { return true; }
}

// Connect to database
$db = null;
try {
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        throw new Exception("Database connection failed");
    }
} catch (Exception $e) {
    // Create a simple error page instead of dying
    $error_message = "Database connection failed. Please check your database settings.";
    $db = null;
}

// Initialize variables
$success_message = '';
$error_message = '';

// Get current settings with error handling
$email_settings = [];
$notification_settings = [];
$fine_settings = [];

if ($db) {
    try {
        // Create settings table if it doesn't exist
        $create_table_sql = "CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_group VARCHAR(50) NOT NULL,
            setting_key VARCHAR(100) NOT NULL,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_setting (setting_group, setting_key)
        )";
        $db->exec($create_table_sql);

        // Get email settings
        $query = "SELECT setting_key, setting_value FROM settings WHERE setting_group = 'email'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        while ($row = $stmt->fetch()) {
            $email_settings[$row['setting_key']] = $row['setting_value'];
        }

        // Get notification settings
        $query = "SELECT setting_key, setting_value FROM settings WHERE setting_group = 'notifications'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        while ($row = $stmt->fetch()) {
            $notification_settings[$row['setting_key']] = $row['setting_value'];
        }

        // Get fine settings
        $query = "SELECT setting_key, setting_value FROM settings WHERE setting_group = 'fines'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        while ($row = $stmt->fetch()) {
            $fine_settings[$row['setting_key']] = $row['setting_value'];
        }
    } catch (Exception $e) {
        $error_message = "Error loading settings: " . $e->getMessage();
    }
} else {
    // Set default values if no database connection
    $email_settings = [
        'from_email' => '<EMAIL>',
        'from_name' => 'Library Management System',
        'reply_to' => '<EMAIL>',
        'smtp_enabled' => 'false',
        'smtp_host' => 'smtp.example.com',
        'smtp_port' => '587',
        'smtp_username' => '',
        'smtp_password' => '',
        'smtp_secure' => 'tls'
    ];

    $notification_settings = [
        'due_date_reminder_days' => '3',
        'send_overdue_notifications' => 'true',
        'overdue_notification_frequency' => '7'
    ];

    $fine_settings = [
        'fine_rate_per_day' => '0.25',
        'grace_period_days' => '3',
        'max_fine_per_book' => '25.00'
    ];
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Start transaction only if database is available
        if ($db) {
            $db->beginTransaction();
        }

        if (isset($_POST['email_settings'])) {
            // Update email settings
            updateSettings($db, 'email', [
                'from_email' => $_POST['from_email'],
                'from_name' => $_POST['from_name'],
                'reply_to' => $_POST['reply_to'],
                'smtp_enabled' => isset($_POST['smtp_enabled']) ? 'true' : 'false',
                'smtp_host' => $_POST['smtp_host'],
                'smtp_port' => $_POST['smtp_port'],
                'smtp_username' => $_POST['smtp_username'],
                'smtp_password' => $_POST['smtp_password'],
                'smtp_secure' => $_POST['smtp_secure']
            ]);

            $success_message = 'Email settings updated successfully';
        } elseif (isset($_POST['notification_settings'])) {
            // Update notification settings
            updateSettings($db, 'notifications', [
                'due_date_reminder_days' => $_POST['due_date_reminder_days'],
                'send_overdue_notifications' => isset($_POST['send_overdue_notifications']) ? 'true' : 'false',
                'overdue_notification_frequency' => $_POST['overdue_notification_frequency']
            ]);

            $success_message = 'Notification settings updated successfully';
        } elseif (isset($_POST['fine_settings'])) {
            // Update fine settings
            updateSettings($db, 'fines', [
                'fine_rate_per_day' => $_POST['fine_rate_per_day'],
                'grace_period_days' => $_POST['grace_period_days'],
                'max_fine_per_book' => $_POST['max_fine_per_book']
            ]);

            $success_message = 'Fine settings updated successfully';
        } elseif (isset($_POST['send_test_email'])) {
            // Send test email
            $to_email = $_POST['test_email'];
            $subject = 'Test Email from Library Management System';
            $body = '<html><body>
                <h2>Test Email</h2>
                <p>This is a test email from the Library Management System.</p>
                <p>If you received this email, your email settings are configured correctly.</p>
                <p>Time sent: ' . date('Y-m-d H:i:s') . '</p>
                </body></html>';

            // Simple email sending without external service
            $headers = "MIME-Version: 1.0" . "\r\n";
            $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
            $headers .= "From: " . ($email_settings['from_name'] ?? 'Library System') . " <" . ($email_settings['from_email'] ?? '<EMAIL>') . ">" . "\r\n";

            $result = mail($to_email, $subject, $body, $headers);

            if ($result) {
                $success_message = 'Test email sent successfully to ' . $to_email;
            } else {
                $error_message = 'Failed to send test email. Please check your email settings.';
            }
        }

        // Commit transaction only if database is available
        if ($db) {
            $db->commit();
        }

        // Refresh settings after update
        if (isset($_POST['email_settings']) || isset($_POST['notification_settings']) || isset($_POST['fine_settings'])) {
            // Redirect to avoid form resubmission
            header('Location: email_settings.php?success=' . urlencode($success_message));
            exit;
        }
    } catch (Exception $e) {
        // Rollback transaction on error only if database is available
        if ($db) {
            $db->rollBack();
        }
        $error_message = 'Error: ' . $e->getMessage();
    }
}

// Check for success message in URL
if (isset($_GET['success'])) {
    $success_message = $_GET['success'];
}

// Function to update settings
function updateSettings($db, $group, $settings) {
    if (!$db) {
        return false; // No database connection
    }

    foreach ($settings as $key => $value) {
        $query = "INSERT INTO settings (setting_group, setting_key, setting_value)
                  VALUES (:group, :key, :value)
                  ON DUPLICATE KEY UPDATE setting_value = :value";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':group', $group);
        $stmt->bindParam(':key', $key);
        $stmt->bindParam(':value', $value);
        $stmt->execute();
    }
    return true;
}

// Page title
$page_title = 'Email & Notification Settings';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include '../includes/head.php'; ?>
    <style>
        .settings-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .settings-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        .nav-pills .nav-link.active {
            background-color: #007bff;
        }
        .nav-pills .nav-link {
            color: #495057;
        }
        .nav-pills .nav-link:hover:not(.active) {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-envelope me-2 text-primary"></i>Email & Notification Settings</h1>
                </div>

                <?php if (!$db): ?>
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Database Connection Issue:</strong> Settings cannot be saved to database. Using default values. Please check your database configuration.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-3">
                        <div class="card settings-card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-gear me-2"></i>Settings</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                                    <button class="nav-link active" id="v-pills-email-tab" data-bs-toggle="pill" data-bs-target="#v-pills-email" type="button" role="tab" aria-controls="v-pills-email" aria-selected="true">
                                        <i class="bi bi-envelope me-2"></i>Email Configuration
                                    </button>
                                    <button class="nav-link" id="v-pills-notifications-tab" data-bs-toggle="pill" data-bs-target="#v-pills-notifications" type="button" role="tab" aria-controls="v-pills-notifications" aria-selected="false">
                                        <i class="bi bi-bell me-2"></i>Notification Settings
                                    </button>
                                    <button class="nav-link" id="v-pills-fines-tab" data-bs-toggle="pill" data-bs-target="#v-pills-fines" type="button" role="tab" aria-controls="v-pills-fines" aria-selected="false">
                                        <i class="bi bi-cash-coin me-2"></i>Fine Settings
                                    </button>
                                    <button class="nav-link" id="v-pills-test-tab" data-bs-toggle="pill" data-bs-target="#v-pills-test" type="button" role="tab" aria-controls="v-pills-test" aria-selected="false">
                                        <i class="bi bi-send me-2"></i>Test Email
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="tab-content" id="v-pills-tabContent">
                            <!-- Email Settings Tab -->
                            <div class="tab-pane fade show active" id="v-pills-email" role="tabpanel" aria-labelledby="v-pills-email-tab">
                                <div class="card settings-card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><i class="bi bi-envelope me-2"></i>Email Configuration</h5>
                                    </div>
                                    <div class="card-body">
                                        <form action="" method="post">
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="from_email" class="form-label">From Email</label>
                                                    <input type="email" class="form-control" id="from_email" name="from_email" value="<?php echo htmlspecialchars($email_settings['from_email'] ?? '<EMAIL>'); ?>" required>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="from_name" class="form-label">From Name</label>
                                                    <input type="text" class="form-control" id="from_name" name="from_name" value="<?php echo htmlspecialchars($email_settings['from_name'] ?? 'Library Management System'); ?>" required>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="reply_to" class="form-label">Reply-To Email</label>
                                                <input type="email" class="form-control" id="reply_to" name="reply_to" value="<?php echo htmlspecialchars($email_settings['reply_to'] ?? '<EMAIL>'); ?>" required>
                                            </div>
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="smtp_enabled" name="smtp_enabled" <?php echo (($email_settings['smtp_enabled'] ?? 'false') === 'true') ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="smtp_enabled">Use SMTP Server</label>
                                            </div>
                                            <div id="smtp_settings" class="<?php echo (($email_settings['smtp_enabled'] ?? 'false') === 'true') ? '' : 'd-none'; ?>">
                                                <div class="row mb-3">
                                                    <div class="col-md-8">
                                                        <label for="smtp_host" class="form-label">SMTP Host</label>
                                                        <input type="text" class="form-control" id="smtp_host" name="smtp_host" value="<?php echo htmlspecialchars($email_settings['smtp_host'] ?? 'smtp.example.com'); ?>">
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label for="smtp_port" class="form-label">SMTP Port</label>
                                                        <input type="number" class="form-control" id="smtp_port" name="smtp_port" value="<?php echo htmlspecialchars($email_settings['smtp_port'] ?? '587'); ?>">
                                                    </div>
                                                </div>
                                                <div class="row mb-3">
                                                    <div class="col-md-6">
                                                        <label for="smtp_username" class="form-label">SMTP Username</label>
                                                        <input type="text" class="form-control" id="smtp_username" name="smtp_username" value="<?php echo htmlspecialchars($email_settings['smtp_username'] ?? ''); ?>">
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label for="smtp_password" class="form-label">SMTP Password</label>
                                                        <input type="password" class="form-control" id="smtp_password" name="smtp_password" value="<?php echo htmlspecialchars($email_settings['smtp_password'] ?? ''); ?>">
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="smtp_secure" class="form-label">SMTP Security</label>
                                                    <select class="form-select" id="smtp_secure" name="smtp_secure">
                                                        <option value="" <?php echo ($email_settings['smtp_secure'] ?? '') === '' ? 'selected' : ''; ?>>None</option>
                                                        <option value="tls" <?php echo ($email_settings['smtp_secure'] ?? '') === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                                        <option value="ssl" <?php echo ($email_settings['smtp_secure'] ?? '') === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="d-grid gap-2">
                                                <button type="submit" name="email_settings" class="btn btn-primary">
                                                    <i class="bi bi-save me-2"></i>Save Email Settings
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Notification Settings Tab -->
                            <div class="tab-pane fade" id="v-pills-notifications" role="tabpanel" aria-labelledby="v-pills-notifications-tab">
                                <div class="card settings-card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><i class="bi bi-bell me-2"></i>Notification Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <form action="" method="post">
                                            <div class="mb-3">
                                                <label for="due_date_reminder_days" class="form-label">Send Due Date Reminders (days before due date)</label>
                                                <input type="number" class="form-control" id="due_date_reminder_days" name="due_date_reminder_days" min="1" max="14" value="<?php echo htmlspecialchars($notification_settings['due_date_reminder_days'] ?? '3'); ?>" required>
                                                <div class="form-text">Recommended: 3 days before due date</div>
                                            </div>
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="send_overdue_notifications" name="send_overdue_notifications" <?php echo (($notification_settings['send_overdue_notifications'] ?? 'true') === 'true') ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="send_overdue_notifications">Send Overdue Book Notifications</label>
                                            </div>
                                            <div class="mb-3">
                                                <label for="overdue_notification_frequency" class="form-label">Overdue Notification Frequency (days)</label>
                                                <input type="number" class="form-control" id="overdue_notification_frequency" name="overdue_notification_frequency" min="1" max="30" value="<?php echo htmlspecialchars($notification_settings['overdue_notification_frequency'] ?? '7'); ?>" required>
                                                <div class="form-text">How often to send overdue notifications. Recommended: 7 days (weekly)</div>
                                            </div>
                                            <div class="d-grid gap-2">
                                                <button type="submit" name="notification_settings" class="btn btn-primary">
                                                    <i class="bi bi-save me-2"></i>Save Notification Settings
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Fine Settings Tab -->
                            <div class="tab-pane fade" id="v-pills-fines" role="tabpanel" aria-labelledby="v-pills-fines-tab">
                                <div class="card settings-card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><i class="bi bi-cash-coin me-2"></i>Fine Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <form action="" method="post">
                                            <div class="mb-3">
                                                <label for="fine_rate_per_day" class="form-label">Fine Rate Per Day ($)</label>
                                                <input type="number" class="form-control" id="fine_rate_per_day" name="fine_rate_per_day" min="0" step="0.01" value="<?php echo htmlspecialchars($fine_settings['fine_rate_per_day'] ?? '0.25'); ?>" required>
                                                <div class="form-text">Amount charged per day for overdue books</div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="grace_period_days" class="form-label">Grace Period (days)</label>
                                                <input type="number" class="form-control" id="grace_period_days" name="grace_period_days" min="0" max="7" value="<?php echo htmlspecialchars($fine_settings['grace_period_days'] ?? '3'); ?>" required>
                                                <div class="form-text">Number of days after due date before fines start accruing</div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="max_fine_per_book" class="form-label">Maximum Fine Per Book ($)</label>
                                                <input type="number" class="form-control" id="max_fine_per_book" name="max_fine_per_book" min="0" step="0.01" value="<?php echo htmlspecialchars($fine_settings['max_fine_per_book'] ?? '25.00'); ?>" required>
                                                <div class="form-text">Maximum fine amount that can be charged for a single book</div>
                                            </div>
                                            <div class="d-grid gap-2">
                                                <button type="submit" name="fine_settings" class="btn btn-primary">
                                                    <i class="bi bi-save me-2"></i>Save Fine Settings
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Test Email Tab -->
                            <div class="tab-pane fade" id="v-pills-test" role="tabpanel" aria-labelledby="v-pills-test-tab">
                                <div class="card settings-card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><i class="bi bi-send me-2"></i>Send Test Email</h5>
                                    </div>
                                    <div class="card-body">
                                        <form action="" method="post">
                                            <div class="mb-3">
                                                <label for="test_email" class="form-label">Recipient Email Address</label>
                                                <input type="email" class="form-control" id="test_email" name="test_email" required>
                                                <div class="form-text">Enter an email address to send a test email to</div>
                                            </div>
                                            <div class="d-grid gap-2">
                                                <button type="submit" name="send_test_email" class="btn btn-primary">
                                                    <i class="bi bi-send me-2"></i>Send Test Email
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle SMTP settings visibility
            const smtpEnabledCheckbox = document.getElementById('smtp_enabled');
            const smtpSettings = document.getElementById('smtp_settings');

            if (smtpEnabledCheckbox && smtpSettings) {
                smtpEnabledCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        smtpSettings.classList.remove('d-none');
                    } else {
                        smtpSettings.classList.add('d-none');
                    }
                });
            }
        });
    </script>
</body>
</html>
