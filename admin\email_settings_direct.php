<?php
/**
 * Direct Email Settings Page - Simplified version for troubleshooting
 */
session_start();

// Initialize variables
$success_message = '';
$error_message = '';

// Try to connect to database
$db = null;
try {
    $db = new PDO("mysql:host=localhost;dbname=lms_db", "root", "");
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    $error_message = "Database connection failed: " . $e->getMessage();
}

// Default settings
$email_settings = [
    'from_email' => '<EMAIL>',
    'from_name' => 'Library Management System',
    'reply_to' => '<EMAIL>',
    'smtp_enabled' => 'false',
    'smtp_host' => 'smtp.example.com',
    'smtp_port' => '587',
    'smtp_username' => '',
    'smtp_password' => '',
    'smtp_secure' => 'tls'
];

// Load settings from database if available
if ($db) {
    try {
        $query = "SELECT setting_key, setting_value FROM settings WHERE setting_group = 'email'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        while ($row = $stmt->fetch()) {
            $email_settings[$row['setting_key']] = $row['setting_value'];
        }
    } catch (Exception $e) {
        $error_message = "Error loading settings: " . $e->getMessage();
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    if ($db) {
        try {
            // Create settings table if it doesn't exist
            $create_table = "CREATE TABLE IF NOT EXISTS settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_group VARCHAR(50) NOT NULL,
                setting_key VARCHAR(100) NOT NULL,
                setting_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_setting (setting_group, setting_key)
            )";
            $db->exec($create_table);

            // Update settings
            $settings_to_save = [
                'from_email' => $_POST['from_email'],
                'from_name' => $_POST['from_name'],
                'reply_to' => $_POST['reply_to'],
                'smtp_enabled' => isset($_POST['smtp_enabled']) ? 'true' : 'false',
                'smtp_host' => $_POST['smtp_host'],
                'smtp_port' => $_POST['smtp_port'],
                'smtp_username' => $_POST['smtp_username'],
                'smtp_password' => $_POST['smtp_password'],
                'smtp_secure' => $_POST['smtp_secure']
            ];

            foreach ($settings_to_save as $key => $value) {
                $query = "INSERT INTO settings (setting_group, setting_key, setting_value)
                          VALUES ('email', :key, :value)
                          ON DUPLICATE KEY UPDATE setting_value = :value";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':key', $key);
                $stmt->bindParam(':value', $value);
                $stmt->execute();
            }

            $success_message = 'Email settings saved successfully!';
            
            // Update local array
            $email_settings = $settings_to_save;
        } catch (Exception $e) {
            $error_message = 'Error saving settings: ' . $e->getMessage();
        }
    } else {
        $error_message = 'Cannot save settings - no database connection';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Settings - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="bi bi-envelope me-2"></i>Email Settings</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($success_message): ?>
                            <div class="alert alert-success"><?php echo $success_message; ?></div>
                        <?php endif; ?>
                        
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger"><?php echo $error_message; ?></div>
                        <?php endif; ?>

                        <form method="post">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="from_email" class="form-label">From Email</label>
                                    <input type="email" class="form-control" id="from_email" name="from_email" 
                                           value="<?php echo htmlspecialchars($email_settings['from_email']); ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="from_name" class="form-label">From Name</label>
                                    <input type="text" class="form-control" id="from_name" name="from_name" 
                                           value="<?php echo htmlspecialchars($email_settings['from_name']); ?>" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="reply_to" class="form-label">Reply-To Email</label>
                                <input type="email" class="form-control" id="reply_to" name="reply_to" 
                                       value="<?php echo htmlspecialchars($email_settings['reply_to']); ?>" required>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="smtp_enabled" name="smtp_enabled" 
                                       <?php echo ($email_settings['smtp_enabled'] === 'true') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="smtp_enabled">Use SMTP Server</label>
                            </div>

                            <div id="smtp_settings" class="<?php echo ($email_settings['smtp_enabled'] === 'true') ? '' : 'd-none'; ?>">
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label for="smtp_host" class="form-label">SMTP Host</label>
                                        <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                               value="<?php echo htmlspecialchars($email_settings['smtp_host']); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="smtp_port" class="form-label">SMTP Port</label>
                                        <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                               value="<?php echo htmlspecialchars($email_settings['smtp_port']); ?>">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="smtp_username" class="form-label">SMTP Username</label>
                                        <input type="text" class="form-control" id="smtp_username" name="smtp_username" 
                                               value="<?php echo htmlspecialchars($email_settings['smtp_username']); ?>">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="smtp_password" class="form-label">SMTP Password</label>
                                        <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                               value="<?php echo htmlspecialchars($email_settings['smtp_password']); ?>">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="smtp_secure" class="form-label">SMTP Security</label>
                                    <select class="form-select" id="smtp_secure" name="smtp_secure">
                                        <option value="" <?php echo ($email_settings['smtp_secure'] === '') ? 'selected' : ''; ?>>None</option>
                                        <option value="tls" <?php echo ($email_settings['smtp_secure'] === 'tls') ? 'selected' : ''; ?>>TLS</option>
                                        <option value="ssl" <?php echo ($email_settings['smtp_secure'] === 'ssl') ? 'selected' : ''; ?>>SSL</option>
                                    </select>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" name="save_settings" class="btn btn-primary">
                                    <i class="bi bi-save me-2"></i>Save Settings
                                </button>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Admin
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const smtpCheckbox = document.getElementById('smtp_enabled');
            const smtpSettings = document.getElementById('smtp_settings');
            
            smtpCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    smtpSettings.classList.remove('d-none');
                } else {
                    smtpSettings.classList.add('d-none');
                }
            });
        });
    </script>
</body>
</html>
