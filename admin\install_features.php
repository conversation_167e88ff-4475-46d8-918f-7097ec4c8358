<?php
/**
 * Install New Admin Dashboard Features
 * This script sets up the database tables and initial data for new features
 */

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../login.php');
    exit;
}

$installation_log = [];
$installation_success = false;
$installation_error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['install_features'])) {
    try {
        // Connect to database
        $database = new Database();
        $db = $database->getConnection();
        
        $installation_log[] = "Starting installation of new admin dashboard features...";
        
        // Read and execute SQL file
        $sql_file = __DIR__ . '/setup_new_features.sql';
        if (!file_exists($sql_file)) {
            throw new Exception("SQL setup file not found: $sql_file");
        }
        
        $sql_content = file_get_contents($sql_file);
        $installation_log[] = "SQL setup file loaded successfully.";
        
        // Split SQL into individual statements
        $statements = array_filter(
            array_map('trim', explode(';', $sql_content)),
            function($stmt) {
                return !empty($stmt) && 
                       !preg_match('/^\s*--/', $stmt) && 
                       !preg_match('/^\s*\/\*/', $stmt);
            }
        );
        
        $installation_log[] = "Found " . count($statements) . " SQL statements to execute.";
        
        // Execute each statement
        $executed = 0;
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (empty($statement)) continue;
            
            try {
                $db->exec($statement);
                $executed++;
            } catch (PDOException $e) {
                // Log the error but continue with other statements
                $installation_log[] = "Warning: " . $e->getMessage();
            }
        }
        
        $installation_log[] = "Successfully executed $executed SQL statements.";
        
        // Verify table creation
        $tables_to_check = ['notifications', 'fines', 'activity_logs'];
        foreach ($tables_to_check as $table) {
            $query = "SHOW TABLES LIKE '$table'";
            $stmt = $db->prepare($query);
            $stmt->execute();
            if ($stmt->rowCount() > 0) {
                $installation_log[] = "✓ Table '$table' created successfully.";
            } else {
                $installation_log[] = "✗ Warning: Table '$table' may not have been created.";
            }
        }
        
        // Insert initial notification
        $query = "INSERT INTO notifications (title, message, type, is_read) VALUES 
                  ('Admin Dashboard Enhanced', 'New features have been successfully installed and are ready to use!', 'success', 0)";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $installation_log[] = "✓ Initial notification created.";
        
        // Log the installation activity
        logActivity($db, 'system', 'Admin dashboard features installed successfully', 'system', null);
        $installation_log[] = "✓ Installation activity logged.";
        
        $installation_success = true;
        $installation_log[] = "Installation completed successfully!";
        
    } catch (Exception $e) {
        $installation_error = $e->getMessage();
        $installation_log[] = "✗ Installation failed: " . $installation_error;
    }
}

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install Admin Dashboard Features</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .feature-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.2s ease-in-out;
        }
        .feature-card:hover {
            transform: translateY(-2px);
        }
        .installation-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-download me-2"></i>Install Admin Dashboard Features
                    </h1>
                </div>

                <?php if ($installation_success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle me-2"></i>
                        <strong>Installation Successful!</strong> All new admin dashboard features have been installed successfully.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                                    <h3 class="mt-3">Installation Complete!</h3>
                                    <p class="text-muted">You can now access all the new features from the admin dashboard.</p>
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                        <a href="dashboard.php" class="btn btn-primary">
                                            <i class="bi bi-speedometer2 me-2"></i>Go to Dashboard
                                        </a>
                                        <a href="notifications.php" class="btn btn-outline-primary">
                                            <i class="bi bi-bell me-2"></i>View Notifications
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php elseif ($installation_error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Installation Failed!</strong> <?php echo h($installation_error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($installation_log)): ?>
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="bi bi-terminal me-2"></i>Installation Log
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="installation-log p-3">
                                        <?php foreach ($installation_log as $log_entry): ?>
                                            <div><?php echo h($log_entry); ?></div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!$installation_success): ?>
                    <!-- Features Overview -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h3 class="mb-3">New Features to Install</h3>
                            <p class="text-muted">This installation will add the following features to your admin dashboard:</p>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6 mb-3">
                            <div class="card feature-card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-primary text-white rounded-circle p-3 me-3">
                                            <i class="bi bi-bell fs-4"></i>
                                        </div>
                                        <h5 class="mb-0">Notification Center</h5>
                                    </div>
                                    <p class="text-muted">Centralized notification management system to track all system alerts and messages.</p>
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-check text-success me-2"></i>Real-time notifications</li>
                                        <li><i class="bi bi-check text-success me-2"></i>Mark as read/unread</li>
                                        <li><i class="bi bi-check text-success me-2"></i>Notification history</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <div class="card feature-card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-success text-white rounded-circle p-3 me-3">
                                            <i class="bi bi-heart-pulse fs-4"></i>
                                        </div>
                                        <h5 class="mb-0">System Health Monitor</h5>
                                    </div>
                                    <p class="text-muted">Monitor system performance, database health, and server status in real-time.</p>
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-check text-success me-2"></i>Database health checks</li>
                                        <li><i class="bi bi-check text-success me-2"></i>Memory usage monitoring</li>
                                        <li><i class="bi bi-check text-success me-2"></i>Disk space tracking</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <div class="card feature-card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-warning text-white rounded-circle p-3 me-3">
                                            <i class="bi bi-currency-dollar fs-4"></i>
                                        </div>
                                        <h5 class="mb-0">Financial Management</h5>
                                    </div>
                                    <p class="text-muted">Track library fines, payments, and generate financial reports.</p>
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-check text-success me-2"></i>Fine tracking</li>
                                        <li><i class="bi bi-check text-success me-2"></i>Payment management</li>
                                        <li><i class="bi bi-check text-success me-2"></i>Financial reports</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <div class="card feature-card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-info text-white rounded-circle p-3 me-3">
                                            <i class="bi bi-activity fs-4"></i>
                                        </div>
                                        <h5 class="mb-0">Activity Logs</h5>
                                    </div>
                                    <p class="text-muted">Comprehensive activity logging and monitoring system.</p>
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-check text-success me-2"></i>User activity tracking</li>
                                        <li><i class="bi bi-check text-success me-2"></i>System event logging</li>
                                        <li><i class="bi bi-check text-success me-2"></i>Advanced filtering</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Installation Section -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="bi bi-gear me-2"></i>Installation
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle me-2"></i>
                                        <strong>Before proceeding:</strong>
                                        <ul class="mb-0 mt-2">
                                            <li>Make sure you have a database backup</li>
                                            <li>Ensure you have admin privileges</li>
                                            <li>The installation will create new database tables and insert sample data</li>
                                        </ul>
                                    </div>
                                    
                                    <form method="post">
                                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                            <button type="submit" name="install_features" class="btn btn-primary btn-lg">
                                                <i class="bi bi-download me-2"></i>Install New Features
                                            </button>
                                            <a href="dashboard.php" class="btn btn-outline-secondary btn-lg">
                                                <i class="bi bi-arrow-left me-2"></i>Cancel
                                            </a>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
