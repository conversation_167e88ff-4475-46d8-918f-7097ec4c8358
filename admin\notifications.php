<?php
/**
 * Admin Notification Center
 * Centralized notification management for administrators
 */

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../login.php');
    exit;
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Handle notification actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['mark_read'])) {
        $notification_id = (int)$_POST['notification_id'];
        $query = "UPDATE notifications SET is_read = 1 WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $notification_id);
        $stmt->execute();
    } elseif (isset($_POST['mark_all_read'])) {
        $query = "UPDATE notifications SET is_read = 1 WHERE is_read = 0";
        $stmt = $db->prepare($query);
        $stmt->execute();
    } elseif (isset($_POST['delete_notification'])) {
        $notification_id = (int)$_POST['notification_id'];
        $query = "DELETE FROM notifications WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $notification_id);
        $stmt->execute();
    }
}

// Get notifications with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Get total count
$count_query = "SELECT COUNT(*) as total FROM notifications";
$count_stmt = $db->prepare($count_query);
$count_stmt->execute();
$total_notifications = $count_stmt->fetch()['total'];
$total_pages = ceil($total_notifications / $per_page);

// Get notifications
$query = "SELECT * FROM notifications 
          ORDER BY created_at DESC 
          LIMIT :offset, :per_page";
$stmt = $db->prepare($query);
$stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
$stmt->bindParam(':per_page', $per_page, PDO::PARAM_INT);
$stmt->execute();
$notifications = $stmt->fetchAll();

// Get unread count
$unread_query = "SELECT COUNT(*) as unread FROM notifications WHERE is_read = 0";
$unread_stmt = $db->prepare($unread_query);
$unread_stmt->execute();
$unread_count = $unread_stmt->fetch()['unread'];

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function getNotificationIcon($type) {
    switch ($type) {
        case 'overdue': return 'bi-exclamation-triangle text-danger';
        case 'new_member': return 'bi-person-plus text-success';
        case 'book_returned': return 'bi-arrow-return-left text-info';
        case 'book_borrowed': return 'bi-arrow-right text-primary';
        case 'system': return 'bi-gear text-secondary';
        case 'fine': return 'bi-currency-dollar text-warning';
        default: return 'bi-info-circle text-info';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Center - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .notification-item {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .notification-item:hover {
            background-color: #f8f9fa;
            border-left-color: #0d6efd;
        }
        .notification-item.unread {
            background-color: #fff3cd;
            border-left-color: #ffc107;
        }
        .notification-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #f8f9fa;
        }
        .notification-time {
            font-size: 0.875rem;
            color: #6c757d;
        }
        .stats-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.2s ease-in-out;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-bell me-2"></i>Notification Center
                        <?php if ($unread_count > 0): ?>
                            <span class="badge bg-danger"><?php echo $unread_count; ?></span>
                        <?php endif; ?>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <form method="post" class="d-inline">
                                <button type="submit" name="mark_all_read" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-check-all me-1"></i> Mark All Read
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Total Notifications</h6>
                                        <h3 class="mb-0"><?php echo $total_notifications; ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-bell fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Unread</h6>
                                        <h3 class="mb-0"><?php echo $unread_count; ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-bell-fill fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Recent Notifications</h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($notifications)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-bell-slash fs-1 text-muted"></i>
                                <p class="text-muted mt-3">No notifications found</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($notifications as $notification): ?>
                                <div class="notification-item p-3 border-bottom <?php echo $notification['is_read'] ? '' : 'unread'; ?>">
                                    <div class="d-flex align-items-start">
                                        <div class="notification-icon me-3">
                                            <i class="bi <?php echo getNotificationIcon($notification['type']); ?> fs-5"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo h($notification['title']); ?></h6>
                                            <p class="mb-1 text-muted"><?php echo h($notification['message']); ?></p>
                                            <small class="notification-time">
                                                <i class="bi bi-clock me-1"></i>
                                                <?php echo date('M j, Y g:i A', strtotime($notification['created_at'])); ?>
                                            </small>
                                        </div>
                                        <div class="ms-3">
                                            <?php if (!$notification['is_read']): ?>
                                                <form method="post" class="d-inline">
                                                    <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                    <button type="submit" name="mark_read" class="btn btn-sm btn-outline-primary" title="Mark as read">
                                                        <i class="bi bi-check"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                            <form method="post" class="d-inline">
                                                <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                <button type="submit" name="delete_notification" class="btn btn-sm btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this notification?')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Notifications pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>">Previous</a>
                            </li>
                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>
                            <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>">Next</a>
                            </li>
                        </ul>
                    </nav>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh notifications every 30 seconds
        setInterval(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
