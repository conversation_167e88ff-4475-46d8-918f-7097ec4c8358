<?php
/**
 * Payment Processing - Enhanced Fine Payment System
 * Process payments for overdue book fines with multiple payment methods
 */

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and has financial access (admin or librarian)
if (!isLoggedIn() || !isStaffWithFinancialAccess()) {
    redirect('../login.php');
    exit;
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

$success_message = '';
$error_message = '';

// Handle payment processing
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['process_payment'])) {
        $fine_id = (int)$_POST['fine_id'];
        $payment_method = sanitize($_POST['payment_method']);
        $payment_reference = sanitize($_POST['payment_reference']);
        $amount = (float)$_POST['amount'];
        $notes = sanitize($_POST['notes']);

        try {
            $db->beginTransaction();

            // Generate receipt number
            $receipt_number = 'RCP-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

            // Update fine status
            $query = "UPDATE fines SET
                        status = 'paid',
                        payment_method = :payment_method,
                        payment_reference = :payment_reference,
                        paid_date = NOW(),
                        processed_by = :processed_by,
                        notes = :notes
                      WHERE id = :fine_id";

            $stmt = $db->prepare($query);
            $stmt->bindParam(':payment_method', $payment_method);
            $stmt->bindParam(':payment_reference', $payment_reference);
            $stmt->bindParam(':processed_by', $_SESSION['user_id']);
            $stmt->bindParam(':notes', $notes);
            $stmt->bindParam(':fine_id', $fine_id);
            $stmt->execute();

            // Get member_id for transaction record
            $query = "SELECT member_id FROM fines WHERE id = :fine_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':fine_id', $fine_id);
            $stmt->execute();
            $member_id = $stmt->fetch()['member_id'];

            // Insert payment transaction record
            $query = "INSERT INTO payment_transactions
                        (fine_id, member_id, amount, payment_method, payment_reference,
                         processed_by, receipt_number, status, notes)
                      VALUES
                        (:fine_id, :member_id, :amount, :payment_method, :payment_reference,
                         :processed_by, :receipt_number, 'completed', :notes)";

            $stmt = $db->prepare($query);
            $stmt->bindParam(':fine_id', $fine_id);
            $stmt->bindParam(':member_id', $member_id);
            $stmt->bindParam(':amount', $amount);
            $stmt->bindParam(':payment_method', $payment_method);
            $stmt->bindParam(':payment_reference', $payment_reference);
            $stmt->bindParam(':processed_by', $_SESSION['user_id']);
            $stmt->bindParam(':receipt_number', $receipt_number);
            $stmt->bindParam(':notes', $notes);
            $stmt->execute();

            $db->commit();
            $success_message = "Payment processed successfully! Receipt Number: " . $receipt_number;

        } catch (Exception $e) {
            $db->rollback();
            $error_message = "Error processing payment: " . $e->getMessage();
        }
    }
}

// Get unpaid fines with member details
$query = "SELECT f.*,
                 CONCAT(m.first_name, ' ', m.last_name) as member_name,
                 m.email as member_email,
                 m.phone as member_phone,
                 b.title as book_title,
                 bl.due_date,
                 DATEDIFF(CURDATE(), bl.due_date) as days_overdue
          FROM fines f
          JOIN members m ON f.member_id = m.id
          LEFT JOIN book_loans bl ON f.loan_id = bl.id
          LEFT JOIN books b ON bl.book_id = b.id
          WHERE f.status = 'unpaid'
          ORDER BY f.created_date DESC";

$stmt = $db->prepare($query);
$stmt->execute();
$unpaid_fines = $stmt->fetchAll();

// Get payment method options
$payment_methods = [
    'cash' => 'Cash',
    'gcash' => 'GCash',
    'paymaya' => 'PayMaya',
    'bank_transfer' => 'Bank Transfer',
    'credit_card' => 'Credit Card',
    'debit_card' => 'Debit Card'
];

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function formatCurrency($amount) {
    return '₱' . number_format($amount, 2);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Processing - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .payment-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.2s ease-in-out;
        }
        .payment-card:hover {
            transform: translateY(-2px);
        }
        .fine-row {
            transition: background-color 0.2s ease;
        }
        .fine-row:hover {
            background-color: #f8f9fa;
        }
        .payment-method-icon {
            width: 24px;
            height: 24px;
        }
        .overdue-badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-credit-card me-2"></i>Payment Processing
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="payment_reports.php" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-file-earmark-bar-graph me-1"></i> Payment Reports
                            </a>
                            <a href="financial_management.php" class="btn btn-sm btn-outline-success">
                                <i class="bi bi-currency-dollar me-1"></i> Financial Overview
                            </a>
                        </div>
                    </div>
                </div>

                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle me-2"></i><?php echo h($success_message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i><?php echo h($error_message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Quick Stats -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card payment-card text-white bg-danger">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Unpaid Fines</h6>
                                        <h3 class="mb-0"><?php echo count($unpaid_fines); ?></h3>
                                        <small class="text-white-50">Pending Payment</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-exclamation-triangle fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card payment-card text-white bg-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Total Amount</h6>
                                        <h3 class="mb-0"><?php echo formatCurrency(array_sum(array_column($unpaid_fines, 'amount'))); ?></h3>
                                        <small class="text-white-50">Outstanding</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-currency-dollar fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Unpaid Fines List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>Unpaid Fines - Ready for Payment Processing
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($unpaid_fines)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-check-circle fs-1 text-success"></i>
                                <p class="text-muted mt-3">No unpaid fines found! All payments are up to date.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Member</th>
                                            <th>Book</th>
                                            <th>Amount</th>
                                            <th>Days Overdue</th>
                                            <th>Fine Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($unpaid_fines as $fine): ?>
                                            <tr class="fine-row">
                                                <td>
                                                    <strong><?php echo h($fine['member_name']); ?></strong><br>
                                                    <small class="text-muted"><?php echo h($fine['member_email']); ?></small>
                                                    <?php if ($fine['member_phone']): ?>
                                                        <br><small class="text-muted"><i class="bi bi-telephone"></i> <?php echo h($fine['member_phone']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo h($fine['book_title'] ?? 'N/A'); ?></td>
                                                <td>
                                                    <strong class="text-danger"><?php echo formatCurrency($fine['amount']); ?></strong>
                                                </td>
                                                <td>
                                                    <?php if ($fine['days_overdue'] > 0): ?>
                                                        <span class="badge bg-danger overdue-badge">
                                                            <?php echo $fine['days_overdue']; ?> days
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('M j, Y', strtotime($fine['created_date'])); ?></td>
                                                <td>
                                                    <button type="button" class="btn btn-success btn-sm"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#paymentModal"
                                                            data-fine-id="<?php echo $fine['id']; ?>"
                                                            data-member-name="<?php echo h($fine['member_name']); ?>"
                                                            data-amount="<?php echo $fine['amount']; ?>"
                                                            data-book-title="<?php echo h($fine['book_title'] ?? 'N/A'); ?>">
                                                        <i class="bi bi-credit-card me-1"></i> Process Payment
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Payment Processing Modal -->
    <div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentModalLabel">
                        <i class="bi bi-credit-card me-2"></i>Process Fine Payment
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" id="paymentForm">
                    <div class="modal-body">
                        <input type="hidden" name="fine_id" id="modal_fine_id">
                        <input type="hidden" name="process_payment" value="1">

                        <!-- Payment Details -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label"><strong>Member:</strong></label>
                                <p id="modal_member_name" class="form-control-plaintext"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Book:</strong></label>
                                <p id="modal_book_title" class="form-control-plaintext"></p>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="amount" class="form-label"><strong>Fine Amount:</strong></label>
                                <input type="number" class="form-control" id="amount" name="amount" step="0.01" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="payment_method" class="form-label"><strong>Payment Method:</strong> <span class="text-danger">*</span></label>
                                <select class="form-select" id="payment_method" name="payment_method" required>
                                    <option value="">Select Payment Method</option>
                                    <?php foreach ($payment_methods as $value => $label): ?>
                                        <option value="<?php echo $value; ?>"><?php echo $label; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3" id="reference_field" style="display: none;">
                            <label for="payment_reference" class="form-label">Payment Reference/Transaction ID:</label>
                            <input type="text" class="form-control" id="payment_reference" name="payment_reference"
                                   placeholder="Enter reference number for digital payments">
                            <div class="form-text">Required for GCash, PayMaya, Bank Transfer, and Card payments</div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes (Optional):</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"
                                      placeholder="Add any additional notes about this payment..."></textarea>
                        </div>

                        <!-- Payment Method Icons -->
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle me-2"></i>Accepted Payment Methods:</h6>
                            <div class="d-flex flex-wrap gap-3 mt-2">
                                <span class="badge bg-primary"><i class="bi bi-cash"></i> Cash</span>
                                <span class="badge bg-success"><i class="bi bi-phone"></i> GCash</span>
                                <span class="badge bg-warning"><i class="bi bi-credit-card"></i> PayMaya</span>
                                <span class="badge bg-info"><i class="bi bi-bank"></i> Bank Transfer</span>
                                <span class="badge bg-secondary"><i class="bi bi-credit-card-2-front"></i> Credit Card</span>
                                <span class="badge bg-dark"><i class="bi bi-credit-card-2-back"></i> Debit Card</span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-1"></i> Cancel
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-check-circle me-1"></i> Process Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Payment Modal Handler
        document.getElementById('paymentModal').addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const fineId = button.getAttribute('data-fine-id');
            const memberName = button.getAttribute('data-member-name');
            const amount = button.getAttribute('data-amount');
            const bookTitle = button.getAttribute('data-book-title');

            // Update modal content
            document.getElementById('modal_fine_id').value = fineId;
            document.getElementById('modal_member_name').textContent = memberName;
            document.getElementById('modal_book_title').textContent = bookTitle;
            document.getElementById('amount').value = parseFloat(amount).toFixed(2);
        });

        // Payment Method Change Handler
        document.getElementById('payment_method').addEventListener('change', function() {
            const referenceField = document.getElementById('reference_field');
            const referenceInput = document.getElementById('payment_reference');

            if (this.value === 'cash') {
                referenceField.style.display = 'none';
                referenceInput.required = false;
            } else if (this.value !== '') {
                referenceField.style.display = 'block';
                referenceInput.required = true;
            } else {
                referenceField.style.display = 'none';
                referenceInput.required = false;
            }
        });

        // Form Validation
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            const paymentMethod = document.getElementById('payment_method').value;
            const paymentReference = document.getElementById('payment_reference').value;

            if (paymentMethod !== 'cash' && paymentMethod !== '' && !paymentReference.trim()) {
                e.preventDefault();
                alert('Payment reference is required for ' + paymentMethod.replace('_', ' ').toUpperCase() + ' payments.');
                return false;
            }

            return confirm('Are you sure you want to process this payment? This action cannot be undone.');
        });
    </script>
</body>
</html>
