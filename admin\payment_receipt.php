<?php
/**
 * Payment Receipt Generator
 * Generate and display payment receipts for fine payments
 */

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and has financial access (admin or librarian)
if (!isLoggedIn() || !isStaffWithFinancialAccess()) {
    redirect('../login.php');
    exit;
}

// Get receipt number from URL
$receipt_number = isset($_GET['receipt']) ? sanitize($_GET['receipt']) : '';

if (empty($receipt_number)) {
    die('Receipt number is required.');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get payment transaction details
$query = "SELECT
            pt.*,
            CONCAT(m.first_name, ' ', m.last_name) as member_name,
            m.email as member_email,
            m.phone as member_phone,
            m.address as member_address,
            b.title as book_title,
            b.author as book_author,
            b.isbn as book_isbn,
            u.username as processed_by_username,
            u.full_name as processed_by_fullname,
            f.created_date as fine_created_date,
            bl.due_date as book_due_date,
            bl.issue_date as book_issue_date
          FROM payment_transactions pt
          JOIN members m ON pt.member_id = m.id
          JOIN fines f ON pt.fine_id = f.id
          LEFT JOIN book_loans bl ON f.loan_id = bl.id
          LEFT JOIN books b ON bl.book_id = b.id
          LEFT JOIN users u ON pt.processed_by = u.id
          WHERE pt.receipt_number = :receipt_number";

$stmt = $db->prepare($query);
$stmt->bindParam(':receipt_number', $receipt_number);
$stmt->execute();
$payment = $stmt->fetch();

if (!$payment) {
    die('Receipt not found.');
}

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function formatCurrency($amount) {
    return '₱' . number_format($amount, 2);
}

function getPaymentMethodDisplay($method) {
    $methods = [
        'cash' => 'Cash',
        'gcash' => 'GCash',
        'paymaya' => 'PayMaya',
        'bank_transfer' => 'Bank Transfer',
        'credit_card' => 'Credit Card',
        'debit_card' => 'Debit Card'
    ];
    return $methods[$method] ?? ucfirst(str_replace('_', ' ', $method));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Receipt - <?php echo h($receipt_number); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        @media print {
            .no-print {
                display: none !important;
            }
            .receipt-container {
                box-shadow: none !important;
                border: none !important;
            }
        }

        .receipt-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .receipt-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .receipt-body {
            padding: 2rem;
        }

        .info-section {
            margin-bottom: 2rem;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.25rem;
        }

        .info-value {
            color: #212529;
            margin-bottom: 1rem;
        }

        .payment-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
        }

        .total-amount {
            font-size: 2rem;
            font-weight: bold;
            color: #28a745;
        }

        .receipt-footer {
            background: #f8f9fa;
            padding: 1.5rem;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }

        .status-badge {
            font-size: 1rem;
            padding: 0.5rem 1rem;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-4">
        <div class="receipt-container">
            <!-- Receipt Header -->
            <div class="receipt-header">
                <h1 class="mb-0">
                    <i class="bi bi-receipt me-2"></i>Payment Receipt
                </h1>
                <p class="mb-0 mt-2">Library Management System</p>
                <h3 class="mt-3 mb-0"><?php echo h($receipt_number); ?></h3>
            </div>

            <!-- Receipt Body -->
            <div class="receipt-body">
                <!-- Transaction Info -->
                <div class="row info-section">
                    <div class="col-md-6">
                        <div class="info-label">Transaction Date & Time:</div>
                        <div class="info-value">
                            <?php echo date('F j, Y \a\t g:i A', strtotime($payment['transaction_date'])); ?>
                        </div>

                        <div class="info-label">Payment Status:</div>
                        <div class="info-value">
                            <span class="badge bg-success status-badge">
                                <i class="bi bi-check-circle me-1"></i><?php echo ucfirst($payment['status']); ?>
                            </span>
                        </div>

                        <div class="info-label">Processed By:</div>
                        <div class="info-value">
                            <?php echo h($payment['processed_by_fullname'] ?: $payment['processed_by_username']); ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-label">Payment Method:</div>
                        <div class="info-value">
                            <strong><?php echo getPaymentMethodDisplay($payment['payment_method']); ?></strong>
                        </div>

                        <?php if ($payment['payment_reference']): ?>
                            <div class="info-label">Payment Reference:</div>
                            <div class="info-value">
                                <code><?php echo h($payment['payment_reference']); ?></code>
                            </div>
                        <?php endif; ?>

                        <div class="info-label">Fine Created:</div>
                        <div class="info-value">
                            <?php echo date('F j, Y', strtotime($payment['fine_created_date'])); ?>
                        </div>
                    </div>
                </div>

                <!-- Member Information -->
                <div class="info-section">
                    <h5 class="border-bottom pb-2 mb-3">
                        <i class="bi bi-person me-2"></i>Member Information
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-label">Name:</div>
                            <div class="info-value"><?php echo h($payment['member_name']); ?></div>

                            <div class="info-label">Email:</div>
                            <div class="info-value"><?php echo h($payment['member_email']); ?></div>
                        </div>
                        <div class="col-md-6">
                            <?php if ($payment['member_phone']): ?>
                                <div class="info-label">Phone:</div>
                                <div class="info-value"><?php echo h($payment['member_phone']); ?></div>
                            <?php endif; ?>

                            <?php if ($payment['member_address']): ?>
                                <div class="info-label">Address:</div>
                                <div class="info-value"><?php echo h($payment['member_address']); ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Book Information -->
                <?php if ($payment['book_title']): ?>
                    <div class="info-section">
                        <h5 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-book me-2"></i>Book Information
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-label">Title:</div>
                                <div class="info-value"><?php echo h($payment['book_title']); ?></div>

                                <div class="info-label">Author:</div>
                                <div class="info-value"><?php echo h($payment['book_author']); ?></div>
                            </div>
                            <div class="col-md-6">
                                <?php if ($payment['book_isbn']): ?>
                                    <div class="info-label">ISBN:</div>
                                    <div class="info-value"><?php echo h($payment['book_isbn']); ?></div>
                                <?php endif; ?>

                                <?php if ($payment['book_due_date']): ?>
                                    <div class="info-label">Due Date:</div>
                                    <div class="info-value">
                                        <?php echo date('F j, Y', strtotime($payment['book_due_date'])); ?>
                                        <?php if (strtotime($payment['book_due_date']) < strtotime($payment['fine_created_date'])): ?>
                                            <span class="badge bg-danger ms-2">Overdue</span>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Payment Summary -->
                <div class="payment-summary">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-1">Fine Payment</h5>
                            <p class="text-muted mb-0">Overdue book fine payment</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="total-amount"><?php echo formatCurrency($payment['amount']); ?></div>
                        </div>
                    </div>
                </div>

                <?php if ($payment['notes']): ?>
                    <div class="info-section">
                        <div class="info-label">Notes:</div>
                        <div class="info-value">
                            <em><?php echo h($payment['notes']); ?></em>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Receipt Footer -->
            <div class="receipt-footer">
                <p class="mb-2">
                    <strong>Thank you for your payment!</strong>
                </p>
                <p class="text-muted mb-0">
                    This is an official receipt for your library fine payment.
                    <br>Please keep this receipt for your records.
                </p>
                <div class="mt-3 no-print">
                    <button onclick="window.print()" class="btn btn-primary me-2">
                        <i class="bi bi-printer me-1"></i> Print Receipt
                    </button>
                    <button onclick="window.close()" class="btn btn-secondary">
                        <i class="bi bi-x-circle me-1"></i> Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
