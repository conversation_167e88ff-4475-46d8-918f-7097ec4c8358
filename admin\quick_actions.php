<?php
/**
 * Quick Actions Panel
 * Provides quick access to common administrative tasks
 */

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../login.php');
    exit;
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Handle quick actions
$action_result = '';
$action_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'backup_database':
                // Simulate database backup
                $backup_dir = '../backups';
                if (!file_exists($backup_dir)) {
                    mkdir($backup_dir, 0755, true);
                }
                $backup_file = $backup_dir . '/quick_backup_' . date('Y-m-d_H-i-s') . '.sql';
                file_put_contents($backup_file, '-- Quick backup created on ' . date('Y-m-d H:i:s'));
                logActivity($db, 'backup', 'Quick database backup created', 'backup', null);
                $action_result = 'Database backup created successfully!';
                $action_type = 'success';
                break;
                
            case 'clear_cache':
                // Simulate cache clearing
                logActivity($db, 'system', 'System cache cleared', 'cache', null);
                $action_result = 'System cache cleared successfully!';
                $action_type = 'success';
                break;
                
            case 'send_overdue_notices':
                // Get overdue books count
                $query = "SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed' AND due_date < CURDATE()";
                $stmt = $db->prepare($query);
                $stmt->execute();
                $overdue_count = $stmt->fetch()['count'];
                
                logActivity($db, 'notification', "Overdue notices sent for $overdue_count books", 'notification', null);
                $action_result = "Overdue notices sent for $overdue_count books!";
                $action_type = 'info';
                break;
                
            case 'update_fines':
                // Calculate and update fines for overdue books
                $query = "SELECT bl.*, m.id as member_id 
                         FROM book_loans bl 
                         JOIN members m ON bl.member_id = m.id 
                         WHERE bl.status = 'borrowed' AND bl.due_date < CURDATE()";
                $stmt = $db->prepare($query);
                $stmt->execute();
                $overdue_loans = $stmt->fetchAll();
                
                $fines_updated = 0;
                foreach ($overdue_loans as $loan) {
                    $days_overdue = (strtotime('now') - strtotime($loan['due_date'])) / (60 * 60 * 24);
                    $fine_amount = $days_overdue * 0.50; // $0.50 per day
                    
                    // Check if fine already exists
                    $check_query = "SELECT id FROM fines WHERE loan_id = :loan_id";
                    $check_stmt = $db->prepare($check_query);
                    $check_stmt->bindParam(':loan_id', $loan['id']);
                    $check_stmt->execute();
                    
                    if ($check_stmt->rowCount() == 0) {
                        // Insert new fine
                        $insert_query = "INSERT INTO fines (member_id, loan_id, amount, status, created_date) 
                                        VALUES (:member_id, :loan_id, :amount, 'unpaid', NOW())";
                        $insert_stmt = $db->prepare($insert_query);
                        $insert_stmt->bindParam(':member_id', $loan['member_id']);
                        $insert_stmt->bindParam(':loan_id', $loan['id']);
                        $insert_stmt->bindParam(':amount', $fine_amount);
                        $insert_stmt->execute();
                        $fines_updated++;
                    }
                }
                
                logActivity($db, 'fine_add', "Updated fines for $fines_updated overdue books", 'fine', null);
                $action_result = "Fines updated for $fines_updated overdue books!";
                $action_type = 'warning';
                break;
                
            case 'generate_report':
                // Simulate report generation
                logActivity($db, 'report', 'Monthly report generated', 'report', null);
                $action_result = 'Monthly report generated successfully!';
                $action_type = 'info';
                break;
        }
    }
}

// Get quick stats for the panel
$quick_stats = [];

// Overdue books
$query = "SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed' AND due_date < CURDATE()";
$stmt = $db->prepare($query);
$stmt->execute();
$quick_stats['overdue_books'] = $stmt->fetch()['count'];

// Pending member registrations (if applicable)
$query = "SELECT COUNT(*) as count FROM members WHERE status = 'pending'";
$stmt = $db->prepare($query);
$stmt->execute();
$quick_stats['pending_members'] = $stmt->fetch()['count'] ?? 0;

// Low stock books
$query = "SELECT COUNT(*) as count FROM books WHERE available_quantity <= 2 AND available_quantity > 0";
$stmt = $db->prepare($query);
$stmt->execute();
$quick_stats['low_stock_books'] = $stmt->fetch()['count'];

// Unpaid fines
$query = "SELECT COUNT(*) as count, SUM(amount) as total FROM fines WHERE status = 'unpaid'";
$stmt = $db->prepare($query);
$stmt->execute();
$fine_data = $stmt->fetch();
$quick_stats['unpaid_fines'] = $fine_data['count'] ?? 0;
$quick_stats['unpaid_amount'] = $fine_data['total'] ?? 0;

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Actions - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .action-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .action-icon {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin: 0 auto 1rem;
        }
        .stats-alert {
            border-left: 4px solid;
            border-radius: 0.375rem;
        }
        .stats-alert.alert-danger {
            border-left-color: #dc3545;
        }
        .stats-alert.alert-warning {
            border-left-color: #ffc107;
        }
        .stats-alert.alert-info {
            border-left-color: #0dcaf0;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-lightning me-2"></i>Quick Actions
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise me-1"></i> Refresh
                        </button>
                    </div>
                </div>

                <?php if ($action_result): ?>
                    <div class="alert alert-<?php echo $action_type; ?> alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle me-2"></i><?php echo h($action_result); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- System Alerts -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="mb-3">
                            <i class="bi bi-exclamation-triangle me-2"></i>System Alerts
                        </h5>
                        
                        <?php if ($quick_stats['overdue_books'] > 0): ?>
                            <div class="alert alert-danger stats-alert mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>Overdue Books Alert</strong><br>
                                        <small><?php echo $quick_stats['overdue_books']; ?> books are currently overdue</small>
                                    </div>
                                    <form method="post" class="d-inline">
                                        <input type="hidden" name="action" value="send_overdue_notices">
                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                            <i class="bi bi-envelope me-1"></i> Send Notices
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if ($quick_stats['unpaid_fines'] > 0): ?>
                            <div class="alert alert-warning stats-alert mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>Unpaid Fines</strong><br>
                                        <small><?php echo $quick_stats['unpaid_fines']; ?> unpaid fines totaling $<?php echo number_format($quick_stats['unpaid_amount'], 2); ?></small>
                                    </div>
                                    <a href="financial_management.php" class="btn btn-sm btn-outline-warning">
                                        <i class="bi bi-currency-dollar me-1"></i> Manage Fines
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if ($quick_stats['low_stock_books'] > 0): ?>
                            <div class="alert alert-info stats-alert mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>Low Stock Alert</strong><br>
                                        <small><?php echo $quick_stats['low_stock_books']; ?> books have low stock (2 or fewer copies)</small>
                                    </div>
                                    <a href="../books/index.php" class="btn btn-sm btn-outline-info">
                                        <i class="bi bi-book me-1"></i> View Books
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if ($quick_stats['overdue_books'] == 0 && $quick_stats['unpaid_fines'] == 0 && $quick_stats['low_stock_books'] == 0): ?>
                            <div class="alert alert-success stats-alert">
                                <i class="bi bi-check-circle me-2"></i>
                                <strong>All systems running smoothly!</strong> No immediate attention required.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions Grid -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="mb-3">
                            <i class="bi bi-lightning-charge me-2"></i>Quick Actions
                        </h5>
                    </div>
                </div>

                <div class="row">
                    <!-- Database Backup -->
                    <div class="col-md-4 mb-4">
                        <div class="card action-card h-100" onclick="performAction('backup_database')">
                            <div class="card-body text-center">
                                <div class="action-icon bg-primary text-white">
                                    <i class="bi bi-download fs-3"></i>
                                </div>
                                <h5 class="card-title">Database Backup</h5>
                                <p class="card-text text-muted">Create a quick backup of the database</p>
                            </div>
                        </div>
                    </div>

                    <!-- Clear Cache -->
                    <div class="col-md-4 mb-4">
                        <div class="card action-card h-100" onclick="performAction('clear_cache')">
                            <div class="card-body text-center">
                                <div class="action-icon bg-success text-white">
                                    <i class="bi bi-arrow-clockwise fs-3"></i>
                                </div>
                                <h5 class="card-title">Clear Cache</h5>
                                <p class="card-text text-muted">Clear system cache and temporary files</p>
                            </div>
                        </div>
                    </div>

                    <!-- Update Fines -->
                    <div class="col-md-4 mb-4">
                        <div class="card action-card h-100" onclick="performAction('update_fines')">
                            <div class="card-body text-center">
                                <div class="action-icon bg-warning text-white">
                                    <i class="bi bi-currency-dollar fs-3"></i>
                                </div>
                                <h5 class="card-title">Update Fines</h5>
                                <p class="card-text text-muted">Calculate and update overdue fines</p>
                            </div>
                        </div>
                    </div>

                    <!-- Send Overdue Notices -->
                    <div class="col-md-4 mb-4">
                        <div class="card action-card h-100" onclick="performAction('send_overdue_notices')">
                            <div class="card-body text-center">
                                <div class="action-icon bg-danger text-white">
                                    <i class="bi bi-envelope fs-3"></i>
                                </div>
                                <h5 class="card-title">Send Overdue Notices</h5>
                                <p class="card-text text-muted">Send email notices for overdue books</p>
                            </div>
                        </div>
                    </div>

                    <!-- Generate Report -->
                    <div class="col-md-4 mb-4">
                        <div class="card action-card h-100" onclick="performAction('generate_report')">
                            <div class="card-body text-center">
                                <div class="action-icon bg-info text-white">
                                    <i class="bi bi-file-earmark-bar-graph fs-3"></i>
                                </div>
                                <h5 class="card-title">Generate Report</h5>
                                <p class="card-text text-muted">Generate monthly activity report</p>
                            </div>
                        </div>
                    </div>

                    <!-- System Health Check -->
                    <div class="col-md-4 mb-4">
                        <div class="card action-card h-100" onclick="window.location.href='system_health.php'">
                            <div class="card-body text-center">
                                <div class="action-icon bg-secondary text-white">
                                    <i class="bi bi-heart-pulse fs-3"></i>
                                </div>
                                <h5 class="card-title">System Health</h5>
                                <p class="card-text text-muted">Check system health and performance</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Quick Actions -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-clock-history me-2"></i>Recent Quick Actions
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php
                                // Get recent activities related to quick actions
                                $query = "SELECT * FROM activity_logs 
                                         WHERE activity_type IN ('backup', 'system', 'notification', 'fine_add', 'report')
                                         ORDER BY created_at DESC 
                                         LIMIT 5";
                                $stmt = $db->prepare($query);
                                $stmt->execute();
                                $recent_actions = $stmt->fetchAll();
                                ?>
                                
                                <?php if (empty($recent_actions)): ?>
                                    <p class="text-muted text-center">No recent quick actions performed</p>
                                <?php else: ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($recent_actions as $action): ?>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong><?php echo h($action['description']); ?></strong><br>
                                                    <small class="text-muted">
                                                        <i class="bi bi-clock me-1"></i>
                                                        <?php echo date('M j, Y g:i A', strtotime($action['created_at'])); ?>
                                                    </small>
                                                </div>
                                                <span class="badge bg-primary"><?php echo h(ucfirst(str_replace('_', ' ', $action['activity_type']))); ?></span>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Hidden form for actions -->
    <form id="actionForm" method="post" style="display: none;">
        <input type="hidden" name="action" id="actionInput">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function performAction(action) {
            if (confirm('Are you sure you want to perform this action?')) {
                document.getElementById('actionInput').value = action;
                document.getElementById('actionForm').submit();
            }
        }
    </script>
</body>
</html>
