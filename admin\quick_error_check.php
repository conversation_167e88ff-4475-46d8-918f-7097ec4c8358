<?php
// Quick error check for dashboard
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Set test session data
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
    $_SESSION['role'] = 'admin';
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Quick Error Check</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head><body>";

echo "<div class='container mt-5'>";
echo "<h1>🔍 Quick Error Check</h1>";

// Test 1: Database connection
echo "<div class='alert alert-info'>";
echo "<h4>1. Database Connection Test</h4>";
try {
    $database = new Database();
    $db = $database->getConnection();
    if ($db) {
        echo "<p class='text-success'>✅ Database connected successfully</p>";
    } else {
        echo "<p class='text-danger'>❌ Database connection failed</p>";
    }
} catch (Exception $e) {
    echo "<p class='text-danger'>❌ Database error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 2: Basic stats calculation
echo "<div class='alert alert-info'>";
echo "<h4>2. Stats Calculation Test</h4>";
try {
    // Test the problematic calculation
    $stats = ['total_members' => 1000];
    $member_stats = ['members_who_borrowed' => 800];
    
    $result = max(0, ($stats['total_members'] ?? 0) - ($member_stats['members_who_borrowed'] ?? 0));
    echo "<p class='text-success'>✅ Calculation works: $result members never borrowed</p>";
} catch (Exception $e) {
    echo "<p class='text-danger'>❌ Calculation error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 3: timeAgo function
echo "<div class='alert alert-info'>";
echo "<h4>3. timeAgo Function Test</h4>";
try {
    if (function_exists('timeAgo')) {
        $test_time = date('Y-m-d H:i:s', strtotime('-1 hour'));
        $result = timeAgo($test_time);
        echo "<p class='text-success'>✅ timeAgo function works: '$result'</p>";
    } else {
        echo "<p class='text-danger'>❌ timeAgo function not found</p>";
    }
} catch (Exception $e) {
    echo "<p class='text-danger'>❌ timeAgo error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 4: Include dashboard (syntax check)
echo "<div class='alert alert-info'>";
echo "<h4>4. Dashboard Syntax Check</h4>";
$dashboard_file = __DIR__ . '/dashboard.php';
$output = shell_exec("php -l \"$dashboard_file\" 2>&1");
if (strpos($output, 'No syntax errors') !== false) {
    echo "<p class='text-success'>✅ Dashboard syntax is valid</p>";
} else {
    echo "<p class='text-danger'>❌ Dashboard syntax errors:</p>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
}
echo "</div>";

// Test 5: Session variables
echo "<div class='alert alert-info'>";
echo "<h4>5. Session Variables</h4>";
echo "<p><strong>User ID:</strong> " . ($_SESSION['user_id'] ?? 'Not set') . "</p>";
echo "<p><strong>Username:</strong> " . ($_SESSION['username'] ?? 'Not set') . "</p>";
echo "<p><strong>Role:</strong> " . ($_SESSION['role'] ?? 'Not set') . "</p>";
echo "</div>";

// Final result
echo "<div class='alert alert-success'>";
echo "<h4>🎯 Result</h4>";
echo "<p>If all tests show green checkmarks (✅), the dashboard should work without errors.</p>";
echo "<p><a href='dashboard.php' class='btn btn-primary'>Test Dashboard Now</a></p>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
