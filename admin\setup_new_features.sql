-- SQL script to create tables for new admin dashboard features
-- Run this script to set up the required database tables

-- Create notifications table
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(50) DEFAULT 'info',
  `is_read` tinyint(1) DEFAULT 0,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create fines table
CREATE TABLE IF NOT EXISTS `fines` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL,
  `loan_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('unpaid','paid','waived') DEFAULT 'unpaid',
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `paid_date` timestamp NULL DEFAULT NULL,
  `waived_date` timestamp NULL DEFAULT NULL,
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_loan_id` (`loan_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_date` (`created_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create activity_logs table (if not exists)
CREATE TABLE IF NOT EXISTS `activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `activity_type` varchar(50) NOT NULL,
  `description` text NOT NULL,
  `entity_type` varchar(50) DEFAULT NULL,
  `entity_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_activity_type` (`activity_type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_entity` (`entity_type`, `entity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert sample notifications
INSERT INTO `notifications` (`title`, `message`, `type`, `is_read`) VALUES
('Welcome to Enhanced Admin Dashboard', 'New features have been added to your admin dashboard including notification center, system health monitor, financial management, and activity logs.', 'system', 0),
('System Health Check', 'Regular system health monitoring is now available. Check the System Health page for detailed information.', 'info', 0),
('Financial Management', 'Track library fines and payments with the new Financial Management module.', 'info', 0);

-- Insert sample activity logs
INSERT INTO `activity_logs` (`user_id`, `activity_type`, `description`, `entity_type`, `ip_address`) VALUES
(1, 'system', 'Admin dashboard features installed', 'system', '127.0.0.1'),
(1, 'system', 'Database tables created for new features', 'database', '127.0.0.1'),
(1, 'system', 'Notification system initialized', 'notification', '127.0.0.1');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_books_available_quantity` ON `books` (`available_quantity`);
CREATE INDEX IF NOT EXISTS `idx_book_loans_status_due_date` ON `book_loans` (`status`, `due_date`);
CREATE INDEX IF NOT EXISTS `idx_members_status` ON `members` (`status`);

-- Add status column to members table if it doesn't exist
ALTER TABLE `members` ADD COLUMN IF NOT EXISTS `status` enum('active','inactive','pending') DEFAULT 'active';

-- Update existing settings for new features
INSERT INTO `settings` (`setting_group`, `setting_key`, `setting_value`, `description`) VALUES
('system', 'enable_notifications', 'true', 'Enable system notifications'),
('system', 'notification_retention_days', '30', 'Number of days to keep notifications'),
('financial', 'fine_rate_per_day', '0.50', 'Fine amount per day for overdue books'),
('financial', 'max_fine_per_book', '25.00', 'Maximum fine amount per book'),
('financial', 'grace_period_days', '3', 'Grace period before fines are applied'),
('system', 'activity_log_retention_days', '90', 'Number of days to keep activity logs'),
('system', 'enable_system_health_monitoring', 'true', 'Enable system health monitoring'),
('system', 'health_check_interval_minutes', '30', 'Interval for automatic health checks')
ON DUPLICATE KEY UPDATE 
`setting_value` = VALUES(`setting_value`),
`description` = VALUES(`description`);

-- Create a view for overdue books with member information
CREATE OR REPLACE VIEW `overdue_books_view` AS
SELECT 
    bl.id as loan_id,
    bl.member_id,
    bl.book_id,
    bl.issue_date,
    bl.due_date,
    bl.status,
    m.name as member_name,
    m.email as member_email,
    m.phone as member_phone,
    b.title as book_title,
    b.author as book_author,
    b.isbn,
    DATEDIFF(CURDATE(), bl.due_date) as days_overdue,
    CASE 
        WHEN DATEDIFF(CURDATE(), bl.due_date) > 0 THEN DATEDIFF(CURDATE(), bl.due_date) * 0.50
        ELSE 0 
    END as calculated_fine
FROM book_loans bl
JOIN members m ON bl.member_id = m.id
JOIN books b ON bl.book_id = b.id
WHERE bl.status = 'borrowed' AND bl.due_date < CURDATE();

-- Create a view for financial summary
CREATE OR REPLACE VIEW `financial_summary_view` AS
SELECT 
    COUNT(*) as total_fines,
    SUM(amount) as total_amount,
    SUM(CASE WHEN status = 'unpaid' THEN amount ELSE 0 END) as unpaid_amount,
    SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as paid_amount,
    SUM(CASE WHEN status = 'waived' THEN amount ELSE 0 END) as waived_amount,
    AVG(amount) as average_fine,
    COUNT(CASE WHEN status = 'unpaid' THEN 1 END) as unpaid_count,
    COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_count,
    COUNT(CASE WHEN status = 'waived' THEN 1 END) as waived_count
FROM fines;

-- Create triggers to automatically log activities
DELIMITER $$

-- Trigger for book additions
CREATE TRIGGER IF NOT EXISTS `log_book_add` 
AFTER INSERT ON `books`
FOR EACH ROW
BEGIN
    INSERT INTO activity_logs (activity_type, description, entity_type, entity_id, created_at)
    VALUES ('book_add', CONCAT('New book added: ', NEW.title), 'book', NEW.id, NOW());
END$$

-- Trigger for book updates
CREATE TRIGGER IF NOT EXISTS `log_book_update` 
AFTER UPDATE ON `books`
FOR EACH ROW
BEGIN
    INSERT INTO activity_logs (activity_type, description, entity_type, entity_id, created_at)
    VALUES ('book_edit', CONCAT('Book updated: ', NEW.title), 'book', NEW.id, NOW());
END$$

-- Trigger for member additions
CREATE TRIGGER IF NOT EXISTS `log_member_add` 
AFTER INSERT ON `members`
FOR EACH ROW
BEGIN
    INSERT INTO activity_logs (activity_type, description, entity_type, entity_id, created_at)
    VALUES ('member_add', CONCAT('New member registered: ', NEW.name), 'member', NEW.id, NOW());
END$$

-- Trigger for loan issues
CREATE TRIGGER IF NOT EXISTS `log_loan_issue` 
AFTER INSERT ON `book_loans`
FOR EACH ROW
BEGIN
    INSERT INTO activity_logs (activity_type, description, entity_type, entity_id, created_at)
    VALUES ('loan_issue', CONCAT('Book loan issued - Loan ID: ', NEW.id), 'loan', NEW.id, NOW());
END$$

-- Trigger for loan returns
CREATE TRIGGER IF NOT EXISTS `log_loan_return` 
AFTER UPDATE ON `book_loans`
FOR EACH ROW
BEGIN
    IF OLD.status != 'returned' AND NEW.status = 'returned' THEN
        INSERT INTO activity_logs (activity_type, description, entity_type, entity_id, created_at)
        VALUES ('loan_return', CONCAT('Book returned - Loan ID: ', NEW.id), 'loan', NEW.id, NOW());
    END IF;
END$$

-- Trigger for fine additions
CREATE TRIGGER IF NOT EXISTS `log_fine_add` 
AFTER INSERT ON `fines`
FOR EACH ROW
BEGIN
    INSERT INTO activity_logs (activity_type, description, entity_type, entity_id, created_at)
    VALUES ('fine_add', CONCAT('Fine added: $', NEW.amount, ' for member ID: ', NEW.member_id), 'fine', NEW.id, NOW());
END$$

DELIMITER ;

-- Create stored procedure to clean old logs
DELIMITER $$

CREATE PROCEDURE IF NOT EXISTS `CleanOldLogs`()
BEGIN
    DECLARE retention_days INT DEFAULT 90;
    
    -- Get retention setting
    SELECT CAST(setting_value AS UNSIGNED) INTO retention_days 
    FROM settings 
    WHERE setting_group = 'system' AND setting_key = 'activity_log_retention_days'
    LIMIT 1;
    
    -- Delete old activity logs
    DELETE FROM activity_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- Delete old notifications
    DELETE FROM notifications 
    WHERE is_read = 1 AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
END$$

DELIMITER ;

-- Create event to automatically clean logs (if events are enabled)
-- SET GLOBAL event_scheduler = ON;

-- CREATE EVENT IF NOT EXISTS `auto_clean_logs`
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO
--   CALL CleanOldLogs();

-- Insert notification about successful setup
INSERT INTO `notifications` (`title`, `message`, `type`, `is_read`) VALUES
('Database Setup Complete', 'All new admin dashboard features have been successfully installed and configured. You can now use the Notification Center, System Health Monitor, Financial Management, Activity Logs, and Quick Actions.', 'success', 0);

-- Show completion message
SELECT 'Admin Dashboard Features Setup Complete!' as Status,
       'All tables, views, triggers, and sample data have been created successfully.' as Message;
