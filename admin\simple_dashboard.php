<?php
/**
 * Simple Admin Dashboard (Backup)
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is an admin (temporarily disabled)
/*
if (!isLoggedIn() || !isAdmin()) {
    redirect(url('login.php'));
    exit;
}
*/

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get basic statistics
$stats = array();

try {
    // Total books
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM books");
    $stmt->execute();
    $stats['total_books'] = $stmt->fetch()['total'] ?? 0;

    // Available books
    $stmt = $db->prepare("SELECT SUM(available_quantity) as available FROM books");
    $stmt->execute();
    $stats['available_books'] = $stmt->fetch()['available'] ?? 0;

    // Total members
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM members");
    $stmt->execute();
    $stats['total_members'] = $stmt->fetch()['total'] ?? 0;

    // Active loans
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM book_loans WHERE status = 'borrowed'");
    $stmt->execute();
    $stats['active_loans'] = $stmt->fetch()['total'] ?? 0;

    // Overdue books
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM book_loans WHERE status = 'overdue' OR (status = 'borrowed' AND due_date < CURDATE())");
    $stmt->execute();
    $stats['overdue_books'] = $stmt->fetch()['total'] ?? 0;

    // Recent books
    $stmt = $db->prepare("SELECT * FROM books ORDER BY created_at DESC LIMIT 5");
    $stmt->execute();
    $recent_books = $stmt->fetchAll();

    // Recent loans
    $stmt = $db->prepare("SELECT bl.*, b.title as book_title, m.first_name, m.last_name
                          FROM book_loans bl
                          JOIN books b ON bl.book_id = b.id
                          JOIN members m ON bl.member_id = m.id
                          ORDER BY bl.issue_date DESC LIMIT 5");
    $stmt->execute();
    $recent_loans = $stmt->fetchAll();

} catch (Exception $e) {
    $error = "Database error: " . $e->getMessage();
}

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Admin Dashboard - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body { background-color: #f8f9fa; }
        .stats-card {
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="#">
            <i class="bi bi-book me-2"></i>Library MS
        </a>
        <div class="navbar-nav">
            <div class="nav-item text-nowrap">
                <a class="nav-link px-3" href="../logout.php">
                    <i class="bi bi-box-arrow-right me-1"></i>Sign out
                </a>
            </div>
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="simple_dashboard.php">
                                <i class="bi bi-speedometer2 me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../books/index.php">
                                <i class="bi bi-book me-2"></i>Books
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../members/index.php">
                                <i class="bi bi-people me-2"></i>Members
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../loans/index.php">
                                <i class="bi bi-journal-arrow-up me-2"></i>Book Loans
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/index.php">
                                <i class="bi bi-file-earmark-bar-graph me-2"></i>Reports
                            </a>
                        </li>
                    </ul>

                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>Administration</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="bi bi-person-gear me-2"></i>Manage Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="bi bi-gear me-2"></i>Settings
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Simple Admin Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="../books/add.php" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-plus-circle me-1"></i> Add Book
                            </a>
                            <a href="../members/add.php" class="btn btn-sm btn-outline-success">
                                <i class="bi bi-person-plus me-1"></i> Add Member
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (isset($error)): ?>
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i><?php echo h($error); ?>
                    </div>
                <?php endif; ?>

                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card text-white bg-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Total Books</h6>
                                        <h3 class="mb-0"><?php echo h($stats['total_books']); ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-book fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card text-white bg-success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Available Books</h6>
                                        <h3 class="mb-0"><?php echo h($stats['available_books']); ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-check-circle fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card text-white bg-info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Total Members</h6>
                                        <h3 class="mb-0"><?php echo h($stats['total_members']); ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-people fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card text-white bg-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Active Loans</h6>
                                        <h3 class="mb-0"><?php echo h($stats['active_loans']); ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-journal-arrow-up fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($stats['overdue_books'] > 0): ?>
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Attention:</strong> There are <?php echo h($stats['overdue_books']); ?> overdue books that need attention.
                    <a href="../reports/overdue.php" class="btn btn-sm btn-outline-danger ms-2">View Overdue Books</a>
                </div>
                <?php endif; ?>

                <!-- Recent Activity -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-book me-2"></i>Recent Books</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recent_books)): ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($recent_books as $book): ?>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1"><?php echo h($book['title']); ?></h6>
                                                    <small class="text-muted">by <?php echo h($book['author']); ?></small>
                                                </div>
                                                <span class="badge bg-primary rounded-pill"><?php echo h($book['available_quantity']); ?> available</span>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No books found.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-journal-arrow-up me-2"></i>Recent Loans</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recent_loans)): ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($recent_loans as $loan): ?>
                                            <div class="list-group-item">
                                                <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1"><?php echo h($loan['book_title']); ?></h6>
                                                    <small><?php echo h(date('M j, Y', strtotime($loan['issue_date']))); ?></small>
                                                </div>
                                                <p class="mb-1">Borrowed by: <?php echo h($loan['first_name'] . ' ' . $loan['last_name']); ?></p>
                                                <small class="text-muted">Due: <?php echo h(date('M j, Y', strtotime($loan['due_date']))); ?></small>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No recent loans found.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-lightning me-2"></i>Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <a href="../books/add.php" class="btn btn-primary w-100">
                                        <i class="bi bi-plus-circle me-2"></i>Add New Book
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="../members/add.php" class="btn btn-success w-100">
                                        <i class="bi bi-person-plus me-2"></i>Add New Member
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="../loans/issue.php" class="btn btn-info w-100">
                                        <i class="bi bi-journal-plus me-2"></i>Issue Book
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="../reports/overdue.php" class="btn btn-warning w-100">
                                        <i class="bi bi-exclamation-triangle me-2"></i>View Overdue
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Debug Info -->
                <div class="mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>Debug Information</h6>
                        </div>
                        <div class="card-body">
                            <small class="text-muted">
                                <strong>File:</strong> <?php echo __FILE__; ?><br>
                                <strong>Time:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
                                <strong>User:</strong> <?php echo $_SESSION['username'] ?? 'Not logged in'; ?><br>
                                <strong>Links:</strong> 
                                <a href="dashboard.php">Original Dashboard</a> | 
                                <a href="dashboard_debug.php">Debug Tool</a> | 
                                <a href="settings.php">Settings</a>
                            </small>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
