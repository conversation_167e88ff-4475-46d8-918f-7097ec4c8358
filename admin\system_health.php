<?php
/**
 * System Health Monitor
 * Monitor system performance, database health, and server status
 */

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../login.php');
    exit;
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// System Health Checks
function checkDatabaseHealth($db) {
    try {
        $query = "SELECT 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        return ['status' => 'healthy', 'message' => 'Database connection successful'];
    } catch (Exception $e) {
        return ['status' => 'error', 'message' => 'Database connection failed: ' . $e->getMessage()];
    }
}

function checkDiskSpace() {
    $bytes = disk_free_space(".");
    $total = disk_total_space(".");
    $used_percentage = (($total - $bytes) / $total) * 100;
    
    $status = 'healthy';
    if ($used_percentage > 90) $status = 'critical';
    elseif ($used_percentage > 80) $status = 'warning';
    
    return [
        'status' => $status,
        'free_space' => formatBytes($bytes),
        'total_space' => formatBytes($total),
        'used_percentage' => round($used_percentage, 1)
    ];
}

function checkMemoryUsage() {
    $memory_limit = ini_get('memory_limit');
    $memory_usage = memory_get_usage(true);
    $memory_peak = memory_get_peak_usage(true);
    
    $limit_bytes = convertToBytes($memory_limit);
    $usage_percentage = ($memory_usage / $limit_bytes) * 100;
    
    $status = 'healthy';
    if ($usage_percentage > 90) $status = 'critical';
    elseif ($usage_percentage > 80) $status = 'warning';
    
    return [
        'status' => $status,
        'current_usage' => formatBytes($memory_usage),
        'peak_usage' => formatBytes($memory_peak),
        'limit' => $memory_limit,
        'usage_percentage' => round($usage_percentage, 1)
    ];
}

function checkPHPVersion() {
    $version = phpversion();
    $status = version_compare($version, '7.4.0', '>=') ? 'healthy' : 'warning';
    
    return [
        'status' => $status,
        'version' => $version,
        'message' => $status === 'warning' ? 'Consider upgrading PHP for better security and performance' : 'PHP version is up to date'
    ];
}

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

function convertToBytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g': $val *= 1024;
        case 'm': $val *= 1024;
        case 'k': $val *= 1024;
    }
    return $val;
}

// Get system health data
$health_checks = [
    'database' => checkDatabaseHealth($db),
    'disk_space' => checkDiskSpace(),
    'memory' => checkMemoryUsage(),
    'php' => checkPHPVersion()
];

// Get database statistics
$db_stats = [];
try {
    // Table sizes
    $query = "SELECT 
                table_name,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size_mb'
              FROM information_schema.TABLES 
              WHERE table_schema = DATABASE()
              ORDER BY (data_length + index_length) DESC";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $db_stats['table_sizes'] = $stmt->fetchAll();
    
    // Connection count (if available)
    $query = "SHOW STATUS LIKE 'Threads_connected'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch();
    $db_stats['connections'] = $result ? $result['Value'] : 'N/A';
    
} catch (Exception $e) {
    $db_stats['error'] = $e->getMessage();
}

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function getStatusBadge($status) {
    switch ($status) {
        case 'healthy': return 'bg-success';
        case 'warning': return 'bg-warning';
        case 'critical': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

function getStatusIcon($status) {
    switch ($status) {
        case 'healthy': return 'bi-check-circle-fill text-success';
        case 'warning': return 'bi-exclamation-triangle-fill text-warning';
        case 'critical': return 'bi-x-circle-fill text-danger';
        default: return 'bi-question-circle-fill text-secondary';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Health Monitor - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .health-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.2s ease-in-out;
        }
        .health-card:hover {
            transform: translateY(-2px);
        }
        .progress-bar {
            transition: width 0.6s ease;
        }
        .metric-icon {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-heart-pulse me-2"></i>System Health Monitor
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise me-1"></i> Refresh
                        </button>
                    </div>
                </div>

                <!-- Overall Health Status -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card health-card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-speedometer2 me-2"></i>Overall System Status
                                </h5>
                                <div class="row">
                                    <?php foreach ($health_checks as $check_name => $check): ?>
                                        <div class="col-md-3 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="metric-icon me-3">
                                                    <i class="bi <?php echo getStatusIcon($check['status']); ?> fs-4"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0"><?php echo ucfirst(str_replace('_', ' ', $check_name)); ?></h6>
                                                    <span class="badge <?php echo getStatusBadge($check['status']); ?>">
                                                        <?php echo ucfirst($check['status']); ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Health Metrics -->
                <div class="row">
                    <!-- Database Health -->
                    <div class="col-md-6 mb-4">
                        <div class="card health-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-database me-2"></i>Database Health
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi <?php echo getStatusIcon($health_checks['database']['status']); ?> fs-4 me-2"></i>
                                    <span><?php echo h($health_checks['database']['message']); ?></span>
                                </div>
                                
                                <?php if (!empty($db_stats['table_sizes'])): ?>
                                    <h6>Table Sizes</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Table</th>
                                                    <th>Size (MB)</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach (array_slice($db_stats['table_sizes'], 0, 5) as $table): ?>
                                                    <tr>
                                                        <td><?php echo h($table['table_name']); ?></td>
                                                        <td><?php echo h($table['size_mb']); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                                
                                <small class="text-muted">
                                    Active Connections: <?php echo h($db_stats['connections'] ?? 'N/A'); ?>
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Memory Usage -->
                    <div class="col-md-6 mb-4">
                        <div class="card health-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-memory me-2"></i>Memory Usage
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Current Usage</span>
                                    <span class="badge <?php echo getStatusBadge($health_checks['memory']['status']); ?>">
                                        <?php echo $health_checks['memory']['usage_percentage']; ?>%
                                    </span>
                                </div>
                                <div class="progress mb-3">
                                    <div class="progress-bar <?php echo str_replace('bg-', 'bg-', getStatusBadge($health_checks['memory']['status'])); ?>" 
                                         style="width: <?php echo $health_checks['memory']['usage_percentage']; ?>%"></div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <small class="text-muted">Current</small><br>
                                        <strong><?php echo $health_checks['memory']['current_usage']; ?></strong>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted">Peak</small><br>
                                        <strong><?php echo $health_checks['memory']['peak_usage']; ?></strong>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted">Limit</small><br>
                                        <strong><?php echo $health_checks['memory']['limit']; ?></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Disk Space -->
                    <div class="col-md-6 mb-4">
                        <div class="card health-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-hdd me-2"></i>Disk Space
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Disk Usage</span>
                                    <span class="badge <?php echo getStatusBadge($health_checks['disk_space']['status']); ?>">
                                        <?php echo $health_checks['disk_space']['used_percentage']; ?>%
                                    </span>
                                </div>
                                <div class="progress mb-3">
                                    <div class="progress-bar <?php echo str_replace('bg-', 'bg-', getStatusBadge($health_checks['disk_space']['status'])); ?>" 
                                         style="width: <?php echo $health_checks['disk_space']['used_percentage']; ?>%"></div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <small class="text-muted">Free Space</small><br>
                                        <strong><?php echo $health_checks['disk_space']['free_space']; ?></strong>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Total Space</small><br>
                                        <strong><?php echo $health_checks['disk_space']['total_space']; ?></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- PHP Information -->
                    <div class="col-md-6 mb-4">
                        <div class="card health-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-code-slash me-2"></i>PHP Environment
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi <?php echo getStatusIcon($health_checks['php']['status']); ?> fs-4 me-2"></i>
                                    <div>
                                        <strong>PHP <?php echo $health_checks['php']['version']; ?></strong><br>
                                        <small class="text-muted"><?php echo $health_checks['php']['message']; ?></small>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Max Execution Time</small><br>
                                        <strong><?php echo ini_get('max_execution_time'); ?>s</strong>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Upload Max Size</small><br>
                                        <strong><?php echo ini_get('upload_max_filesize'); ?></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="row">
                    <div class="col-12">
                        <div class="card health-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-info-circle me-2"></i>System Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <small class="text-muted">Server Software</small><br>
                                        <strong><?php echo h($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'); ?></strong>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">Operating System</small><br>
                                        <strong><?php echo h(PHP_OS); ?></strong>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">Server Time</small><br>
                                        <strong><?php echo date('Y-m-d H:i:s'); ?></strong>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">Uptime</small><br>
                                        <strong id="uptime">Calculating...</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh every 30 seconds
        setInterval(function() {
            location.reload();
        }, 30000);
        
        // Calculate uptime (simplified)
        document.getElementById('uptime').textContent = 'Active';
    </script>
</body>
</html>
