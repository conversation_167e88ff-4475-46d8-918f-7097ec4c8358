<?php
// Direct admin access test - bypasses authentication for testing
echo "<h1>Admin Dashboard Direct Access Test</h1>";

echo "<h2>File System Check</h2>";
echo "<p><strong>Current directory:</strong> " . __DIR__ . "</p>";
echo "<p><strong>Dashboard file exists:</strong> " . (file_exists('dashboard.php') ? 'Yes' : 'No') . "</p>";
echo "<p><strong>Dashboard file readable:</strong> " . (is_readable('dashboard.php') ? 'Yes' : 'No') . "</p>";

echo "<h2>URL Information</h2>";
echo "<p><strong>Current URL:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p><strong>Script Name:</strong> " . $_SERVER['SCRIPT_NAME'] . "</p>";

echo "<h2>Database Test</h2>";
try {
    require_once '../config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Test basic query
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM books");
    $stmt->execute();
    $result = $stmt->fetch();
    echo "<p>📚 Total books in database: " . ($result['count'] ?? 0) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<h2>Session Test</h2>";
session_start();
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Session data:</strong> " . print_r($_SESSION, true) . "</p>";

echo "<h2>Quick Links</h2>";
echo "<ul>";
echo "<li><a href='dashboard.php'>Try Dashboard (may require login)</a></li>";
echo "<li><a href='index.php'>Admin Index</a></li>";
echo "<li><a href='../login.php'>Login Page</a></li>";
echo "<li><a href='../home.php'>Home Page</a></li>";
echo "</ul>";

echo "<h2>Authentication Status</h2>";
try {
    require_once '../includes/functions.php';
    echo "<p><strong>Is logged in:</strong> " . (isLoggedIn() ? 'Yes' : 'No') . "</p>";
    echo "<p><strong>Is admin:</strong> " . (isAdmin() ? 'Yes' : 'No') . "</p>";
} catch (Exception $e) {
    echo "<p style='color: orange;'>⚠️ Could not check authentication: " . $e->getMessage() . "</p>";
}
?>
