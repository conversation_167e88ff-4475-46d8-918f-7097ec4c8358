<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if user wants to login as admin
if (isset($_POST['login_as_admin'])) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        // Get the first admin user
        $query = "SELECT * FROM users WHERE role = 'admin' LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $admin = $stmt->fetch();
        
        if ($admin) {
            // Set session variables for admin login
            $_SESSION['user_id'] = $admin['id'];
            $_SESSION['username'] = $admin['username'];
            $_SESSION['role'] = $admin['role'];
            $_SESSION['logged_in'] = true;
            
            // Redirect to admin dashboard
            header('Location: admin/dashboard.php');
            exit;
        } else {
            $error = "No admin user found in database.";
        }
    } catch (Exception $e) {
        $error = "Database error: " . $e->getMessage();
    }
}

// Check if user wants to create admin
if (isset($_POST['create_admin'])) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $username = 'admin';
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        $email = '<EMAIL>';
        
        // Check if admin already exists
        $check_query = "SELECT COUNT(*) as count FROM users WHERE username = :username OR role = 'admin'";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->bindParam(':username', $username);
        $check_stmt->execute();
        $exists = $check_stmt->fetch()['count'] > 0;
        
        if (!$exists) {
            $query = "INSERT INTO users (username, password, email, role, created_at) VALUES (:username, :password, :email, 'admin', NOW())";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':password', $password);
            $stmt->bindParam(':email', $email);
            
            if ($stmt->execute()) {
                $success = "Admin user created successfully! Username: admin, Password: admin123";
            } else {
                $error = "Failed to create admin user.";
            }
        } else {
            $error = "Admin user already exists.";
        }
    } catch (Exception $e) {
        $error = "Database error: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Access Fix - LMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="bi bi-shield-lock me-2"></i>Admin Access Fix</h4>
                    </div>
                    <div class="card-body">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle me-2"></i><?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (isset($success)): ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle me-2"></i><?php echo $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <h5>Current Session Status:</h5>
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <p><strong>Logged In:</strong> <?php echo isLoggedIn() ? '<span class="text-success">Yes</span>' : '<span class="text-danger">No</span>'; ?></p>
                                <p><strong>Is Admin:</strong> <?php echo isAdmin() ? '<span class="text-success">Yes</span>' : '<span class="text-danger">No</span>'; ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>User ID:</strong> <?php echo $_SESSION['user_id'] ?? 'Not set'; ?></p>
                                <p><strong>Role:</strong> <?php echo $_SESSION['role'] ?? 'Not set'; ?></p>
                            </div>
                        </div>
                        
                        <?php
                        try {
                            $database = new Database();
                            $db = $database->getConnection();
                            
                            // Check for admin users
                            $query = "SELECT * FROM users WHERE role = 'admin'";
                            $stmt = $db->prepare($query);
                            $stmt->execute();
                            $admins = $stmt->fetchAll();
                            
                            echo "<h5>Available Admin Users:</h5>";
                            if (count($admins) > 0) {
                                echo "<div class='table-responsive'>";
                                echo "<table class='table table-sm'>";
                                echo "<thead><tr><th>ID</th><th>Username</th><th>Email</th><th>Created</th></tr></thead>";
                                echo "<tbody>";
                                foreach ($admins as $admin) {
                                    echo "<tr>";
                                    echo "<td>" . $admin['id'] . "</td>";
                                    echo "<td>" . $admin['username'] . "</td>";
                                    echo "<td>" . $admin['email'] . "</td>";
                                    echo "<td>" . ($admin['created_at'] ?? 'N/A') . "</td>";
                                    echo "</tr>";
                                }
                                echo "</tbody></table></div>";
                            } else {
                                echo "<p class='text-warning'>No admin users found in database.</p>";
                            }
                        } catch (Exception $e) {
                            echo "<p class='text-danger'>Database error: " . $e->getMessage() . "</p>";
                        }
                        ?>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <form method="POST" class="d-grid">
                                    <button type="submit" name="create_admin" class="btn btn-success">
                                        <i class="bi bi-person-plus me-2"></i>Create Admin User
                                    </button>
                                </form>
                                <small class="text-muted">Creates: admin / admin123</small>
                            </div>
                            <div class="col-md-6">
                                <form method="POST" class="d-grid">
                                    <button type="submit" name="login_as_admin" class="btn btn-primary">
                                        <i class="bi bi-box-arrow-in-right me-2"></i>Login as Admin
                                    </button>
                                </form>
                                <small class="text-muted">Auto-login as first admin</small>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="row">
                            <div class="col-md-4">
                                <a href="admin/dashboard.php" class="btn btn-outline-primary w-100">
                                    <i class="bi bi-speedometer2 me-2"></i>Admin Dashboard
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="login.php" class="btn btn-outline-secondary w-100">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>Regular Login
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="test_admin_session.php" class="btn btn-outline-info w-100">
                                    <i class="bi bi-bug me-2"></i>Debug Session
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
