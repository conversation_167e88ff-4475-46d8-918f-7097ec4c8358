/* Modern Hero Section */
.hero-section {
    background: linear-gradient(135deg, rgba(5, 5, 20, 0.92) 0%, rgba(10, 20, 40, 0.85) 100%) !important; /* Even darker gradient */
    position: relative;
    color: white !important;
    padding: 40px 0 20px; /* Further reduced padding */
    margin-bottom: 20px; /* Further reduced margin */
    overflow: hidden;
    min-height: 100vh; /* Set to 100vh to ensure it fits exactly one screen */
    height: 100vh; /* Fixed height to ensure it fits exactly one screen */
    max-height: 100vh; /* Maximum height to ensure it doesn't overflow */
    display: flex;
    align-items: center;
    box-shadow: inset 0 0 150px rgba(0, 0, 0, 0.8); /* Stronger inner shadow */
}

/* Add vignette effect for better contrast */
.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, transparent 40%, rgba(0, 0, 0, 0.6) 100%);
    z-index: 1;
    pointer-events: none;
}

/* Add subtle pattern overlay for depth */
.hero-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/png;base64,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');
    opacity: 0.03;
    z-index: 1;
    pointer-events: none;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../../uploads/images/library.jpg') center/cover no-repeat !important;
    opacity: 1 !important; /* Increased opacity for better visibility */
    z-index: 1;
}

/* Add a semi-transparent dark overlay to improve text readability */
.hero-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to right,
        rgba(0,0,0,0.7) 0%,
        rgba(0,0,0,0.6) 30%,
        rgba(0,0,0,0.5) 60%,
        rgba(0,0,0,0.4) 100%); /* Adjusted gradient for better readability with library image */
    z-index: 1;
    box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.5); /* Inner shadow for depth */
}

.hero-section .container {
    z-index: 5; /* Increased z-index to be above all overlays */
    position: relative; /* Ensure proper stacking */
}

.min-vh-75 {
    min-height: 100vh;
    height: 100%;
}

.hero-content {
    padding: 1.5rem; /* Further reduced padding */
    background: rgba(0, 0, 0, 0.6); /* Even darker background for better contrast */
    border-radius: 20px;
    backdrop-filter: blur(10px); /* Stronger blur effect */
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4), 0 0 20px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.15); /* Enhanced border */
    position: relative; /* For the glow effect */
    overflow: hidden; /* Contain the glow */
    animation: pulseGlow 6s ease-in-out infinite; /* Subtle pulsing animation */
}

@keyframes pulseGlow {
    0%, 100% { box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4), 0 0 20px rgba(0, 0, 0, 0.3); }
    50% { box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4), 0 0 30px rgba(0, 212, 255, 0.2); }
}

/* Add subtle glow effect to the hero content */
.hero-content::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 212, 255, 0.15) 0%, rgba(0, 0, 0, 0) 70%);
    opacity: 0.7;
    z-index: -1;
    animation: rotateGradient 15s linear infinite; /* Rotating animation */
}

/* Add a subtle border glow */
.hero-content::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        rgba(0, 212, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.05) 75%,
        rgba(0, 212, 255, 0.1) 100%);
    border-radius: 22px; /* Slightly larger than the content border-radius */
    z-index: -1;
    opacity: 0.5;
    animation: borderGlow 4s ease-in-out infinite; /* Pulsing animation */
}

@keyframes rotateGradient {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes borderGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.7; }
}

.hero-title {
    font-size: 3.5rem; /* Smaller font size */
    font-weight: 900; /* Bolder */
    line-height: 1; /* Even tighter line height */
    margin-bottom: 0.5rem; /* Reduced margin */
    letter-spacing: -0.5px;
    opacity: 1; /* Make visible immediately */
    transform: translateY(0); /* Remove initial transform */
    animation: fadeInUp 0.8s ease forwards;
    animation-delay: 0.3s;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8), 0 0 1px rgba(0, 0, 0, 1); /* Enhanced shadow */
    color: #ffffff; /* Ensure white is bright */
}

.hero-title .highlight {
    color: #00d4ff; /* Brighter blue color */
    position: relative;
    display: inline-block;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5); /* Add glow effect */
    font-weight: 900; /* Make it bolder */
}

.hero-title .highlight:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 8px;
    background-color: #00d4ff; /* Match the new highlight color */
    opacity: 0.5; /* Slightly more visible */
    border-radius: 4px;
    animation: expandWidth 1s ease forwards;
    animation-delay: 1.2s;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5); /* Add glow effect to match text */
}

.hero-subtitle {
    font-size: 1.2rem; /* Smaller font size */
    font-weight: 500; /* Bolder */
    opacity: 1; /* Make visible immediately */
    transform: translateY(0); /* Remove initial transform */
    animation: fadeInUp 0.8s ease forwards;
    animation-delay: 0.6s;
    text-shadow: 1px 1px 5px rgba(0, 0, 0, 0.9), 0 0 2px rgba(0, 0, 0, 1); /* Enhanced shadow */
    line-height: 1.4; /* Tighter line height */
    max-width: 90%; /* Prevent text from stretching too wide */
    margin-bottom: 1rem; /* Less space below */
    position: relative; /* For the highlight effect */
    display: inline-block; /* For the highlight effect */
}

/* Add subtle highlight effect to subtitle */
.hero-subtitle::after {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    height: 30%;
    width: 5px;
    background: #00d4ff; /* Match our highlight color */
    transform: translateY(-50%);
    border-radius: 3px;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5); /* Glow effect */
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes expandWidth {
    to {
        width: 100%;
    }
}

.search-container {
    position: relative;
    max-width: 500px;
    opacity: 1; /* Make visible immediately */
    transform: translateY(0); /* Remove initial transform */
    animation: fadeInUp 0.8s ease forwards;
    animation-delay: 0.9s;
}

.search-form .form-control {
    border-radius: 50px;
    padding: 10px 20px; /* Reduced padding */
    border: 2px solid rgba(255, 255, 255, 0.2); /* Subtle border */
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3); /* Stronger shadow */
    transition: all 0.3s ease;
    font-size: 1rem; /* Smaller font */
    background-color: rgba(255, 255, 255, 0.95); /* Slightly transparent */
    height: 45px; /* Fixed height */
}

.search-form .form-control:focus {
    box-shadow: 0 4px 25px rgba(76, 201, 240, 0.25);
    transform: translateY(-2px);
}

.search-form .btn {
    border-radius: 0 50px 50px 0;
    padding: 10px 20px; /* Reduced padding */
    background-color: #00d4ff; /* Match our highlight color */
    border-color: #00d4ff;
    transition: all 0.3s ease;
    font-size: 1rem; /* Smaller font */
    font-weight: 600;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3); /* Match input shadow */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* Text shadow for readability */
    height: 45px; /* Fixed height */
}

.search-form .btn i {
    color: #000; /* Black icon */
}

.search-form .btn:hover {
    background-color: #00bfe6; /* Slightly darker */
    border-color: #00bfe6;
    transform: translateX(3px);
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.4); /* Glowing effect */
}

/* Theme toggle button next to search bar */
.theme-toggle-btn {
    border-radius: 50%;
    width: 45px; /* Match search bar height */
    height: 45px; /* Match search bar height */
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    background-color: #212529;
    border-color: #212529;
}

.theme-toggle-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
}

.theme-toggle-btn i {
    font-size: 1.1rem;
}

.hero-stats {
    margin-top: 0.5rem; /* Reduced margin */
    opacity: 1; /* Make visible immediately */
    transform: translateY(0); /* Remove initial transform */
    animation: fadeInUp 0.8s ease forwards;
    animation-delay: 1.2s;
    display: flex; /* Ensure flex display */
    flex-wrap: wrap; /* Allow wrapping on small screens */
    gap: 8px; /* Reduced gap */
}

.stat-item {
    text-align: center;
    background: rgba(0, 0, 0, 0.4); /* Darker background for better contrast */
    border-radius: 8px;
    padding: 6px 15px; /* Further reduced padding */
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item:hover {
    transform: translateY(-5px);
    background: rgba(0, 0, 0, 0.5); /* Slightly darker on hover */
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    border-color: rgba(0, 212, 255, 0.3); /* Subtle glow border */
}

.stat-number {
    font-size: 1.3rem; /* Even smaller font size */
    font-weight: 700;
    color: #00d4ff; /* Match the highlight color */
    text-shadow: 0 0 8px rgba(0, 212, 255, 0.5); /* Add glow effect */
    line-height: 1.1; /* Tighter line height */
}

.stat-label {
    font-size: 0.7rem; /* Even smaller font size */
    opacity: 0.9; /* Slightly more visible */
    text-transform: uppercase;
    letter-spacing: 0.5px; /* Reduced letter spacing */
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5); /* Add text shadow */
    font-weight: 600; /* Make it slightly bolder */
    line-height: 1.1; /* Tighter line height */
}

.hero-buttons {
    opacity: 1; /* Make visible immediately */
    transform: translateY(0); /* Remove initial transform */
    animation: fadeInUp 0.8s ease forwards;
    animation-delay: 1.5s;
    margin-top: 0.5rem; /* Reduced top margin */
}

.hero-buttons .btn {
    border-radius: 50px;
    padding: 6px 15px; /* Further reduced padding */
    font-weight: 600;
    font-size: 0.85rem; /* Even smaller font */
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.hero-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s ease;
}

.hero-buttons .btn:hover::before {
    left: 100%;
}

.hero-buttons .btn-primary {
    background-color: #00d4ff; /* Match our highlight color */
    border-color: #00d4ff;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 212, 255, 0.3); /* Add glow */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* Text shadow for readability */
    font-size: 1.1rem; /* Larger font */
    letter-spacing: 0.5px; /* Spacing for better readability */
}

.hero-buttons .btn-primary:hover {
    background-color: #00bfe6; /* Slightly darker */
    border-color: #00bfe6;
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 212, 255, 0.5); /* Enhanced glow */
}

.hero-buttons .btn-outline-light {
    border-width: 2px; /* Thicker border */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); /* Text shadow for readability */
    font-size: 1.1rem; /* Larger font */
    letter-spacing: 0.5px; /* Spacing for better readability */
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3); /* Add shadow */
}

.hero-buttons .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.15); /* Slightly more visible */
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3); /* Enhanced shadow */
    border-color: #ffffff; /* Brighter border */
}

/* Hero Image Animation */
.hero-image-container {
    display: none; /* Hide the entire container as requested */
}

.hero-circle {
    display: none; /* Hide the circle as requested */
}

.floating-book {
    display: none; /* Hide the floating books as requested */
}

.book-1, .book-2, .book-3 {
    display: none; /* Hide all book elements as requested */
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

@keyframes scaleIn {
    to {
        transform: scale(1);
    }
}

@keyframes bookAppear {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float1 {
    0%, 100% { transform: translateY(0) rotate(-5deg); }
    50% { transform: translateY(-15px) rotate(-5deg); }
}

@keyframes float2 {
    0%, 100% { transform: translateY(0) rotate(8deg); }
    50% { transform: translateY(-20px) rotate(8deg); }
}

@keyframes float3 {
    0%, 100% { transform: translateY(0) rotate(-10deg); }
    50% { transform: translateY(-10px) rotate(-10deg); }
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 15px; /* Positioned closer to the bottom */
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    animation: bounce 2s infinite;
    z-index: 10; /* Ensure it's above other elements */
}

.scroll-text {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 5px;
    opacity: 0.9; /* More visible */
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7); /* Add text shadow */
    font-weight: 600; /* Bolder */
}

.scroll-icon {
    font-size: 1.8rem; /* Larger icon */
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5); /* Glow effect */
    color: #00d4ff; /* Match our highlight color */
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0) translateX(-50%); }
    40% { transform: translateY(-10px) translateX(-50%); }
    60% { transform: translateY(-5px) translateX(-50%); }
}

/* Responsive adjustments for better mobile experience */
@media (max-width: 991.98px) {
    .hero-title {
        font-size: 3rem; /* Smaller font on mobile */
    }

    .hero-subtitle {
        font-size: 1.2rem; /* Smaller font on mobile */
        max-width: 100%; /* Full width on mobile */
    }

    .hero-content {
        padding: 2rem; /* Less padding on mobile */
    }

    .search-form .form-control,
    .search-form .btn {
        padding: 12px 20px; /* Smaller padding on mobile */
        font-size: 1rem; /* Smaller font on mobile */
    }

    .theme-toggle-btn {
        width: 45px;
        height: 45px;
    }

    .hero-buttons .btn {
        font-size: 1rem; /* Smaller font on mobile */
        padding: 10px 20px; /* Smaller padding on mobile */
    }

    .stat-item {
        padding: 10px 15px; /* Smaller padding on mobile */
    }
}

@media (max-width: 575.98px) {
    .hero-title {
        font-size: 2.5rem; /* Even smaller font on small mobile */
    }

    .hero-section {
        padding: 80px 0 60px; /* Less padding on small mobile */
    }

    .hero-stats {
        justify-content: center; /* Center stats on small mobile */
    }

    .theme-toggle-btn {
        width: 40px;
        height: 40px;
    }

    .theme-toggle-btn i {
        font-size: 1rem;
    }
}

/* For very small screens, stack the search and toggle button */
@media (max-width: 400px) {
    .search-container .d-flex {
        flex-direction: column;
    }

    .search-form {
        margin-bottom: 10px;
        margin-right: 0 !important;
    }

    .theme-toggle-btn {
        align-self: flex-end;
    }
}

.stats-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transition: transform 0.3s;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.book-card {
    height: 100%;
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.3s;
}

.book-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.book-cover-link {
    display: block;
    background-color: #f8f9fa;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    overflow: hidden;
    text-align: center;
    transition: all 0.3s ease;
}

.book-cover-link:hover {
    background-color: #e9ecef;
}

.card-img-top {
    height: 250px;
    object-fit: contain;
    padding: 10px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    transition: transform 0.3s ease;
}

.book-cover-link:hover .card-img-top {
    transform: scale(1.05);
}

/* Modern Featured Books Section */
.featured-books-section {
    background-color: #f8f9fa;
    padding: 80px 0;
    position: relative;
}

.section-header {
    margin-bottom: 50px;
}

.section-pre-title {
    color: #00d4ff; /* Match our highlight color */
    text-transform: uppercase;
    letter-spacing: 3px;
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-shadow: 0 0 5px rgba(0, 212, 255, 0.3); /* Subtle glow */
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: #333;
}

.section-line {
    width: 80px;
    height: 3px;
    background-color: #00d4ff; /* Match our highlight color */
    margin: 0 auto 20px;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5); /* Add glow effect */
    border-radius: 3px;
}

.section-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.modern-book-card {
    background-color: #fff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.modern-book-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.book-cover-wrapper {
    position: relative;
    overflow: hidden;
    padding-top: 20px;
    background-color: #f8f9fa;
    display: flex;
    justify-content: center;
    align-items: center;
}

.book-cover {
    height: 250px;
    width: 180px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;
}

.book-cover img {
    max-height: 100%;
    max-width: 100%;
    object-fit: contain;
}

.book-cover.no-image {
    background-color: #e9ecef;
    color: #adb5bd;
}

.book-actions {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
}

.modern-book-card:hover .book-actions {
    opacity: 1;
    transform: translateX(0);
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    margin-bottom: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.action-btn:hover {
    background-color: #4cc9f0;
    color: #fff;
}

.book-info {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.book-category {
    margin-bottom: 10px;
}

.book-category span {
    background-color: #f0f7ff;
    color: #4cc9f0;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.book-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    line-height: 1.4;
}

.book-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.book-title a:hover {
    color: #4cc9f0;
}

.book-author {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.book-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.availability {
    font-size: 0.9rem;
    font-weight: 500;
}

.availability.available {
    color: #28a745;
}

.availability.unavailable {
    color: #dc3545;
}

.details-link {
    color: #4cc9f0;
    font-weight: 600;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.details-link:hover {
    color: #3db8df;
}

.details-link i {
    transition: transform 0.3s ease;
}

.details-link:hover i {
    transform: translateX(5px);
}

.card-img-placeholder {
    height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e9ecef;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

/* Modern Services Section */
.services-section {
    background-color: #f8f9fa;
    padding: 80px 0;
}

.service-card {
    background-color: #fff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 30px;
    position: relative;
    z-index: 1;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #4cc9f0 0%, #4361ee 100%);
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.service-card:hover::before {
    opacity: 1;
}

.service-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: rgba(76, 201, 240, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 2rem;
    color: #4cc9f0;
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
}

.service-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.service-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: #333;
    transition: color 0.3s ease;
}

.service-card:hover .service-title {
    color: #fff;
}

.service-description {
    color: #6c757d;
    margin-bottom: 20px;
    flex-grow: 1;
    transition: color 0.3s ease;
}

.service-card:hover .service-description {
    color: rgba(255, 255, 255, 0.9);
}

.service-link {
    color: #4cc9f0;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    margin-top: auto;
}

.service-link i {
    margin-left: 5px;
    transition: transform 0.3s ease;
}

.service-link:hover i {
    transform: translateX(5px);
}

.service-card:hover .service-link {
    color: #fff;
}

.navbar {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Modern Categories Section */
.categories-section {
    background-color: #fff;
    padding: 80px 0;
}

.modern-category-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 25px 15px;
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 100%;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.modern-category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #4cc9f0, #4361ee);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.modern-category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.modern-category-card:hover::before {
    transform: scaleX(1);
}

.category-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 1.8rem;
    transition: all 0.3s ease;
}

.category-icon.fiction {
    background-color: rgba(76, 201, 240, 0.1);
    color: #4cc9f0;
}

.category-icon.non-fiction {
    background-color: rgba(67, 97, 238, 0.1);
    color: #4361ee;
}

.category-icon.science {
    background-color: rgba(247, 183, 49, 0.1);
    color: #f7b731;
}

.category-icon.history {
    background-color: rgba(235, 77, 75, 0.1);
    color: #eb4d4b;
}

.category-icon.biography {
    background-color: rgba(32, 191, 107, 0.1);
    color: #20bf6b;
}

.category-icon.all {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.modern-category-card:hover .category-icon {
    transform: scale(1.1);
}

.modern-category-card:hover .category-icon.fiction {
    background-color: #4cc9f0;
    color: #fff;
}

.modern-category-card:hover .category-icon.non-fiction {
    background-color: #4361ee;
    color: #fff;
}

.modern-category-card:hover .category-icon.science {
    background-color: #f7b731;
    color: #fff;
}

.modern-category-card:hover .category-icon.history {
    background-color: #eb4d4b;
    color: #fff;
}

.modern-category-card:hover .category-icon.biography {
    background-color: #20bf6b;
    color: #fff;
}

.modern-category-card:hover .category-icon.all {
    background-color: #6c757d;
    color: #fff;
}

.category-name {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    text-align: center;
    transition: color 0.3s ease;
}

.modern-category-card:hover .category-name {
    color: #4cc9f0;
}

.category-arrow {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.modern-category-card:hover .category-arrow {
    opacity: 1;
    transform: translateY(0);
}

/* Event Cards */
.event-card {
    transition: all 0.3s ease;
    border-radius: 10px;
    overflow: hidden;
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.event-date {
    display: inline-block;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 8px 15px;
    text-align: center;
    line-height: 1;
}

.event-month {
    display: block;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    color: #6c757d;
}

.event-day {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #0d6efd;
}

/* Call to Action Section */
.cta-section {
    background: linear-gradient(rgba(13, 110, 253, 0.9), rgba(13, 110, 253, 0.8)), url('../../uploads/images/library.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: white;
    position: relative;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.cta-section .container {
    position: relative;
    z-index: 2;
}

.cta-section .btn {
    transition: all 0.3s ease;
    border-radius: 30px;
    font-weight: 500;
}

.cta-section .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2) !important;
}
