<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if book ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: catalog.php');
    exit;
}

$book_id = (int)$_GET['id'];

// Connect to database
try {
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        die('Database connection failed');
    }
} catch (Exception $e) {
    die('Database error: ' . $e->getMessage());
}

// Get book details
try {
    $query = "SELECT * FROM books WHERE id = :book_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':book_id', $book_id);
    $stmt->execute();

    if ($stmt->rowCount() === 0) {
        header('Location: catalog.php');
        exit;
    }
} catch (Exception $e) {
    die('Error fetching book details: ' . $e->getMessage());
}

$book = $stmt->fetch();

// Get related books from the same category
$related_books = [];
if (!empty($book['category'])) {
    $query = "SELECT * FROM books
              WHERE category = :category
              AND id != :book_id
              ORDER BY RAND()
              LIMIT 4";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':category', $book['category']);
    $stmt->bindParam(':book_id', $book_id);
    $stmt->execute();
    $related_books = $stmt->fetchAll();
}

// Check if member is logged in and has already borrowed this book
$has_borrowed = false;
$can_reserve = false;
$is_reserved = false;

if (isMemberLoggedIn()) {
    $member_id = $_SESSION['member_id'];

    // Check if member has already borrowed this book
    $query = "SELECT * FROM book_loans
              WHERE book_id = :book_id AND member_id = :member_id AND status != 'returned'";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':book_id', $book_id);
    $stmt->bindParam(':member_id', $member_id);
    $stmt->execute();
    $has_borrowed = $stmt->rowCount() > 0;

    // Check if member has already reserved this book
    $query = "SELECT * FROM book_reservations
              WHERE book_id = :book_id AND member_id = :member_id AND status IN ('pending', 'ready')";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':book_id', $book_id);
    $stmt->bindParam(':member_id', $member_id);
    $stmt->execute();
    $is_reserved = $stmt->rowCount() > 0;

    // Check if book is available for reservation (not available for borrowing but not already reserved by this member)
    $can_reserve = ($book['available_quantity'] == 0) && !$has_borrowed && !$is_reserved;
}

// Process reservation request
$reservation_success = false;
$reservation_error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reserve']) && isMemberLoggedIn()) {
    $member_id = $_SESSION['member_id'];

    // Check if book is already available
    if ($book['available_quantity'] > 0) {
        $reservation_error = 'This book is currently available. You can borrow it directly.';
    }
    // Check if member has already borrowed this book
    elseif ($has_borrowed) {
        $reservation_error = 'You have already borrowed this book.';
    }
    // Check if member has already reserved this book
    elseif ($is_reserved) {
        $reservation_error = 'You have already reserved this book.';
    }
    else {
        try {
            // Set expiry date to 3 days after the book becomes available
            $expiry_date = date('Y-m-d', strtotime('+3 days'));

            // Insert reservation
            $query = "INSERT INTO book_reservations (book_id, member_id, expiry_date, status)
                      VALUES (:book_id, :member_id, :expiry_date, 'pending')";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':book_id', $book_id);
            $stmt->bindParam(':member_id', $member_id);
            $stmt->bindParam(':expiry_date', $expiry_date);

            if ($stmt->execute()) {
                $reservation_success = true;
                $is_reserved = true;
                $can_reserve = false;
            } else {
                $reservation_error = 'Failed to reserve the book. Please try again.';
            }
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) { // Duplicate entry error
                $reservation_error = 'You have already reserved this book.';
            } else {
                $reservation_error = 'Database error: ' . $e->getMessage();
            }
        }
    }
}

// Process borrowing request
$borrow_success = false;
$borrow_error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['borrow']) && isMemberLoggedIn()) {
    $member_id = $_SESSION['member_id'];

    // Check if book is available
    if ($book['available_quantity'] <= 0) {
        $borrow_error = 'This book is not currently available for borrowing.';
    }
    // Check if member has already borrowed this book
    elseif ($has_borrowed) {
        $borrow_error = 'You have already borrowed this book.';
    }
    else {
        try {
            // Check if member has reached the maximum allowed loans
            $query = "SELECT COUNT(*) as loan_count FROM book_loans
                      WHERE member_id = :member_id AND status = 'borrowed'";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':member_id', $member_id);
            $stmt->execute();
            $loan_count = $stmt->fetch()['loan_count'];

            // Get max loans setting (default to 5)
            $query = "SELECT setting_value FROM settings WHERE setting_key = 'max_loans_per_member'";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $max_loans = $stmt->rowCount() > 0 ? (int)$stmt->fetch()['setting_value'] : 5;

            if ($loan_count >= $max_loans) {
                $borrow_error = "You have reached the maximum number of loans ($max_loans).";
            } else {
                // Start transaction
                $db->beginTransaction();

                // Set dates
                $issue_date = date('Y-m-d');
                $due_date = date('Y-m-d', strtotime('+14 days')); // 2 weeks loan period

                // Insert loan record
                $query = "INSERT INTO book_loans (book_id, member_id, issue_date, due_date, status)
                          VALUES (:book_id, :member_id, :issue_date, :due_date, 'borrowed')";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':book_id', $book_id);
                $stmt->bindParam(':member_id', $member_id);
                $stmt->bindParam(':issue_date', $issue_date);
                $stmt->bindParam(':due_date', $due_date);

                if ($stmt->execute()) {
                    // Update book available quantity
                    $query = "UPDATE books SET available_quantity = available_quantity - 1
                              WHERE id = :book_id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':book_id', $book_id);

                    if ($stmt->execute()) {
                        // Log activity
                        logActivity($db, 'checkout', "Member borrowed: {$book['title']}", 'book', $book_id);

                        // Commit transaction
                        $db->commit();

                        $borrow_success = true;
                        $has_borrowed = true;

                        // Update book data to reflect new availability
                        $book['available_quantity']--;

                    } else {
                        $db->rollBack();
                        $borrow_error = 'Failed to update book availability.';
                    }
                } else {
                    $db->rollBack();
                    $borrow_error = 'Failed to create loan record.';
                }
            }
        } catch (PDOException $e) {
            $db->rollBack();
            $borrow_error = 'Database error: ' . $e->getMessage();
        }
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo h($book['title']); ?> - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .book-container {
            max-width: 1000px;
            margin: 20px auto;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .book-cover {
            max-height: 400px;
            object-fit: contain;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        .book-cover:hover {
            transform: scale(1.03);
        }
        .book-cover-placeholder {
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #e9ecef;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .modal-img {
            max-width: 100%;
            max-height: 80vh;
        }
        .navbar {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .related-books {
            margin-top: 2rem;
            padding: 2rem 0;
            background-color: #f8f9fa;
            border-radius: 15px;
        }
        .related-book-card {
            transition: all 0.3s ease;
            height: 100%;
            border: none;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        .related-book-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        .related-book-img {
            height: 200px;
            object-fit: contain;
            padding: 1rem;
            background-color: #f8f9fa;
        }
        .related-book-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 2.5rem;
        }
        .related-book-author {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 1rem;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="home.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo isMemberLoggedIn() ? 'member_dashboard.php' : 'home.php'; ?>">
                            <?php echo isMemberLoggedIn() ? 'Dashboard' : 'Home'; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="catalog.php">Book Catalog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">About Us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">Contact</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <?php if (isLoggedIn()): ?>
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle me-1"></i><?php echo h($_SESSION['username']); ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton">
                                <li><a class="dropdown-item" href="admin/dashboard.php">Admin Dashboard</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                            </ul>
                        </div>
                    <?php elseif (isMemberLoggedIn()): ?>
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle me-1"></i><?php echo h($_SESSION['member_name']); ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton">
                                <li><a class="dropdown-item" href="member_dashboard.php">My Dashboard</a></li>
                                <li><a class="dropdown-item" href="member_profile.php">My Profile</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-outline-light me-2">Login</a>
                        <a href="register.php" class="btn btn-primary">Register</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <div class="book-container">
        <div class="mb-3">
            <a href="catalog.php" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Catalog
            </a>
        </div>

        <?php if ($borrow_success): ?>
            <div class="alert alert-success">
                <i class="bi bi-check-circle-fill me-2"></i>
                Book borrowed successfully! Due date: <?php echo date('F j, Y', strtotime('+14 days')); ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($borrow_error)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <?php echo h($borrow_error); ?>
            </div>
        <?php endif; ?>

        <?php if ($reservation_success): ?>
            <div class="alert alert-success">
                <i class="bi bi-check-circle-fill me-2"></i>
                Book reserved successfully! You will be notified when the book becomes available.
            </div>
        <?php endif; ?>

        <?php if (!empty($reservation_error)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <?php echo h($reservation_error); ?>
            </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-4 mb-md-0">
                        <?php if (!empty($book['cover_image'])): ?>
                            <?php
                            // Check if the cover_image is a URL or a local file
                            if (strpos($book['cover_image'], 'http') === 0) {
                                $image_src = $book['cover_image'];
                            } else {
                                // Try different paths to find the image
                                $possible_paths = [
                                    'uploads/covers/' . $book['cover_image'],
                                    './uploads/covers/' . $book['cover_image']
                                ];

                                $image_src = 'uploads/covers/' . $book['cover_image']; // Default
                                foreach ($possible_paths as $path) {
                                    if (file_exists($path)) {
                                        $image_src = $path;
                                        break;
                                    }
                                }
                            }
                            ?>
                            <img src="<?php echo h($image_src); ?>" class="book-cover" alt="<?php echo h($book['title']); ?>"
                                 data-bs-toggle="modal" data-bs-target="#coverModal"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                            <div class="book-cover-placeholder" style="display: none;">
                                <i class="bi bi-book fs-1 text-secondary"></i>
                            </div>
                        <?php else: ?>
                            <div class="book-cover-placeholder">
                                <i class="bi bi-book fs-1 text-secondary"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-8">
                        <h2 class="mb-2"><?php echo h($book['title']); ?></h2>
                        <h5 class="text-muted mb-3">by <?php echo h($book['author']); ?></h5>

                        <div class="mb-3">
                            <?php if (!empty($book['category'])): ?>
                                <span class="badge bg-secondary"><?php echo h($book['category']); ?></span>
                            <?php endif; ?>
                            <?php if (!empty($book['publication_year'])): ?>
                                <span class="badge bg-light text-dark"><?php echo h($book['publication_year']); ?></span>
                            <?php endif; ?>
                        </div>

                        <!-- Share buttons -->
                        <div class="mb-3">
                            <div class="d-flex align-items-center">
                                <span class="me-2">Share:</span>
                                <div class="share-buttons">
                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(getCurrentUrl()); ?>" target="_blank" class="btn btn-sm btn-outline-primary me-1" title="Share on Facebook">
                                        <i class="bi bi-facebook"></i>
                                    </a>
                                    <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(getCurrentUrl()); ?>&text=<?php echo urlencode('Check out this book: ' . $book['title']); ?>" target="_blank" class="btn btn-sm btn-outline-info me-1" title="Share on Twitter">
                                        <i class="bi bi-twitter"></i>
                                    </a>
                                    <a href="mailto:?subject=<?php echo urlencode('Book Recommendation: ' . $book['title']); ?>&body=<?php echo urlencode('I thought you might be interested in this book: ' . $book['title'] . ' by ' . $book['author'] . '. Check it out here: ' . getCurrentUrl()); ?>" class="btn btn-sm btn-outline-secondary me-1" title="Share via Email">
                                        <i class="bi bi-envelope"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-dark copy-link" data-url="<?php echo h(getCurrentUrl()); ?>" title="Copy Link">
                                        <i class="bi bi-link-45deg"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <p><strong>ISBN:</strong> <?php echo h($book['isbn'] ?: 'N/A'); ?></p>
                            <p><strong>Publisher:</strong> <?php echo h($book['publisher'] ?: 'N/A'); ?></p>
                            <p><strong>Shelf Location:</strong> <?php echo h($book['shelf_location'] ?: 'N/A'); ?></p>
                            <p>
                                <strong>Availability:</strong>
                                <?php if ($book['available_quantity'] > 0): ?>
                                    <span class="text-success">
                                        <?php echo h($book['available_quantity']); ?> of <?php echo h($book['quantity']); ?> copies available
                                    </span>
                                <?php else: ?>
                                    <span class="text-danger">Currently unavailable</span>
                                <?php endif; ?>
                            </p>
                        </div>

                        <?php if (!empty($book['description'])): ?>
                            <div class="mb-4">
                                <h5>Description</h5>
                                <p><?php echo nl2br(h($book['description'])); ?></p>
                            </div>
                        <?php endif; ?>

                        <div class="d-grid gap-2 d-md-block">
                            <?php if (isLoggedIn()): ?>
                                <!-- Admin can always see book details but doesn't borrow -->
                                <a href="../books/edit.php?id=<?php echo $book_id; ?>" class="btn btn-warning">
                                    <i class="bi bi-pencil me-2"></i>Edit Book
                                </a>
                            <?php elseif (isMemberLoggedIn()): ?>
                                <?php if ($has_borrowed): ?>
                                    <button class="btn btn-secondary" disabled>
                                        <i class="bi bi-check-circle-fill me-2"></i>Already Borrowed
                                    </button>
                                <?php elseif ($is_reserved): ?>
                                    <button class="btn btn-secondary" disabled>
                                        <i class="bi bi-check-circle-fill me-2"></i>Reserved
                                    </button>
                                <?php elseif ($book['available_quantity'] > 0): ?>
                                    <form method="post" action="<?php echo h($_SERVER['PHP_SELF'] . '?id=' . $book_id); ?>" class="d-inline">
                                        <button type="submit" name="borrow" class="btn btn-primary">
                                            <i class="bi bi-journal-arrow-up me-2"></i>Borrow This Book
                                        </button>
                                    </form>
                                <?php elseif ($can_reserve): ?>
                                    <form method="post" action="<?php echo h($_SERVER['PHP_SELF'] . '?id=' . $book_id); ?>" class="d-inline">
                                        <button type="submit" name="reserve" class="btn btn-warning">
                                            <i class="bi bi-bookmark-plus me-2"></i>Reserve Book
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <button class="btn btn-secondary" disabled>
                                        <i class="bi bi-x-circle me-2"></i>Not Available
                                    </button>
                                <?php endif; ?>
                            <?php else: ?>
                                <a href="login.php" class="btn btn-primary">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>Login to Borrow
                                </a>
                            <?php endif; ?>
                            <a href="catalog.php" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Catalog
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($related_books)): ?>
        <div class="related-books mt-4">
            <div class="container">
                <h3 class="mb-4"><i class="bi bi-book-half me-2 text-primary"></i>Related Books in <?php echo h($book['category']); ?></h3>
                <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4">
                    <?php foreach ($related_books as $related_book): ?>
                        <div class="col">
                            <div class="related-book-card">
                                <div class="text-center">
                                    <?php if (!empty($related_book['cover_image'])): ?>
                                        <?php
                                        // Check if the cover_image is a URL or a local file
                                        if (strpos($related_book['cover_image'], 'http') === 0) {
                                            $related_image_src = $related_book['cover_image'];
                                        } else {
                                            $related_image_src = 'uploads/covers/' . $related_book['cover_image'];
                                        }
                                        ?>
                                        <img src="<?php echo h($related_image_src); ?>" class="related-book-img" alt="<?php echo h($related_book['title']); ?>"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                        <div class="related-book-img d-flex align-items-center justify-content-center" style="display: none;">
                                            <i class="bi bi-book fs-1 text-secondary"></i>
                                        </div>
                                    <?php else: ?>
                                        <div class="related-book-img d-flex align-items-center justify-content-center">
                                            <i class="bi bi-book fs-1 text-secondary"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="card-body">
                                    <h5 class="related-book-title"><?php echo h($related_book['title']); ?></h5>
                                    <p class="related-book-author">by <?php echo h($related_book['author']); ?></p>
                                    <div class="d-grid">
                                        <a href="book_details.php?id=<?php echo h($related_book['id']); ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye me-1"></i>View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <?php include 'includes/footer.php'; ?>

    <!-- Book Cover Modal -->
    <?php if (!empty($book['cover_image'])): ?>
    <div class="modal fade" id="coverModal" tabindex="-1" aria-labelledby="coverModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="coverModalLabel"><?php echo h($book['title']); ?> by <?php echo h($book['author']); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img src="<?php echo h($image_src); ?>" class="modal-img" alt="<?php echo h($book['title']); ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Copy link functionality
        document.addEventListener('DOMContentLoaded', function() {
            const copyLinkBtn = document.querySelector('.copy-link');
            if (copyLinkBtn) {
                copyLinkBtn.addEventListener('click', function() {
                    const url = this.getAttribute('data-url');
                    navigator.clipboard.writeText(url).then(function() {
                        // Change button text temporarily
                        const originalHTML = copyLinkBtn.innerHTML;
                        copyLinkBtn.innerHTML = '<i class="bi bi-check"></i> Copied!';
                        copyLinkBtn.classList.add('btn-success');
                        copyLinkBtn.classList.remove('btn-outline-dark');

                        setTimeout(function() {
                            copyLinkBtn.innerHTML = originalHTML;
                            copyLinkBtn.classList.remove('btn-success');
                            copyLinkBtn.classList.add('btn-outline-dark');
                        }, 2000);
                    }).catch(function(err) {
                        console.error('Could not copy text: ', err);
                    });
                });
            }
        });
    </script>
</body>
</html>
