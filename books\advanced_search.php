<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn() && !isMemberLoggedIn()) {
    redirect('../auth/login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Initialize variables
$books = [];
$total_results = 0;
$search_performed = false;

// Get all categories for filter dropdown
$query = "SELECT DISTINCT category FROM books WHERE category IS NOT NULL AND category != '' ORDER BY category";
$stmt = $db->prepare($query);
$stmt->execute();
$categories = $stmt->fetchAll();

// Get all publishers for filter dropdown
$query = "SELECT DISTINCT publisher FROM books WHERE publisher IS NOT NULL AND publisher != '' ORDER BY publisher";
$stmt = $db->prepare($query);
$stmt->execute();
$publishers = $stmt->fetchAll();

// Get publication years for filter dropdown
$query = "SELECT DISTINCT publication_year FROM books WHERE publication_year IS NOT NULL AND publication_year != 0 ORDER BY publication_year DESC";
$stmt = $db->prepare($query);
$stmt->execute();
$years = $stmt->fetchAll();

// Process search form
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['search'])) {
    $search_performed = true;
    
    // Get search parameters
    $title = trim($_GET['title'] ?? '');
    $author = trim($_GET['author'] ?? '');
    $isbn = trim($_GET['isbn'] ?? '');
    $category = $_GET['category'] ?? '';
    $publisher = $_GET['publisher'] ?? '';
    $year_from = !empty($_GET['year_from']) ? (int)$_GET['year_from'] : null;
    $year_to = !empty($_GET['year_to']) ? (int)$_GET['year_to'] : null;
    $availability = $_GET['availability'] ?? 'all';
    
    // Build query
    $query = "SELECT * FROM books WHERE 1=1";
    $params = [];
    
    if (!empty($title)) {
        $query .= " AND title LIKE :title";
        $params[':title'] = "%$title%";
    }
    
    if (!empty($author)) {
        $query .= " AND author LIKE :author";
        $params[':author'] = "%$author%";
    }
    
    if (!empty($isbn)) {
        $query .= " AND isbn LIKE :isbn";
        $params[':isbn'] = "%$isbn%";
    }
    
    if (!empty($category)) {
        $query .= " AND category = :category";
        $params[':category'] = $category;
    }
    
    if (!empty($publisher)) {
        $query .= " AND publisher = :publisher";
        $params[':publisher'] = $publisher;
    }
    
    if (!empty($year_from)) {
        $query .= " AND publication_year >= :year_from";
        $params[':year_from'] = $year_from;
    }
    
    if (!empty($year_to)) {
        $query .= " AND publication_year <= :year_to";
        $params[':year_to'] = $year_to;
    }
    
    if ($availability === 'available') {
        $query .= " AND available_quantity > 0";
    } elseif ($availability === 'unavailable') {
        $query .= " AND available_quantity = 0";
    }
    
    // Add order by
    $query .= " ORDER BY title ASC";
    
    // Execute query
    $stmt = $db->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $books = $stmt->fetchAll();
    $total_results = count($books);
}

// Page title
$page_title = 'Advanced Book Search';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include '../includes/head.php'; ?>
    <style>
        .search-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .search-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        .book-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            height: 100%;
        }
        .book-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        .book-cover {
            height: 200px;
            object-fit: cover;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }
        .book-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .book-category {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 3px 8px;
            border-radius: 20px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-search me-2 text-primary"></i>Advanced Book Search</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="<?php echo url('books/index.php'); ?>" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i> Back to Books
                        </a>
                    </div>
                </div>

                <!-- Advanced Search Form -->
                <div class="card search-card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-funnel me-2"></i>Search Filters</h5>
                    </div>
                    <div class="card-body">
                        <form action="" method="get" class="row g-3">
                            <div class="col-md-4">
                                <label for="title" class="form-label">Title</label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="<?php echo htmlspecialchars($_GET['title'] ?? ''); ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="author" class="form-label">Author</label>
                                <input type="text" class="form-control" id="author" name="author"
                                       value="<?php echo htmlspecialchars($_GET['author'] ?? ''); ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="isbn" class="form-label">ISBN</label>
                                <input type="text" class="form-control" id="isbn" name="isbn"
                                       value="<?php echo htmlspecialchars($_GET['isbn'] ?? ''); ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">All Categories</option>
                                    <?php foreach ($categories as $cat): ?>
                                        <option value="<?php echo htmlspecialchars($cat['category']); ?>"
                                                <?php echo (isset($_GET['category']) && $_GET['category'] === $cat['category']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($cat['category']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="publisher" class="form-label">Publisher</label>
                                <select class="form-select" id="publisher" name="publisher">
                                    <option value="">All Publishers</option>
                                    <?php foreach ($publishers as $pub): ?>
                                        <option value="<?php echo htmlspecialchars($pub['publisher']); ?>"
                                                <?php echo (isset($_GET['publisher']) && $_GET['publisher'] === $pub['publisher']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($pub['publisher']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="availability" class="form-label">Availability</label>
                                <select class="form-select" id="availability" name="availability">
                                    <option value="all" <?php echo (!isset($_GET['availability']) || $_GET['availability'] === 'all') ? 'selected' : ''; ?>>All Books</option>
                                    <option value="available" <?php echo (isset($_GET['availability']) && $_GET['availability'] === 'available') ? 'selected' : ''; ?>>Available Only</option>
                                    <option value="unavailable" <?php echo (isset($_GET['availability']) && $_GET['availability'] === 'unavailable') ? 'selected' : ''; ?>>Unavailable Only</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="year_from" class="form-label">Year From</label>
                                <select class="form-select" id="year_from" name="year_from">
                                    <option value="">Any Year</option>
                                    <?php foreach ($years as $year): ?>
                                        <option value="<?php echo $year['publication_year']; ?>"
                                                <?php echo (isset($_GET['year_from']) && (int)$_GET['year_from'] === (int)$year['publication_year']) ? 'selected' : ''; ?>>
                                            <?php echo $year['publication_year']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="year_to" class="form-label">Year To</label>
                                <select class="form-select" id="year_to" name="year_to">
                                    <option value="">Any Year</option>
                                    <?php foreach ($years as $year): ?>
                                        <option value="<?php echo $year['publication_year']; ?>"
                                                <?php echo (isset($_GET['year_to']) && (int)$_GET['year_to'] === (int)$year['publication_year']) ? 'selected' : ''; ?>>
                                            <?php echo $year['publication_year']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <div class="d-grid gap-2 d-md-flex w-100">
                                    <button type="submit" name="search" class="btn btn-primary flex-grow-1">
                                        <i class="bi bi-search me-2"></i>Search
                                    </button>
                                    <a href="advanced_search.php" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Clear
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Search Results -->
                <?php if ($search_performed): ?>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3 class="h4"><i class="bi bi-list-ul me-2 text-primary"></i>Search Results</h3>
                        <span class="badge bg-primary rounded-pill"><?php echo $total_results; ?> books found</span>
                    </div>

                    <?php if ($total_results > 0): ?>
                        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-4 g-4">
                            <?php foreach ($books as $book): ?>
                                <div class="col">
                                    <div class="card book-card h-100">
                                        <div class="position-relative">
                                            <?php if (!empty($book['cover_image']) && file_exists('../uploads/covers/' . $book['cover_image'])): ?>
                                                <img src="<?php echo url('uploads/covers/' . $book['cover_image']); ?>" class="book-cover w-100" alt="<?php echo htmlspecialchars($book['title']); ?>">
                                            <?php else: ?>
                                                <img src="<?php echo url('assets/img/book-placeholder.jpg'); ?>" class="book-cover w-100" alt="No Cover Available">
                                            <?php endif; ?>
                                            
                                            <?php if (!empty($book['category'])): ?>
                                                <span class="book-category"><?php echo htmlspecialchars($book['category']); ?></span>
                                            <?php endif; ?>
                                            
                                            <?php if ($book['available_quantity'] == 0): ?>
                                                <span class="badge bg-danger book-badge">Not Available</span>
                                            <?php elseif ($book['available_quantity'] < $book['quantity']): ?>
                                                <span class="badge bg-warning text-dark book-badge"><?php echo $book['available_quantity']; ?> Available</span>
                                            <?php else: ?>
                                                <span class="badge bg-success book-badge">Available</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="card-body">
                                            <h5 class="card-title"><?php echo htmlspecialchars($book['title']); ?></h5>
                                            <h6 class="card-subtitle mb-2 text-muted"><?php echo htmlspecialchars($book['author']); ?></h6>
                                            <p class="card-text small">
                                                <strong>ISBN:</strong> <?php echo htmlspecialchars($book['isbn']); ?><br>
                                                <strong>Publisher:</strong> <?php echo htmlspecialchars($book['publisher']); ?><br>
                                                <strong>Year:</strong> <?php echo $book['publication_year']; ?>
                                            </p>
                                        </div>
                                        <div class="card-footer bg-transparent border-top-0">
                                            <div class="d-grid gap-2">
                                                <a href="view.php?id=<?php echo $book['id']; ?>" class="btn btn-outline-primary btn-sm">
                                                    <i class="bi bi-eye me-1"></i>View Details
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>No books found matching your search criteria.
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
