<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Initialize variables
$errors = [];
$success_message = '';
$update_count = 0;
$books = [];
$selected_books = [];

// Get all categories for dropdown
$query = "SELECT DISTINCT category FROM books WHERE category IS NOT NULL AND category != '' ORDER BY category";
$stmt = $db->prepare($query);
$stmt->execute();
$categories = $stmt->fetchAll();

// Process search form
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['search'])) {
    $search_term = trim($_GET['search_term'] ?? '');
    $category_filter = $_GET['category_filter'] ?? '';
    
    if (!empty($search_term) || !empty($category_filter)) {
        // Build query based on search term and filter
        $query = "SELECT id, isbn, title, author, category FROM books WHERE 1=1";
        
        if (!empty($search_term)) {
            $query .= " AND (title LIKE :search_term OR author LIKE :search_term OR isbn LIKE :search_term)";
        }
        
        if (!empty($category_filter)) {
            $query .= " AND category = :category";
        }
        
        $query .= " ORDER BY title LIMIT 100";
        
        $stmt = $db->prepare($query);
        
        if (!empty($search_term)) {
            $search_param = "%$search_term%";
            $stmt->bindParam(':search_term', $search_param);
        }
        
        if (!empty($category_filter)) {
            $stmt->bindParam(':category', $category_filter);
        }
        
        $stmt->execute();
        $books = $stmt->fetchAll();
        
        if (count($books) === 0) {
            $errors[] = 'No books found matching your criteria';
        }
    } else {
        $errors[] = 'Please enter a search term or select a category';
    }
}

// Process batch update form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['batch_update'])) {
    $selected_books = $_POST['selected_books'] ?? [];
    $new_category = $_POST['new_category'] ?? '';
    $new_shelf_location = $_POST['new_shelf_location'] ?? '';
    
    if (empty($selected_books)) {
        $errors[] = 'Please select at least one book to update';
    } elseif (empty($new_category) && empty($new_shelf_location)) {
        $errors[] = 'Please specify a new category or shelf location';
    } else {
        // Start transaction
        $db->beginTransaction();
        
        try {
            // Build update query based on what fields are being updated
            $query = "UPDATE books SET ";
            $params = [];
            
            if (!empty($new_category)) {
                $query .= "category = :category";
                $params[':category'] = $new_category;
            }
            
            if (!empty($new_shelf_location)) {
                if (!empty($new_category)) {
                    $query .= ", ";
                }
                $query .= "shelf_location = :shelf_location";
                $params[':shelf_location'] = $new_shelf_location;
            }
            
            $query .= " WHERE id = :id";
            
            $stmt = $db->prepare($query);
            
            // Update each selected book
            foreach ($selected_books as $book_id) {
                $params[':id'] = $book_id;
                
                foreach ($params as $key => $value) {
                    $stmt->bindValue($key, $value);
                }
                
                if ($stmt->execute()) {
                    $update_count++;
                }
            }
            
            // Commit transaction
            $db->commit();
            
            $success_message = "$update_count books updated successfully";
            
            // Log activity
            logActivity($db, 'update', "Batch updated $update_count books", 'books', null);
            
            // Clear selected books after successful update
            $selected_books = [];
            
        } catch (PDOException $e) {
            // Rollback transaction on error
            $db->rollBack();
            $errors[] = 'Database error: ' . $e->getMessage();
        }
    }
}

// Page title
$page_title = 'Batch Update Books';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include '../includes/head.php'; ?>
    <style>
        .batch-update-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .batch-update-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        .book-checkbox {
            width: 20px;
            height: 20px;
        }
        .select-all-container {
            margin-bottom: 1rem;
            padding: 0.5rem;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-pencil-square me-2 text-primary"></i>Batch Update Books</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="<?php echo url('books/index.php'); ?>" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i> Back to Books
                        </a>
                    </div>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Search Form -->
                <div class="card batch-update-card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-search me-2"></i>Find Books to Update</h5>
                    </div>
                    <div class="card-body">
                        <form action="" method="get" class="row g-3">
                            <div class="col-md-5">
                                <label for="search_term" class="form-label">Search Term</label>
                                <input type="text" class="form-control" id="search_term" name="search_term" 
                                       placeholder="Enter title, author, or ISBN" 
                                       value="<?php echo htmlspecialchars($_GET['search_term'] ?? ''); ?>">
                            </div>
                            <div class="col-md-5">
                                <label for="category_filter" class="form-label">Category Filter</label>
                                <select class="form-select" id="category_filter" name="category_filter">
                                    <option value="">All Categories</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo htmlspecialchars($category['category']); ?>" 
                                                <?php echo (isset($_GET['category_filter']) && $_GET['category_filter'] === $category['category']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['category']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" name="search" class="btn btn-primary w-100">
                                    <i class="bi bi-search me-2"></i>Search
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <?php if (count($books) > 0): ?>
                    <!-- Batch Update Form -->
                    <form action="" method="post">
                        <div class="card batch-update-card mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="bi bi-pencil-square me-2"></i>Update Selected Books</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-5">
                                        <label for="new_category" class="form-label">New Category</label>
                                        <select class="form-select" id="new_category" name="new_category">
                                            <option value="">-- No Change --</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo htmlspecialchars($category['category']); ?>">
                                                    <?php echo htmlspecialchars($category['category']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                            <option value="new_category">+ Add New Category</option>
                                        </select>
                                    </div>
                                    <div class="col-md-5">
                                        <label for="new_shelf_location" class="form-label">New Shelf Location</label>
                                        <input type="text" class="form-control" id="new_shelf_location" name="new_shelf_location" placeholder="e.g., F-12">
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button type="submit" name="batch_update" class="btn btn-success w-100">
                                            <i class="bi bi-check2-all me-2"></i>Update Books
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- New Category Input (initially hidden) -->
                                <div id="new_category_input" class="mt-3" style="display: none;">
                                    <label for="custom_category" class="form-label">Enter New Category Name</label>
                                    <input type="text" class="form-control" id="custom_category" name="custom_category" placeholder="Enter new category name">
                                </div>
                            </div>
                        </div>

                        <!-- Book Selection Table -->
                        <div class="card batch-update-card">
                            <div class="card-header bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><i class="bi bi-list-check me-2 text-primary"></i>Select Books to Update</h5>
                                    <span class="badge bg-primary rounded-pill"><?php echo count($books); ?> books found</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="select-all-container">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="select_all">
                                        <label class="form-check-label fw-bold" for="select_all">
                                            Select All Books
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle">
                                        <thead>
                                            <tr>
                                                <th width="50">Select</th>
                                                <th>ISBN</th>
                                                <th>Title</th>
                                                <th>Author</th>
                                                <th>Category</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($books as $book): ?>
                                                <tr>
                                                    <td>
                                                        <div class="form-check">
                                                            <input class="form-check-input book-checkbox" type="checkbox" 
                                                                   name="selected_books[]" value="<?php echo $book['id']; ?>"
                                                                   <?php echo in_array($book['id'], $selected_books) ? 'checked' : ''; ?>>
                                                        </div>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($book['isbn']); ?></td>
                                                    <td><?php echo htmlspecialchars($book['title']); ?></td>
                                                    <td><?php echo htmlspecialchars($book['author']); ?></td>
                                                    <td>
                                                        <?php if (empty($book['category'])): ?>
                                                            <span class="text-muted">Uncategorized</span>
                                                        <?php else: ?>
                                                            <?php echo htmlspecialchars($book['category']); ?>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </form>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Select all functionality
            const selectAllCheckbox = document.getElementById('select_all');
            const bookCheckboxes = document.querySelectorAll('.book-checkbox');
            
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    bookCheckboxes.forEach(checkbox => {
                        checkbox.checked = selectAllCheckbox.checked;
                    });
                });
            }
            
            // New category input toggle
            const categorySelect = document.getElementById('new_category');
            const newCategoryInput = document.getElementById('new_category_input');
            const customCategoryInput = document.getElementById('custom_category');
            
            if (categorySelect && newCategoryInput) {
                categorySelect.addEventListener('change', function() {
                    if (categorySelect.value === 'new_category') {
                        newCategoryInput.style.display = 'block';
                        customCategoryInput.focus();
                    } else {
                        newCategoryInput.style.display = 'none';
                    }
                });
                
                // Handle form submission to use custom category
                document.querySelector('form').addEventListener('submit', function(e) {
                    if (categorySelect.value === 'new_category' && customCategoryInput.value.trim() !== '') {
                        e.preventDefault();
                        // Create a new option and select it
                        const newOption = document.createElement('option');
                        newOption.value = customCategoryInput.value.trim();
                        newOption.text = customCategoryInput.value.trim();
                        categorySelect.add(newOption, categorySelect.options[categorySelect.options.length - 1]);
                        categorySelect.value = customCategoryInput.value.trim();
                        newCategoryInput.style.display = 'none';
                        this.submit();
                    }
                });
            }
        });
    </script>
</body>
</html>
