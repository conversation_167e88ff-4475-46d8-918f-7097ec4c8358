<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Initialize variables
$errors = [];
$success_message = '';
$import_count = 0;
$export_data = [];

// Process CSV import
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['import'])) {
    // Check if file was uploaded without errors
    if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] == 0) {
        $file_name = $_FILES['csv_file']['name'];
        $file_size = $_FILES['csv_file']['size'];
        $file_tmp = $_FILES['csv_file']['tmp_name'];
        $file_type = $_FILES['csv_file']['type'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        
        // Check file extension
        if ($file_ext !== 'csv') {
            $errors[] = 'Only CSV files are allowed';
        }
        
        // Check file size (max 5MB)
        if ($file_size > 5242880) {
            $errors[] = 'File size must be less than 5MB';
        }
        
        // If no errors, process the file
        if (empty($errors)) {
            // Open the file
            if (($handle = fopen($file_tmp, 'r')) !== false) {
                // Read the header row
                $header = fgetcsv($handle, 1000, ',');
                
                // Required columns
                $required_columns = ['isbn', 'title', 'author', 'category', 'publication_year', 'publisher', 'quantity', 'available_quantity', 'shelf_location', 'description'];
                
                // Check if all required columns are present
                $missing_columns = array_diff($required_columns, $header);
                if (!empty($missing_columns)) {
                    $errors[] = 'Missing required columns: ' . implode(', ', $missing_columns);
                } else {
                    // Start transaction
                    $db->beginTransaction();
                    
                    try {
                        // Process each row
                        while (($data = fgetcsv($handle, 1000, ',')) !== false) {
                            // Create associative array from header and data
                            $row = array_combine($header, $data);
                            
                            // Validate data
                            if (empty($row['title']) || empty($row['author'])) {
                                continue; // Skip rows with missing title or author
                            }
                            
                            // Check if book already exists
                            $check_query = "SELECT id FROM books WHERE isbn = :isbn";
                            $check_stmt = $db->prepare($check_query);
                            $check_stmt->bindParam(':isbn', $row['isbn']);
                            $check_stmt->execute();
                            
                            if ($check_stmt->rowCount() > 0) {
                                // Update existing book
                                $query = "UPDATE books SET 
                                          title = :title,
                                          author = :author,
                                          category = :category,
                                          publication_year = :publication_year,
                                          publisher = :publisher,
                                          quantity = :quantity,
                                          available_quantity = :available_quantity,
                                          shelf_location = :shelf_location,
                                          description = :description
                                          WHERE isbn = :isbn";
                            } else {
                                // Insert new book
                                $query = "INSERT INTO books (isbn, title, author, category, publication_year, publisher,
                                          quantity, available_quantity, shelf_location, description)
                                          VALUES (:isbn, :title, :author, :category, :publication_year, :publisher,
                                          :quantity, :available_quantity, :shelf_location, :description)";
                            }
                            
                            $stmt = $db->prepare($query);
                            $stmt->bindParam(':isbn', $row['isbn']);
                            $stmt->bindParam(':title', $row['title']);
                            $stmt->bindParam(':author', $row['author']);
                            $stmt->bindParam(':category', $row['category']);
                            $stmt->bindParam(':publication_year', $row['publication_year']);
                            $stmt->bindParam(':publisher', $row['publisher']);
                            $stmt->bindParam(':quantity', $row['quantity']);
                            $stmt->bindParam(':available_quantity', $row['available_quantity']);
                            $stmt->bindParam(':shelf_location', $row['shelf_location']);
                            $stmt->bindParam(':description', $row['description']);
                            
                            if ($stmt->execute()) {
                                $import_count++;
                            }
                        }
                        
                        // Commit transaction
                        $db->commit();
                        
                        $success_message = "$import_count books imported/updated successfully";
                        
                        // Log activity
                        logActivity($db, 'import', "Imported $import_count books via CSV", 'books', null);
                        
                    } catch (PDOException $e) {
                        // Rollback transaction on error
                        $db->rollBack();
                        $errors[] = 'Database error: ' . $e->getMessage();
                    }
                }
                
                fclose($handle);
            } else {
                $errors[] = 'Could not open the file';
            }
        }
    } else {
        $errors[] = 'Please select a CSV file to import';
    }
}

// Process CSV export
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['export'])) {
    $category_filter = isset($_POST['category_filter']) ? $_POST['category_filter'] : '';
    
    // Build query based on filter
    $query = "SELECT isbn, title, author, category, publication_year, publisher, 
              quantity, available_quantity, shelf_location, description 
              FROM books";
    
    if (!empty($category_filter)) {
        $query .= " WHERE category = :category";
    }
    
    $query .= " ORDER BY title";
    
    $stmt = $db->prepare($query);
    
    if (!empty($category_filter)) {
        $stmt->bindParam(':category', $category_filter);
    }
    
    $stmt->execute();
    $export_data = $stmt->fetchAll();
    
    if (count($export_data) > 0) {
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="books_export_' . date('Y-m-d') . '.csv"');
        
        // Open output stream
        $output = fopen('php://output', 'w');
        
        // Output header row
        fputcsv($output, ['isbn', 'title', 'author', 'category', 'publication_year', 'publisher', 
                         'quantity', 'available_quantity', 'shelf_location', 'description']);
        
        // Output data rows
        foreach ($export_data as $row) {
            fputcsv($output, [
                $row['isbn'],
                $row['title'],
                $row['author'],
                $row['category'],
                $row['publication_year'],
                $row['publisher'],
                $row['quantity'],
                $row['available_quantity'],
                $row['shelf_location'],
                $row['description']
            ]);
        }
        
        fclose($output);
        exit;
    } else {
        $errors[] = 'No books found to export';
    }
}

// Get all categories for filter dropdown
$query = "SELECT DISTINCT category FROM books WHERE category IS NOT NULL AND category != '' ORDER BY category";
$stmt = $db->prepare($query);
$stmt->execute();
$categories = $stmt->fetchAll();

// Page title
$page_title = 'Bulk Book Operations';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include '../includes/head.php'; ?>
    <style>
        .import-export-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            height: 100%;
        }
        .import-export-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-file-earmark-spreadsheet me-2 text-primary"></i>Bulk Book Operations</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="<?php echo url('books/index.php'); ?>" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i> Back to Books
                        </a>
                    </div>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Import Section -->
                    <div class="col-md-6 mb-4">
                        <div class="card import-export-card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-file-earmark-arrow-up me-2"></i>Import Books from CSV</h5>
                            </div>
                            <div class="card-body">
                                <form action="" method="post" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="csv_file" class="form-label">Select CSV File</label>
                                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                                        <div class="form-text">
                                            CSV file must include these columns: isbn, title, author, category, publication_year, publisher, quantity, available_quantity, shelf_location, description
                                        </div>
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" name="import" class="btn btn-primary">
                                            <i class="bi bi-file-earmark-arrow-up me-2"></i>Import Books
                                        </button>
                                    </div>
                                </form>
                                
                                <div class="mt-4">
                                    <h6>CSV Format Example:</h6>
                                    <pre class="bg-light p-2 rounded small">isbn,title,author,category,publication_year,publisher,quantity,available_quantity,shelf_location,description
9780451524935,1984,George Orwell,Fiction,1949,Signet Classics,5,5,F-12,"Dystopian novel about totalitarianism"
9780061120084,To Kill a Mockingbird,Harper Lee,Fiction,1960,Harper Perennial,3,3,F-15,"Classic novel about racial injustice"</pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Export Section -->
                    <div class="col-md-6 mb-4">
                        <div class="card import-export-card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="bi bi-file-earmark-arrow-down me-2"></i>Export Books to CSV</h5>
                            </div>
                            <div class="card-body">
                                <form action="" method="post">
                                    <div class="mb-3">
                                        <label for="category_filter" class="form-label">Filter by Category (Optional)</label>
                                        <select class="form-select" id="category_filter" name="category_filter">
                                            <option value="">All Categories</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo htmlspecialchars($category['category']); ?>">
                                                    <?php echo htmlspecialchars($category['category']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" name="export" class="btn btn-success">
                                            <i class="bi bi-file-earmark-arrow-down me-2"></i>Export Books
                                        </button>
                                    </div>
                                </form>
                                
                                <div class="mt-4">
                                    <h6>Export Features:</h6>
                                    <ul>
                                        <li>Export all books or filter by category</li>
                                        <li>CSV file includes all book details</li>
                                        <li>Use the exported file as a template for bulk imports</li>
                                        <li>Make changes in Excel or other spreadsheet software</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
