<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Set up pagination
$records_per_page = 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $records_per_page;

// Get search term and filter if any
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$filter = isset($_GET['filter']) ? sanitize($_GET['filter']) : '';

// Build WHERE clause based on search and filter
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(title LIKE :search OR author LIKE :search OR isbn LIKE :search)";
    $params[':search'] = "%{$search}%";
}

if ($filter === 'available') {
    $where_conditions[] = "available_quantity > 0";
} elseif ($filter === 'unavailable') {
    $where_conditions[] = "available_quantity = 0";
} elseif ($filter === 'low_stock') {
    $where_conditions[] = "available_quantity > 0 AND available_quantity <= 2";
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// Count total records for pagination
$count_query = "SELECT COUNT(*) as total FROM books " . $where_clause;
$stmt = $db->prepare($count_query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$total_rows = $stmt->fetch()['total'];
$total_pages = ceil($total_rows / $records_per_page);

// Get books with pagination
$query = "SELECT * FROM books " . $where_clause . " ORDER BY title ASC LIMIT :offset, :records_per_page";
$stmt = $db->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
$stmt->bindParam(':records_per_page', $records_per_page, PDO::PARAM_INT);
$stmt->execute();
$books = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Books - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div>
                        <h1 class="h2"><i class="bi bi-book me-2 text-primary"></i>Books</h1>
                        <?php if (!empty($filter)): ?>
                            <div class="mt-2">
                                <span class="badge bg-info">
                                    <i class="bi bi-funnel me-1"></i>
                                    Filtered:
                                    <?php
                                    switch($filter) {
                                        case 'available': echo 'Available Books Only'; break;
                                        case 'unavailable': echo 'Unavailable Books Only'; break;
                                        case 'low_stock': echo 'Low Stock Books'; break;
                                        default: echo ucfirst($filter);
                                    }
                                    ?>
                                </span>
                                <a href="index.php<?php echo !empty($search) ? '?search=' . urlencode($search) : ''; ?>" class="btn btn-sm btn-outline-secondary ms-2">
                                    <i class="bi bi-x-circle me-1"></i>Clear Filter
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="add.php" class="btn btn-sm btn-primary">
                                <i class="bi bi-plus-lg"></i> Add New Book
                            </a>
                            <a href="bulk_operations.php" class="btn btn-sm btn-success">
                                <i class="bi bi-file-earmark-spreadsheet"></i> Bulk Import/Export
                            </a>
                            <a href="batch_update.php" class="btn btn-sm btn-info">
                                <i class="bi bi-pencil-square"></i> Batch Update
                            </a>
                        </div>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- Search Form -->
                <div class="row mb-3">
                    <div class="col-md-8">
                        <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" method="get" class="d-flex">
                            <input type="text" name="search" id="searchInput" class="form-control me-2" placeholder="Search by title, author, or ISBN" value="<?php echo $search; ?>">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> Search
                            </button>
                            <?php if (!empty($search)): ?>
                                <a href="index.php" class="btn btn-secondary ms-2">
                                    <i class="bi bi-x-circle"></i> Clear
                                </a>
                            <?php endif; ?>
                            <a href="advanced_search.php" class="btn btn-info ms-2 text-white">
                                <i class="bi bi-funnel"></i> Advanced Search
                            </a>
                        </form>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="btn-group" role="group" aria-label="View options">
                            <button type="button" class="btn btn-outline-secondary active" id="tableViewBtn">
                                <i class="bi bi-table"></i> Table
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="gridViewBtn">
                                <i class="bi bi-grid-3x3-gap"></i> Grid
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Books Table View -->
                <div id="tableView" class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ISBN</th>
                                <th>Title</th>
                                <th>Author</th>
                                <th>Category</th>
                                <th>Quantity</th>
                                <th>Available</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (count($books) > 0): ?>
                                <?php foreach ($books as $book): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($book['isbn']); ?></td>
                                    <td><?php echo htmlspecialchars($book['title']); ?></td>
                                    <td><?php echo htmlspecialchars($book['author']); ?></td>
                                    <td><?php echo htmlspecialchars($book['category']); ?></td>
                                    <td><?php echo $book['quantity']; ?></td>
                                    <td>
                                        <?php if ($book['available_quantity'] == 0): ?>
                                            <span class="badge bg-danger">Not Available</span>
                                        <?php elseif ($book['available_quantity'] < $book['quantity']): ?>
                                            <span class="badge bg-warning text-dark"><?php echo $book['available_quantity']; ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-success"><?php echo $book['available_quantity']; ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="view.php?id=<?php echo $book['id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View Details">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="edit.php?id=<?php echo $book['id']; ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="delete.php?id=<?php echo $book['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="Delete">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center">No books found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Books Grid View (initially hidden) -->
                <div id="gridView" class="row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-4 g-4" style="display: none;">
                    <?php if (count($books) > 0): ?>
                        <?php foreach ($books as $book): ?>
                            <div class="col">
                                <div class="card h-100 shadow-sm">
                                    <div class="position-relative">
                                        <?php if (!empty($book['cover_image'])): ?>
                                            <?php
                                            $possible_paths = [
                                                '../uploads/covers/' . $book['cover_image'],
                                                'uploads/covers/' . $book['cover_image']
                                            ];

                                            $image_src = '../uploads/covers/' . $book['cover_image']; // Default
                                            foreach ($possible_paths as $path) {
                                                if (file_exists($path)) {
                                                    $image_src = $path;
                                                    break;
                                                }
                                            }
                                            ?>
                                            <img src="<?php echo htmlspecialchars($image_src); ?>" class="card-img-top" alt="<?php echo htmlspecialchars($book['title']); ?>"
                                                 style="height: 200px; object-fit: cover;"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                            <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 200px; display: none;">
                                                <i class="bi bi-book fs-1 text-secondary"></i>
                                            </div>
                                        <?php else: ?>
                                            <img src="<?php echo url('../assets/img/book-placeholder.jpg'); ?>" class="card-img-top" alt="No Cover Available" style="height: 200px; object-fit: cover;">
                                        <?php endif; ?>

                                        <?php if ($book['available_quantity'] == 0): ?>
                                            <span class="position-absolute top-0 end-0 badge bg-danger m-2">Not Available</span>
                                        <?php elseif ($book['available_quantity'] < $book['quantity']): ?>
                                            <span class="position-absolute top-0 end-0 badge bg-warning text-dark m-2"><?php echo $book['available_quantity']; ?> Available</span>
                                        <?php else: ?>
                                            <span class="position-absolute top-0 end-0 badge bg-success m-2">Available</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo htmlspecialchars($book['title']); ?></h5>
                                        <h6 class="card-subtitle mb-2 text-muted"><?php echo htmlspecialchars($book['author']); ?></h6>
                                        <p class="card-text small">
                                            <strong>ISBN:</strong> <?php echo htmlspecialchars($book['isbn']); ?><br>
                                            <strong>Category:</strong> <?php echo htmlspecialchars($book['category'] ?: 'Uncategorized'); ?>
                                        </p>
                                    </div>
                                    <div class="card-footer bg-transparent">
                                        <div class="d-flex justify-content-between">
                                            <a href="view.php?id=<?php echo $book['id']; ?>" class="btn btn-sm btn-info">
                                                <i class="bi bi-eye"></i> View
                                            </a>
                                            <a href="edit.php?id=<?php echo $book['id']; ?>" class="btn btn-sm btn-warning">
                                                <i class="bi bi-pencil"></i> Edit
                                            </a>
                                            <a href="delete.php?id=<?php echo $book['id']; ?>" class="btn btn-sm btn-danger btn-delete">
                                                <i class="bi bi-trash"></i> Delete
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="col-12">
                            <div class="alert alert-info">No books found</div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo $page <= 1 ? '#' : '?page='.($page-1).(!empty($search) ? '&search='.$search : ''); ?>">Previous</a>
                        </li>

                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo $page >= $total_pages ? '#' : '?page='.($page+1).(!empty($search) ? '&search='.$search : ''); ?>">Next</a>
                        </li>
                    </ul>
                </nav>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // View toggle functionality
            const tableViewBtn = document.getElementById('tableViewBtn');
            const gridViewBtn = document.getElementById('gridViewBtn');
            const tableView = document.getElementById('tableView');
            const gridView = document.getElementById('gridView');

            // Check if view preference is stored in localStorage
            const viewPreference = localStorage.getItem('booksViewPreference');
            if (viewPreference === 'grid') {
                showGridView();
            }

            // Table view button click
            tableViewBtn.addEventListener('click', function() {
                showTableView();
                localStorage.setItem('booksViewPreference', 'table');
            });

            // Grid view button click
            gridViewBtn.addEventListener('click', function() {
                showGridView();
                localStorage.setItem('booksViewPreference', 'grid');
            });

            function showTableView() {
                tableView.style.display = 'block';
                gridView.style.display = 'none';
                tableViewBtn.classList.add('active');
                gridViewBtn.classList.remove('active');
            }

            function showGridView() {
                tableView.style.display = 'none';
                gridView.style.display = 'flex';
                tableViewBtn.classList.remove('active');
                gridViewBtn.classList.add('active');
            }
        });
    </script>
</body>
</html>
