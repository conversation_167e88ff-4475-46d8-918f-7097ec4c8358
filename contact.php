<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Initialize variables
$name = $email = $subject = $message = '';
$errors = [];
$success = false;

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate name
    if (empty($_POST['name'])) {
        $errors['name'] = 'Name is required';
    } else {
        $name = sanitize($_POST['name']);
    }

    // Validate email
    if (empty($_POST['email'])) {
        $errors['email'] = 'Email is required';
    } elseif (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
        $errors['email'] = 'Invalid email format';
    } else {
        $email = sanitize($_POST['email']);
    }

    // Validate subject
    if (empty($_POST['subject'])) {
        $errors['subject'] = 'Subject is required';
    } else {
        $subject = sanitize($_POST['subject']);
    }

    // Validate message
    if (empty($_POST['message'])) {
        $errors['message'] = 'Message is required';
    } else {
        $message = sanitize($_POST['message']);
    }

    // If no errors, process the form
    if (empty($errors)) {
        // In a real application, you would send an email or save to database
        // For now, we'll just set a success message
        $success = true;

        // Clear form fields after successful submission
        $name = $email = $subject = $message = '';
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .contact-container {
            max-width: 1000px;
            margin: 20px auto;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .navbar {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .map-container {
            width: 100%;
            margin-top: 1.5rem;
            padding: 1rem;
            border-radius: 0.5rem;
            background-color: #f8f9fa;
        }
        .map-container iframe {
            width: 100%;
            height: 300px;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="home.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="home.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="catalog.php">Book Catalog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">About Us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="contact.php">Contact</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <button id="darkModeToggle" class="btn btn-outline-light me-3" title="Toggle Dark Mode">
                        <i id="darkModeIcon" class="bi bi-moon"></i>
                    </button>

                    <?php if (isLoggedIn()): ?>
                        <a href="admin/dashboard.php" class="btn btn-outline-light me-2">Admin Dashboard</a>
                        <a href="logout.php" class="btn btn-outline-danger">Logout</a>
                    <?php elseif (isMemberLoggedIn()): ?>
                        <a href="member_dashboard.php" class="btn btn-outline-light me-2">My Dashboard</a>
                        <a href="logout.php" class="btn btn-outline-danger">Logout</a>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-outline-light me-2">Login</a>
                        <a href="register.php" class="btn btn-primary">Register</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <div class="contact-container">
        <h1 class="mb-4 text-center">Contact Us</h1>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle-fill me-2"></i>
                Thank you for your message! We will get back to you as soon as possible.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Send Us a Message</h2>
                        <form method="post" action="<?php echo h($_SERVER['PHP_SELF']); ?>">
                            <div class="mb-3">
                                <label for="name" class="form-label">Your Name</label>
                                <input type="text" class="form-control <?php echo isset($errors['name']) ? 'is-invalid' : ''; ?>" id="name" name="name" value="<?php echo h($name); ?>">
                                <?php if (isset($errors['name'])): ?>
                                    <div class="invalid-feedback"><?php echo h($errors['name']); ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control <?php echo isset($errors['email']) ? 'is-invalid' : ''; ?>" id="email" name="email" value="<?php echo h($email); ?>">
                                <?php if (isset($errors['email'])): ?>
                                    <div class="invalid-feedback"><?php echo h($errors['email']); ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label for="subject" class="form-label">Subject</label>
                                <input type="text" class="form-control <?php echo isset($errors['subject']) ? 'is-invalid' : ''; ?>" id="subject" name="subject" value="<?php echo h($subject); ?>">
                                <?php if (isset($errors['subject'])): ?>
                                    <div class="invalid-feedback"><?php echo h($errors['subject']); ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label for="message" class="form-label">Message</label>
                                <textarea class="form-control <?php echo isset($errors['message']) ? 'is-invalid' : ''; ?>" id="message" name="message" rows="5"><?php echo h($message); ?></textarea>
                                <?php if (isset($errors['message'])): ?>
                                    <div class="invalid-feedback"><?php echo h($errors['message']); ?></div>
                                <?php endif; ?>
                            </div>

                            <button type="submit" class="btn btn-primary">Send Message</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Contact Information</h2>

                        <div class="mb-4">
                            <h5><i class="bi bi-geo-alt-fill me-2 text-primary"></i>Address</h5>
                            <p class="ms-4">123 Library Street<br>Booktown, BT 12345</p>
                        </div>

                        <div class="mb-4">
                            <h5><i class="bi bi-telephone-fill me-2 text-primary"></i>Phone</h5>
                            <p class="ms-4">(*************</p>
                        </div>

                        <div class="mb-4">
                            <h5><i class="bi bi-envelope-fill me-2 text-primary"></i>Email</h5>
                            <p class="ms-4"><EMAIL></p>
                        </div>

                        <div class="mb-4">
                            <h5><i class="bi bi-clock-fill me-2 text-primary"></i>Hours</h5>
                            <p class="ms-4">
                                Monday - Friday: 9:00 AM - 8:00 PM<br>
                                Saturday: 10:00 AM - 6:00 PM<br>
                                Sunday: 12:00 PM - 5:00 PM
                            </p>
                        </div>

                        <div class="map-container">
                            <h5 class="mb-3"><i class="bi bi-map-fill me-2 text-primary"></i>Our Location</h5>
                            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3944.6742456686726!2d125.53649731478283!3d8.629029993797343!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3301c1b1e1d95c8d%3A0x1f3b5e8e3b0c0d1d!2sCabadbaran%20City%2C%20Agusan%20del%20Norte!5e0!3m2!1sen!2sph!4v1620123456789!5m2!1sen!2sph" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                            <p class="mt-2 text-muted small">Our library is conveniently located in Cabadbaran, serving the surrounding communities including Santiago, Tubay, Remedios T. Romualdez, Magallanes, and Buenavista.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="container my-5">
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h2 class="text-center mb-4"><i class="bi bi-question-circle me-2 text-primary"></i>Frequently Asked Questions</h2>

                            <div class="accordion" id="faqAccordion">
                                <div class="accordion-item border-0 mb-3 shadow-sm">
                                    <h2 class="accordion-header" id="faqHeading1">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse1" aria-expanded="true" aria-controls="faqCollapse1">
                                            How do I get a library card?
                                        </button>
                                    </h2>
                                    <div id="faqCollapse1" class="accordion-collapse collapse show" aria-labelledby="faqHeading1" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            <p>To get a library card, please visit our library in person with a valid ID and proof of address. The process takes just a few minutes, and your card will be issued immediately. Library cards are free for residents of our community.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="accordion-item border-0 mb-3 shadow-sm">
                                    <h2 class="accordion-header" id="faqHeading2">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse2" aria-expanded="false" aria-controls="faqCollapse2">
                                            How many books can I borrow at once?
                                        </button>
                                    </h2>
                                    <div id="faqCollapse2" class="accordion-collapse collapse" aria-labelledby="faqHeading2" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            <p>Members can borrow up to 5 books at a time. Special arrangements can be made for students and researchers who may need additional materials for their studies.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="accordion-item border-0 mb-3 shadow-sm">
                                    <h2 class="accordion-header" id="faqHeading3">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse3" aria-expanded="false" aria-controls="faqCollapse3">
                                            What is the loan period for borrowed books?
                                        </button>
                                    </h2>
                                    <div id="faqCollapse3" class="accordion-collapse collapse" aria-labelledby="faqHeading3" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            <p>The standard loan period is 14 days for regular books. Reference materials and special collections may have different loan periods or may be for in-library use only.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="accordion-item border-0 mb-3 shadow-sm">
                                    <h2 class="accordion-header" id="faqHeading4">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse4" aria-expanded="false" aria-controls="faqCollapse4">
                                            How do I renew my books?
                                        </button>
                                    </h2>
                                    <div id="faqCollapse4" class="accordion-collapse collapse" aria-labelledby="faqHeading4" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            <p>You can renew your books online through your account, by phone, or in person at the library. Books can be renewed up to two times if no one else has reserved them.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="accordion-item border-0 shadow-sm">
                                    <h2 class="accordion-header" id="faqHeading5">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse5" aria-expanded="false" aria-controls="faqCollapse5">
                                            What happens if I return a book late?
                                        </button>
                                    </h2>
                                    <div id="faqCollapse5" class="accordion-collapse collapse" aria-labelledby="faqHeading5" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            <p>Late returns incur a fine of $1.00 per day per item. If you know you'll be late, please contact us to discuss options. We're here to help, not to collect fines!</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <p class="mb-0">Don't see your question here? <a href="#contactForm" class="text-decoration-none">Contact us</a> and we'll be happy to help!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo url('assets/js/dark-mode.js'); ?>"></script>
</body>
</html>
