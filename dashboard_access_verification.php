<?php
/**
 * Dashboard Access Verification
 * Quick test to ensure all dashboards are accessible and functional
 */

session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Dashboard Access Verification</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css' rel='stylesheet'>
    <style>
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .test-card { margin-bottom: 20px; }
    </style>
</head>
<body>
<div class='container mt-4'>
    <h1><i class='bi bi-speedometer2 me-2'></i>Dashboard Access Verification</h1>
    <p class='text-muted'>Testing accessibility and functionality of all dashboard components...</p>";

// Test database connection
echo "<div class='card test-card'>
<div class='card-header'>
    <h5><i class='bi bi-database me-2'></i>Database Connection Test</h5>
</div>
<div class='card-body'>";

try {
    $database = new Database();
    $db = $database->getConnection();
    echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Database connection successful</p>";
    
    // Test basic queries
    $test_queries = [
        "SELECT COUNT(*) as count FROM users" => "Users table",
        "SELECT COUNT(*) as count FROM members" => "Members table", 
        "SELECT COUNT(*) as count FROM books" => "Books table",
        "SELECT COUNT(*) as count FROM book_loans" => "Book loans table"
    ];
    
    foreach ($test_queries as $query => $description) {
        try {
            $stmt = $db->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch();
            echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>$description: {$result['count']} records</p>";
        } catch (Exception $e) {
            echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>$description: Error - " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>Database connection failed: " . $e->getMessage() . "</p>";
}

echo "</div></div>";

// Test authentication functions
echo "<div class='card test-card'>
<div class='card-header'>
    <h5><i class='bi bi-shield-check me-2'></i>Authentication Functions Test</h5>
</div>
<div class='card-body'>";

$auth_functions = ['isLoggedIn', 'isAdmin', 'isLibrarian', 'isMemberLoggedIn'];
foreach ($auth_functions as $function) {
    if (function_exists($function)) {
        echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Function '$function' exists</p>";
    } else {
        echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>Function '$function' missing</p>";
    }
}

echo "</div></div>";

// Test dashboard files
echo "<div class='card test-card'>
<div class='card-header'>
    <h5><i class='bi bi-file-earmark-code me-2'></i>Dashboard Files Test</h5>
</div>
<div class='card-body'>";

$dashboard_files = [
    'admin/dashboard.php' => 'Admin Dashboard',
    'librarian/dashboard.php' => 'Librarian Dashboard', 
    'member/member_dashboard.php' => 'Member Dashboard',
    'member_dashboard.php' => 'Alternative Member Dashboard'
];

foreach ($dashboard_files as $file => $name) {
    if (file_exists($file)) {
        echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>$name: File exists</p>";
        
        // Check if file is readable
        if (is_readable($file)) {
            echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>$name: File is readable</p>";
        } else {
            echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>$name: File not readable</p>";
        }
    } else {
        echo "<p class='status-warning'><i class='bi bi-exclamation-triangle-fill me-2'></i>$name: File not found</p>";
    }
}

echo "</div></div>";

// Test required directories
echo "<div class='card test-card'>
<div class='card-header'>
    <h5><i class='bi bi-folder-check me-2'></i>Required Directories Test</h5>
</div>
<div class='card-body'>";

$required_dirs = [
    'uploads' => 'Uploads directory',
    'uploads/covers' => 'Book covers directory',
    'uploads/members' => 'Member photos directory',
    'assets/css' => 'CSS assets directory',
    'assets/js' => 'JavaScript assets directory'
];

foreach ($required_dirs as $dir => $description) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>$description: Exists and writable</p>";
        } else {
            echo "<p class='status-warning'><i class='bi bi-exclamation-triangle-fill me-2'></i>$description: Exists but not writable</p>";
        }
    } else {
        echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>$description: Directory missing</p>";
    }
}

echo "</div></div>";

// Dashboard access links
echo "<div class='card test-card'>
<div class='card-header'>
    <h5><i class='bi bi-link-45deg me-2'></i>Dashboard Access Links</h5>
</div>
<div class='card-body'>
    <div class='row'>";

$dashboards = [
    'admin/dashboard.php' => ['name' => 'Admin Dashboard', 'class' => 'btn-primary', 'icon' => 'person-gear'],
    'librarian/dashboard.php' => ['name' => 'Librarian Dashboard', 'class' => 'btn-success', 'icon' => 'book'],
    'member/member_dashboard.php' => ['name' => 'Member Dashboard', 'class' => 'btn-info', 'icon' => 'people']
];

foreach ($dashboards as $url => $info) {
    echo "<div class='col-md-4 mb-3'>
        <div class='card h-100'>
            <div class='card-body text-center'>
                <i class='bi bi-{$info['icon']} fs-1 mb-3'></i>
                <h5 class='card-title'>{$info['name']}</h5>
                <a href='$url' class='btn {$info['class']}' target='_blank'>
                    <i class='bi bi-box-arrow-up-right me-1'></i>Open Dashboard
                </a>
            </div>
        </div>
    </div>";
}

echo "</div></div></div>";

// System status summary
echo "<div class='card test-card'>
<div class='card-header'>
    <h5><i class='bi bi-clipboard-check me-2'></i>System Status Summary</h5>
</div>
<div class='card-body'>";

// Quick system health check
$system_health = [
    'Database' => isset($db) ? 'Connected' : 'Failed',
    'Authentication' => function_exists('isLoggedIn') ? 'Available' : 'Missing',
    'Admin Dashboard' => file_exists('admin/dashboard.php') ? 'Available' : 'Missing',
    'Librarian Dashboard' => file_exists('librarian/dashboard.php') ? 'Available' : 'Missing',
    'Member Dashboard' => file_exists('member/member_dashboard.php') ? 'Available' : 'Missing'
];

echo "<div class='row'>";
foreach ($system_health as $component => $status) {
    $status_class = ($status === 'Connected' || $status === 'Available') ? 'success' : 'danger';
    $icon = ($status === 'Connected' || $status === 'Available') ? 'check-circle-fill' : 'x-circle-fill';
    
    echo "<div class='col-md-6 mb-2'>
        <div class='alert alert-$status_class d-flex align-items-center' role='alert'>
            <i class='bi bi-$icon me-2'></i>
            <div>
                <strong>$component:</strong> $status
            </div>
        </div>
    </div>";
}
echo "</div>";

echo "</div></div>";

echo "<div class='text-center mt-4'>
    <a href='index.php' class='btn btn-secondary me-2'>
        <i class='bi bi-house me-1'></i>Back to Home
    </a>
    <a href='system_comprehensive_fix.php' class='btn btn-warning'>
        <i class='bi bi-tools me-1'></i>Run Full System Fix
    </a>
</div>";

echo "</div>
</body>
</html>";
?>
