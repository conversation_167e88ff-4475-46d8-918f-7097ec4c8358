<?php
// Set the correct path based on the current working directory
$config_path = file_exists('../config/database.php') ? '../config/database.php' : 'config/database.php';
require_once $config_path;

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<h2>Creating Sample Loan Data for Testing Return Features</h2>";

try {
    // Get some members and books for creating sample loans
    $query = "SELECT id FROM members LIMIT 3";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $members = $stmt->fetchAll();

    $query = "SELECT id FROM books WHERE available_quantity > 0 LIMIT 5";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $books = $stmt->fetchAll();

    if (count($members) == 0) {
        echo "<p style='color: red;'>No members found. Please create some members first.</p>";
        exit;
    }

    if (count($books) == 0) {
        echo "<p style='color: red;'>No available books found. Please add some books first.</p>";
        exit;
    }

    echo "<p>Found " . count($members) . " members and " . count($books) . " available books.</p>";

    // Create sample loans
    $loans_created = 0;

    foreach ($members as $member) {
        foreach ($books as $index => $book) {
            // Create different types of loans for testing
            if ($index == 0) {
                // Current loan (not overdue)
                $issue_date = date('Y-m-d', strtotime('-5 days'));
                $due_date = date('Y-m-d', strtotime('+9 days'));
            } elseif ($index == 1) {
                // Overdue loan
                $issue_date = date('Y-m-d', strtotime('-20 days'));
                $due_date = date('Y-m-d', strtotime('-3 days'));
            } elseif ($index == 2) {
                // Due soon loan
                $issue_date = date('Y-m-d', strtotime('-12 days'));
                $due_date = date('Y-m-d', strtotime('+2 days'));
            } else {
                // Regular loan
                $issue_date = date('Y-m-d', strtotime('-7 days'));
                $due_date = date('Y-m-d', strtotime('+7 days'));
            }

            // Check if loan already exists
            $query = "SELECT id FROM book_loans WHERE member_id = :member_id AND book_id = :book_id AND status = 'borrowed'";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':member_id', $member['id']);
            $stmt->bindParam(':book_id', $book['id']);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                // Create the loan
                $query = "INSERT INTO book_loans (member_id, book_id, issue_date, due_date, status)
                          VALUES (:member_id, :book_id, :issue_date, :due_date, 'borrowed')";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':member_id', $member['id']);
                $stmt->bindParam(':book_id', $book['id']);
                $stmt->bindParam(':issue_date', $issue_date);
                $stmt->bindParam(':due_date', $due_date);
                $stmt->execute();

                // Update book availability
                $query = "UPDATE books SET available_quantity = available_quantity - 1 WHERE id = :book_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':book_id', $book['id']);
                $stmt->execute();

                $loans_created++;
                echo "<p>✓ Created loan: Member {$member['id']} borrowed Book {$book['id']} (Due: $due_date)</p>";
            }

            // Limit to 2 loans per member for testing
            if ($index >= 1) break;
        }
    }

    echo "<h3>Sample Loan Creation Complete!</h3>";
    echo "<p><strong>Total loans created:</strong> $loans_created</p>";

    // Show summary of current loans
    $query = "SELECT bl.*, b.title, b.author, m.first_name, m.last_name
              FROM book_loans bl
              JOIN books b ON bl.book_id = b.id
              JOIN members m ON bl.member_id = m.id
              WHERE bl.status = 'borrowed'
              ORDER BY bl.due_date";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $current_loans = $stmt->fetchAll();

    if (count($current_loans) > 0) {
        echo "<h4>Current Active Loans:</h4>";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>Member</th><th>Book</th><th>Issue Date</th><th>Due Date</th><th>Status</th>";
        echo "</tr>";

        foreach ($current_loans as $loan) {
            $is_overdue = strtotime($loan['due_date']) < time();
            $due_soon = !$is_overdue && strtotime($loan['due_date']) < strtotime('+3 days');

            $row_color = '';
            if ($is_overdue) {
                $row_color = 'background-color: #ffebee;';
            } elseif ($due_soon) {
                $row_color = 'background-color: #fff3e0;';
            }

            echo "<tr style='$row_color'>";
            echo "<td>{$loan['first_name']} {$loan['last_name']} (ID: {$loan['member_id']})</td>";
            echo "<td>{$loan['title']} by {$loan['author']}</td>";
            echo "<td>{$loan['issue_date']}</td>";
            echo "<td>{$loan['due_date']}</td>";
            echo "<td>" . ($is_overdue ? 'OVERDUE' : ($due_soon ? 'DUE SOON' : 'ACTIVE')) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    echo "<div style='margin-top: 20px; padding: 15px; background-color: #e8f5e8; border-radius: 5px;'>";
    echo "<h4>Next Steps:</h4>";
    echo "<ol>";
    echo "<li>Login as one of the members who has active loans</li>";
    echo "<li>Go to the member dashboard</li>";
    echo "<li>Try returning books using the new return features</li>";
    echo "<li>Test the rating and review functionality</li>";
    echo "</ol>";
    echo "<p><strong>Test Links:</strong></p>";
    echo "<ul>";
    echo "<li><a href='../member/member_dashboard.php'>Member Dashboard</a></li>";
    echo "<li><a href='../member/return_book.php'>Return Books Page</a></li>";
    echo "<li><a href='../test_return_features.php'>Test Return Features</a></li>";
    echo "</ul>";
    echo "</div>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
