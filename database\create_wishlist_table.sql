-- Create member wishlist table
CREATE TABLE IF NOT EXISTS member_wishlist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    book_id INT NOT NULL,
    added_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    UNIQUE KEY unique_wishlist (member_id, book_id)
);

-- Create book reviews table
CREATE TABLE IF NOT EXISTS book_reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    book_id INT NOT NULL,
    member_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    review_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    FOR<PERSON><PERSON>N KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    UNIQUE KEY unique_review (book_id, member_id)
);

-- <PERSON><PERSON> reading goals table
CREATE TABLE IF NOT EXISTS reading_goals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    goal_year YEAR NOT NULL,
    target_books INT NOT NULL DEFAULT 12,
    books_read INT DEFAULT 0,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    UNIQUE KEY unique_goal (member_id, goal_year)
);

-- Insert sample data for testing (optional)
-- INSERT INTO reading_goals (member_id, goal_year, target_books) 
-- SELECT id, YEAR(CURDATE()), 12 FROM members LIMIT 5;
