<?php
// Set the correct path based on the current working directory
$config_path = file_exists('../config/database.php') ? '../config/database.php' : 'config/database.php';
require_once $config_path;

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<h2>Setting up Enhanced Member Dashboard Features</h2>";

try {
    // Create member wishlist table
    $sql = "CREATE TABLE IF NOT EXISTS member_wishlist (
        id INT AUTO_INCREMENT PRIMARY KEY,
        member_id INT NOT NULL,
        book_id INT NOT NULL,
        added_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
        UNIQUE KEY unique_wishlist (member_id, book_id)
    )";
    $db->exec($sql);
    echo "<p>✓ Member wishlist table created successfully</p>";

    // Create book reviews table
    $sql = "CREATE TABLE IF NOT EXISTS book_reviews (
        id INT AUTO_INCREMENT PRIMARY KEY,
        book_id INT NOT NULL,
        member_id INT NOT NULL,
        rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
        review_text TEXT,
        review_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
        UNIQUE KEY unique_review (book_id, member_id)
    )";
    $db->exec($sql);
    echo "<p>✓ Book reviews table created successfully</p>";

    // Create reading goals table
    $sql = "CREATE TABLE IF NOT EXISTS reading_goals (
        id INT AUTO_INCREMENT PRIMARY KEY,
        member_id INT NOT NULL,
        goal_year YEAR NOT NULL,
        target_books INT NOT NULL DEFAULT 12,
        books_read INT DEFAULT 0,
        created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
        UNIQUE KEY unique_goal (member_id, goal_year)
    )";
    $db->exec($sql);
    echo "<p>✓ Reading goals table created successfully</p>";

    // Check if notifications table exists, if not create it
    $sql = "CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NULL,
        type VARCHAR(50) DEFAULT 'info',
        title VARCHAR(255),
        message TEXT NOT NULL,
        entity_type VARCHAR(50) NULL,
        entity_id INT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )";
    $db->exec($sql);
    echo "<p>✓ Notifications table created successfully</p>";

    // Add some sample notifications for testing
    $sql = "INSERT IGNORE INTO notifications (user_id, type, message) VALUES
            (NULL, 'info', 'Welcome to the enhanced library management system!'),
            (NULL, 'success', 'Check out the new wishlist and reading statistics features!')";
    $db->exec($sql);
    echo "<p>✓ Sample notifications added</p>";

    echo "<h3>Database setup completed successfully!</h3>";
    echo "<p>You can now use the enhanced member dashboard features including:</p>";
    echo "<ul>";
    echo "<li>Member wishlist functionality</li>";
    echo "<li>Book reviews and ratings</li>";
    echo "<li>Reading goals tracking</li>";
    echo "<li>Enhanced notifications</li>";
    echo "</ul>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
