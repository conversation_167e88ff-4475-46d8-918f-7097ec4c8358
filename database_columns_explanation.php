<?php
/**
 * Database Columns Explanation
 * This document explains the purpose of each column in the members table
 */
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Columns Explanation - LMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">📊 Database Columns Explanation</h1>
        <p class="lead">Understanding the purpose of each column in the members table</p>

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3>👥 Members Table Columns</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Column Name</th>
                                        <th>Purpose</th>
                                        <th>When Used</th>
                                        <th>Example Value</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>profile_picture</code></td>
                                        <td>🖼️ Stores filename of member's profile photo</td>
                                        <td>When member uploads a profile picture</td>
                                        <td>alice_profile.jpg</td>
                                    </tr>
                                    <tr>
                                        <td><code>notification_preferences</code></td>
                                        <td>🔔 Stores member's email notification settings as JSON</td>
                                        <td>When member configures notification preferences</td>
                                        <td>{"due_date_reminder":1,"overdue_notification":1}</td>
                                    </tr>
                                    <tr>
                                        <td><code>google_id</code></td>
                                        <td>🔗 Stores Google account ID for OAuth login</td>
                                        <td>When member links their Google account</td>
                                        <td>123456789012345678901</td>
                                    </tr>
                                    <tr>
                                        <td><code>google_token</code></td>
                                        <td>🔑 Stores Google OAuth access token</td>
                                        <td>For accessing Google services on behalf of user</td>
                                        <td>ya29.a0AfH6SMC...</td>
                                    </tr>
                                    <tr>
                                        <td><code>google_picture</code></td>
                                        <td>📸 Stores URL to member's Google profile picture</td>
                                        <td>When member signs in with Google</td>
                                        <td>https://lh3.googleusercontent.com/...</td>
                                    </tr>
                                    <tr>
                                        <td><code>remember_token</code></td>
                                        <td>💾 Stores token for "Remember Me" functionality</td>
                                        <td>When member checks "Remember Me" at login</td>
                                        <td>abc123def456ghi789</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h3>🔍 Why These Columns Are NULL</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="bi bi-info-circle me-2"></i>Normal Behavior</h5>
                            <p>These columns are NULL because they are <strong>optional features</strong> that members can use if they want to:</p>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h5>🖼️ Profile Picture</h5>
                                <ul>
                                    <li>Only filled when member uploads a photo</li>
                                    <li>System uses default avatar if NULL</li>
                                    <li>Stored in <code>/uploads/profiles/</code> folder</li>
                                </ul>

                                <h5>🔔 Notification Preferences</h5>
                                <ul>
                                    <li>Only set when member configures notifications</li>
                                    <li>System uses default settings if NULL</li>
                                    <li>Stored as JSON format</li>
                                </ul>

                                <h5>💾 Remember Token</h5>
                                <ul>
                                    <li>Only created when "Remember Me" is checked</li>
                                    <li>Allows automatic login without password</li>
                                    <li>Expires after certain time</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5>🔗 Google Integration</h5>
                                <ul>
                                    <li><strong>google_id:</strong> Only when linked to Google</li>
                                    <li><strong>google_token:</strong> For API access</li>
                                    <li><strong>google_picture:</strong> Google profile photo URL</li>
                                </ul>

                                <div class="alert alert-warning mt-3">
                                    <h6><i class="bi bi-exclamation-triangle me-2"></i>Important Note</h6>
                                    <p class="mb-0">These NULL values are <strong>perfectly normal</strong> and don't indicate any problems. They will only be filled when members use these specific features.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h3>🛠️ How to Use These Features</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card border-primary">
                                    <div class="card-body text-center">
                                        <i class="bi bi-person-circle display-4 text-primary"></i>
                                        <h5>Profile Picture</h5>
                                        <p>Members can upload photos in their profile page</p>
                                        <a href="member/profile.php" class="btn btn-primary btn-sm">Go to Profile</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <i class="bi bi-bell display-4 text-success"></i>
                                        <h5>Notifications</h5>
                                        <p>Configure email alerts for due dates and overdue books</p>
                                        <a href="member/profile.php#preferences" class="btn btn-success btn-sm">Set Preferences</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <i class="bi bi-google display-4 text-warning"></i>
                                        <h5>Google Login</h5>
                                        <p>Link Google account for easy sign-in</p>
                                        <a href="google_signin.php" class="btn btn-warning btn-sm">Link Google</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h3>📋 Summary</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h5><i class="bi bi-check-circle me-2"></i>Everything is Working Correctly!</h5>
                            <p>The NULL values you see are completely normal. These columns are designed to be empty until members choose to use these optional features.</p>
                        </div>

                        <h6>What you can do:</h6>
                        <ul>
                            <li>✅ Leave them as NULL - the system handles this perfectly</li>
                            <li>✅ Test the features by uploading a profile picture</li>
                            <li>✅ Try linking a Google account</li>
                            <li>✅ Configure notification preferences</li>
                        </ul>

                        <div class="mt-3">
                            <a href="verify_members_and_loans.php" class="btn btn-primary me-2">
                                <i class="bi bi-eye me-1"></i>View Members Data
                            </a>
                            <a href="member_login.php" class="btn btn-success me-2">
                                <i class="bi bi-box-arrow-in-right me-1"></i>Test Member Login
                            </a>
                            <a href="index.php" class="btn btn-secondary">
                                <i class="bi bi-house me-1"></i>Back to Home
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
