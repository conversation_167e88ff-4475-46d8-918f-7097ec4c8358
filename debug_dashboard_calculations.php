<?php
/**
 * Debug Dashboard Calculations
 * This script helps identify calculation inconsistencies in the dashboard
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>Dashboard Calculations Debug Report</h2>";
    echo "<hr>";
    
    // Basic counts
    echo "<h3>Basic Counts</h3>";
    
    $queries = [
        'Total Books' => "SELECT COUNT(*) as count FROM books",
        'Total Book Copies' => "SELECT SUM(quantity) as count FROM books",
        'Available Book Copies' => "SELECT SUM(available_quantity) as count FROM books",
        'Total Members' => "SELECT COUNT(*) as count FROM members",
        'Total Loans (All)' => "SELECT COUNT(*) as count FROM book_loans",
        'Borrowed Loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed'",
        'Overdue Loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'overdue'",
        'Returned Loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'returned'",
        'Active Loans (Borrowed + Overdue)' => "SELECT COUNT(*) as count FROM book_loans WHERE status IN ('borrowed', 'overdue')",
        'Overdue by Date' => "SELECT COUNT(*) as count FROM book_loans WHERE due_date < CURDATE() AND status = 'borrowed'",
        'Combined Overdue' => "SELECT COUNT(*) as count FROM book_loans WHERE (status = 'overdue' OR (due_date < CURDATE() AND status = 'borrowed'))"
    ];
    
    foreach ($queries as $label => $query) {
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        $count = $result['count'] ?? 0;
        echo "<p><strong>{$label}:</strong> {$count}</p>";
    }
    
    echo "<hr>";
    echo "<h3>Member Statistics</h3>";
    
    $member_queries = [
        'Members with Active Loans' => "SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status IN ('borrowed', 'overdue')",
        'Members who Returned Books' => "SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status = 'returned'",
        'Members with Overdue Books' => "SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status = 'overdue'",
        'Members who Ever Borrowed' => "SELECT COUNT(DISTINCT member_id) as count FROM book_loans",
        'Members with Fines' => "SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE fine > 0",
        'Total Fines Amount' => "SELECT COALESCE(SUM(fine), 0) as count FROM book_loans WHERE fine > 0"
    ];
    
    foreach ($member_queries as $label => $query) {
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        $count = $result['count'] ?? 0;
        if ($label === 'Total Fines Amount') {
            echo "<p><strong>{$label}:</strong> $" . number_format($count, 2) . "</p>";
        } else {
            echo "<p><strong>{$label}:</strong> {$count}</p>";
        }
    }
    
    echo "<hr>";
    echo "<h3>Data Consistency Checks</h3>";
    
    // Check for data inconsistencies
    $stmt = $db->prepare("SELECT COUNT(*) as total_books FROM books");
    $stmt->execute();
    $total_books = $stmt->fetch()['total_books'];
    
    $stmt = $db->prepare("SELECT SUM(available_quantity) as available FROM books");
    $stmt->execute();
    $available_books = $stmt->fetch()['available'];
    
    $stmt = $db->prepare("SELECT SUM(quantity) as total_copies FROM books");
    $stmt->execute();
    $total_copies = $stmt->fetch()['total_copies'];
    
    echo "<p><strong>Total Book Titles:</strong> {$total_books}</p>";
    echo "<p><strong>Total Book Copies:</strong> {$total_copies}</p>";
    echo "<p><strong>Available Book Copies:</strong> {$available_books}</p>";
    
    if ($available_books > $total_copies) {
        echo "<p style='color: red;'><strong>⚠️ ISSUE:</strong> Available copies ({$available_books}) cannot be greater than total copies ({$total_copies})</p>";
    }
    
    if ($total_books > $total_copies) {
        echo "<p style='color: red;'><strong>⚠️ ISSUE:</strong> Total book titles ({$total_books}) cannot be greater than total copies ({$total_copies})</p>";
    }
    
    // Check loan status consistency
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM book_loans WHERE status NOT IN ('borrowed', 'returned', 'overdue')");
    $stmt->execute();
    $invalid_status = $stmt->fetch()['count'];
    
    if ($invalid_status > 0) {
        echo "<p style='color: red;'><strong>⚠️ ISSUE:</strong> Found {$invalid_status} loans with invalid status</p>";
    }
    
    echo "<hr>";
    echo "<h3>Sample Data</h3>";
    
    // Show sample books data
    echo "<h4>Sample Books (First 5)</h4>";
    $stmt = $db->prepare("SELECT title, quantity, available_quantity FROM books LIMIT 5");
    $stmt->execute();
    $books = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Title</th><th>Total Quantity</th><th>Available Quantity</th></tr>";
    foreach ($books as $book) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($book['title']) . "</td>";
        echo "<td>" . $book['quantity'] . "</td>";
        echo "<td>" . $book['available_quantity'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show sample loans data
    echo "<h4>Sample Loans (First 5)</h4>";
    $stmt = $db->prepare("SELECT bl.status, bl.issue_date, bl.due_date, bl.fine, b.title 
                          FROM book_loans bl 
                          JOIN books b ON bl.book_id = b.id 
                          ORDER BY bl.issue_date DESC 
                          LIMIT 5");
    $stmt->execute();
    $loans = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Book Title</th><th>Status</th><th>Issue Date</th><th>Due Date</th><th>Fine</th></tr>";
    foreach ($loans as $loan) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($loan['title']) . "</td>";
        echo "<td>" . $loan['status'] . "</td>";
        echo "<td>" . $loan['issue_date'] . "</td>";
        echo "<td>" . $loan['due_date'] . "</td>";
        echo "<td>$" . number_format($loan['fine'], 2) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
