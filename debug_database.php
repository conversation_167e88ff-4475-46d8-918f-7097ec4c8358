<?php
/**
 * Database Debug Script - Check why users are not being stored
 */

echo "<h1>🔍 Database Debug - Checking User Storage Issue</h1>";

try {
    // Test 1: Check database connection
    echo "<h2>1. Database Connection Test</h2>";
    require_once 'config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<p style='color: green;'>✅ Database connection successful!</p>";
    } else {
        echo "<p style='color: red;'>❌ Database connection failed!</p>";
        exit;
    }
    
    // Test 2: Check if members table exists
    echo "<h2>2. Members Table Check</h2>";
    
    $query = "SHOW TABLES LIKE 'members'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $table_exists = $stmt->rowCount() > 0;
    
    if ($table_exists) {
        echo "<p style='color: green;'>✅ Members table exists!</p>";
    } else {
        echo "<p style='color: red;'>❌ Members table does not exist!</p>";
        echo "<p><strong>Solution:</strong> Run the database setup script:</p>";
        echo "<p><a href='setup_database.php' style='background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Setup Database</a></p>";
        exit;
    }
    
    // Test 3: Check table structure
    echo "<h2>3. Table Structure Check</h2>";
    
    $query = "DESCRIBE members";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $required_fields = ['id', 'first_name', 'last_name', 'email', 'membership_status', 'membership_date'];
    $existing_fields = [];
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
        
        $existing_fields[] = $column['Field'];
    }
    echo "</table>";
    
    // Check for missing required fields
    $missing_fields = array_diff($required_fields, $existing_fields);
    if (empty($missing_fields)) {
        echo "<p style='color: green;'>✅ All required fields are present!</p>";
    } else {
        echo "<p style='color: red;'>❌ Missing required fields: " . implode(', ', $missing_fields) . "</p>";
    }
    
    // Check if password field exists
    $has_password = in_array('password', $existing_fields);
    if ($has_password) {
        echo "<p style='color: green;'>✅ Password field exists!</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Password field missing. Run update script:</p>";
        echo "<p><a href='update_members_table.php' style='background: #ffc107; color: black; padding: 10px; text-decoration: none; border-radius: 5px;'>Update Members Table</a></p>";
    }
    
    // Test 4: Check current data
    echo "<h2>4. Current Data Check</h2>";
    
    $query = "SELECT COUNT(*) as count FROM members";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $member_count = $stmt->fetch()['count'];
    
    echo "<p>Current number of members: <strong>$member_count</strong></p>";
    
    if ($member_count > 0) {
        echo "<p style='color: green;'>✅ Members are already stored in the database!</p>";
        
        // Show some sample data
        $query = "SELECT id, first_name, last_name, email, membership_status, membership_date FROM members LIMIT 5";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $members = $stmt->fetchAll();
        
        echo "<h3>Sample Members:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Name</th><th>Email</th><th>Status</th><th>Date</th></tr>";
        
        foreach ($members as $member) {
            echo "<tr>";
            echo "<td>" . $member['id'] . "</td>";
            echo "<td>" . htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) . "</td>";
            echo "<td>" . htmlspecialchars($member['email']) . "</td>";
            echo "<td>" . $member['membership_status'] . "</td>";
            echo "<td>" . $member['membership_date'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p style='color: orange;'>⚠️ No members found in database yet.</p>";
    }
    
    // Test 5: Try to insert a test member
    echo "<h2>5. Test Member Insertion</h2>";
    
    $test_email = 'test_debug_' . time() . '@example.com';
    
    try {
        if ($has_password) {
            $query = "INSERT INTO members (first_name, last_name, email, phone, address, membership_date, membership_status, password) 
                      VALUES ('Test', 'User', :email, '555-TEST', '123 Test St', CURDATE(), 'active', :password)";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':email', $test_email);
            $test_password = password_hash('testpassword', PASSWORD_DEFAULT);
            $stmt->bindParam(':password', $test_password);
        } else {
            $query = "INSERT INTO members (first_name, last_name, email, phone, address, membership_date, membership_status) 
                      VALUES ('Test', 'User', :email, '555-TEST', '123 Test St', CURDATE(), 'active')";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':email', $test_email);
        }
        
        if ($stmt->execute()) {
            $new_member_id = $db->lastInsertId();
            echo "<p style='color: green;'>✅ Successfully inserted test member with ID: $new_member_id</p>";
            
            // Verify the insertion
            $query = "SELECT * FROM members WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $new_member_id);
            $stmt->execute();
            $inserted_member = $stmt->fetch();
            
            if ($inserted_member) {
                echo "<p style='color: green;'>✅ Test member successfully retrieved from database!</p>";
                echo "<p><strong>Member Details:</strong></p>";
                echo "<ul>";
                echo "<li>ID: " . $inserted_member['id'] . "</li>";
                echo "<li>Name: " . htmlspecialchars($inserted_member['first_name'] . ' ' . $inserted_member['last_name']) . "</li>";
                echo "<li>Email: " . htmlspecialchars($inserted_member['email']) . "</li>";
                echo "<li>Status: " . $inserted_member['membership_status'] . "</li>";
                echo "<li>Date: " . $inserted_member['membership_date'] . "</li>";
                echo "</ul>";
                
                // Clean up - delete test member
                $query = "DELETE FROM members WHERE id = :id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':id', $new_member_id);
                if ($stmt->execute()) {
                    echo "<p style='color: blue;'>🧹 Test member cleaned up successfully.</p>";
                }
            }
            
        } else {
            echo "<p style='color: red;'>❌ Failed to insert test member!</p>";
            $error_info = $stmt->errorInfo();
            echo "<p>Error: " . $error_info[2] . "</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Database error during insertion: " . $e->getMessage() . "</p>";
    }
    
    // Test 6: Check database configuration
    echo "<h2>6. Database Configuration Check</h2>";
    
    // Check database config file
    if (file_exists('config/database.php')) {
        echo "<p style='color: green;'>✅ Database config file exists</p>";
        
        // Try to read config (safely)
        $config_content = file_get_contents('config/database.php');
        if (strpos($config_content, 'class Database') !== false) {
            echo "<p style='color: green;'>✅ Database class found in config</p>";
        } else {
            echo "<p style='color: red;'>❌ Database class not found in config</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Database config file missing!</p>";
    }
    
    // Final summary
    echo "<h2>🎯 Summary & Next Steps</h2>";
    
    if ($member_count > 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>✅ GOOD NEWS: Users ARE being stored in the database!</h3>";
        echo "<p>You currently have <strong>$member_count member(s)</strong> in your database.</p>";
        echo "<p><strong>Next steps:</strong></p>";
        echo "<ul>";
        echo "<li><a href='members/index.php'>View all members</a></li>";
        echo "<li><a href='members/add.php'>Add a new member</a></li>";
        echo "<li><a href='members/statistics.php'>View member statistics</a></li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ No members found, but system is ready!</h3>";
        echo "<p>The database is properly set up and ready to store users.</p>";
        echo "<p><strong>To add your first member:</strong></p>";
        echo "<ul>";
        echo "<li><a href='members/add.php' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Add New Member</a></li>";
        echo "<li><a href='register.php'>Register via public form</a></li>";
        echo "<li><a href='members/bulk_operations.php'>Import members from CSV</a></li>";
        echo "</ul>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ Error occurred:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p><strong>Possible solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Check if XAMPP/WAMP is running</li>";
    echo "<li>Verify database credentials in config/database.php</li>";
    echo "<li>Run setup_database.php to create tables</li>";
    echo "<li>Check if MySQL service is started</li>";
    echo "</ul>";
    echo "</div>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h1, h2, h3 { color: #333; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    a { color: #007bff; text-decoration: none; }
    a:hover { text-decoration: underline; }
</style>
