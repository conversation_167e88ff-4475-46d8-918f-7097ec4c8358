<?php
session_start();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Dashboard Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-container { max-width: 900px; margin: 2rem auto; padding: 2rem; }
        .debug-section { background: white; padding: 1.5rem; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 1rem; }
        .status-success { background: #d4edda; color: #155724; padding: 1rem; border-radius: 6px; }
        .status-error { background: #f8d7da; color: #721c24; padding: 1rem; border-radius: 6px; }
        .status-warning { background: #fff3cd; color: #856404; padding: 1rem; border-radius: 6px; }
        .code-block { background: #f8f9fa; padding: 1rem; border-radius: 6px; font-family: monospace; border-left: 4px solid #007bff; }
    </style>
</head>
<body class="bg-light">
    <div class="debug-container">
        <h1 class="text-center mb-4">🔍 Member Dashboard Debug Tool</h1>
        
        <div class="debug-section">
            <h3>1. Session Information</h3>
            <?php if (isset($_SESSION['member_id'])): ?>
                <div class="status-success">
                    ✅ Member is logged in!<br>
                    Member ID: <?php echo $_SESSION['member_id']; ?><br>
                    Member Name: <?php echo $_SESSION['member_name'] ?? 'Not set'; ?>
                </div>
            <?php else: ?>
                <div class="status-error">
                    ❌ No member session found. You need to log in first.
                </div>
            <?php endif; ?>
        </div>

        <div class="debug-section">
            <h3>2. File Existence Check</h3>
            <?php
            $files_to_check = [
                'member_dashboard.php' => 'Root member dashboard',
                'member/member_dashboard.php' => 'Member directory dashboard',
                'config/database.php' => 'Database config',
                'includes/functions.php' => 'Functions file'
            ];
            
            foreach ($files_to_check as $file => $description) {
                if (file_exists($file)) {
                    echo "<div class='status-success mb-2'>✅ {$description}: {$file}</div>";
                } else {
                    echo "<div class='status-error mb-2'>❌ Missing: {$description}: {$file}</div>";
                }
            }
            ?>
        </div>

        <div class="debug-section">
            <h3>3. Database Connection Test</h3>
            <?php
            try {
                require_once 'config/database.php';
                $database = new Database();
                $db = $database->getConnection();
                echo "<div class='status-success'>✅ Database connection successful!</div>";
                
                // Test members table
                $query = "SELECT COUNT(*) as count FROM members";
                $stmt = $db->prepare($query);
                $stmt->execute();
                $result = $stmt->fetch();
                echo "<div class='status-success mt-2'>✅ Members table accessible. Total members: " . $result['count'] . "</div>";
                
            } catch (Exception $e) {
                echo "<div class='status-error'>❌ Database error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>

        <div class="debug-section">
            <h3>4. Quick Access Links</h3>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <a href="member_dashboard.php" class="btn btn-primary w-100">Try Root Dashboard</a>
                </div>
                <div class="col-md-6 mb-3">
                    <a href="member/member_dashboard.php" class="btn btn-success w-100">Try Member Directory</a>
                </div>
            </div>
        </div>

        <div class="debug-section">
            <h3>5. Login Options</h3>
            <?php if (!isset($_SESSION['member_id'])): ?>
                <div class="status-warning mb-3">
                    You need to log in as a member first. Here are your options:
                </div>
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <a href="login.php" class="btn btn-info w-100">Regular Login</a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="quick_member_access.php" class="btn btn-warning w-100">Quick Member Login</a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="register.php" class="btn btn-secondary w-100">Register New Member</a>
                    </div>
                </div>
            <?php else: ?>
                <div class="status-success">
                    You are logged in! Try accessing the dashboard now.
                </div>
            <?php endif; ?>
        </div>

        <div class="debug-section">
            <h3>6. System Information</h3>
            <div class="code-block">
                Current URL: <?php echo $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?><br>
                Document Root: <?php echo $_SERVER['DOCUMENT_ROOT']; ?><br>
                Script Path: <?php echo __FILE__; ?><br>
                Working Directory: <?php echo getcwd(); ?>
            </div>
        </div>

        <div class="debug-section">
            <h3>7. Correct URLs for Your System</h3>
            <p>Based on your system setup, use these URLs:</p>
            <div class="code-block">
                Member Dashboard (Root): <a href="member_dashboard.php">http://localhost/xampp/htdocs/LMS_SYSTEM/member_dashboard.php</a><br>
                Member Dashboard (Directory): <a href="member/member_dashboard.php">http://localhost/xampp/htdocs/LMS_SYSTEM/member/member_dashboard.php</a><br>
                Login Page: <a href="login.php">http://localhost/xampp/htdocs/LMS_SYSTEM/login.php</a>
            </div>
        </div>

        <?php if (!isset($_SESSION['member_id'])): ?>
        <div class="debug-section">
            <h3>8. Quick Test Login</h3>
            <p>Let me create a quick test login for you:</p>
            <form method="POST" action="">
                <div class="row">
                    <div class="col-md-4">
                        <input type="email" name="test_email" class="form-control" placeholder="Enter member email" value="<EMAIL>">
                    </div>
                    <div class="col-md-4">
                        <input type="password" name="test_password" class="form-control" placeholder="Password" value="password">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" name="test_login" class="btn btn-primary w-100">Test Login</button>
                    </div>
                </div>
            </form>
            
            <?php
            if (isset($_POST['test_login'])) {
                try {
                    require_once 'config/database.php';
                    $database = new Database();
                    $db = $database->getConnection();
                    
                    $email = $_POST['test_email'];
                    $password = $_POST['test_password'];
                    
                    $query = "SELECT * FROM members WHERE email = :email LIMIT 1";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':email', $email);
                    $stmt->execute();
                    $member = $stmt->fetch();
                    
                    if ($member && password_verify($password, $member['password'])) {
                        $_SESSION['member_id'] = $member['id'];
                        $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
                        echo "<div class='status-success mt-3'>✅ Test login successful! <a href='member_dashboard.php'>Go to Dashboard</a></div>";
                    } else {
                        echo "<div class='status-error mt-3'>❌ Login failed. Try different credentials or check if member exists.</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='status-error mt-3'>❌ Login error: " . $e->getMessage() . "</div>";
                }
            }
            ?>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
