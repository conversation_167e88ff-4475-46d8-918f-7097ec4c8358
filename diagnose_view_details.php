<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h1>🔍 View Details Diagnostic</h1>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; }
.error { color: red; }
.warning { color: orange; }
.info { color: blue; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

// 1. Check database connection
echo "<div class='section'>";
echo "<h2>1. Database Connection</h2>";
try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<p class='success'>✅ Database connected successfully</p>";
    } else {
        echo "<p class='error'>❌ Database connection failed</p>";
        exit;
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
    exit;
}
echo "</div>";

// 2. Check required functions
echo "<div class='section'>";
echo "<h2>2. Required Functions</h2>";
$required_functions = ['url', 'h', 'isLoggedIn', 'isMemberLoggedIn'];
foreach ($required_functions as $func) {
    if (function_exists($func)) {
        echo "<p class='success'>✅ Function $func exists</p>";
    } else {
        echo "<p class='error'>❌ Function $func missing</p>";
    }
}
echo "</div>";

// 3. Test URL generation
echo "<div class='section'>";
echo "<h2>3. URL Generation Test</h2>";
$test_urls = [
    'books/index.php',
    'members/index.php',
    'loans/index.php',
    'loans/overdue.php'
];

foreach ($test_urls as $path) {
    try {
        $generated_url = url($path);
        echo "<p class='info'>📍 $path → $generated_url</p>";
        
        // Check if the file exists
        $file_path = $path;
        if (file_exists($file_path)) {
            echo "<p class='success'>✅ File exists: $file_path</p>";
        } else {
            echo "<p class='warning'>⚠️ File not found: $file_path</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ URL generation error for $path: " . $e->getMessage() . "</p>";
    }
}
echo "</div>";

// 4. Check file permissions
echo "<div class='section'>";
echo "<h2>4. File Permissions</h2>";
$files_to_check = [
    'books/index.php',
    'members/index.php', 
    'loans/index.php',
    'loans/overdue.php',
    'admin/dashboard.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $readable = is_readable($file);
        echo "<p class='info'>📄 $file - Permissions: " . substr(sprintf('%o', $perms), -4) . " - Readable: " . ($readable ? 'Yes' : 'No') . "</p>";
    } else {
        echo "<p class='error'>❌ File not found: $file</p>";
    }
}
echo "</div>";

// 5. Test basic statistics
echo "<div class='section'>";
echo "<h2>5. Basic Statistics</h2>";
try {
    $query = "SELECT COUNT(*) as count FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $book_count = $stmt->fetch()['count'];
    echo "<p class='success'>📚 Total Books: $book_count</p>";
    
    $query = "SELECT COUNT(*) as count FROM members";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $member_count = $stmt->fetch()['count'];
    echo "<p class='success'>👥 Total Members: $member_count</p>";
    
    $query = "SELECT COUNT(*) as count FROM book_loans WHERE status != 'returned'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $loan_count = $stmt->fetch()['count'];
    echo "<p class='success'>📖 Active Loans: $loan_count</p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Statistics query error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 6. Check session and authentication
echo "<div class='section'>";
echo "<h2>6. Session & Authentication</h2>";
echo "<p class='info'>Session ID: " . session_id() . "</p>";
echo "<p class='info'>Logged in as admin: " . (isLoggedIn() ? 'Yes' : 'No') . "</p>";
echo "<p class='info'>Logged in as member: " . (isMemberLoggedIn() ? 'Yes' : 'No') . "</p>";

if (isset($_SESSION['user_id'])) {
    echo "<p class='info'>User ID: " . $_SESSION['user_id'] . "</p>";
}
if (isset($_SESSION['username'])) {
    echo "<p class='info'>Username: " . $_SESSION['username'] . "</p>";
}
echo "</div>";

// 7. Test actual links
echo "<div class='section'>";
echo "<h2>7. Test Links</h2>";
echo "<p>Click these links to test if they work:</p>";
echo "<ul>";
echo "<li><a href='" . url('books/index.php') . "' target='_blank'>Books Management</a></li>";
echo "<li><a href='" . url('members/index.php') . "' target='_blank'>Members Management</a></li>";
echo "<li><a href='" . url('loans/index.php') . "' target='_blank'>Loans Management</a></li>";
echo "<li><a href='" . url('loans/overdue.php') . "' target='_blank'>Overdue Loans</a></li>";
echo "<li><a href='admin/dashboard.php' target='_blank'>Admin Dashboard</a></li>";
echo "</ul>";
echo "</div>";

// 8. JavaScript test
echo "<div class='section'>";
echo "<h2>8. JavaScript Console Test</h2>";
echo "<p>Check the browser console (F12) for any JavaScript errors.</p>";
echo "<button onclick='testConsole()' class='btn'>Test Console Logging</button>";
echo "</div>";

echo "<script>";
echo "function testConsole() {";
echo "    console.log('✅ JavaScript is working');";
echo "    console.log('Current URL:', window.location.href);";
echo "    console.log('Base URL from PHP:', '" . BASE_URL . "');";
echo "    alert('Check the browser console for test results');";
echo "}";
echo "</script>";

echo "<div class='section'>";
echo "<h2>🎯 Quick Actions</h2>";
echo "<p><a href='test_view_details_fix.php'>🔗 Test View Details Links</a></p>";
echo "<p><a href='admin/dashboard.php'>📊 Go to Admin Dashboard</a></p>";
echo "<p><a href='quick_admin_login.php'>🔑 Quick Admin Login</a></p>";
echo "</div>";
?>
