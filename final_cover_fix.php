<?php
session_start();
require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Final Cover Fix</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h2>Final Book Cover Fix</h2>";

// Step 1: Ensure all books have cover images
$covers = [
    '1984_cover.jpg',
    '1747724281_AGameOfThrones.jpg',
    '1747724303_Good to Great.jpg',
    '1747724335_Pride and Prejudice.jpg',
    '1747725415_steve-jobs-9781451648553_hr.jpg',
    '1747725431_the-alchemist-a-graphic-novel.jpg',
    '1747725459_Book-Review-The-Catcher-in-the-Rye-by-J-<PERSON>-<PERSON><PERSON>.jpg',
    '1747725503_the-great-gatsby-and-other-works-9781645173519_hr.jpg',
    '1747725673_The_Martian_(Weir_novel).jpg',
    '1747725698_fuck-640x996.jpg',
    '1747725734_71qFBdNS+dL.jpg',
    '1747725751_9781784870799.jpg'
];

if (isset($_POST['apply_final_fix'])) {
    echo "<h3>Applying Final Fix...</h3>";
    
    // Get all books
    $query = "SELECT id, title FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $books = $stmt->fetchAll();
    
    $updated = 0;
    $cover_index = 0;
    
    foreach ($books as $book) {
        $cover = $covers[$cover_index % count($covers)];
        
        $update_query = "UPDATE books SET cover_image = :cover WHERE id = :id";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':cover', $cover);
        $update_stmt->bindParam(':id', $book['id']);
        
        if ($update_stmt->execute()) {
            echo "<div class='alert alert-success'>✅ Updated '{$book['title']}' with {$cover}</div>";
            $updated++;
        }
        
        $cover_index++;
    }
    
    echo "<div class='alert alert-info'><strong>Updated {$updated} books</strong></div>";
    
    // Test display
    echo "<h3>Testing Display</h3>";
    $query = "SELECT * FROM books LIMIT 6";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $test_books = $stmt->fetchAll();
    
    echo "<div class='row'>";
    foreach ($test_books as $book) {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card'>";
        echo "<img src='uploads/covers/{$book['cover_image']}' class='card-img-top' style='height: 200px; object-fit: cover;' alt='{$book['title']}' onerror='this.style.border=\"2px solid red\";'>";
        echo "<div class='card-body'>";
        echo "<h6 class='card-title'>" . htmlspecialchars($book['title']) . "</h6>";
        echo "<p class='card-text'><small>" . htmlspecialchars($book['cover_image']) . "</small></p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='mt-4'>";
    echo "<a href='catalog.php' class='btn btn-primary btn-lg'>View Catalog</a>";
    echo "</div>";
    
} else {
    echo "<h3>Current Status</h3>";
    
    // Check current state
    $query = "SELECT COUNT(*) as total FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $total = $stmt->fetch()['total'];
    
    $query = "SELECT COUNT(*) as with_covers FROM books WHERE cover_image IS NOT NULL AND cover_image != ''";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $with_covers = $stmt->fetch()['with_covers'];
    
    echo "<p>Total books: {$total}</p>";
    echo "<p>Books with covers: {$with_covers}</p>";
    
    // Check if cover files exist
    echo "<h3>Cover Files Check</h3>";
    foreach ($covers as $cover) {
        $exists = file_exists('uploads/covers/' . $cover);
        echo "<p>{$cover}: " . ($exists ? '✅ Found' : '❌ Missing') . "</p>";
    }
    
    echo "<form method='post' class='mt-4'>";
    echo "<button type='submit' name='apply_final_fix' class='btn btn-danger btn-lg'>Apply Final Fix (Update All Books)</button>";
    echo "</form>";
}

echo "</div></body></html>";
?>
