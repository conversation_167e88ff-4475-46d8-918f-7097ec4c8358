<?php
/**
 * Fix Data Inconsistencies
 * This script fixes common data inconsistencies in the LMS database
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>Fixing Data Inconsistencies</h2>";
    echo "<hr>";
    
    // Start transaction
    $db->beginTransaction();
    
    $fixes_applied = 0;
    
    // Fix 1: Ensure available_quantity is not greater than quantity for books
    echo "<h3>Fix 1: Book Quantity Consistency</h3>";
    
    $stmt = $db->prepare("SELECT id, title, quantity, available_quantity FROM books WHERE available_quantity > quantity");
    $stmt->execute();
    $inconsistent_books = $stmt->fetchAll();
    
    if (count($inconsistent_books) > 0) {
        echo "<p>Found " . count($inconsistent_books) . " books with available_quantity > quantity</p>";
        
        foreach ($inconsistent_books as $book) {
            echo "<p>Fixing book: " . htmlspecialchars($book['title']) . " (Available: {$book['available_quantity']}, Total: {$book['quantity']})</p>";
            
            // Set available_quantity to quantity
            $update_stmt = $db->prepare("UPDATE books SET available_quantity = quantity WHERE id = ?");
            $update_stmt->execute([$book['id']]);
            $fixes_applied++;
        }
    } else {
        echo "<p>✅ No book quantity inconsistencies found.</p>";
    }
    
    // Fix 2: Update book availability based on active loans
    echo "<h3>Fix 2: Recalculate Book Availability</h3>";
    
    // Get all books and recalculate their availability
    $stmt = $db->prepare("SELECT id, title, quantity FROM books");
    $stmt->execute();
    $all_books = $stmt->fetchAll();
    
    foreach ($all_books as $book) {
        // Count active loans for this book
        $loan_stmt = $db->prepare("SELECT COUNT(*) as active_loans FROM book_loans WHERE book_id = ? AND status IN ('borrowed', 'overdue')");
        $loan_stmt->execute([$book['id']]);
        $active_loans = $loan_stmt->fetch()['active_loans'];
        
        // Calculate correct available quantity
        $correct_available = max(0, $book['quantity'] - $active_loans);
        
        // Update if different
        $current_stmt = $db->prepare("SELECT available_quantity FROM books WHERE id = ?");
        $current_stmt->execute([$book['id']]);
        $current_available = $current_stmt->fetch()['available_quantity'];
        
        if ($current_available != $correct_available) {
            echo "<p>Updating book: " . htmlspecialchars($book['title']) . " (Current available: {$current_available}, Correct: {$correct_available})</p>";
            
            $update_stmt = $db->prepare("UPDATE books SET available_quantity = ? WHERE id = ?");
            $update_stmt->execute([$correct_available, $book['id']]);
            $fixes_applied++;
        }
    }
    
    // Fix 3: Update overdue status for loans past due date
    echo "<h3>Fix 3: Update Overdue Loan Status</h3>";
    
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM book_loans WHERE due_date < CURDATE() AND status = 'borrowed'");
    $stmt->execute();
    $overdue_count = $stmt->fetch()['count'];
    
    if ($overdue_count > 0) {
        echo "<p>Found {$overdue_count} loans that should be marked as overdue</p>";
        
        // Update overdue loans
        $update_stmt = $db->prepare("UPDATE book_loans SET status = 'overdue' WHERE due_date < CURDATE() AND status = 'borrowed'");
        $update_stmt->execute();
        $fixes_applied++;
        
        echo "<p>✅ Updated {$overdue_count} loans to overdue status</p>";
    } else {
        echo "<p>✅ No loans need to be marked as overdue.</p>";
    }
    
    // Fix 4: Calculate fines for overdue books (if not already set)
    echo "<h3>Fix 4: Calculate Missing Fines</h3>";
    
    $stmt = $db->prepare("SELECT id, due_date, DATEDIFF(CURDATE(), due_date) as days_overdue 
                          FROM book_loans 
                          WHERE status = 'overdue' AND fine = 0 AND due_date < CURDATE()");
    $stmt->execute();
    $loans_needing_fines = $stmt->fetchAll();
    
    if (count($loans_needing_fines) > 0) {
        echo "<p>Found " . count($loans_needing_fines) . " overdue loans without fines</p>";
        
        $fine_per_day = 1.00; // $1 per day fine
        
        foreach ($loans_needing_fines as $loan) {
            $fine_amount = $loan['days_overdue'] * $fine_per_day;
            
            $update_stmt = $db->prepare("UPDATE book_loans SET fine = ? WHERE id = ?");
            $update_stmt->execute([$fine_amount, $loan['id']]);
            
            echo "<p>Added fine of $" . number_format($fine_amount, 2) . " for loan ID {$loan['id']} ({$loan['days_overdue']} days overdue)</p>";
            $fixes_applied++;
        }
    } else {
        echo "<p>✅ No missing fines to calculate.</p>";
    }
    
    // Commit transaction
    $db->commit();
    
    echo "<hr>";
    echo "<h3>Summary</h3>";
    echo "<p><strong>Total fixes applied:</strong> {$fixes_applied}</p>";
    
    if ($fixes_applied > 0) {
        echo "<p style='color: green;'>✅ Data inconsistencies have been fixed. Please refresh your dashboard to see updated calculations.</p>";
        echo "<p><a href='admin/dashboard.php'>Go to Admin Dashboard</a></p>";
    } else {
        echo "<p style='color: green;'>✅ No data inconsistencies found. Your database is in good shape!</p>";
    }
    
} catch (Exception $e) {
    // Rollback transaction on error
    if ($db->inTransaction()) {
        $db->rollback();
    }
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
