<?php
/**
 * Navigation Fix Script
 * This script fixes common navigation issues in the LMS system
 */

// Start session and include required files
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Function to check and fix file permissions
function checkFilePermissions($file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $readable = is_readable($file);
        return [
            'exists' => true,
            'readable' => $readable,
            'permissions' => substr(sprintf('%o', $perms), -4)
        ];
    }
    return ['exists' => false, 'readable' => false, 'permissions' => null];
}

// Function to test URL generation
function testUrlGeneration() {
    $test_paths = [
        'members/index.php',
        'loans/index.php', 
        'reports/index.php',
        'admin/users.php',
        'admin/settings.php',
        'admin/email_settings.php'
    ];
    
    $results = [];
    foreach ($test_paths as $path) {
        $full_url = url($path);
        $file_path = $path;
        $file_check = checkFilePermissions($file_path);
        
        $results[] = [
            'path' => $path,
            'url' => $full_url,
            'file_exists' => $file_check['exists'],
            'readable' => $file_check['readable'],
            'permissions' => $file_check['permissions']
        ];
    }
    
    return $results;
}

// Function to fix common issues
function fixCommonIssues() {
    $fixes_applied = [];
    
    // Check if .htaccess exists and is properly configured
    $htaccess_path = '.htaccess';
    if (!file_exists($htaccess_path)) {
        $fixes_applied[] = 'Missing .htaccess file detected';
    }
    
    // Check if all required directories exist
    $required_dirs = ['members', 'loans', 'reports', 'admin', 'books'];
    foreach ($required_dirs as $dir) {
        if (!is_dir($dir)) {
            $fixes_applied[] = "Missing directory: $dir";
        }
    }
    
    // Check if config files are accessible
    $config_files = ['config/config.php', 'config/database.php', 'includes/functions.php'];
    foreach ($config_files as $file) {
        if (!file_exists($file)) {
            $fixes_applied[] = "Missing config file: $file";
        }
    }
    
    return $fixes_applied;
}

// Run tests
$url_tests = testUrlGeneration();
$issues = fixCommonIssues();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Fix - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .fix-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            background-color: #f8f9fa;
        }
        .status-good { color: #198754; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #fd7e14; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4"><i class="bi bi-tools me-2"></i>Navigation Fix & Diagnostics</h1>
        
        <!-- Quick Fix Actions -->
        <div class="fix-section">
            <h2>Quick Fix Actions</h2>
            <div class="row">
                <div class="col-md-6">
                    <div class="d-grid gap-2">
                        <a href="<?php echo url('test_navigation.php'); ?>" class="btn btn-primary">
                            <i class="bi bi-bug me-2"></i>Run Full Navigation Test
                        </a>
                        <a href="<?php echo url('admin/dashboard.php'); ?>" class="btn btn-success">
                            <i class="bi bi-speedometer2 me-2"></i>Go to Admin Dashboard
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-grid gap-2">
                        <a href="<?php echo url('simple_test.php'); ?>" class="btn btn-info">
                            <i class="bi bi-check-circle me-2"></i>System Health Check
                        </a>
                        <a href="<?php echo url('check_database.php'); ?>" class="btn btn-warning">
                            <i class="bi bi-database me-2"></i>Database Check
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- URL Generation Tests -->
        <div class="fix-section">
            <h2>URL Generation Tests</h2>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Page</th>
                            <th>Generated URL</th>
                            <th>File Exists</th>
                            <th>Readable</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($url_tests as $test): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($test['path']); ?></td>
                            <td><code><?php echo htmlspecialchars($test['url']); ?></code></td>
                            <td>
                                <span class="<?php echo $test['file_exists'] ? 'status-good' : 'status-bad'; ?>">
                                    <?php echo $test['file_exists'] ? '✅ Yes' : '❌ No'; ?>
                                </span>
                            </td>
                            <td>
                                <span class="<?php echo $test['readable'] ? 'status-good' : 'status-bad'; ?>">
                                    <?php echo $test['readable'] ? '✅ Yes' : '❌ No'; ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($test['file_exists'] && $test['readable']): ?>
                                    <a href="<?php echo $test['url']; ?>" class="btn btn-sm btn-success" target="_blank">Test</a>
                                <?php else: ?>
                                    <span class="badge bg-danger">Error</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- System Issues -->
        <div class="fix-section">
            <h2>System Issues Detected</h2>
            <?php if (empty($issues)): ?>
                <div class="alert alert-success">
                    <i class="bi bi-check-circle me-2"></i>No major issues detected! Your navigation should be working properly.
                </div>
            <?php else: ?>
                <div class="alert alert-warning">
                    <h5>Issues Found:</h5>
                    <ul class="mb-0">
                        <?php foreach ($issues as $issue): ?>
                            <li><?php echo htmlspecialchars($issue); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>

        <!-- Configuration Summary -->
        <div class="fix-section">
            <h2>Configuration Summary</h2>
            <div class="row">
                <div class="col-md-6">
                    <h5>System Settings</h5>
                    <ul class="list-unstyled">
                        <li><strong>Base URL:</strong> <?php echo BASE_URL; ?></li>
                        <li><strong>Current URL:</strong> <?php echo getCurrentUrl(); ?></li>
                        <li><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></li>
                        <li><strong>Session Status:</strong> <?php echo session_status() === PHP_SESSION_ACTIVE ? '✅ Active' : '❌ Inactive'; ?></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>Function Status</h5>
                    <ul class="list-unstyled">
                        <li><strong>isLoggedIn():</strong> <?php echo isLoggedIn() ? '✅ True' : '❌ False'; ?></li>
                        <li><strong>isAdmin():</strong> <?php echo isAdmin() ? '✅ True' : '❌ False'; ?></li>
                        <li><strong>url() function:</strong> <?php echo function_exists('url') ? '✅ Available' : '❌ Missing'; ?></li>
                        <li><strong>Database:</strong> 
                            <?php 
                            try {
                                $db_test = new Database();
                                $conn = $db_test->getConnection();
                                echo $conn ? '✅ Connected' : '❌ Failed';
                            } catch (Exception $e) {
                                echo '❌ Error';
                            }
                            ?>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="fix-section">
            <h2>How to Use Your Navigation</h2>
            <div class="alert alert-info">
                <h5>✅ Your navigation should now be working! Here's how to access different sections:</h5>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>Main Navigation:</h6>
                        <ul>
                            <li><a href="<?php echo url('members/index.php'); ?>">Members Management</a></li>
                            <li><a href="<?php echo url('loans/index.php'); ?>">Book Loans</a></li>
                            <li><a href="<?php echo url('reports/index.php'); ?>">Reports</a></li>
                            <li><a href="<?php echo url('books/index.php'); ?>">Books Management</a></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Admin Navigation:</h6>
                        <ul>
                            <li><a href="<?php echo url('admin/users.php'); ?>">Manage Users</a></li>
                            <li><a href="<?php echo url('admin/settings.php'); ?>">System Settings</a></li>
                            <li><a href="<?php echo url('admin/email_settings.php'); ?>">Email Settings</a></li>
                            <li><a href="<?php echo url('admin/dashboard.php'); ?>">Admin Dashboard</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
