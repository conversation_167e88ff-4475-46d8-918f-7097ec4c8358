<?php
/**
 * Fix Reminder System - Ensure reminder_logs table exists
 *
 * This script ensures the reminder_logs table exists and fixes any issues
 * with the reminder system.
 */

// Include database connection
require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Initialize variables
$success = true;
$messages = [];

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Fix Reminder System - Library Management System</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { padding: 40px 0; background-color: #f8f9fa; }
        .fix-card { max-width: 800px; margin: 0 auto; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
    </style>
</head>
<body>
    <div class='container'>
        <div class='card fix-card'>
            <div class='card-header bg-primary text-white'>
                <h4 class='mb-0'>🔧 Fix Reminder System</h4>
            </div>
            <div class='card-body'>";

// Check if reminder_logs table exists
try {
    $query = "SHOW TABLES LIKE 'reminder_logs'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $table_exists = $stmt->rowCount() > 0;

    if ($table_exists) {
        echo "<p>✅ reminder_logs table already exists</p>";
    } else {
        echo "<p>⚠️ reminder_logs table does not exist - creating it now...</p>";

        // Create reminder_logs table
        $create_query = "CREATE TABLE reminder_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            loan_id INT NOT NULL,
            reminder_type ENUM('due_date', 'overdue') NOT NULL,
            days_before INT,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (loan_id) REFERENCES book_loans(id) ON DELETE CASCADE
        )";
        $db->exec($create_query);
        echo "<p>✅ Created reminder_logs table successfully</p>";
    }
} catch (PDOException $e) {
    $success = false;
    echo "<p class='text-danger'>❌ Error with reminder_logs table: " . $e->getMessage() . "</p>";
}

// Check if email_logs table exists (optional but useful)
try {
    $query = "SHOW TABLES LIKE 'email_logs'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $table_exists = $stmt->rowCount() > 0;

    if (!$table_exists) {
        echo "<p>⚠️ email_logs table does not exist - creating it now...</p>";

        // Create email_logs table
        $create_query = "CREATE TABLE email_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            recipient_email VARCHAR(100) NOT NULL,
            subject VARCHAR(255) NOT NULL,
            status ENUM('pending', 'sent', 'failed') NOT NULL DEFAULT 'pending',
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $db->exec($create_query);
        echo "<p>✅ Created email_logs table successfully</p>";
    } else {
        echo "<p>✅ email_logs table already exists</p>";
    }
} catch (PDOException $e) {
    echo "<p class='text-warning'>⚠️ Note: email_logs table issue: " . $e->getMessage() . "</p>";
}

// Check if settings table exists (required for email settings page)
try {
    $query = "SHOW TABLES LIKE 'settings'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $table_exists = $stmt->rowCount() > 0;

    if (!$table_exists) {
        echo "<p>⚠️ settings table does not exist - creating it now...</p>";

        // Create settings table
        $create_query = "CREATE TABLE settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_group VARCHAR(50) NOT NULL,
            setting_key VARCHAR(50) NOT NULL,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY group_key (setting_group, setting_key)
        )";
        $db->exec($create_query);
        echo "<p>✅ Created settings table successfully</p>";

        // Insert default settings
        $default_settings = [
            // Email settings
            ['email', 'from_email', '<EMAIL>'],
            ['email', 'from_name', 'Library Management System'],
            ['email', 'reply_to', '<EMAIL>'],
            ['email', 'smtp_enabled', 'false'],
            ['email', 'smtp_host', 'smtp.example.com'],
            ['email', 'smtp_port', '587'],
            ['email', 'smtp_username', ''],
            ['email', 'smtp_password', ''],
            ['email', 'smtp_secure', 'tls'],

            // Notification settings
            ['notifications', 'due_date_reminder_days', '3'],
            ['notifications', 'send_overdue_notifications', 'true'],
            ['notifications', 'overdue_notification_frequency', '7'],

            // Fine settings
            ['fines', 'fine_rate_per_day', '0.25'],
            ['fines', 'grace_period_days', '3'],
            ['fines', 'max_fine_per_book', '25.00']
        ];

        $insert_query = "INSERT INTO settings (setting_group, setting_key, setting_value) VALUES (?, ?, ?)";
        $insert_stmt = $db->prepare($insert_query);

        foreach ($default_settings as $setting) {
            $insert_stmt->execute($setting);
        }

        echo "<p>✅ Inserted default settings successfully</p>";
    } else {
        echo "<p>✅ settings table already exists</p>";
    }
} catch (PDOException $e) {
    $success = false;
    echo "<p class='text-danger'>❌ Error with settings table: " . $e->getMessage() . "</p>";
}

// Test the reminder system by checking if we can insert a test record
try {
    // Get a sample loan ID to test with
    $query = "SELECT id FROM book_loans WHERE status = 'overdue' LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $sample_loan = $stmt->fetch();

    if ($sample_loan) {
        // Test inserting a reminder log (we'll delete it right after)
        $test_query = "INSERT INTO reminder_logs (loan_id, reminder_type, days_before, sent_at) VALUES (?, 'overdue', 5, NOW())";
        $test_stmt = $db->prepare($test_query);
        $test_stmt->execute([$sample_loan['id']]);

        // Get the inserted ID and delete the test record
        $test_id = $db->lastInsertId();
        $delete_query = "DELETE FROM reminder_logs WHERE id = ?";
        $delete_stmt = $db->prepare($delete_query);
        $delete_stmt->execute([$test_id]);

        echo "<p>✅ Reminder system test successful - can insert and delete records</p>";
    } else {
        echo "<p>ℹ️ No overdue loans found to test with, but table structure is correct</p>";
    }
} catch (PDOException $e) {
    $success = false;
    echo "<p class='text-danger'>❌ Error testing reminder system: " . $e->getMessage() . "</p>";
}

// Display final status
if ($success) {
    echo "<div class='alert alert-success mt-3'>
            <h5>✅ Reminder System Fixed Successfully!</h5>
            <p>The reminder system is now working correctly. You can:</p>
            <ul>
                <li>Send reminders from the overdue books page</li>
                <li>View reminder logs in the database</li>
                <li>Use the email notification system</li>
            </ul>
          </div>";
} else {
    echo "<div class='alert alert-danger mt-3'>
            <h5>❌ Some Issues Remain</h5>
            <p>Please check the error messages above and contact your system administrator.</p>
          </div>";
}

echo "        </div>
            <div class='card-footer'>
                <div class='d-flex justify-content-between'>
                    <a href='index.php' class='btn btn-primary'>Go to Dashboard</a>
                    <a href='loans/overdue.php' class='btn btn-success'>View Overdue Books</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
