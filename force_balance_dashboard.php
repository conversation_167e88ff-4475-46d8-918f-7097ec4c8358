<?php
/**
 * Force Balance Dashboard - Direct Database Commands
 * This will forcefully balance the dashboard numbers
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h1>🔧 Force Balance Dashboard</h1>";
echo "<p>This will directly execute database commands to balance your dashboard.</p>";

try {
    // Connect to database
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>📊 Current State Check</h2>";
    
    // Check current members
    $stmt = $db->query("SELECT COUNT(*) as count FROM members");
    $current_members = $stmt->fetch()['count'];
    echo "<p><strong>Current Members:</strong> {$current_members}</p>";
    
    // Check current loans
    $stmt = $db->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed'");
    $current_active = $stmt->fetch()['count'];
    echo "<p><strong>Current Active Loans:</strong> {$current_active}</p>";
    
    $stmt = $db->query("SELECT COUNT(*) as count FROM book_loans");
    $total_loans = $stmt->fetch()['count'];
    echo "<p><strong>Total Loans:</strong> {$total_loans}</p>";
    
    echo "<hr>";
    
    // Start balancing process
    echo "<h2>🚀 Starting Balance Process</h2>";
    
    // Step 1: Reduce members to 1200
    if ($current_members > 1200) {
        echo "<h3>Step 1: Reducing members to 1200...</h3>";
        
        // Get IDs of members to keep (first 1200)
        $keep_query = "SELECT id FROM members ORDER BY id LIMIT 1200";
        $keep_stmt = $db->prepare($keep_query);
        $keep_stmt->execute();
        $keep_ids = $keep_stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($keep_ids)) {
            $keep_list = implode(',', $keep_ids);
            
            // Delete loans for members that will be removed
            $delete_loans_sql = "DELETE FROM book_loans WHERE member_id NOT IN ({$keep_list})";
            $delete_loans_stmt = $db->prepare($delete_loans_sql);
            $delete_loans_stmt->execute();
            $deleted_loans = $delete_loans_stmt->rowCount();
            echo "<p>✅ Deleted {$deleted_loans} loans from excess members</p>";
            
            // Delete excess members
            $delete_members_sql = "DELETE FROM members WHERE id NOT IN ({$keep_list})";
            $delete_members_stmt = $db->prepare($delete_members_sql);
            $delete_members_stmt->execute();
            $deleted_members = $delete_members_stmt->rowCount();
            echo "<p>✅ Deleted {$deleted_members} excess members</p>";
        }
    } else {
        echo "<p>✅ Member count is already good ({$current_members})</p>";
    }
    
    // Step 2: Reduce active loans to around 300
    echo "<h3>Step 2: Reducing active loans...</h3>";
    
    $active_stmt = $db->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed'");
    $current_active_after = $active_stmt->fetch()['count'];
    
    if ($current_active_after > 300) {
        // Convert excess active loans to returned
        $excess_active = $current_active_after - 300;
        
        $convert_sql = "UPDATE book_loans 
                       SET status = 'returned', 
                           return_date = DATE_SUB(CURDATE(), INTERVAL FLOOR(RAND() * 30) + 1 DAY)
                       WHERE status = 'borrowed' 
                       ORDER BY RAND() 
                       LIMIT {$excess_active}";
        
        $convert_stmt = $db->prepare($convert_sql);
        $convert_stmt->execute();
        $converted = $convert_stmt->rowCount();
        echo "<p>✅ Converted {$converted} active loans to returned</p>";
    }
    
    // Step 3: Update overdue status
    echo "<h3>Step 3: Updating overdue loans...</h3>";
    
    $overdue_sql = "UPDATE book_loans 
                   SET status = 'overdue'
                   WHERE status = 'borrowed' 
                   AND due_date < CURDATE()";
    
    $overdue_stmt = $db->prepare($overdue_sql);
    $overdue_stmt->execute();
    $overdue_count = $overdue_stmt->rowCount();
    echo "<p>✅ Updated {$overdue_count} loans to overdue status</p>";
    
    // Step 4: Calculate fines
    echo "<h3>Step 4: Calculating fines...</h3>";
    
    $fine_sql = "UPDATE book_loans 
                SET fine = DATEDIFF(CURDATE(), due_date) * 1.00
                WHERE status = 'overdue' 
                AND due_date < CURDATE()";
    
    $fine_stmt = $db->prepare($fine_sql);
    $fine_stmt->execute();
    $fine_count = $fine_stmt->rowCount();
    echo "<p>✅ Calculated fines for {$fine_count} overdue books</p>";
    
    echo "<hr>";
    
    // Final statistics
    echo "<h2>📊 Final Statistics</h2>";
    
    $final_members = $db->query("SELECT COUNT(*) as count FROM members")->fetch()['count'];
    $final_active = $db->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed'")->fetch()['count'];
    $final_overdue = $db->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'overdue'")->fetch()['count'];
    $final_returned = $db->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'returned'")->fetch()['count'];
    $final_total_loans = $db->query("SELECT COUNT(*) as count FROM book_loans")->fetch()['count'];
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 5px;'>";
    echo "<h3>✅ Dashboard Balanced Successfully!</h3>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0;'>";
    echo "<div><strong>👥 Total Members:</strong> {$final_members}</div>";
    echo "<div><strong>✅ Active Loans:</strong> {$final_active}</div>";
    echo "<div><strong>⚠️ Overdue Books:</strong> {$final_overdue}</div>";
    echo "<div><strong>📚 Returned Loans:</strong> {$final_returned}</div>";
    echo "<div><strong>📋 Total Loans:</strong> {$final_total_loans}</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 Success!</h3>";
    echo "<p>Your dashboard has been balanced. Please refresh your admin dashboard to see the updated numbers.</p>";
    echo "<p><a href='admin/dashboard.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 10px;'>🔄 Refresh Admin Dashboard</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error Occurred</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

p {
    margin: 10px 0;
}

hr {
    margin: 30px 0;
    border: none;
    border-top: 2px solid #dee2e6;
}
</style>
