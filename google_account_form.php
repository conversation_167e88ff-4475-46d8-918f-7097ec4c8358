<?php
/**
 * Google Account Information Form
 *
 * This script provides a form for users to enter their Google account information.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if already logged in
if (isLoggedIn() || isMemberLoggedIn()) {
    redirect(url('index.php'));
    exit;
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Account Information - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-image: url('assets/images/library-background.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .form-container {
            max-width: 500px;
            width: 100%;
            margin: 0 auto;
            animation: fadeIn 0.8s ease-in-out;
        }
        .card {
            border: none;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.8rem 2rem rgba(0, 0, 0, 0.25);
        }
        .card-header {
            background-color: #343a40;
            color: white;
            font-weight: bold;
            padding: 1.2rem;
        }
        .btn-google {
            background-color: #ffffff;
            color: #757575;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-family: 'Roboto', sans-serif;
            font-weight: 500;
            font-size: 16px;
            padding: 12px 16px;
            position: relative;
            text-align: center;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        }
        .btn-google:hover {
            background-color: #f8f9fa;
            box-shadow: 0 2px 4px rgba(0,0,0,0.12);
            border-color: #c6c6c6;
            color: #3c4043;
        }
        .btn-google:active {
            background-color: #f1f3f4;
            box-shadow: 0 1px 2px rgba(0,0,0,0.15);
        }
        .btn-google img {
            vertical-align: middle;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="form-container">
        <div class="card">
            <div class="card-header text-center py-3">
                <h4 class="mb-0"><i class="bi bi-book me-2"></i>Library Management System</h4>
            </div>
            <div class="card-body p-4">
                <h5 class="card-title mb-4 text-center">Enter Your Google Account Information</h5>
                
                <form action="direct_member_login.php" method="get">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" placeholder="Enter your Google email address" required>
                        <div class="form-text">Enter the email address associated with your Google account.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="name" name="name" placeholder="Enter your full name" required>
                        <div class="form-text">Enter your full name as it appears in your Google account.</div>
                    </div>
                    
                    <div class="d-grid gap-2 mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
                        </button>
                        <a href="login.php" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-2"></i>Back to Login
                        </a>
                    </div>
                </form>
                
                <div class="text-center mt-4">
                    <p class="text-muted small">This information will be used to create or find your member account.</p>
                </div>
            </div>
            <div class="card-footer text-center py-3">
                <p class="mb-0">Library Management System &copy; <?php echo date('Y'); ?></p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
