<?php
/**
 * Google Authentication Callback Handler - EMERGENCY FIX
 *
 * This script handles the callback from Google OAuth authentication.
 */

// Emergency redirect to fix access issues
if (isset($_GET['code']) || isset($_GET['state'])) {
    // Clear any existing sessions
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Redirect to emergency access page
    header('Location: emergency_access.php');
    exit;
}

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
} else {
    session_regenerate_id(true);
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Emergency fallback - if Google auth files don't exist, redirect
if (!file_exists('includes/google_auth.php')) {
    header('Location: emergency_access.php');
    exit;
}

require_once 'includes/google_auth.php';

// Check if user is already logged in
if (isLoggedIn()) {
    // Redirect based on role
    if (isAdmin()) {
        redirect(url('admin/dashboard.php'));
    } elseif (isLibrarian()) {
        redirect(url('librarian/dashboard.php'));
    } else {
        redirect(url('index.php'));
    }
} elseif (isMemberLoggedIn()) {
    redirect(url('member_dashboard.php'));
}

// Check if we have an authorization code
if (!isset($_GET['code'])) {
    // No authorization code, could be because no Google account is signed in
    // or because prompt=none failed (user not logged into Google)

    // Check if this is a retry after prompt=none failed
    $is_retry = isset($_GET['retry']) && $_GET['retry'] === '1';

    // Get the Google login URL
    $google_login_url = getGoogleLoginUrl('member_dashboard.php', true);

    if ($is_retry) {
        // This is a retry, use prompt=select_account to show the account selection
        if (strpos($google_login_url, '?') !== false) {
            $google_login_url .= '&prompt=select_account';
        } else {
            $google_login_url .= '?prompt=select_account';
        }

        // Log the retry attempt
        error_log("Google Auth: Retry with prompt=select_account");
    } else {
        // First attempt failed, retry with prompt=select_account
        // Add retry=1 parameter to indicate this is a retry
        $callback_url = url('google_callback.php') . '?retry=1';

        // Log the failure and redirect to retry
        error_log("Google Auth: prompt=none failed, redirecting to retry");

        // Redirect to the callback URL with retry parameter
        header("Location: $callback_url");
        exit;
    }

    // Redirect to Google sign-in page
    header("Location: $google_login_url");
    exit;
}

// Verify state parameter to prevent CSRF attacks
if (!isset($_GET['state']) || !isset($_SESSION['google_auth_state']) || $_GET['state'] !== $_SESSION['google_auth_state']) {
    // Invalid state, potential CSRF attack
    error_log('Google Auth: Invalid state parameter. Potential CSRF attack.');
    redirect(url('google_auth_status.php?status=error&message=Security verification failed. Please try again.'));
}

// Get the authorization code
$auth_code = $_GET['code'];

// Clear the state from session
unset($_SESSION['google_auth_state']);

// Always skip the processing status page for faster authentication
$skip_processing = true;

// Check if this is a persistent login request
$persistent_login = isset($_GET['persistent']) && $_GET['persistent'] === '1';

// Check if this is a streamlined sign-in process
$streamlined_signin = isset($_SESSION['streamlined_google_signin']) && $_SESSION['streamlined_google_signin'];

// We're skipping the processing status page to ensure direct redirection to the member dashboard
// This ensures a smoother user experience

// Get user profile from Google
$user_data = getGoogleUserProfile($auth_code);

if (!$user_data) {
    // Failed to get user profile, redirect to status page with error
    redirect(url('google_auth_status.php?status=error&message=Failed to get user profile from Google. Please try again.'));
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Check if user exists by Google ID
$user = findUserByGoogleId($db, $user_data['google_id']);

if ($user) {
    // User exists, update Google token
    if ($user['type'] === 'staff') {
        // Update staff user
        $query = "UPDATE users SET google_token = :token, google_picture = :picture WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':token', $user_data['token']);
        $stmt->bindParam(':picture', $user_data['picture']);
        $stmt->bindParam(':id', $user['data']['id']);
        $stmt->execute();

        // Set session variables
        $_SESSION['user_id'] = $user['data']['id'];
        $_SESSION['username'] = $user['data']['username'];
        $_SESSION['email'] = $user['data']['email'];
        $_SESSION['role'] = $user['data']['role'];

        // Store Google user info in localStorage for "Continue as" feature
        echo "<script>
            // Store Google user info for 'Continue as' feature
            const googleUser = {
                name: " . json_encode(!empty($user['data']['full_name']) ? $user['data']['full_name'] : $user['data']['username']) . ",
                email: " . json_encode($user['data']['email']) . ",
                picture: " . json_encode($user_data['picture']) . "
            };
            localStorage.setItem('googleUser', JSON.stringify(googleUser));
        </script>";

        // Set persistent login cookie if requested
        if ($persistent_login) {
            // Set a cookie that expires in 30 days
            setcookie('persistent_google', '1', time() + (86400 * 30), '/');

            // Generate a remember token for the user
            $remember_token = bin2hex(random_bytes(32));

            // Update the user's remember token in the database
            $query = "UPDATE users SET remember_token = :remember_token WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':remember_token', $remember_token);
            $stmt->bindParam(':id', $user['data']['id']);
            $stmt->execute();

            // Set a cookie with the remember token
            setcookie('google_remember_token', $remember_token, time() + (86400 * 30), '/');
        }

        // Redirect based on role with success status
        if ($user['data']['role'] === 'admin') {
            // Show success status before redirecting
            redirect(url('google_auth_status.php?status=success&redirect=admin/dashboard.php'));
        } elseif ($user['data']['role'] === 'librarian') {
            // Store full name in session for librarian
            $_SESSION['full_name'] = !empty($user['data']['full_name']) ? $user['data']['full_name'] : $user['data']['username'];
            // Show success status before redirecting
            redirect(url('google_auth_status.php?status=success&redirect=librarian/dashboard.php'));
        } else {
            // Show success status before redirecting
            redirect(url('google_auth_status.php?status=success&redirect=index.php'));
        }
    } else {
        // Update member
        $query = "UPDATE members SET google_token = :token, google_picture = :picture WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':token', $user_data['token']);
        $stmt->bindParam(':picture', $user_data['picture']);
        $stmt->bindParam(':id', $user['data']['id']);
        $stmt->execute();

        // Set session variables
        $_SESSION['member_id'] = $user['data']['id'];
        $_SESSION['member_name'] = $user['data']['first_name'] . ' ' . $user['data']['last_name'];
        $_SESSION['member_email'] = $user['data']['email'];

        // Store Google user info in localStorage for "Continue as" feature
        echo "<script>
            // Store Google user info for 'Continue as' feature
            const googleUser = {
                name: " . json_encode($user['data']['first_name'] . ' ' . $user['data']['last_name']) . ",
                email: " . json_encode($user['data']['email']) . ",
                picture: " . json_encode($user_data['picture']) . "
            };
            localStorage.setItem('googleUser', JSON.stringify(googleUser));
        </script>";

        // Set persistent login cookie if requested
        if ($persistent_login) {
            // Set a cookie that expires in 30 days
            setcookie('persistent_google', '1', time() + 86400 * 30, '/');

            // Generate a remember token for the member
            $remember_token = bin2hex(random_bytes(32));

            // Update the member's remember token in the database
            $query = "UPDATE members SET remember_token = :remember_token WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':remember_token', $remember_token);
            $stmt->bindParam(':id', $user['data']['id']);
            $stmt->execute();

            // Set a cookie with the remember token
            setcookie('google_remember_token', $remember_token, time() + 86400 * 30, '/');
        }

        // Log the redirection attempt
        error_log("Google Auth: Redirecting member to dashboard. Member ID: " . $_SESSION['member_id']);

        // Set a flag to indicate this was a Google login
        $_SESSION['google_login'] = true;

        // Force redirect to member dashboard with a timestamp parameter to avoid caching issues
        header("Location: " . url('member_dashboard.php') . "?login=" . time());
        exit;
    }
} else {
    // User doesn't exist by Google ID, check by email
    $user = findUserByEmail($db, $user_data['email']);

    if ($user) {
        // User exists by email, link Google account
        if ($user['type'] === 'staff') {
            // Link staff user
            $query = "UPDATE users SET google_id = :google_id, google_token = :token, google_picture = :picture WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':google_id', $user_data['google_id']);
            $stmt->bindParam(':token', $user_data['token']);
            $stmt->bindParam(':picture', $user_data['picture']);
            $stmt->bindParam(':id', $user['data']['id']);
            $stmt->execute();

            // Set session variables
            $_SESSION['user_id'] = $user['data']['id'];
            $_SESSION['username'] = $user['data']['username'];
            $_SESSION['email'] = $user['data']['email'];
            $_SESSION['role'] = $user['data']['role'];

            // Store Google user info in localStorage for "Continue as" feature
            echo "<script>
                // Store Google user info for 'Continue as' feature
                const googleUser = {
                    name: " . json_encode(!empty($user['data']['full_name']) ? $user['data']['full_name'] : $user['data']['username']) . ",
                    email: " . json_encode($user['data']['email']) . ",
                    picture: " . json_encode($user_data['picture']) . "
                };
                localStorage.setItem('googleUser', JSON.stringify(googleUser));
            </script>";

            // Redirect based on role with success status
            if ($user['data']['role'] === 'admin') {
                // Set flag for Google account linked welcome notification
                $_SESSION['google_account_linked'] = true;

                // Show linking status before redirecting
                redirect(url('google_auth_status.php?status=success&message=Your Google account has been linked successfully.&redirect=admin/dashboard.php'));
            } elseif ($user['data']['role'] === 'librarian') {
                // Store full name in session for librarian
                $_SESSION['full_name'] = !empty($user['data']['full_name']) ? $user['data']['full_name'] : $user['data']['username'];
                // Set flag for Google account linked welcome notification
                $_SESSION['google_account_linked'] = true;

                // Show linking status before redirecting
                redirect(url('google_auth_status.php?status=success&message=Your Google account has been linked successfully.&redirect=librarian/dashboard.php'));
            } else {
                // Set flag for Google account linked welcome notification
                $_SESSION['google_account_linked'] = true;

                // Show linking status before redirecting
                redirect(url('google_auth_status.php?status=success&message=Your Google account has been linked successfully.&redirect=index.php'));
            }
        } else {
            // Link member
            $query = "UPDATE members SET google_id = :google_id, google_token = :token, google_picture = :picture WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':google_id', $user_data['google_id']);
            $stmt->bindParam(':token', $user_data['token']);
            $stmt->bindParam(':picture', $user_data['picture']);
            $stmt->bindParam(':id', $user['data']['id']);
            $stmt->execute();

            // Set session variables
            $_SESSION['member_id'] = $user['data']['id'];
            $_SESSION['member_name'] = $user['data']['first_name'] . ' ' . $user['data']['last_name'];
            $_SESSION['member_email'] = $user['data']['email'];

            // Store Google user info in localStorage for "Continue as" feature
            echo "<script>
                // Store Google user info for 'Continue as' feature
                const googleUser = {
                    name: " . json_encode($user['data']['first_name'] . ' ' . $user['data']['last_name']) . ",
                    email: " . json_encode($user['data']['email']) . ",
                    picture: " . json_encode($user_data['picture']) . "
                };
                localStorage.setItem('googleUser', JSON.stringify(googleUser));
            </script>";

            // Set flag for Google account linked welcome notification
            $_SESSION['google_account_linked'] = true;

            // Log the redirection attempt
            error_log("Google Auth: Redirecting member to dashboard after linking Google account. Member ID: " . $_SESSION['member_id']);

            // Set a flag to indicate this was a Google login
            $_SESSION['google_login'] = true;

            // Force redirect to member dashboard with a timestamp parameter to avoid caching issues
            header("Location: " . url('member_dashboard.php') . "?login=" . time());
            exit;
        }
    } else {
        // New user, create a member account
        // Generate a random password
        $random_password = bin2hex(random_bytes(8));
        $hashed_password = password_hash($random_password, PASSWORD_DEFAULT);

        // Set membership date to today
        $membership_date = date('Y-m-d');

        // Insert new member
        $query = "INSERT INTO members (first_name, last_name, email, membership_date, membership_status, password, google_id, google_token, google_picture)
                  VALUES (:first_name, :last_name, :email, :membership_date, 'active', :password, :google_id, :token, :picture)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':first_name', $user_data['first_name']);
        $stmt->bindParam(':last_name', $user_data['last_name']);
        $stmt->bindParam(':email', $user_data['email']);
        $stmt->bindParam(':membership_date', $membership_date);
        $stmt->bindParam(':password', $hashed_password);
        $stmt->bindParam(':google_id', $user_data['google_id']);
        $stmt->bindParam(':token', $user_data['token']);
        $stmt->bindParam(':picture', $user_data['picture']);

        if ($stmt->execute()) {
            $member_id = $db->lastInsertId();

            // Set session variables
            $_SESSION['member_id'] = $member_id;
            $_SESSION['member_name'] = $user_data['first_name'] . ' ' . $user_data['last_name'];
            $_SESSION['member_email'] = $user_data['email'];

            // Store Google user info in localStorage for "Continue as" feature
            echo "<script>
                // Store Google user info for 'Continue as' feature
                const googleUser = {
                    name: " . json_encode($user_data['first_name'] . ' ' . $user_data['last_name']) . ",
                    email: " . json_encode($user_data['email']) . ",
                    picture: " . json_encode($user_data['picture']) . "
                };
                localStorage.setItem('googleUser', JSON.stringify(googleUser));
            </script>";

            // Set flag for first-time Google login welcome notification
            $_SESSION['new_google_user'] = true;

            // Log the redirection attempt
            error_log("Google Auth: Redirecting new member to dashboard. Member ID: " . $_SESSION['member_id']);

            // Set a flag to indicate this was a Google login
            $_SESSION['google_login'] = true;

            // Force redirect to member dashboard with a timestamp parameter to avoid caching issues
            header("Location: " . url('member_dashboard.php') . "?login=" . time());
            exit;
        } else {
            // Failed to create member, redirect to login page
            $_SESSION['error'] = 'Failed to create member account. Please try again.';
            redirect(url('login.php'));
        }
    }
}

// If we get here, something unexpected happened
// Log the error
error_log("Google Auth: Unexpected error. User data: " . json_encode($user_data));

// Redirect to login page with error
$_SESSION['error'] = 'An unexpected error occurred during Google sign-in. Please try again.';
redirect(url('login.php'));
