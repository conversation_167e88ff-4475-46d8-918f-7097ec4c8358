<?php
/**
 * Email Helper Functions
 */

function getSetting($group, $key, $default = "") {
    global $db;
    
    if (!$db) {
        return $default;
    }
    
    try {
        $query = "SELECT setting_value FROM settings WHERE setting_group = ? AND setting_key = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$group, $key]);
        $result = $stmt->fetch();
        
        return $result ? $result["setting_value"] : $default;
    } catch (Exception $e) {
        return $default;
    }
}

function updateSetting($group, $key, $value) {
    global $db;
    
    if (!$db) {
        return false;
    }
    
    try {
        $query = "INSERT INTO settings (setting_group, setting_key, setting_value) 
                  VALUES (?, ?, ?) 
                  ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
        $stmt = $db->prepare($query);
        return $stmt->execute([$group, $key, $value]);
    } catch (Exception $e) {
        return false;
    }
}

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, "UTF-8");
}
?>