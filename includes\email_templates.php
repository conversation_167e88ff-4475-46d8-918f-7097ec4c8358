<?php
/**
 * Email Templates for Library Management System
 * Contains functions to generate email content for various notifications
 */

/**
 * Generate due date reminder email content
 * 
 * @param array $member Member information
 * @param array $loan Loan information
 * @param array $book Book information
 * @return array Email subject and body
 */
function getDueDateReminderEmail($member, $loan, $book) {
    $subject = "Reminder: Book Due Soon - " . $book['title'];
    
    $body = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #007bff; color: white; padding: 10px 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .book-details { background-color: #fff; padding: 15px; border-left: 4px solid #007bff; margin: 15px 0; }
            .footer { font-size: 12px; text-align: center; margin-top: 20px; color: #666; }
            .button { display: inline-block; background-color: #007bff; color: white; padding: 10px 20px; 
                     text-decoration: none; border-radius: 4px; margin-top: 15px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>Library Book Due Reminder</h2>
            </div>
            <div class='content'>
                <p>Dear " . htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) . ",</p>
                
                <p>This is a friendly reminder that the following book is due to be returned soon:</p>
                
                <div class='book-details'>
                    <h3>" . htmlspecialchars($book['title']) . "</h3>
                    <p><strong>Author:</strong> " . htmlspecialchars($book['author']) . "</p>
                    <p><strong>ISBN:</strong> " . htmlspecialchars($book['isbn']) . "</p>
                    <p><strong>Due Date:</strong> " . date('F j, Y', strtotime($loan['due_date'])) . "</p>
                </div>
                
                <p>Please return the book by the due date to avoid any late fees.</p>
                
                <p>If you need more time with the book, please contact the library to request a renewal.</p>
                
                <a href='#' class='button'>Renew Book</a>
                
                <p>Thank you for using our library services!</p>
            </div>
            <div class='footer'>
                <p>This is an automated message from the Library Management System. Please do not reply to this email.</p>
                <p>&copy; " . date('Y') . " Library Management System</p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    return [
        'subject' => $subject,
        'body' => $body
    ];
}

/**
 * Generate overdue book notification email content
 * 
 * @param array $member Member information
 * @param array $loan Loan information
 * @param array $book Book information
 * @param float $fine Fine amount (if any)
 * @return array Email subject and body
 */
function getOverdueBookEmail($member, $loan, $book, $fine = 0) {
    $subject = "Overdue Book Notice - " . $book['title'];
    
    $body = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #dc3545; color: white; padding: 10px 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .book-details { background-color: #fff; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0; }
            .fine-notice { background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
            .footer { font-size: 12px; text-align: center; margin-top: 20px; color: #666; }
            .button { display: inline-block; background-color: #dc3545; color: white; padding: 10px 20px; 
                     text-decoration: none; border-radius: 4px; margin-top: 15px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>Overdue Book Notice</h2>
            </div>
            <div class='content'>
                <p>Dear " . htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) . ",</p>
                
                <p>Our records indicate that the following book is overdue:</p>
                
                <div class='book-details'>
                    <h3>" . htmlspecialchars($book['title']) . "</h3>
                    <p><strong>Author:</strong> " . htmlspecialchars($book['author']) . "</p>
                    <p><strong>ISBN:</strong> " . htmlspecialchars($book['isbn']) . "</p>
                    <p><strong>Due Date:</strong> " . date('F j, Y', strtotime($loan['due_date'])) . "</p>
                    <p><strong>Days Overdue:</strong> " . floor((time() - strtotime($loan['due_date'])) / (60 * 60 * 24)) . "</p>
                </div>";
    
    if ($fine > 0) {
        $body .= "
                <div class='fine-notice'>
                    <h3>Fine Information</h3>
                    <p>A fine of $" . number_format($fine, 2) . " has been applied to your account.</p>
                    <p>Please return the book and pay the fine as soon as possible.</p>
                </div>";
    }
    
    $body .= "
                <p>Please return this book to the library as soon as possible to avoid additional fees.</p>
                
                <a href='#' class='button'>View My Account</a>
                
                <p>If you have already returned this book, please disregard this notice and we apologize for any inconvenience.</p>
            </div>
            <div class='footer'>
                <p>This is an automated message from the Library Management System. Please do not reply to this email.</p>
                <p>&copy; " . date('Y') . " Library Management System</p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    return [
        'subject' => $subject,
        'body' => $body
    ];
}

/**
 * Generate book reservation ready email content
 * 
 * @param array $member Member information
 * @param array $reservation Reservation information
 * @param array $book Book information
 * @return array Email subject and body
 */
function getBookReservationReadyEmail($member, $reservation, $book) {
    $subject = "Book Reservation Ready for Pickup - " . $book['title'];
    
    $body = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #28a745; color: white; padding: 10px 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .book-details { background-color: #fff; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0; }
            .pickup-notice { background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0; }
            .footer { font-size: 12px; text-align: center; margin-top: 20px; color: #666; }
            .button { display: inline-block; background-color: #28a745; color: white; padding: 10px 20px; 
                     text-decoration: none; border-radius: 4px; margin-top: 15px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>Book Reservation Ready</h2>
            </div>
            <div class='content'>
                <p>Dear " . htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) . ",</p>
                
                <p>Good news! The book you reserved is now available for pickup:</p>
                
                <div class='book-details'>
                    <h3>" . htmlspecialchars($book['title']) . "</h3>
                    <p><strong>Author:</strong> " . htmlspecialchars($book['author']) . "</p>
                    <p><strong>ISBN:</strong> " . htmlspecialchars($book['isbn']) . "</p>
                </div>
                
                <div class='pickup-notice'>
                    <h3>Pickup Information</h3>
                    <p><strong>Reservation ID:</strong> " . $reservation['id'] . "</p>
                    <p><strong>Available Until:</strong> " . date('F j, Y', strtotime($reservation['expiry_date'])) . "</p>
                    <p>Please bring your library card or ID when you come to pick up the book.</p>
                </div>
                
                <p>If you no longer need this book, please cancel your reservation so that other members can borrow it.</p>
                
                <a href='#' class='button'>View My Reservations</a>
                
                <p>Thank you for using our library services!</p>
            </div>
            <div class='footer'>
                <p>This is an automated message from the Library Management System. Please do not reply to this email.</p>
                <p>&copy; " . date('Y') . " Library Management System</p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    return [
        'subject' => $subject,
        'body' => $body
    ];
}
