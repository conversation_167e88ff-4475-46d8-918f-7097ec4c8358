<?php
/**
 * Google OAuth Helper Functions
 * 
 * This file contains helper functions for Google OAuth authentication
 * without requiring external libraries.
 */

require_once __DIR__ . '/../config/google_oauth.php';

/**
 * Generate Google OAuth authorization URL
 */
function getGoogleAuthUrl($state = null) {
    if (!isGoogleOAuthConfigured()) {
        return false;
    }
    
    if ($state === null) {
        $state = bin2hex(random_bytes(16));
        $_SESSION['google_oauth_state'] = $state;
    }
    
    $params = [
        'client_id' => GOOGLE_CLIENT_ID,
        'redirect_uri' => GOOGLE_REDIRECT_URI,
        'scope' => implode(' ', GOOGLE_SCOPES),
        'response_type' => 'code',
        'state' => $state,
        'access_type' => 'online',
        'prompt' => 'select_account'
    ];
    
    return GOOGLE_AUTH_URL . '?' . http_build_query($params);
}

/**
 * Exchange authorization code for access token
 */
function getGoogleAccessToken($auth_code) {
    if (!isGoogleOAuthConfigured()) {
        return false;
    }
    
    $post_data = [
        'client_id' => GOOGLE_CLIENT_ID,
        'client_secret' => GOOGLE_CLIENT_SECRET,
        'redirect_uri' => GOOGLE_REDIRECT_URI,
        'grant_type' => 'authorization_code',
        'code' => $auth_code
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, GOOGLE_TOKEN_URL);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded'
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        $token_data = json_decode($response, true);
        return $token_data;
    }
    
    return false;
}

/**
 * Get user information from Google using access token
 */
function getGoogleUserInfo($access_token) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, GOOGLE_USER_INFO_URL . '?access_token=' . $access_token);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $access_token
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        $user_data = json_decode($response, true);
        return $user_data;
    }
    
    return false;
}

/**
 * Complete Google OAuth flow
 */
function processGoogleCallback($auth_code, $state) {
    // Verify state parameter
    if (!isset($_SESSION['google_oauth_state']) || $_SESSION['google_oauth_state'] !== $state) {
        return ['success' => false, 'error' => 'Invalid state parameter'];
    }
    
    // Clear state from session
    unset($_SESSION['google_oauth_state']);
    
    // Exchange code for access token
    $token_data = getGoogleAccessToken($auth_code);
    if (!$token_data || !isset($token_data['access_token'])) {
        return ['success' => false, 'error' => 'Failed to get access token'];
    }
    
    // Get user information
    $user_info = getGoogleUserInfo($token_data['access_token']);
    if (!$user_info) {
        return ['success' => false, 'error' => 'Failed to get user information'];
    }
    
    return [
        'success' => true,
        'user_data' => [
            'id' => $user_info['id'],
            'email' => $user_info['email'],
            'name' => $user_info['name'],
            'given_name' => $user_info['given_name'] ?? '',
            'family_name' => $user_info['family_name'] ?? '',
            'picture' => $user_info['picture'] ?? '',
            'verified_email' => $user_info['verified_email'] ?? false
        ]
    ];
}

/**
 * Find or create member from Google user data
 */
function findOrCreateMemberFromGoogle($db, $google_user_data) {
    // First, try to find existing member by Google ID
    $query = "SELECT * FROM members WHERE google_id = :google_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':google_id', $google_user_data['id']);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        return ['success' => true, 'data' => $stmt->fetch(), 'is_new' => false];
    }
    
    // Try to find by email
    $query = "SELECT * FROM members WHERE email = :email";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':email', $google_user_data['email']);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        // Update existing member with Google ID
        $member = $stmt->fetch();
        $update_query = "UPDATE members SET google_id = :google_id WHERE id = :id";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':google_id', $google_user_data['id']);
        $update_stmt->bindParam(':id', $member['id']);
        $update_stmt->execute();
        
        return ['success' => true, 'data' => $member, 'is_new' => false];
    }
    
    // Create new member
    $first_name = $google_user_data['given_name'] ?: 'Google';
    $last_name = $google_user_data['family_name'] ?: 'User';
    $email = $google_user_data['email'];
    $membership_date = date('Y-m-d');
    
    $insert_query = "INSERT INTO members (first_name, last_name, email, membership_date, membership_status, google_id, created_at) 
                     VALUES (:first_name, :last_name, :email, :membership_date, 'active', :google_id, NOW())";
    $insert_stmt = $db->prepare($insert_query);
    $insert_stmt->bindParam(':first_name', $first_name);
    $insert_stmt->bindParam(':last_name', $last_name);
    $insert_stmt->bindParam(':email', $email);
    $insert_stmt->bindParam(':membership_date', $membership_date);
    $insert_stmt->bindParam(':google_id', $google_user_data['id']);
    
    if ($insert_stmt->execute()) {
        $member_id = $db->lastInsertId();
        $new_member = [
            'id' => $member_id,
            'first_name' => $first_name,
            'last_name' => $last_name,
            'email' => $email,
            'membership_date' => $membership_date,
            'membership_status' => 'active',
            'google_id' => $google_user_data['id']
        ];
        
        return ['success' => true, 'data' => $new_member, 'is_new' => true];
    }
    
    return ['success' => false, 'error' => 'Failed to create member'];
}
