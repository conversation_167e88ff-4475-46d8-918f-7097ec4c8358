<?php
// Include config file if not already included
if (!defined('BASE_URL')) {
    require_once __DIR__ . '/../config/config.php';
}

// Include functions if not already included
if (!function_exists('isAdmin')) {
    require_once __DIR__ . '/functions.php';
}
?>
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>" href="<?php echo isAdmin() ? url('admin/dashboard.php') : url('index.php'); ?>">
                    <i class="bi bi-speedometer2 me-2"></i>
                    Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/books/') !== false ? 'active' : ''; ?>" href="<?php echo url('books/index.php'); ?>">
                    <i class="bi bi-book me-2"></i>
                    Books
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/members/') !== false ? 'active' : ''; ?>" href="<?php echo url('members/index.php'); ?>">
                    <i class="bi bi-people me-2"></i>
                    Members
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/loans/') !== false ? 'active' : ''; ?>" href="<?php echo url('loans/index.php'); ?>">
                    <i class="bi bi-journal-arrow-up me-2"></i>
                    Book Loans
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/reports/') !== false ? 'active' : ''; ?>" href="<?php echo url('reports/index.php'); ?>">
                    <i class="bi bi-file-earmark-bar-graph me-2"></i>
                    Reports
                </a>
            </li>
        </ul>

        <?php if (isMemberLoggedIn()): ?>
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>Member Services</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/member/profile.php') !== false ? 'active' : ''; ?>" href="<?php echo url('member/profile.php'); ?>">
                    <i class="bi bi-person-circle me-2"></i>
                    My Profile
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/member/my_loans.php') !== false ? 'active' : ''; ?>" href="<?php echo url('member/my_loans.php'); ?>">
                    <i class="bi bi-journal-bookmark me-2"></i>
                    My Loans & Reservations
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/member/self_checkout.php') !== false ? 'active' : ''; ?>" href="<?php echo url('member/self_checkout.php'); ?>">
                    <i class="bi bi-upc-scan me-2"></i>
                    Self-Checkout
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/member/member_card.php') !== false ? 'active' : ''; ?>" href="<?php echo url('member/member_card.php'); ?>">
                    <i class="bi bi-credit-card-2-front me-2"></i>
                    Member Card
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/member/recommendations.php') !== false ? 'active' : ''; ?>" href="<?php echo url('member/recommendations.php'); ?>">
                    <i class="bi bi-lightbulb me-2"></i>
                    Book Recommendations
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/members/my_fines.php') !== false ? 'active' : ''; ?>" href="<?php echo url('members/my_fines.php'); ?>">
                    <i class="bi bi-currency-dollar me-2"></i>
                    My Fines & Payments
                </a>
            </li>
        </ul>
        <?php endif; ?>

        <?php if (isStaffWithFinancialAccess()): ?>
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>Financial Management</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/admin/payment_processing.php') !== false ? 'active' : ''; ?>" href="<?php echo url('admin/payment_processing.php'); ?>">
                    <i class="bi bi-credit-card me-2"></i>
                    Payment Processing
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/admin/payment_reports.php') !== false ? 'active' : ''; ?>" href="<?php echo url('admin/payment_reports.php'); ?>">
                    <i class="bi bi-file-earmark-bar-graph me-2"></i>
                    Payment Reports
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/admin/financial_management.php') !== false ? 'active' : ''; ?>" href="<?php echo url('admin/financial_management.php'); ?>">
                    <i class="bi bi-currency-dollar me-2"></i>
                    Financial Overview
                </a>
            </li>
        </ul>
        <?php endif; ?>

        <?php if (isAdmin()): ?>

        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>Administration</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/admin/users') !== false ? 'active' : ''; ?>" href="<?php echo url('admin/users.php'); ?>">
                    <i class="bi bi-person-gear me-2"></i>
                    Manage Users
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/admin/settings') !== false ? 'active' : ''; ?>" href="<?php echo url('admin/settings.php'); ?>">
                    <i class="bi bi-gear me-2"></i>
                    Settings
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/admin/email_settings.php') !== false ? 'active' : ''; ?>" href="<?php echo url('admin/email_settings.php'); ?>">
                    <i class="bi bi-envelope me-2"></i>
                    Email Settings
                </a>
            </li>
        </ul>
        <?php endif; ?>
    </div>
</nav>
