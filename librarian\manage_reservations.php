<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is librarian
if (!isLoggedIn() || !isLibrarian()) {
    redirect('../login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Initialize variables
$reservation_id = '';
$errors = [];
$success = false;

// Get all reservations
$query = "SELECT br.*, b.title as book_title, b.author as book_author, b.isbn,
          m.first_name, m.last_name, m.email
          FROM book_reservations br
          JOIN books b ON br.book_id = b.id
          JOIN members m ON br.member_id = m.id
          ORDER BY br.reservation_date DESC";
$stmt = $db->prepare($query);
$stmt->execute();
$reservations = $stmt->fetchAll();

// Process form submission for updating reservation status
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $reservation_id = trim($_POST['reservation_id'] ?? '');
    $new_status = trim($_POST['new_status'] ?? '');
    
    // Validate input
    if (empty($reservation_id)) {
        $errors[] = 'Reservation ID is required';
    }
    
    if (empty($new_status)) {
        $errors[] = 'New status is required';
    }
    
    // If no errors, update the reservation
    if (empty($errors)) {
        try {
            // Update reservation status
            $query = "UPDATE book_reservations 
                      SET status = :status
                      WHERE id = :reservation_id";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(':status', $new_status);
            $stmt->bindParam(':reservation_id', $reservation_id);
            
            if ($stmt->execute()) {
                // Set success message
                setMessage('Reservation status updated successfully', 'success');
                $success = true;
                
                // Redirect to avoid form resubmission
                redirect('manage_reservations.php');
            } else {
                $errors[] = 'Error updating reservation status';
            }
            
        } catch (PDOException $e) {
            $errors[] = 'Error updating reservation: ' . $e->getMessage();
        }
    }
}

// Process form submission for deleting reservation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_reservation'])) {
    $reservation_id = trim($_POST['reservation_id'] ?? '');
    
    // Validate input
    if (empty($reservation_id)) {
        $errors[] = 'Reservation ID is required';
    }
    
    // If no errors, delete the reservation
    if (empty($errors)) {
        try {
            // Delete reservation
            $query = "DELETE FROM book_reservations WHERE id = :reservation_id";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(':reservation_id', $reservation_id);
            
            if ($stmt->execute()) {
                // Set success message
                setMessage('Reservation deleted successfully', 'success');
                $success = true;
                
                // Redirect to avoid form resubmission
                redirect('manage_reservations.php');
            } else {
                $errors[] = 'Error deleting reservation';
            }
            
        } catch (PDOException $e) {
            $errors[] = 'Error deleting reservation: ' . $e->getMessage();
        }
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Reservations - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .content-container {
            max-width: 1000px;
            margin: 20px auto;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="issue_book.php">Issue Book</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="return_book.php">Return Book</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="manage_reservations.php">Manage Reservations</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="../logout.php" class="btn btn-outline-light btn-sm">
                        <i class="bi bi-box-arrow-right me-1"></i>Sign out
                    </a>
                </div>
            </div>
        </div>
    </nav>
    
    <div class="content-container">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Manage Reservations</h5>
            </div>
            <div class="card-body">
                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo h($_SESSION['message_type']); ?> alert-dismissible fade show" role="alert">
                        <?php echo h($_SESSION['message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>
                
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo h($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <?php if (count($reservations) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Book</th>
                                    <th>Member</th>
                                    <th>Reservation Date</th>
                                    <th>Expiry Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reservations as $reservation): ?>
                                <tr>
                                    <td><?php echo h($reservation['id']); ?></td>
                                    <td>
                                        <strong><?php echo h($reservation['book_title']); ?></strong><br>
                                        <small><?php echo h($reservation['book_author']); ?></small>
                                    </td>
                                    <td>
                                        <?php echo h($reservation['first_name'] . ' ' . $reservation['last_name']); ?><br>
                                        <small><?php echo h($reservation['email']); ?></small>
                                    </td>
                                    <td><?php echo formatDate($reservation['reservation_date']); ?></td>
                                    <td><?php echo formatDate($reservation['expiry_date']); ?></td>
                                    <td>
                                        <?php if ($reservation['status'] === 'pending'): ?>
                                            <span class="badge bg-secondary">Pending</span>
                                        <?php elseif ($reservation['status'] === 'ready'): ?>
                                            <span class="badge bg-success">Ready for Pickup</span>
                                        <?php elseif ($reservation['status'] === 'cancelled'): ?>
                                            <span class="badge bg-danger">Cancelled</span>
                                        <?php elseif ($reservation['status'] === 'completed'): ?>
                                            <span class="badge bg-primary">Completed</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                Actions
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <form method="post" action="<?php echo h($_SERVER['PHP_SELF']); ?>">
                                                        <input type="hidden" name="reservation_id" value="<?php echo h($reservation['id']); ?>">
                                                        <input type="hidden" name="new_status" value="ready">
                                                        <button type="submit" name="update_status" class="dropdown-item">Mark as Ready</button>
                                                    </form>
                                                </li>
                                                <li>
                                                    <form method="post" action="<?php echo h($_SERVER['PHP_SELF']); ?>">
                                                        <input type="hidden" name="reservation_id" value="<?php echo h($reservation['id']); ?>">
                                                        <input type="hidden" name="new_status" value="completed">
                                                        <button type="submit" name="update_status" class="dropdown-item">Mark as Completed</button>
                                                    </form>
                                                </li>
                                                <li>
                                                    <form method="post" action="<?php echo h($_SERVER['PHP_SELF']); ?>">
                                                        <input type="hidden" name="reservation_id" value="<?php echo h($reservation['id']); ?>">
                                                        <input type="hidden" name="new_status" value="cancelled">
                                                        <button type="submit" name="update_status" class="dropdown-item">Mark as Cancelled</button>
                                                    </form>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <form method="post" action="<?php echo h($_SERVER['PHP_SELF']); ?>" onsubmit="return confirm('Are you sure you want to delete this reservation?');">
                                                        <input type="hidden" name="reservation_id" value="<?php echo h($reservation['id']); ?>">
                                                        <button type="submit" name="delete_reservation" class="dropdown-item text-danger">Delete Reservation</button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <p class="mb-0">No reservations found.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
