<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Initialize variables
$book_id = $member_id = '';
$issue_date = date('Y-m-d');
$due_date = date('Y-m-d', strtotime('+14 days')); // Default due date is 2 weeks from today
$errors = [];

// Get all available books
$query = "SELECT id, title, author FROM books WHERE available_quantity > 0 ORDER BY title";
$stmt = $db->prepare($query);
$stmt->execute();
$available_books = $stmt->fetchAll();

// Get all active members
$query = "SELECT id, first_name, last_name, email FROM members WHERE membership_status = 'active' ORDER BY last_name, first_name";
$stmt = $db->prepare($query);
$stmt->execute();
$active_members = $stmt->fetchAll();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $book_id = sanitize($_POST['book_id']);
    $member_id = sanitize($_POST['member_id']);
    $issue_date = sanitize($_POST['issue_date']);
    $due_date = sanitize($_POST['due_date']);
    
    // Validate input
    if (empty($book_id)) {
        $errors[] = 'Please select a book';
    }
    
    if (empty($member_id)) {
        $errors[] = 'Please select a member';
    }
    
    if (empty($issue_date)) {
        $errors[] = 'Issue date is required';
    }
    
    if (empty($due_date)) {
        $errors[] = 'Due date is required';
    }
    
    // Check if book is available
    if (!empty($book_id)) {
        $query = "SELECT available_quantity FROM books WHERE id = :book_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':book_id', $book_id);
        $stmt->execute();
        $book = $stmt->fetch();
        
        if ($book['available_quantity'] <= 0) {
            $errors[] = 'Selected book is not available for loan';
        }
    }
    
    // Check if member has any overdue books
    if (!empty($member_id)) {
        $query = "SELECT COUNT(*) as overdue FROM book_loans 
                  WHERE member_id = :member_id AND status = 'overdue'";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->execute();
        $overdue_count = $stmt->fetch()['overdue'];
        
        if ($overdue_count > 0) {
            $errors[] = 'Member has overdue books. Please return them before issuing new books.';
        }
    }
    
    // If no errors, issue book
    if (empty($errors)) {
        // Begin transaction
        $db->beginTransaction();
        
        try {
            // Insert loan record
            $query = "INSERT INTO book_loans (book_id, member_id, issue_date, due_date, status) 
                      VALUES (:book_id, :member_id, :issue_date, :due_date, 'borrowed')";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':book_id', $book_id);
            $stmt->bindParam(':member_id', $member_id);
            $stmt->bindParam(':issue_date', $issue_date);
            $stmt->bindParam(':due_date', $due_date);
            $stmt->execute();
            
            // Update book available quantity
            $query = "UPDATE books SET available_quantity = available_quantity - 1 
                      WHERE id = :book_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':book_id', $book_id);
            $stmt->execute();
            
            // Commit transaction
            $db->commit();
            
            setMessage('Book issued successfully', 'success');
            redirect('index.php');
        } catch (Exception $e) {
            // Rollback transaction on error
            $db->rollBack();
            $errors[] = 'Failed to issue book: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Issue Book - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Issue Book</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="index.php" class="btn btn-sm btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Loans
                        </a>
                    </div>
                </div>
                
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <?php if (empty($available_books)): ?>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        No books are available for loan at this time.
                    </div>
                <?php endif; ?>
                
                <?php if (empty($active_members)): ?>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        No active members found. Please add members before issuing books.
                    </div>
                <?php endif; ?>
                
                <div class="card">
                    <div class="card-body">
                        <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" method="post">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="book_id" class="form-label">Book <span class="text-danger">*</span></label>
                                    <select class="form-select" id="book_id" name="book_id" required <?php echo empty($available_books) ? 'disabled' : ''; ?>>
                                        <option value="">Select Book</option>
                                        <?php foreach ($available_books as $book): ?>
                                            <option value="<?php echo $book['id']; ?>" <?php echo $book_id == $book['id'] ? 'selected' : ''; ?>>
                                                <?php echo $book['title'] . ' by ' . $book['author']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="member_id" class="form-label">Member <span class="text-danger">*</span></label>
                                    <select class="form-select" id="member_id" name="member_id" required <?php echo empty($active_members) ? 'disabled' : ''; ?>>
                                        <option value="">Select Member</option>
                                        <?php foreach ($active_members as $member): ?>
                                            <option value="<?php echo $member['id']; ?>" <?php echo $member_id == $member['id'] ? 'selected' : ''; ?>>
                                                <?php echo $member['first_name'] . ' ' . $member['last_name'] . ' (' . $member['email'] . ')'; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="issue_date" class="form-label">Issue Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="issue_date" name="issue_date" value="<?php echo $issue_date; ?>" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="due_date" class="form-label">Due Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="due_date" name="due_date" value="<?php echo $due_date; ?>" required>
                                </div>
                                
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary" <?php echo (empty($available_books) || empty($active_members)) ? 'disabled' : ''; ?>>
                                        Issue Book
                                    </button>
                                    <a href="index.php" class="btn btn-secondary">Cancel</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
</body>
</html>
