<?php
/**
 * Complete Logout Test Page
 * This page helps test and verify all logout functionality
 */

session_start();

// Include required files with error handling
try {
    require_once 'config/database.php';
    require_once 'config/config.php';
    require_once 'includes/functions.php';
    $includes_loaded = true;
} catch (Exception $e) {
    $includes_loaded = false;
    $include_error = $e->getMessage();
}

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Complete Logout Test - LMS</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css'>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: white; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
    </style>
</head>
<body>";

echo "<div class='container'>";
echo "<h1 class='mb-4'><i class='bi bi-box-arrow-right'></i> Complete Logout Test</h1>";

// System Status
echo "<div class='test-section'>";
echo "<h3>System Status</h3>";

if ($includes_loaded) {
    echo "<p class='success'><i class='bi bi-check-circle'></i> All includes loaded successfully</p>";
} else {
    echo "<p class='error'><i class='bi bi-x-circle'></i> Include error: $include_error</p>";
}

echo "</div>";

// Current Session Status
echo "<div class='test-section'>";
echo "<h3>Current Session Status</h3>";

echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";

if ($includes_loaded && function_exists('isLoggedIn')) {
    if (isLoggedIn()) {
        echo "<p class='success'><i class='bi bi-person-check'></i> Admin/Staff logged in</p>";
        echo "<ul>";
        echo "<li><strong>User ID:</strong> " . ($_SESSION['user_id'] ?? 'Not set') . "</li>";
        echo "<li><strong>Username:</strong> " . ($_SESSION['username'] ?? 'Not set') . "</li>";
        echo "<li><strong>Role:</strong> " . ($_SESSION['role'] ?? 'Not set') . "</li>";
        echo "</ul>";
    } elseif (function_exists('isMemberLoggedIn') && isMemberLoggedIn()) {
        echo "<p class='success'><i class='bi bi-person-check'></i> Member logged in</p>";
        echo "<ul>";
        echo "<li><strong>Member ID:</strong> " . ($_SESSION['member_id'] ?? 'Not set') . "</li>";
        echo "<li><strong>Member Name:</strong> " . ($_SESSION['member_name'] ?? 'Not set') . "</li>";
        echo "</ul>";
    } else {
        echo "<p class='info'><i class='bi bi-person-x'></i> No one is logged in</p>";
    }
} else {
    // Manual session check
    if (isset($_SESSION['user_id'])) {
        echo "<p class='success'><i class='bi bi-person-check'></i> Admin/Staff session detected</p>";
        echo "<ul>";
        echo "<li><strong>User ID:</strong> " . $_SESSION['user_id'] . "</li>";
        echo "<li><strong>Username:</strong> " . ($_SESSION['username'] ?? 'Not set') . "</li>";
        echo "<li><strong>Role:</strong> " . ($_SESSION['role'] ?? 'Not set') . "</li>";
        echo "</ul>";
    } elseif (isset($_SESSION['member_id'])) {
        echo "<p class='success'><i class='bi bi-person-check'></i> Member session detected</p>";
        echo "<ul>";
        echo "<li><strong>Member ID:</strong> " . $_SESSION['member_id'] . "</li>";
        echo "<li><strong>Member Name:</strong> " . ($_SESSION['member_name'] ?? 'Not set') . "</li>";
        echo "</ul>";
    } else {
        echo "<p class='info'><i class='bi bi-person-x'></i> No session detected</p>";
    }
}

echo "</div>";

// Logout File Check
echo "<div class='test-section'>";
echo "<h3>Logout Files Check</h3>";

$logout_files = [
    'logout.php' => 'Main logout file',
    'logout_fix.php' => 'Fixed logout file',
    'auth/logout.php' => 'Auth logout file',
    'logout_success.php' => 'Logout success page',
    'member_logout.php' => 'Member logout file'
];

foreach ($logout_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='success'><i class='bi bi-check-circle'></i> $description exists: $file</p>";
    } else {
        echo "<p class='error'><i class='bi bi-x-circle'></i> $description missing: $file</p>";
    }
}

echo "</div>";

// Test Logout Links
echo "<div class='test-section'>";
echo "<h3>Test Logout Links</h3>";

echo "<div class='alert alert-warning'>";
echo "<i class='bi bi-exclamation-triangle'></i> <strong>Warning:</strong> Clicking these links will log you out!";
echo "</div>";

echo "<div class='row'>";

echo "<div class='col-md-6'>";
echo "<h5>Working Logout Links</h5>";
echo "<a href='logout_fix.php' class='btn btn-danger mb-2 me-2'><i class='bi bi-box-arrow-right'></i> Fixed Logout</a><br>";
echo "<a href='logout.php' class='btn btn-warning mb-2 me-2'><i class='bi bi-box-arrow-right'></i> Main Logout</a><br>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h5>Other Logout Options</h5>";
echo "<a href='auth/logout.php' class='btn btn-info mb-2 me-2'><i class='bi bi-box-arrow-right'></i> Auth Logout</a><br>";
echo "<a href='member_logout.php' class='btn btn-secondary mb-2 me-2'><i class='bi bi-box-arrow-right'></i> Member Logout</a><br>";
echo "</div>";

echo "</div>";
echo "</div>";

// Quick Login for Testing
echo "<div class='test-section'>";
echo "<h3>Quick Login for Testing</h3>";

if (!isset($_SESSION['user_id']) && !isset($_SESSION['member_id'])) {
    echo "<p class='info'>You are not logged in. Use these links to login and test logout:</p>";
    echo "<a href='login.php' class='btn btn-success me-2'><i class='bi bi-box-arrow-in-right'></i> Login Page</a>";
    echo "<a href='quick_login.php' class='btn btn-primary me-2'><i class='bi bi-lightning'></i> Quick Admin Login</a>";
} else {
    echo "<p class='success'>You are logged in. You can test the logout functionality above.</p>";
}

echo "</div>";

// Navigation
echo "<div class='test-section'>";
echo "<h3>Navigation</h3>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>Main Pages</h5>";
echo "<a href='access.php' class='btn btn-outline-primary mb-2 me-2'><i class='bi bi-house'></i> Access Portal</a><br>";
echo "<a href='index.php' class='btn btn-outline-secondary mb-2 me-2'><i class='bi bi-house'></i> Home</a><br>";
echo "<a href='home.php' class='btn btn-outline-info mb-2 me-2'><i class='bi bi-house-door'></i> Public Home</a><br>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h5>Admin Pages</h5>";
echo "<a href='admin/dashboard.php' class='btn btn-outline-warning mb-2 me-2'><i class='bi bi-speedometer2'></i> Admin Dashboard</a><br>";
echo "<a href='books/index.php' class='btn btn-outline-success mb-2 me-2'><i class='bi bi-book'></i> Manage Books</a><br>";
echo "<a href='members/index.php' class='btn btn-outline-danger mb-2 me-2'><i class='bi bi-people'></i> Manage Members</a><br>";
echo "</div>";
echo "</div>";

echo "</div>";

// Debug Information
echo "<div class='test-section'>";
echo "<h3>Debug Information</h3>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>Server Info</h5>";
echo "<p><strong>Script Name:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";
echo "<p><strong>Request URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";
echo "<p><strong>HTTP Host:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "</p>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h5>Session Info</h5>";
echo "<p><strong>Session Status:</strong> " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</p>";
echo "<p><strong>Session Name:</strong> " . session_name() . "</p>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Session Variables:</strong> " . count($_SESSION) . "</p>";
echo "</div>";
echo "</div>";

echo "</div>";

echo "</div>"; // container
echo "</body></html>";
?>
