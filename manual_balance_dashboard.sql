-- Manual Dashboard Balance Script
-- Run these SQL commands one by one in phpMyAdmin or your database tool

-- Step 1: Check current statistics
SELECT 'BEFORE BALANCE' as status;
SELECT 
    'Members' as type, COUNT(*) as count FROM members
UNION ALL
SELECT 
    'Active Loans' as type, COUNT(*) as count FROM book_loans WHERE status = 'borrowed'
UNION ALL
SELECT 
    'Overdue Loans' as type, COUNT(*) as count FROM book_loans WHERE status = 'overdue'
UNION ALL
SELECT 
    'Total Loans' as type, COUNT(*) as count FROM book_loans;

-- Step 2: Delete excess members (keep only first 1200 by ID)
DELETE FROM members 
WHERE id NOT IN (
    SELECT id FROM (
        SELECT id FROM members ORDER BY id LIMIT 1200
    ) AS temp
);

-- Step 3: Delete orphaned loans (loans without valid members)
DELETE FROM book_loans 
WHERE member_id NOT IN (SELECT id FROM members);

-- Step 4: Convert most active loans to returned status (keep only 300 active)
UPDATE book_loans 
SET status = 'returned', 
    return_date = DATE_SUB(CURDATE(), INTERVAL FLOOR(RAND() * 60) + 1 DAY)
WHERE status = 'borrowed' 
AND id NOT IN (
    SELECT id FROM (
        SELECT id FROM book_loans 
        WHERE status = 'borrowed' 
        ORDER BY issue_date DESC 
        LIMIT 300
    ) AS temp
);

-- Step 5: Update overdue status for books past due date
UPDATE book_loans 
SET status = 'overdue'
WHERE status = 'borrowed' 
AND due_date < CURDATE();

-- Step 6: Calculate fines for overdue books
UPDATE book_loans 
SET fine = DATEDIFF(CURDATE(), due_date) * 1.00
WHERE status = 'overdue' 
AND due_date < CURDATE();

-- Step 7: Make some overdue loans returned with late fees
UPDATE book_loans 
SET status = 'returned',
    return_date = DATE_ADD(due_date, INTERVAL FLOOR(RAND() * 10) + 1 DAY),
    fine = FLOOR(RAND() * 10) + 1
WHERE status = 'overdue' 
AND id IN (
    SELECT id FROM (
        SELECT id FROM book_loans 
        WHERE status = 'overdue' 
        ORDER BY RAND() 
        LIMIT 50
    ) AS temp
);

-- Step 8: Check final statistics
SELECT 'AFTER BALANCE' as status;
SELECT 
    'Members' as type, COUNT(*) as count FROM members
UNION ALL
SELECT 
    'Active Loans' as type, COUNT(*) as count FROM book_loans WHERE status = 'borrowed'
UNION ALL
SELECT 
    'Overdue Loans' as type, COUNT(*) as count FROM book_loans WHERE status = 'overdue'
UNION ALL
SELECT 
    'Returned Loans' as type, COUNT(*) as count FROM book_loans WHERE status = 'returned'
UNION ALL
SELECT 
    'Total Loans' as type, COUNT(*) as count FROM book_loans;
