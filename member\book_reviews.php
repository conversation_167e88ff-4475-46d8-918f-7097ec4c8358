<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if member is logged in
if (!isset($_SESSION['member_id'])) {
    header('Location: ../login.php');
    exit;
}

// Get member ID from session
$member_id = $_SESSION['member_id'];

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Handle review submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_review'])) {
    $book_id = $_POST['book_id'];
    $rating = $_POST['rating'];
    $review_text = trim($_POST['review_text']);
    
    try {
        // Check if member has already reviewed this book
        $query = "SELECT * FROM book_reviews WHERE book_id = :book_id AND member_id = :member_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':book_id', $book_id);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            // Update existing review
            $query = "UPDATE book_reviews SET rating = :rating, review_text = :review_text, review_date = NOW() 
                      WHERE book_id = :book_id AND member_id = :member_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':rating', $rating);
            $stmt->bindParam(':review_text', $review_text);
            $stmt->bindParam(':book_id', $book_id);
            $stmt->bindParam(':member_id', $member_id);
            $stmt->execute();
            
            $success_message = "Your review has been updated successfully!";
        } else {
            // Insert new review
            $query = "INSERT INTO book_reviews (book_id, member_id, rating, review_text) 
                      VALUES (:book_id, :member_id, :rating, :review_text)";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':book_id', $book_id);
            $stmt->bindParam(':member_id', $member_id);
            $stmt->bindParam(':rating', $rating);
            $stmt->bindParam(':review_text', $review_text);
            $stmt->execute();
            
            $success_message = "Your review has been submitted successfully!";
        }
    } catch (PDOException $e) {
        $error_message = "Error submitting review: " . $e->getMessage();
    }
}

// Get member's reviews
$query = "SELECT br.*, b.title, b.author, b.cover_image
          FROM book_reviews br
          JOIN books b ON br.book_id = b.id
          WHERE br.member_id = :member_id
          ORDER BY br.review_date DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$member_reviews = $stmt->fetchAll();

// Get books that member has read but not reviewed
$query = "SELECT DISTINCT b.id, b.title, b.author, b.cover_image
          FROM books b
          JOIN book_loans bl ON b.id = bl.book_id
          WHERE bl.member_id = :member_id 
          AND bl.status = 'returned'
          AND b.id NOT IN (
              SELECT book_id FROM book_reviews WHERE member_id = :member_id
          )
          ORDER BY b.title";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$books_to_review = $stmt->fetchAll();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function renderStars($rating) {
    $stars = '';
    for ($i = 1; $i <= 5; $i++) {
        if ($i <= $rating) {
            $stars .= '<i class="bi bi-star-fill text-warning"></i>';
        } else {
            $stars .= '<i class="bi bi-star text-muted"></i>';
        }
    }
    return $stars;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Book Reviews - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .book-cover {
            width: 60px;
            height: 90px;
            object-fit: cover;
            border-radius: 5px;
        }
        .review-card {
            transition: all 0.3s ease;
        }
        .review-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .star-rating {
            cursor: pointer;
        }
        .star-rating i {
            font-size: 1.2rem;
            margin-right: 2px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="member_dashboard.php">Dashboard</a>
                <a class="nav-link" href="../logout.php">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-star me-2"></i>My Book Reviews</h2>
                    <a href="member_dashboard.php" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>

                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo h($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo h($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Tabs -->
                <ul class="nav nav-tabs" id="reviewTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="my-reviews-tab" data-bs-toggle="tab" data-bs-target="#my-reviews" type="button" role="tab">
                            My Reviews (<?php echo count($member_reviews); ?>)
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="write-review-tab" data-bs-toggle="tab" data-bs-target="#write-review" type="button" role="tab">
                            Write Review (<?php echo count($books_to_review); ?> books)
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="reviewTabsContent">
                    <!-- My Reviews Tab -->
                    <div class="tab-pane fade show active" id="my-reviews" role="tabpanel">
                        <div class="mt-4">
                            <?php if (count($member_reviews) > 0): ?>
                            <div class="row">
                                <?php foreach ($member_reviews as $review): ?>
                                <div class="col-md-6 mb-4">
                                    <div class="card review-card h-100">
                                        <div class="card-body">
                                            <div class="d-flex">
                                                <div class="me-3">
                                                    <?php if ($review['cover_image']): ?>
                                                        <img src="../<?php echo h($review['cover_image']); ?>" alt="Cover" class="book-cover">
                                                    <?php else: ?>
                                                        <div class="book-cover d-flex align-items-center justify-content-center bg-light">
                                                            <i class="bi bi-book text-secondary"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="card-title"><?php echo h($review['title']); ?></h6>
                                                    <p class="text-muted mb-2">by <?php echo h($review['author']); ?></p>
                                                    <div class="mb-2">
                                                        <?php echo renderStars($review['rating']); ?>
                                                        <span class="ms-2"><?php echo $review['rating']; ?>/5</span>
                                                    </div>
                                                    <?php if ($review['review_text']): ?>
                                                    <p class="card-text"><?php echo h($review['review_text']); ?></p>
                                                    <?php endif; ?>
                                                    <small class="text-muted">Reviewed on <?php echo formatDate($review['review_date']); ?></small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php else: ?>
                            <div class="text-center py-5">
                                <i class="bi bi-star display-1 text-muted"></i>
                                <h4 class="mt-3">No reviews yet</h4>
                                <p class="text-muted">Start reviewing books you've read!</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Write Review Tab -->
                    <div class="tab-pane fade" id="write-review" role="tabpanel">
                        <div class="mt-4">
                            <?php if (count($books_to_review) > 0): ?>
                            <div class="row">
                                <?php foreach ($books_to_review as $book): ?>
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="d-flex">
                                                <div class="me-3">
                                                    <?php if ($book['cover_image']): ?>
                                                        <img src="../<?php echo h($book['cover_image']); ?>" alt="Cover" class="book-cover">
                                                    <?php else: ?>
                                                        <div class="book-cover d-flex align-items-center justify-content-center bg-light">
                                                            <i class="bi bi-book text-secondary"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="card-title"><?php echo h($book['title']); ?></h6>
                                                    <p class="text-muted mb-3">by <?php echo h($book['author']); ?></p>
                                                    <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#reviewModal" 
                                                            onclick="setReviewBook(<?php echo $book['id']; ?>, '<?php echo addslashes($book['title']); ?>', '<?php echo addslashes($book['author']); ?>')">
                                                        <i class="bi bi-star me-1"></i>Write Review
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php else: ?>
                            <div class="text-center py-5">
                                <i class="bi bi-check-circle display-1 text-success"></i>
                                <h4 class="mt-3">All caught up!</h4>
                                <p class="text-muted">You've reviewed all the books you've read.</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Review Modal -->
    <div class="modal fade" id="reviewModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Write Review</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="book_id" id="review_book_id">
                        <h6 id="review_book_title"></h6>
                        <p class="text-muted" id="review_book_author"></p>
                        
                        <div class="mb-3">
                            <label class="form-label">Rating *</label>
                            <div class="star-rating" id="starRating">
                                <i class="bi bi-star" data-rating="1"></i>
                                <i class="bi bi-star" data-rating="2"></i>
                                <i class="bi bi-star" data-rating="3"></i>
                                <i class="bi bi-star" data-rating="4"></i>
                                <i class="bi bi-star" data-rating="5"></i>
                            </div>
                            <input type="hidden" name="rating" id="rating" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="review_text" class="form-label">Review (Optional)</label>
                            <textarea class="form-control" name="review_text" id="review_text" rows="4" 
                                      placeholder="Share your thoughts about this book..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="submit_review" class="btn btn-primary">Submit Review</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function setReviewBook(bookId, title, author) {
            document.getElementById('review_book_id').value = bookId;
            document.getElementById('review_book_title').textContent = title;
            document.getElementById('review_book_author').textContent = 'by ' + author;
            document.getElementById('rating').value = '';
            document.getElementById('review_text').value = '';
            
            // Reset stars
            document.querySelectorAll('#starRating i').forEach(star => {
                star.className = 'bi bi-star';
            });
        }

        // Star rating functionality
        document.querySelectorAll('#starRating i').forEach(star => {
            star.addEventListener('click', function() {
                const rating = this.getAttribute('data-rating');
                document.getElementById('rating').value = rating;
                
                // Update star display
                document.querySelectorAll('#starRating i').forEach((s, index) => {
                    if (index < rating) {
                        s.className = 'bi bi-star-fill text-warning';
                    } else {
                        s.className = 'bi bi-star';
                    }
                });
            });
            
            star.addEventListener('mouseover', function() {
                const rating = this.getAttribute('data-rating');
                document.querySelectorAll('#starRating i').forEach((s, index) => {
                    if (index < rating) {
                        s.className = 'bi bi-star-fill text-warning';
                    } else {
                        s.className = 'bi bi-star';
                    }
                });
            });
        });
    </script>
</body>
</html>
