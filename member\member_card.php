<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if member is logged in
if (!isMemberLoggedIn()) {
    redirect('../member_login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get member ID from session
$member_id = $_SESSION['member_id'];

// Get member details
$query = "SELECT * FROM members WHERE id = :member_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$member = $stmt->fetch();

// Generate QR code data
$qr_data = json_encode([
    'id' => $member['id'],
    'name' => $member['first_name'] . ' ' . $member['last_name'],
    'membership_number' => $member['membership_number'] ?? $member['id'],
    'timestamp' => time()
]);

// Base64 encode the data for the QR code
$qr_data_encoded = base64_encode($qr_data);

// Generate QR code URL using Google Charts API
$qr_code_url = "https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=" . urlencode($qr_data_encoded) . "&choe=UTF-8";

// Page title
$page_title = 'My Member Card';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include '../includes/head.php'; ?>
    <style>
        .member-card {
            max-width: 500px;
            margin: 0 auto;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            position: relative;
        }
        .member-card-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .member-card-body {
            padding: 20px;
            display: flex;
            align-items: center;
        }
        .member-info {
            flex: 1;
            padding-right: 20px;
        }
        .member-qr {
            background-color: white;
            padding: 10px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .member-card-footer {
            padding: 15px 20px;
            background-color: rgba(0, 0, 0, 0.1);
            font-size: 0.9rem;
        }
        .library-logo {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .library-logo i {
            font-size: 30px;
            color: #007bff;
        }
        .profile-picture {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin-right: 20px;
        }
        .print-card-btn {
            max-width: 500px;
            margin: 20px auto;
        }
        @media print {
            body * {
                visibility: hidden;
            }
            .member-card, .member-card * {
                visibility: visible;
            }
            .member-card {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                box-shadow: none;
            }
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-credit-card-2-front me-2 text-primary"></i>My Member Card</h1>
                </div>

                <div class="alert alert-info mb-4 no-print">
                    <i class="bi bi-info-circle me-2"></i>
                    This is your digital library card. You can show this card on your phone when visiting the library, or print it for physical use.
                </div>

                <div class="member-card mb-4">
                    <div class="library-logo">
                        <i class="bi bi-book"></i>
                    </div>
                    <div class="member-card-header">
                        <h4 class="mb-0">Library Membership Card</h4>
                        <small>Member since: <?php echo date('F j, Y', strtotime($member['membership_date'])); ?></small>
                    </div>
                    <div class="member-card-body">
                        <?php if (!empty($member['profile_picture']) && file_exists('../uploads/profiles/' . $member['profile_picture'])): ?>
                            <img src="<?php echo url('../uploads/profiles/' . $member['profile_picture']); ?>" alt="Profile Picture" class="profile-picture">
                        <?php else: ?>
                            <img src="<?php echo url('../assets/img/default-profile.jpg'); ?>" alt="Default Profile" class="profile-picture">
                        <?php endif; ?>
                        
                        <div class="member-info">
                            <h5><?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?></h5>
                            <p class="mb-1">
                                <strong>Member ID:</strong> <?php echo htmlspecialchars($member['membership_number'] ?? $member['id']); ?>
                            </p>
                            <p class="mb-1">
                                <strong>Email:</strong> <?php echo htmlspecialchars($member['email']); ?>
                            </p>
                            <?php if (!empty($member['phone'])): ?>
                                <p class="mb-0">
                                    <strong>Phone:</strong> <?php echo htmlspecialchars($member['phone']); ?>
                                </p>
                            <?php endif; ?>
                        </div>
                        
                        <div class="member-qr">
                            <img src="<?php echo $qr_code_url; ?>" alt="QR Code" width="120" height="120">
                        </div>
                    </div>
                    <div class="member-card-footer">
                        <div class="d-flex justify-content-between">
                            <span>Valid until: <?php echo date('F j, Y', strtotime('+1 year', strtotime($member['membership_date']))); ?></span>
                            <span>Library Management System</span>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 print-card-btn no-print">
                    <button type="button" class="btn btn-primary" onclick="window.print()">
                        <i class="bi bi-printer me-2"></i>Print Member Card
                    </button>
                </div>
                
                <div class="card mt-4 no-print">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>How to Use Your Member Card</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Show this digital card on your phone when visiting the library</li>
                            <li>The librarian will scan the QR code to identify you</li>
                            <li>You can also print this card for physical use</li>
                            <li>Keep your member card safe and don't share it with others</li>
                        </ol>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>Important:</strong> Your member card is used to borrow books and access library services. If you lose your card, please contact the library staff immediately.
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
