<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if member is logged in
if (!isset($_SESSION['member_id'])) {
    header('Location: ../login.php');
    exit;
}

// Get member ID from session
$member_id = $_SESSION['member_id'];

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get member details
$query = "SELECT * FROM members WHERE id = :member_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$member = $stmt->fetch();

// Check if the member is signed in with Google
$is_google_user = !empty($member['google_id']);

// Get current loans
$query = "SELECT bl.*, b.title, b.author, b.isbn, b.cover_image
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id AND bl.status != 'returned'
          ORDER BY bl.due_date ASC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$current_loans = $stmt->fetchAll();

// Get loan history
$query = "SELECT bl.*, b.title, b.author, b.isbn
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id AND bl.status = 'returned'
          ORDER BY bl.return_date DESC
          LIMIT 5";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$loan_history = $stmt->fetchAll();

// Get reservations
$query = "SELECT br.*, b.title, b.author, b.isbn
          FROM book_reservations br
          JOIN books b ON br.book_id = b.id
          WHERE br.member_id = :member_id AND br.status IN ('pending', 'ready')
          ORDER BY br.reservation_date DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$reservations = $stmt->fetchAll();

// Calculate total fines
$query = "SELECT SUM(fine) as total_fine FROM book_loans
          WHERE member_id = :member_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$total_fine = $stmt->fetch()['total_fine'] ?? 0;

// Get notifications for member (if notifications table exists)
$notifications = [];
$unread_notifications_count = 0;
try {
    $query = "SELECT * FROM notifications
              WHERE (user_id = :member_id OR user_id IS NULL)
              AND is_read = 0
              ORDER BY created_at DESC
              LIMIT 5";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':member_id', $member_id);
    $stmt->execute();
    $notifications = $stmt->fetchAll();
    $unread_notifications_count = count($notifications);
} catch (PDOException $e) {
    // Notifications table might not exist, continue without notifications
}

// Get reading statistics
$query = "SELECT
            COUNT(*) as total_books_read,
            COUNT(DISTINCT b.category) as categories_explored,
            COUNT(DISTINCT b.author) as authors_read,
            AVG(DATEDIFF(bl.return_date, bl.issue_date)) as avg_reading_days
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id AND bl.status = 'returned'";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$reading_stats = $stmt->fetch();

// Get books due soon (within 3 days)
$query = "SELECT bl.*, b.title, b.author
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id
          AND bl.status = 'borrowed'
          AND bl.due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 3 DAY)
          ORDER BY bl.due_date ASC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$books_due_soon = $stmt->fetchAll();

// Get overdue books
$query = "SELECT bl.*, b.title, b.author
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id
          AND bl.status = 'borrowed'
          AND bl.due_date < CURDATE()
          ORDER BY bl.due_date ASC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$overdue_books = $stmt->fetchAll();

// Get wishlist count (if table exists)
$wishlist_count = 0;
try {
    $query = "SELECT COUNT(*) as count FROM member_wishlist WHERE member_id = :member_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':member_id', $member_id);
    $stmt->execute();
    $wishlist_count = $stmt->fetch()['count'];
} catch (PDOException $e) {
    // Wishlist table might not exist, continue without wishlist
}

// Check for return success message
$return_success_message = '';
if (isset($_SESSION['return_success'])) {
    $return_success_message = $_SESSION['return_success'];
    unset($_SESSION['return_success']);
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Dashboard - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .dashboard-container {
            max-width: 1200px;
            margin: 20px auto;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            font-weight: bold;
        }
        .navbar {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .book-cover {
            width: 80px;
            height: 120px;
            object-fit: cover;
            border: 1px solid #ddd;
        }
        .overdue {
            color: #dc3545;
            font-weight: bold;
        }
        /* Enhanced dashboard styles */
        .welcome-card {
            background: linear-gradient(to right, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .stats-card {
            text-align: center;
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .stats-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #007bff;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            border: none;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            font-weight: bold;
        }
        .book-cover {
            width: 80px;
            height: 120px;
            object-fit: cover;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        /* Enhanced styles for new features */
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -35px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #dee2e6;
        }

        .timeline-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #007bff;
        }

        .quick-action-btn {
            height: 80px;
            transition: all 0.3s ease;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .notification-badge {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .alert {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stats-card:hover {
            transform: translateY(-2px);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .welcome-card .text-muted {
            color: rgba(255,255,255,0.8) !important;
        }

        .card {
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        /* Star rating styles */
        .star-rating {
            cursor: pointer;
        }

        .star-rating i {
            font-size: 1.2rem;
            margin-right: 3px;
            color: #ddd;
            transition: color 0.2s;
        }

        .star-rating i:hover {
            color: #ffc107;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .dashboard-container {
                margin: 10px;
            }

            .stats-card {
                margin-bottom: 15px;
            }

            .welcome-card {
                text-align: center;
            }

            .quick-action-btn {
                height: 60px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="member_dashboard.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../catalog.php">Book Catalog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="self_checkout.php">Self Checkout</a>
                    </li>
                </ul>

                <!-- Quick Search -->
                <form class="d-flex me-3" action="../catalog.php" method="get">
                    <div class="input-group">
                        <input class="form-control form-control-sm" type="search" name="search" placeholder="Search books..." aria-label="Search">
                        <button class="btn btn-outline-light btn-sm" type="submit">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </form>

                <div class="d-flex align-items-center">
                    <!-- Notification Bell -->
                    <?php if ($unread_notifications_count > 0): ?>
                    <div class="dropdown me-3">
                        <button class="btn btn-outline-light position-relative" type="button" id="notificationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?php echo $unread_notifications_count; ?>
                                <span class="visually-hidden">unread notifications</span>
                            </span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationDropdown" style="width: 300px;">
                            <li><h6 class="dropdown-header">Notifications</h6></li>
                            <?php foreach ($notifications as $notification): ?>
                            <li>
                                <div class="dropdown-item-text small">
                                    <div class="d-flex justify-content-between">
                                        <span class="fw-bold"><?php echo h($notification['type']); ?></span>
                                        <small class="text-muted"><?php echo timeAgo($notification['created_at']); ?></small>
                                    </div>
                                    <div><?php echo h($notification['message']); ?></div>
                                </div>
                            </li>
                            <?php endforeach; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center" href="../notifications/">View All</a></li>
                        </ul>
                    </div>
                    <?php endif; ?>

                    <!-- User Dropdown -->
                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle me-1"></i><?php echo h($_SESSION['member_name']); ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton">
                            <li><a class="dropdown-item" href="profile.php">My Profile</a></li>
                            <li><a class="dropdown-item" href="my_loans.php">My Loans</a></li>
                            <li><a class="dropdown-item" href="recommendations.php">Recommendations</a></li>
                            <?php if (!$is_google_user): ?>
                            <li><a class="dropdown-item" href="../google_account.php">
                                <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="16" height="16" class="me-2">
                                Link Google Account
                            </a></li>
                            <?php else: ?>
                            <li>
                                <div class="dropdown-item d-flex align-items-center">
                                    <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="16" height="16" class="me-2">
                                    <span class="text-muted">Signed in with Google</span>
                                </div>
                            </li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="dashboard-container">
        <?php
        // Log member dashboard access
        error_log("Member Dashboard: Member ID: " . $_SESSION['member_id'] . ", Name: " . $_SESSION['member_name']);

        // Check for streamlined Google sign-in
        if (isset($_SESSION['streamlined_google_signin']) && $_SESSION['streamlined_google_signin']) {
            // Clear the streamlined sign-in flag
            unset($_SESSION['streamlined_google_signin']);

            // Show a small notification for streamlined sign-in
            error_log("Member Dashboard: Streamlined Google sign-in completed");
            ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert" style="background-color: #f8f9fa; color: #3c4043; border-color: #dadce0;">
                <div class="d-flex align-items-center">
                    <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="18" height="18" class="me-2">
                    <span>You've been signed in with your Google account</span>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php
        }
        // Check for regular Google login
        elseif (isset($_SESSION['google_login']) && $_SESSION['google_login']) { ?>
            <?php if (isset($_SESSION['new_google_user']) && $_SESSION['new_google_user']) { ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-google me-2"></i>
                    <strong>Welcome!</strong> Your account has been created using your Google account information.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php
                error_log("Member Dashboard: New Google user welcome message shown");
                unset($_SESSION['new_google_user']);
                ?>
            <?php } else { ?>
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="bi bi-google me-2"></i>
                    <strong>Welcome back!</strong> You've been signed in with your Google account.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php error_log("Member Dashboard: Returning Google user welcome message shown"); ?>
            <?php } ?>
            <?php unset($_SESSION['google_login']); ?>
        <?php } ?>

        <?php if (isset($_GET['login'])) { ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="bi bi-info-circle me-2"></i>
                <strong>Debug Info:</strong> Login timestamp: <?php echo h($_GET['login']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php } ?>

        <div class="row">
            <div class="col-md-12">
                <h2 class="mb-4">Member Dashboard</h2>

                <!-- Return Success Message -->
                <?php if (!empty($return_success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i><?php echo h($return_success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Alert Cards for Overdue and Due Soon Books -->
                <?php if (count($overdue_books) > 0): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <h5 class="alert-heading"><i class="bi bi-exclamation-triangle-fill me-2"></i>Overdue Books</h5>
                    <p class="mb-2">You have <?php echo count($overdue_books); ?> overdue book(s). Please return them as soon as possible to avoid additional fines.</p>
                    <ul class="mb-0">
                        <?php foreach ($overdue_books as $book): ?>
                        <li><strong><?php echo h($book['title']); ?></strong> by <?php echo h($book['author']); ?> - Due: <?php echo formatDate($book['due_date']); ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (count($books_due_soon) > 0): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <h5 class="alert-heading"><i class="bi bi-clock-fill me-2"></i>Books Due Soon</h5>
                    <p class="mb-2">You have <?php echo count($books_due_soon); ?> book(s) due within the next 3 days.</p>
                    <ul class="mb-0">
                        <?php foreach ($books_due_soon as $book): ?>
                        <li><strong><?php echo h($book['title']); ?></strong> by <?php echo h($book['author']); ?> - Due: <?php echo formatDate($book['due_date']); ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Welcome Card -->
                <div class="welcome-card">
                    <div class="row">
                        <div class="col-md-8">
                            <h4>Welcome back, <?php echo h($member['first_name']); ?>!</h4>
                            <p class="text-muted">Member since <?php echo formatDate($member['membership_date']); ?></p>
                        </div>
                        <div class="col-md-4 text-end">
                            <?php if ($total_fine > 0): ?>
                                <p class="text-danger mb-2">
                                    <i class="bi bi-exclamation-circle"></i>
                                    Outstanding Fine: $<?php echo number_format($total_fine, 2); ?>
                                </p>
                            <?php else: ?>
                                <p class="text-success mb-2">
                                    <i class="bi bi-check-circle"></i> No outstanding fines
                                </p>
                            <?php endif; ?>
                            <a href="profile.php" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-pencil"></i> Edit Profile
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <div class="stats-card">
                            <div class="stats-icon">
                                <i class="bi bi-book"></i>
                            </div>
                            <h3><?php echo count($current_loans); ?></h3>
                            <p class="text-muted">Current Loans</p>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <div class="stats-card">
                            <div class="stats-icon" style="color: #6c757d;">
                                <i class="bi bi-bookmark"></i>
                            </div>
                            <h3><?php echo count($reservations); ?></h3>
                            <p class="text-muted">Reservations</p>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <div class="stats-card">
                            <div class="stats-icon" style="color: #dc3545;">
                                <i class="bi bi-heart"></i>
                            </div>
                            <h3><?php echo $wishlist_count; ?></h3>
                            <p class="text-muted">Wishlist</p>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <div class="stats-card">
                            <div class="stats-icon" style="color: #28a745;">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <h3><?php echo $reading_stats['total_books_read'] ?? 0; ?></h3>
                            <p class="text-muted">Books Read</p>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <div class="stats-card">
                            <div class="stats-icon" style="color: #ffc107;">
                                <i class="bi bi-star"></i>
                            </div>
                            <h3><?php echo $reading_stats['categories_explored'] ?? 0; ?></h3>
                            <p class="text-muted">Categories Explored</p>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <div class="stats-card">
                            <div class="stats-icon" style="color: #17a2b8;">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <h3><?php echo $reading_stats['authors_read'] ?? 0; ?></h3>
                            <p class="text-muted">Authors Read</p>
                        </div>
                    </div>
                </div>

                <!-- Reading Progress Card -->
                <?php if ($reading_stats['total_books_read'] > 0): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Reading Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center">
                                <h4 class="text-primary"><?php echo $reading_stats['total_books_read']; ?></h4>
                                <p class="text-muted mb-0">Total Books Read</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <h4 class="text-success"><?php echo $reading_stats['authors_read']; ?></h4>
                                <p class="text-muted mb-0">Authors Explored</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <h4 class="text-info"><?php echo $reading_stats['categories_explored']; ?></h4>
                                <p class="text-muted mb-0">Categories</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <h4 class="text-warning"><?php echo round($reading_stats['avg_reading_days'] ?? 0, 1); ?></h4>
                                <p class="text-muted mb-0">Avg. Days per Book</p>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Member Info Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Member Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Name:</strong> <?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></p>
                                <p><strong>Email:</strong> <?php echo h($member['email']); ?></p>
                                <p><strong>Phone:</strong> <?php echo h($member['phone'] ?: 'Not provided'); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Membership Date:</strong> <?php echo formatDate($member['membership_date']); ?></p>
                                <p><strong>Status:</strong>
                                    <span class="badge bg-success"><?php echo ucfirst($member['membership_status']); ?></span>
                                </p>
                                <p><strong>Outstanding Fines:</strong>
                                    <?php if ($total_fine > 0): ?>
                                        <span class="text-danger">$<?php echo number_format($total_fine, 2); ?></span>
                                    <?php else: ?>
                                        <span class="text-success">$0.00</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Loans -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Current Loans</h5>
                        <span class="badge bg-primary"><?php echo count($current_loans); ?></span>
                    </div>
                    <div class="card-body">
                        <?php if (count($current_loans) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Book</th>
                                            <th>Title & Author</th>
                                            <th>Issue Date</th>
                                            <th>Due Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($current_loans as $loan): ?>
                                            <tr>
                                                <td>
                                                    <?php if ($loan['cover_image']): ?>
                                                        <img src="../<?php echo h($loan['cover_image']); ?>" alt="Cover" class="book-cover">
                                                    <?php else: ?>
                                                        <div class="book-cover d-flex align-items-center justify-content-center bg-light">
                                                            <i class="bi bi-book fs-1 text-secondary"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong><?php echo h($loan['title']); ?></strong>
                                                    <br><small class="text-muted">by <?php echo h($loan['author']); ?></small>
                                                </td>
                                                <td><?php echo formatDate($loan['issue_date']); ?></td>
                                                <td class="<?php echo strtotime($loan['due_date']) < time() ? 'overdue' : ''; ?>">
                                                    <?php echo formatDate($loan['due_date']); ?>
                                                    <?php if (strtotime($loan['due_date']) < time()): ?>
                                                        <br><span class="badge bg-danger">Overdue</span>
                                                    <?php elseif (strtotime($loan['due_date']) < strtotime('+3 days')): ?>
                                                        <br><span class="badge bg-warning">Due Soon</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($loan['status'] === 'borrowed'): ?>
                                                        <span class="badge bg-primary">Borrowed</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Overdue</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <button class="btn btn-sm btn-success" data-bs-toggle="modal"
                                                            data-bs-target="#quickReturnModal"
                                                            onclick="setQuickReturn(<?php echo $loan['id']; ?>, '<?php echo addslashes($loan['title']); ?>', '<?php echo addslashes($loan['author']); ?>', <?php echo $loan['book_id']; ?>)">
                                                        <i class="bi bi-arrow-return-left me-1"></i>Return
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-center">You don't have any books checked out at the moment.</p>
                            <div class="text-center">
                                <a href="../catalog.php" class="btn btn-primary">Browse Books</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-lightning-fill me-2"></i>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="../catalog.php" class="btn btn-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="bi bi-search fs-4 mb-2"></i>
                                    <span>Browse Books</span>
                                </a>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="my_loans.php" class="btn btn-info w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="bi bi-book fs-4 mb-2"></i>
                                    <span>My Loans</span>
                                </a>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="self_checkout.php" class="btn btn-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="bi bi-qr-code-scan fs-4 mb-2"></i>
                                    <span>Self Checkout</span>
                                </a>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="recommendations.php" class="btn btn-success w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="bi bi-star fs-4 mb-2"></i>
                                    <span>Recommendations</span>
                                </a>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="wishlist.php" class="btn btn-danger w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="bi bi-heart fs-4 mb-2"></i>
                                    <span>My Wishlist</span>
                                    <?php if ($wishlist_count > 0): ?>
                                    <span class="badge bg-light text-dark"><?php echo $wishlist_count; ?></span>
                                    <?php endif; ?>
                                </a>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="book_reviews.php" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="bi bi-star-half fs-4 mb-2"></i>
                                    <span>My Reviews</span>
                                </a>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="profile.php" class="btn btn-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="bi bi-person fs-4 mb-2"></i>
                                    <span>My Profile</span>
                                </a>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="return_book.php" class="btn btn-outline-danger w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="bi bi-arrow-return-left fs-4 mb-2"></i>
                                    <span>Return Books</span>
                                    <?php if (count($current_loans) > 0): ?>
                                    <span class="badge bg-light text-dark"><?php echo count($current_loans); ?></span>
                                    <?php endif; ?>
                                </a>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="reading_goals.php" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="bi bi-target fs-4 mb-2"></i>
                                    <span>Reading Goals</span>
                                </a>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="../catalog.php?category=New%20Arrivals" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="bi bi-plus-circle fs-4 mb-2"></i>
                                    <span>New Arrivals</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity Feed -->
                <?php if (count($loan_history) > 0): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>Recent Activity</h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <?php foreach (array_slice($loan_history, 0, 5) as $activity): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Returned: <?php echo h($activity['title']); ?></h6>
                                    <p class="text-muted mb-0">by <?php echo h($activity['author']); ?></p>
                                    <small class="text-muted"><?php echo formatDate($activity['return_date']); ?></small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php if (count($loan_history) > 5): ?>
                        <div class="text-center mt-3">
                            <a href="my_loans.php?tab=history" class="btn btn-outline-primary btn-sm">View All History</a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

            </div>
        </div>
    </div>

    <!-- Quick Return Modal -->
    <div class="modal fade" id="quickReturnModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Quick Return & Rate</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post" action="return_book.php">
                    <div class="modal-body">
                        <input type="hidden" name="loan_id" id="quick_loan_id">
                        <input type="hidden" name="quick_return" value="1">

                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle me-2"></i>Returning: <span id="quick_book_title"></span></h6>
                            <p class="mb-0">by <span id="quick_book_author"></span></p>
                        </div>

                        <div class="mb-3">
                            <h6>Rate this book (Optional)</h6>
                            <div class="star-rating" id="quickStarRating">
                                <i class="bi bi-star" data-rating="1"></i>
                                <i class="bi bi-star" data-rating="2"></i>
                                <i class="bi bi-star" data-rating="3"></i>
                                <i class="bi bi-star" data-rating="4"></i>
                                <i class="bi bi-star" data-rating="5"></i>
                            </div>
                            <input type="hidden" name="rating" id="quick_rating">
                            <small class="text-muted">Click the stars to rate</small>
                        </div>

                        <div class="mb-3">
                            <label for="quick_review_text" class="form-label">Quick Review (Optional)</label>
                            <textarea class="form-control" name="review_text" id="quick_review_text" rows="2"
                                      placeholder="What did you think of this book?"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="return_book" class="btn btn-success">
                            <i class="bi bi-arrow-return-left me-2"></i>Return Book
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function setQuickReturn(loanId, title, author, bookId) {
            document.getElementById('quick_loan_id').value = loanId;
            document.getElementById('quick_book_title').textContent = title;
            document.getElementById('quick_book_author').textContent = author;
            document.getElementById('quick_rating').value = '';
            document.getElementById('quick_review_text').value = '';

            // Reset stars
            document.querySelectorAll('#quickStarRating i').forEach(star => {
                star.className = 'bi bi-star';
            });
        }

        // Star rating functionality for quick return
        document.querySelectorAll('#quickStarRating i').forEach(star => {
            star.addEventListener('click', function() {
                const rating = this.getAttribute('data-rating');
                document.getElementById('quick_rating').value = rating;

                // Update star display
                document.querySelectorAll('#quickStarRating i').forEach((s, index) => {
                    if (index < rating) {
                        s.className = 'bi bi-star-fill text-warning';
                    } else {
                        s.className = 'bi bi-star';
                    }
                });
            });

            star.addEventListener('mouseover', function() {
                const rating = this.getAttribute('data-rating');
                document.querySelectorAll('#quickStarRating i').forEach((s, index) => {
                    if (index < rating) {
                        s.className = 'bi bi-star-fill text-warning';
                    } else {
                        s.className = 'bi bi-star';
                    }
                });
            });
        });

        // Reset stars on mouse leave
        document.getElementById('quickStarRating').addEventListener('mouseleave', function() {
            const currentRating = document.getElementById('quick_rating').value;
            document.querySelectorAll('#quickStarRating i').forEach((s, index) => {
                if (index < currentRating) {
                    s.className = 'bi bi-star-fill text-warning';
                } else {
                    s.className = 'bi bi-star';
                }
            });
        });
    </script>
</body>
</html>