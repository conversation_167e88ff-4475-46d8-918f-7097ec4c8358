<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if member is logged in
if (!isset($_SESSION['member_id'])) {
    header('Location: ../login.php');
    exit;
}

// Get member ID from session
$member_id = $_SESSION['member_id'];

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Handle goal setting
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['set_goal'])) {
    $goal_year = $_POST['goal_year'];
    $target_books = $_POST['target_books'];
    
    try {
        // Check if goal already exists for this year
        $query = "SELECT * FROM reading_goals WHERE member_id = :member_id AND goal_year = :goal_year";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->bindParam(':goal_year', $goal_year);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            // Update existing goal
            $query = "UPDATE reading_goals SET target_books = :target_books, updated_date = NOW() 
                      WHERE member_id = :member_id AND goal_year = :goal_year";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':target_books', $target_books);
            $stmt->bindParam(':member_id', $member_id);
            $stmt->bindParam(':goal_year', $goal_year);
            $stmt->execute();
            
            $success_message = "Reading goal updated successfully!";
        } else {
            // Insert new goal
            $query = "INSERT INTO reading_goals (member_id, goal_year, target_books) 
                      VALUES (:member_id, :goal_year, :target_books)";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':member_id', $member_id);
            $stmt->bindParam(':goal_year', $goal_year);
            $stmt->bindParam(':target_books', $target_books);
            $stmt->execute();
            
            $success_message = "Reading goal set successfully!";
        }
        
        // Update books read count for this year
        $query = "UPDATE reading_goals rg 
                  SET books_read = (
                      SELECT COUNT(*) FROM book_loans bl 
                      WHERE bl.member_id = :member_id 
                      AND bl.status = 'returned' 
                      AND YEAR(bl.return_date) = :goal_year
                  )
                  WHERE rg.member_id = :member_id AND rg.goal_year = :goal_year";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->bindParam(':goal_year', $goal_year);
        $stmt->execute();
        
    } catch (PDOException $e) {
        $error_message = "Error setting goal: " . $e->getMessage();
    }
}

// Get current year's goal
$current_year = date('Y');
$query = "SELECT * FROM reading_goals WHERE member_id = :member_id AND goal_year = :current_year";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->bindParam(':current_year', $current_year);
$stmt->execute();
$current_goal = $stmt->fetch();

// Get all goals
$query = "SELECT * FROM reading_goals WHERE member_id = :member_id ORDER BY goal_year DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$all_goals = $stmt->fetchAll();

// Get monthly reading data for current year
$query = "SELECT 
            MONTH(bl.return_date) as month,
            COUNT(*) as books_read
          FROM book_loans bl
          WHERE bl.member_id = :member_id 
          AND bl.status = 'returned'
          AND YEAR(bl.return_date) = :current_year
          GROUP BY MONTH(bl.return_date)
          ORDER BY MONTH(bl.return_date)";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->bindParam(':current_year', $current_year);
$stmt->execute();
$monthly_data = $stmt->fetchAll();

// Create monthly array
$monthly_books = array_fill(1, 12, 0);
foreach ($monthly_data as $data) {
    $monthly_books[$data['month']] = $data['books_read'];
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function getProgressPercentage($books_read, $target_books) {
    if ($target_books == 0) return 0;
    return min(100, round(($books_read / $target_books) * 100));
}

function getProgressColor($percentage) {
    if ($percentage >= 100) return 'success';
    if ($percentage >= 75) return 'info';
    if ($percentage >= 50) return 'warning';
    return 'danger';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reading Goals - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
        }
        .goal-card {
            transition: all 0.3s ease;
        }
        .goal-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .progress-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="member_dashboard.php">Dashboard</a>
                <a class="nav-link" href="../logout.php">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-target me-2"></i>Reading Goals</h2>
                    <a href="member_dashboard.php" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>

                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo h($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo h($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Current Year Goal -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card goal-card">
                            <div class="card-header">
                                <h5 class="mb-0"><?php echo $current_year; ?> Reading Goal</h5>
                            </div>
                            <div class="card-body">
                                <?php if ($current_goal): ?>
                                    <?php 
                                    $percentage = getProgressPercentage($current_goal['books_read'], $current_goal['target_books']);
                                    $color = getProgressColor($percentage);
                                    ?>
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h4>Goal: <?php echo $current_goal['target_books']; ?> books</h4>
                                            <h5 class="text-<?php echo $color; ?>">Read: <?php echo $current_goal['books_read']; ?> books</h5>
                                            <div class="progress mb-3" style="height: 20px;">
                                                <div class="progress-bar bg-<?php echo $color; ?>" role="progressbar" 
                                                     style="width: <?php echo $percentage; ?>%" 
                                                     aria-valuenow="<?php echo $percentage; ?>" aria-valuemin="0" aria-valuemax="100">
                                                    <?php echo $percentage; ?>%
                                                </div>
                                            </div>
                                            <p class="text-muted">
                                                <?php 
                                                $remaining = max(0, $current_goal['target_books'] - $current_goal['books_read']);
                                                if ($remaining > 0) {
                                                    echo "You need to read $remaining more books to reach your goal.";
                                                } else {
                                                    echo "Congratulations! You've reached your reading goal!";
                                                }
                                                ?>
                                            </p>
                                        </div>
                                        <div class="col-md-4 text-center">
                                            <div class="progress-circle bg-<?php echo $color; ?> text-white">
                                                <?php echo $percentage; ?>%
                                            </div>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="bi bi-target display-4 text-muted"></i>
                                        <h4 class="mt-3">No goal set for <?php echo $current_year; ?></h4>
                                        <p class="text-muted">Set a reading goal to track your progress!</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Set/Update Goal</h6>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    <div class="mb-3">
                                        <label for="goal_year" class="form-label">Year</label>
                                        <select class="form-select" name="goal_year" id="goal_year" required>
                                            <option value="<?php echo $current_year; ?>" selected><?php echo $current_year; ?></option>
                                            <option value="<?php echo $current_year + 1; ?>"><?php echo $current_year + 1; ?></option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="target_books" class="form-label">Target Books</label>
                                        <input type="number" class="form-control" name="target_books" id="target_books" 
                                               min="1" max="365" value="<?php echo $current_goal ? $current_goal['target_books'] : 12; ?>" required>
                                    </div>
                                    <button type="submit" name="set_goal" class="btn btn-primary w-100">
                                        <i class="bi bi-target me-2"></i>Set Goal
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monthly Progress Chart -->
                <?php if ($current_goal): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Monthly Progress - <?php echo $current_year; ?></h5>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlyChart" width="400" height="100"></canvas>
                    </div>
                </div>
                <?php endif; ?>

                <!-- All Goals History -->
                <?php if (count($all_goals) > 0): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Goal History</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($all_goals as $goal): ?>
                                <?php 
                                $percentage = getProgressPercentage($goal['books_read'], $goal['target_books']);
                                $color = getProgressColor($percentage);
                                ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card goal-card h-100">
                                    <div class="card-body text-center">
                                        <h6><?php echo $goal['goal_year']; ?></h6>
                                        <div class="progress mb-2">
                                            <div class="progress-bar bg-<?php echo $color; ?>" style="width: <?php echo $percentage; ?>%"></div>
                                        </div>
                                        <p class="mb-0"><?php echo $goal['books_read']; ?> / <?php echo $goal['target_books']; ?> books</p>
                                        <small class="text-muted"><?php echo $percentage; ?>% complete</small>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <?php if ($current_goal): ?>
    <script>
        // Monthly progress chart
        const ctx = document.getElementById('monthlyChart').getContext('2d');
        const monthlyChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Books Read',
                    data: <?php echo json_encode(array_values($monthly_books)); ?>,
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
    <?php endif; ?>
</body>
</html>
