<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if member is logged in
if (!isset($_SESSION['member_id'])) {
    header('Location: ../login.php');
    exit;
}

// Get member ID from session
$member_id = $_SESSION['member_id'];

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Handle book return
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['return_book'])) {
    $loan_id = $_POST['loan_id'];
    $rating = isset($_POST['rating']) ? $_POST['rating'] : null;
    $review_text = isset($_POST['review_text']) ? trim($_POST['review_text']) : '';

    try {
        // Start transaction
        $db->beginTransaction();

        // Get loan details
        $query = "SELECT bl.*, b.id as book_id, b.title, b.author
                  FROM book_loans bl
                  JOIN books b ON bl.book_id = b.id
                  WHERE bl.id = :loan_id AND bl.member_id = :member_id AND bl.status = 'borrowed'";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':loan_id', $loan_id);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            throw new Exception('Loan not found or already returned');
        }

        $loan = $stmt->fetch();
        $return_date = date('Y-m-d');

        // Calculate fine if overdue
        $fine = 0;
        if (strtotime($return_date) > strtotime($loan['due_date'])) {
            $fine = calculateFine($loan['due_date'], $return_date);
        }

        // Update loan record
        $query = "UPDATE book_loans
                  SET status = 'returned', return_date = :return_date, fine = :fine
                  WHERE id = :loan_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':return_date', $return_date);
        $stmt->bindParam(':fine', $fine);
        $stmt->bindParam(':loan_id', $loan_id);
        $stmt->execute();

        // Update book available quantity
        $query = "UPDATE books SET available_quantity = available_quantity + 1 WHERE id = :book_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':book_id', $loan['book_id']);
        $stmt->execute();

        // Add review if provided
        if ($rating && $rating >= 1 && $rating <= 5) {
            // Check if review already exists
            $query = "SELECT id FROM book_reviews WHERE book_id = :book_id AND member_id = :member_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':book_id', $loan['book_id']);
            $stmt->bindParam(':member_id', $member_id);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                // Update existing review
                $query = "UPDATE book_reviews
                          SET rating = :rating, review_text = :review_text, review_date = NOW()
                          WHERE book_id = :book_id AND member_id = :member_id";
            } else {
                // Insert new review
                $query = "INSERT INTO book_reviews (book_id, member_id, rating, review_text)
                          VALUES (:book_id, :member_id, :rating, :review_text)";
            }

            $stmt = $db->prepare($query);
            $stmt->bindParam(':book_id', $loan['book_id']);
            $stmt->bindParam(':member_id', $member_id);
            $stmt->bindParam(':rating', $rating);
            $stmt->bindParam(':review_text', $review_text);
            $stmt->execute();
        }

        // Log activity
        logActivity($db, 'return', "Member self-returned: {$loan['title']}", 'book', $loan['book_id']);

        // Commit transaction
        $db->commit();

        $fine_message = $fine > 0 ? " A fine of $" . number_format($fine, 2) . " has been applied." : "";
        $rating_message = $rating ? " Thank you for rating this book!" : "";
        $success_message = "Book '{$loan['title']}' returned successfully!{$fine_message}{$rating_message}";

        // Set session message for dashboard redirect
        $_SESSION['return_success'] = $success_message;

        // Redirect to dashboard if this was a quick return
        if (isset($_POST['quick_return'])) {
            header('Location: member_dashboard.php');
            exit;
        }

    } catch (Exception $e) {
        $db->rollBack();
        $error_message = "Error returning book: " . $e->getMessage();
    }
}

// Get member's current loans
$query = "SELECT bl.*, b.title, b.author, b.isbn, b.cover_image, b.id as book_id
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id AND bl.status = 'borrowed'
          ORDER BY bl.due_date ASC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$current_loans = $stmt->fetchAll();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Return Books - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .book-cover {
            width: 80px;
            height: 120px;
            object-fit: cover;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .loan-card {
            transition: all 0.3s ease;
        }
        .loan-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .overdue {
            border-left: 4px solid #dc3545;
        }
        .due-soon {
            border-left: 4px solid #ffc107;
        }
        .star-rating {
            cursor: pointer;
        }
        .star-rating i {
            font-size: 1.5rem;
            margin-right: 3px;
            color: #ddd;
            transition: color 0.2s;
        }
        .star-rating i.active {
            color: #ffc107;
        }
        .star-rating i:hover {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="member_dashboard.php">Dashboard</a>
                <a class="nav-link" href="../logout.php">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-arrow-return-left me-2"></i>Return Books</h2>
                    <a href="member_dashboard.php" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>

                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i><?php echo h($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-circle me-2"></i><?php echo h($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (count($current_loans) > 0): ?>
                <div class="row">
                    <?php foreach ($current_loans as $loan): ?>
                        <?php
                        $is_overdue = strtotime($loan['due_date']) < time();
                        $due_soon = !$is_overdue && strtotime($loan['due_date']) < strtotime('+3 days');
                        $card_class = $is_overdue ? 'overdue' : ($due_soon ? 'due-soon' : '');
                        $fine = $is_overdue ? calculateFine($loan['due_date']) : 0;
                        ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card loan-card h-100 <?php echo $card_class; ?>">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-4">
                                        <?php if ($loan['cover_image']): ?>
                                            <img src="../<?php echo h($loan['cover_image']); ?>" alt="Cover" class="book-cover">
                                        <?php else: ?>
                                            <div class="book-cover d-flex align-items-center justify-content-center bg-light">
                                                <i class="bi bi-book fs-1 text-secondary"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-8">
                                        <h6 class="card-title"><?php echo h($loan['title']); ?></h6>
                                        <p class="card-text">
                                            <small class="text-muted">by <?php echo h($loan['author']); ?></small><br>
                                            <small><strong>Due:</strong> <?php echo formatDate($loan['due_date']); ?></small>
                                        </p>

                                        <?php if ($is_overdue): ?>
                                        <div class="alert alert-danger alert-sm p-2">
                                            <small><i class="bi bi-exclamation-triangle me-1"></i>Overdue</small>
                                            <?php if ($fine > 0): ?>
                                            <br><small>Fine: $<?php echo number_format($fine, 2); ?></small>
                                            <?php endif; ?>
                                        </div>
                                        <?php elseif ($due_soon): ?>
                                        <div class="alert alert-warning alert-sm p-2">
                                            <small><i class="bi bi-clock me-1"></i>Due Soon</small>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <button class="btn btn-success w-100" data-bs-toggle="modal"
                                        data-bs-target="#returnModal"
                                        onclick="setReturnBook(<?php echo $loan['id']; ?>, '<?php echo addslashes($loan['title']); ?>', '<?php echo addslashes($loan['author']); ?>', <?php echo $loan['book_id']; ?>)">
                                    <i class="bi bi-arrow-return-left me-2"></i>Return Book
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="bi bi-check-circle display-1 text-success"></i>
                    <h4 class="mt-3">No books to return</h4>
                    <p class="text-muted">You don't have any books currently borrowed.</p>
                    <a href="../catalog.php" class="btn btn-primary">
                        <i class="bi bi-search me-2"></i>Browse Books
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Return Modal -->
    <div class="modal fade" id="returnModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Return Book & Rate Your Experience</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="loan_id" id="return_loan_id">

                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle me-2"></i>Returning: <span id="return_book_title"></span></h6>
                            <p class="mb-0">by <span id="return_book_author"></span></p>
                        </div>

                        <div class="mb-4">
                            <h6>How would you rate this book? (Optional)</h6>
                            <div class="star-rating" id="starRating">
                                <i class="bi bi-star" data-rating="1"></i>
                                <i class="bi bi-star" data-rating="2"></i>
                                <i class="bi bi-star" data-rating="3"></i>
                                <i class="bi bi-star" data-rating="4"></i>
                                <i class="bi bi-star" data-rating="5"></i>
                            </div>
                            <input type="hidden" name="rating" id="rating">
                            <small class="text-muted">Click the stars to rate this book</small>
                        </div>

                        <div class="mb-3">
                            <label for="review_text" class="form-label">Write a review (Optional)</label>
                            <textarea class="form-control" name="review_text" id="review_text" rows="3"
                                      placeholder="Share your thoughts about this book..."></textarea>
                        </div>

                        <div class="alert alert-warning">
                            <small><i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>Note:</strong> Once you return this book, it will be available for other members to borrow.
                            Any applicable fines will be calculated automatically.</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="return_book" class="btn btn-success">
                            <i class="bi bi-arrow-return-left me-2"></i>Return Book
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function setReturnBook(loanId, title, author, bookId) {
            document.getElementById('return_loan_id').value = loanId;
            document.getElementById('return_book_title').textContent = title;
            document.getElementById('return_book_author').textContent = author;
            document.getElementById('rating').value = '';
            document.getElementById('review_text').value = '';

            // Reset stars
            document.querySelectorAll('#starRating i').forEach(star => {
                star.className = 'bi bi-star';
            });
        }

        // Star rating functionality
        document.querySelectorAll('#starRating i').forEach(star => {
            star.addEventListener('click', function() {
                const rating = this.getAttribute('data-rating');
                document.getElementById('rating').value = rating;

                // Update star display
                document.querySelectorAll('#starRating i').forEach((s, index) => {
                    if (index < rating) {
                        s.className = 'bi bi-star-fill active';
                    } else {
                        s.className = 'bi bi-star';
                    }
                });
            });

            star.addEventListener('mouseover', function() {
                const rating = this.getAttribute('data-rating');
                document.querySelectorAll('#starRating i').forEach((s, index) => {
                    if (index < rating) {
                        s.className = 'bi bi-star-fill active';
                    } else {
                        s.className = 'bi bi-star';
                    }
                });
            });
        });

        // Reset stars on mouse leave
        document.getElementById('starRating').addEventListener('mouseleave', function() {
            const currentRating = document.getElementById('rating').value;
            document.querySelectorAll('#starRating i').forEach((s, index) => {
                if (index < currentRating) {
                    s.className = 'bi bi-star-fill active';
                } else {
                    s.className = 'bi bi-star';
                }
            });
        });
    </script>
</body>
</html>
