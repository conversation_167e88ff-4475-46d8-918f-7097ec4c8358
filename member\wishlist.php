<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if member is logged in
if (!isset($_SESSION['member_id'])) {
    header('Location: ../login.php');
    exit;
}

// Get member ID from session
$member_id = $_SESSION['member_id'];

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Handle wishlist actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_to_wishlist'])) {
        $book_id = $_POST['book_id'];
        
        // Check if book is already in wishlist
        $query = "SELECT * FROM member_wishlist WHERE member_id = :member_id AND book_id = :book_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->bindParam(':book_id', $book_id);
        $stmt->execute();
        
        if ($stmt->rowCount() == 0) {
            // Add to wishlist
            $query = "INSERT INTO member_wishlist (member_id, book_id, added_date) VALUES (:member_id, :book_id, NOW())";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':member_id', $member_id);
            $stmt->bindParam(':book_id', $book_id);
            $stmt->execute();
            
            $success_message = "Book added to your wishlist!";
        } else {
            $error_message = "Book is already in your wishlist.";
        }
    } elseif (isset($_POST['remove_from_wishlist'])) {
        $book_id = $_POST['book_id'];
        
        $query = "DELETE FROM member_wishlist WHERE member_id = :member_id AND book_id = :book_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->bindParam(':book_id', $book_id);
        $stmt->execute();
        
        $success_message = "Book removed from your wishlist.";
    }
}

// Get wishlist items
$query = "SELECT mw.*, b.title, b.author, b.isbn, b.cover_image, b.available_quantity, b.category
          FROM member_wishlist mw
          JOIN books b ON mw.book_id = b.id
          WHERE mw.member_id = :member_id
          ORDER BY mw.added_date DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$wishlist_items = $stmt->fetchAll();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Wishlist - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .book-cover {
            width: 80px;
            height: 120px;
            object-fit: cover;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .wishlist-card {
            transition: all 0.3s ease;
        }
        .wishlist-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="member_dashboard.php">Dashboard</a>
                <a class="nav-link" href="../logout.php">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-heart me-2"></i>My Wishlist</h2>
                    <a href="member_dashboard.php" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>

                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo h($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo h($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (count($wishlist_items) > 0): ?>
                <div class="row">
                    <?php foreach ($wishlist_items as $item): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card wishlist-card h-100">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-4">
                                        <?php if ($item['cover_image']): ?>
                                            <img src="../<?php echo h($item['cover_image']); ?>" alt="Cover" class="book-cover">
                                        <?php else: ?>
                                            <div class="book-cover d-flex align-items-center justify-content-center bg-light">
                                                <i class="bi bi-book fs-1 text-secondary"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-8">
                                        <h6 class="card-title"><?php echo h($item['title']); ?></h6>
                                        <p class="card-text">
                                            <small class="text-muted">by <?php echo h($item['author']); ?></small><br>
                                            <span class="badge bg-secondary"><?php echo h($item['category']); ?></span>
                                        </p>
                                        <p class="card-text">
                                            <?php if ($item['available_quantity'] > 0): ?>
                                                <span class="text-success">
                                                    <i class="bi bi-check-circle"></i> Available
                                                </span>
                                            <?php else: ?>
                                                <span class="text-warning">
                                                    <i class="bi bi-clock"></i> Not Available
                                                </span>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="d-flex justify-content-between">
                                    <a href="../book_details.php?id=<?php echo $item['book_id']; ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> View Details
                                    </a>
                                    <form method="post" class="d-inline">
                                        <input type="hidden" name="book_id" value="<?php echo $item['book_id']; ?>">
                                        <button type="submit" name="remove_from_wishlist" class="btn btn-sm btn-outline-danger">
                                            <i class="bi bi-trash"></i> Remove
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="bi bi-heart display-1 text-muted"></i>
                    <h4 class="mt-3">Your wishlist is empty</h4>
                    <p class="text-muted">Start adding books you'd like to read!</p>
                    <a href="../catalog.php" class="btn btn-primary">
                        <i class="bi bi-search me-2"></i>Browse Books
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
