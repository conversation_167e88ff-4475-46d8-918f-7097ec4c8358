<?php
/**
 * Member Activity Log
 */
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and has appropriate role
if (!isset($_SESSION['user_id']) && !isset($_SESSION['member_id'])) {
    redirect(url('login.php'));
}

// Check if user has permission (admin or librarian)
if (isset($_SESSION['role']) && !in_array($_SESSION['role'], ['admin', 'librarian'])) {
    redirect(url('index.php'));
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Get member ID if specified
$member_id = isset($_GET['member_id']) ? (int)$_GET['member_id'] : 0;
$member_info = null;

if ($member_id > 0) {
    // Get member information
    $query = "SELECT * FROM members WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $member_id);
    $stmt->execute();
    $member_info = $stmt->fetch();
    
    if (!$member_info) {
        redirect(url('members/index.php'));
    }
}

// Pagination
$records_per_page = 20;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $records_per_page;

// Build query for activity log
if ($member_id > 0) {
    // Member-specific activity
    $count_query = "SELECT COUNT(*) as total FROM (
        SELECT 'loan' as activity_type, issue_date as activity_date, 'Book borrowed' as description, b.title as details
        FROM book_loans bl 
        JOIN books b ON bl.book_id = b.id 
        WHERE bl.member_id = :member_id
        
        UNION ALL
        
        SELECT 'return' as activity_type, return_date as activity_date, 'Book returned' as description, b.title as details
        FROM book_loans bl 
        JOIN books b ON bl.book_id = b.id 
        WHERE bl.member_id = :member_id AND bl.return_date IS NOT NULL
        
        UNION ALL
        
        SELECT 'reservation' as activity_type, created_at as activity_date, 'Book reserved' as description, b.title as details
        FROM book_reservations br 
        JOIN books b ON br.book_id = b.id 
        WHERE br.member_id = :member_id
    ) as activities";
    
    $stmt = $db->prepare($count_query);
    $stmt->bindParam(':member_id', $member_id);
    $stmt->execute();
    $total_records = $stmt->fetch()['total'];
    
    $activities_query = "SELECT * FROM (
        SELECT 'loan' as activity_type, issue_date as activity_date, 'Book borrowed' as description, b.title as details, bl.due_date
        FROM book_loans bl 
        JOIN books b ON bl.book_id = b.id 
        WHERE bl.member_id = :member_id
        
        UNION ALL
        
        SELECT 'return' as activity_type, return_date as activity_date, 'Book returned' as description, b.title as details, NULL as due_date
        FROM book_loans bl 
        JOIN books b ON bl.book_id = b.id 
        WHERE bl.member_id = :member_id AND bl.return_date IS NOT NULL
        
        UNION ALL
        
        SELECT 'reservation' as activity_type, created_at as activity_date, 'Book reserved' as description, b.title as details, NULL as due_date
        FROM book_reservations br 
        JOIN books b ON br.book_id = b.id 
        WHERE br.member_id = :member_id
    ) as activities 
    ORDER BY activity_date DESC 
    LIMIT :offset, :records_per_page";
    
} else {
    // All member activities
    $count_query = "SELECT COUNT(*) as total FROM (
        SELECT bl.issue_date as activity_date
        FROM book_loans bl 
        
        UNION ALL
        
        SELECT bl.return_date as activity_date
        FROM book_loans bl 
        WHERE bl.return_date IS NOT NULL
        
        UNION ALL
        
        SELECT br.created_at as activity_date
        FROM book_reservations br
    ) as activities";
    
    $stmt = $db->prepare($count_query);
    $stmt->execute();
    $total_records = $stmt->fetch()['total'];
    
    $activities_query = "SELECT * FROM (
        SELECT 'loan' as activity_type, bl.issue_date as activity_date, 'Book borrowed' as description, 
               CONCAT(m.first_name, ' ', m.last_name) as member_name, b.title as details, bl.due_date, m.id as member_id
        FROM book_loans bl 
        JOIN books b ON bl.book_id = b.id 
        JOIN members m ON bl.member_id = m.id
        
        UNION ALL
        
        SELECT 'return' as activity_type, bl.return_date as activity_date, 'Book returned' as description, 
               CONCAT(m.first_name, ' ', m.last_name) as member_name, b.title as details, NULL as due_date, m.id as member_id
        FROM book_loans bl 
        JOIN books b ON bl.book_id = b.id 
        JOIN members m ON bl.member_id = m.id
        WHERE bl.return_date IS NOT NULL
        
        UNION ALL
        
        SELECT 'reservation' as activity_type, br.created_at as activity_date, 'Book reserved' as description, 
               CONCAT(m.first_name, ' ', m.last_name) as member_name, b.title as details, NULL as due_date, m.id as member_id
        FROM book_reservations br 
        JOIN books b ON br.book_id = b.id 
        JOIN members m ON br.member_id = m.id
    ) as activities 
    ORDER BY activity_date DESC 
    LIMIT :offset, :records_per_page";
}

$stmt = $db->prepare($activities_query);
if ($member_id > 0) {
    $stmt->bindParam(':member_id', $member_id);
}
$stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
$stmt->bindParam(':records_per_page', $records_per_page, PDO::PARAM_INT);
$stmt->execute();
$activities = $stmt->fetchAll();

$total_pages = ceil($total_records / $records_per_page);

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function getActivityIcon($type) {
    switch ($type) {
        case 'loan': return 'bi-book';
        case 'return': return 'bi-arrow-return-left';
        case 'reservation': return 'bi-bookmark';
        default: return 'bi-circle';
    }
}

function getActivityColor($type) {
    switch ($type) {
        case 'loan': return 'text-primary';
        case 'return': return 'text-success';
        case 'reservation': return 'text-warning';
        default: return 'text-muted';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $member_info ? 'Activity Log - ' . $member_info['first_name'] . ' ' . $member_info['last_name'] : 'Member Activity Log'; ?> - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <?php if ($member_info): ?>
                            Activity Log - <?php echo h($member_info['first_name'] . ' ' . $member_info['last_name']); ?>
                        <?php else: ?>
                            Member Activity Log
                        <?php endif; ?>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <?php if ($member_info): ?>
                            <a href="view.php?id=<?php echo $member_id; ?>" class="btn btn-sm btn-outline-secondary me-2">
                                <i class="bi bi-arrow-left"></i> Back to Member
                            </a>
                        <?php endif; ?>
                        <a href="index.php" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-people"></i> All Members
                        </a>
                    </div>
                </div>

                <?php if ($member_info): ?>
                    <!-- Member Info Card -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h5><?php echo h($member_info['first_name'] . ' ' . $member_info['last_name']); ?></h5>
                                    <p class="text-muted mb-0">
                                        <i class="bi bi-envelope me-2"></i><?php echo h($member_info['email']); ?>
                                        <?php if ($member_info['phone']): ?>
                                            <span class="ms-3"><i class="bi bi-telephone me-2"></i><?php echo h($member_info['phone']); ?></span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <div class="col-md-4 text-md-end">
                                    <span class="badge bg-<?php echo $member_info['membership_status'] === 'active' ? 'success' : ($member_info['membership_status'] === 'inactive' ? 'secondary' : 'danger'); ?>">
                                        <?php echo ucfirst($member_info['membership_status']); ?>
                                    </span>
                                    <p class="text-muted small mb-0 mt-1">
                                        Member since <?php echo date('M j, Y', strtotime($member_info['membership_date'])); ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Activity Timeline -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history me-2"></i>Activity Timeline
                            <span class="badge bg-secondary ms-2"><?php echo number_format($total_records); ?> activities</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (count($activities) > 0): ?>
                            <div class="timeline">
                                <?php foreach ($activities as $activity): ?>
                                    <div class="timeline-item mb-3">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <div class="timeline-marker bg-light border rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                    <i class="<?php echo getActivityIcon($activity['activity_type']); ?> <?php echo getActivityColor($activity['activity_type']); ?>"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <div class="card">
                                                    <div class="card-body py-2">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div>
                                                                <h6 class="mb-1"><?php echo h($activity['description']); ?></h6>
                                                                <p class="mb-1"><strong><?php echo h($activity['details']); ?></strong></p>
                                                                <?php if (!$member_info && isset($activity['member_name'])): ?>
                                                                    <p class="mb-1 text-muted">
                                                                        <i class="bi bi-person me-1"></i>
                                                                        <a href="view.php?id=<?php echo $activity['member_id']; ?>">
                                                                            <?php echo h($activity['member_name']); ?>
                                                                        </a>
                                                                    </p>
                                                                <?php endif; ?>
                                                                <?php if ($activity['activity_type'] === 'loan' && isset($activity['due_date'])): ?>
                                                                    <p class="mb-0 text-muted small">
                                                                        <i class="bi bi-calendar me-1"></i>
                                                                        Due: <?php echo date('M j, Y', strtotime($activity['due_date'])); ?>
                                                                    </p>
                                                                <?php endif; ?>
                                                            </div>
                                                            <div class="text-end">
                                                                <small class="text-muted">
                                                                    <?php echo date('M j, Y', strtotime($activity['activity_date'])); ?><br>
                                                                    <?php echo date('g:i A', strtotime($activity['activity_date'])); ?>
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="bi bi-clock-history display-1 text-muted"></i>
                                <h4 class="mt-3">No Activity Found</h4>
                                <p class="text-muted">
                                    <?php if ($member_info): ?>
                                        This member hasn't performed any library activities yet.
                                    <?php else: ?>
                                        No member activities recorded in the system.
                                    <?php endif; ?>
                                </p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Activity pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?<?php echo $member_id > 0 ? 'member_id=' . $member_id . '&' : ''; ?>page=<?php echo $page - 1; ?>">Previous</a>
                            </li>
                            
                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?<?php echo $member_id > 0 ? 'member_id=' . $member_id . '&' : ''; ?>page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>
                            
                            <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?<?php echo $member_id > 0 ? 'member_id=' . $member_id . '&' : ''; ?>page=<?php echo $page + 1; ?>">Next</a>
                            </li>
                        </ul>
                    </nav>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        .timeline-marker {
            position: relative;
        }
        .timeline-item:not(:last-child) .timeline-marker::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            width: 2px;
            height: 30px;
            background-color: #dee2e6;
        }
    </style>
</body>
</html>
