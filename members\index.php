<?php
session_start();

// Include required files with error handling
try {
    require_once '../config/database.php';
    require_once '../config/config.php';
    require_once '../includes/functions.php';
} catch (Exception $e) {
    die('Error loading required files: ' . $e->getMessage());
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Set up pagination
$records_per_page = 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $records_per_page;

// Get search term if any
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Count total records for pagination
if (!empty($search)) {
    $query = "SELECT COUNT(*) as total FROM members
              WHERE first_name LIKE :search OR last_name LIKE :search OR email LIKE :search";
    $stmt = $db->prepare($query);
    $search_term = "%{$search}%";
    $stmt->bindParam(':search', $search_term);
} else {
    $query = "SELECT COUNT(*) as total FROM members";
    $stmt = $db->prepare($query);
}
$stmt->execute();
$total_rows = $stmt->fetch()['total'];
$total_pages = ceil($total_rows / $records_per_page);

// Get members with pagination
if (!empty($search)) {
    $query = "SELECT * FROM members
              WHERE first_name LIKE :search OR last_name LIKE :search OR email LIKE :search
              ORDER BY last_name, first_name ASC LIMIT :offset, :records_per_page";
    $stmt = $db->prepare($query);
    $search_term = "%{$search}%";
    $stmt->bindParam(':search', $search_term);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':records_per_page', $records_per_page, PDO::PARAM_INT);
} else {
    $query = "SELECT * FROM members ORDER BY last_name, first_name ASC LIMIT :offset, :records_per_page";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':records_per_page', $records_per_page, PDO::PARAM_INT);
}
$stmt->execute();
$members = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Members - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Members</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2" role="group">
                            <a href="advanced_search.php" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-search"></i> Advanced Search
                            </a>
                            <a href="statistics.php" class="btn btn-sm btn-outline-info">
                                <i class="bi bi-graph-up"></i> Statistics
                            </a>
                            <a href="bulk_operations.php" class="btn btn-sm btn-outline-warning">
                                <i class="bi bi-gear"></i> Bulk Operations
                            </a>
                            <a href="activity_log.php" class="btn btn-sm btn-outline-success">
                                <i class="bi bi-clock-history"></i> Activity Log
                            </a>
                            <a href="quick_actions.php" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-lightning"></i> Quick Actions
                            </a>
                        </div>
                        <a href="add.php" class="btn btn-sm btn-primary">
                            <i class="bi bi-plus-lg"></i> Add New Member
                        </a>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- Search Form -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" method="get" class="d-flex">
                            <input type="text" name="search" id="searchInput" class="form-control me-2" placeholder="Search by name or email" value="<?php echo $search; ?>">
                            <button type="submit" class="btn btn-primary">Search</button>
                            <?php if (!empty($search)): ?>
                                <a href="index.php" class="btn btn-secondary ms-2">Clear</a>
                            <?php endif; ?>
                        </form>
                    </div>
                </div>

                <!-- Members Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Membership Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (count($members) > 0): ?>
                                <?php foreach ($members as $member): ?>
                                <tr>
                                    <td><?php echo $member['id']; ?></td>
                                    <td><?php echo $member['first_name'] . ' ' . $member['last_name']; ?></td>
                                    <td><?php echo $member['email']; ?></td>
                                    <td><?php echo $member['phone']; ?></td>
                                    <td><?php echo formatDate($member['membership_date']); ?></td>
                                    <td>
                                        <?php if ($member['membership_status'] === 'active'): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php elseif ($member['membership_status'] === 'inactive'): ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Suspended</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="view.php?id=<?php echo $member['id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View Details">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="edit.php?id=<?php echo $member['id']; ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="delete.php?id=<?php echo $member['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="Delete">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center">No members found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo $page <= 1 ? '#' : '?page='.($page-1).(!empty($search) ? '&search='.$search : ''); ?>">Previous</a>
                        </li>

                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search='.$search : ''; ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo $page >= $total_pages ? '#' : '?page='.($page+1).(!empty($search) ? '&search='.$search : ''); ?>">Next</a>
                        </li>
                    </ul>
                </nav>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
</body>
</html>
