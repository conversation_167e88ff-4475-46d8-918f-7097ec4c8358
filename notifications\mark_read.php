<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    // Return error response for AJAX requests
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
        header('HTTP/1.1 401 Unauthorized');
        exit;
    }
    redirect('../login.php');
}

// Check if notification ID was provided
if (!isset($_POST['notification_id']) || !is_numeric($_POST['notification_id'])) {
    // Return error response for AJAX requests
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
        header('HTTP/1.1 400 Bad Request');
        exit;
    }
    setMessage('Invalid notification ID', 'danger');
    redirect('index.php');
}

$notification_id = $_POST['notification_id'];

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Mark notification as read
$query = "UPDATE notifications 
          SET is_read = 1 
          WHERE id = :id AND (user_id = :user_id OR user_id IS NULL)";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $notification_id);
$stmt->bindParam(':user_id', $_SESSION['user_id']);
$result = $stmt->execute();

// Log activity
if ($result) {
    logActivity($db, 'mark_read', 'Marked notification as read', 'notification', $notification_id);
}

// Return success response for AJAX requests
if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
    header('Content-Type: application/json');
    echo json_encode(['success' => $result]);
    exit;
}

// Set message and redirect for non-AJAX requests
if ($result) {
    setMessage('Notification marked as read', 'success');
} else {
    setMessage('Failed to mark notification as read', 'danger');
}

// Redirect back to referring page or notifications page
$redirect_to = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'index.php';
redirect($redirect_to);
