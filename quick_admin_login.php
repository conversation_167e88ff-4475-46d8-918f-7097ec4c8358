<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get or create admin user
    $query = "SELECT * FROM users WHERE role = 'admin' LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if (!$admin) {
        // Create admin user if none exists
        $username = 'admin';
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        $email = '<EMAIL>';
        
        $create_query = "INSERT INTO users (username, password, email, role, created_at) VALUES (:username, :password, :email, 'admin', NOW())";
        $create_stmt = $db->prepare($create_query);
        $create_stmt->bindParam(':username', $username);
        $create_stmt->bindParam(':password', $password);
        $create_stmt->bindParam(':email', $email);
        $create_stmt->execute();
        
        // Get the newly created admin
        $stmt->execute();
        $admin = $stmt->fetch();
    }
    
    if ($admin) {
        // Set session variables for admin login
        $_SESSION['user_id'] = $admin['id'];
        $_SESSION['username'] = $admin['username'];
        $_SESSION['role'] = $admin['role'];
        $_SESSION['logged_in'] = true;
        
        // Log the activity
        logActivity($db, 'login', 'Quick admin login via bypass');
        
        // Redirect to admin dashboard
        header('Location: admin/dashboard.php');
        exit;
    } else {
        echo "Failed to create or find admin user.";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
