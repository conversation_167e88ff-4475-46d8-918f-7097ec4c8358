<?php
/**
 * Quick Dashboard Fix
 * Fixes common dashboard access issues
 */

session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Quick Dashboard Fix</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h1><i class='bi bi-wrench me-2'></i>Quick Dashboard Fix</h1>
    <p class='text-muted'>Applying quick fixes to ensure dashboard accessibility...</p>";

$fixes_applied = [];
$errors = [];

// Fix 1: Ensure database connection
try {
    $database = new Database();
    $db = $database->getConnection();
    echo "<div class='alert alert-success'>
        <i class='bi bi-check-circle-fill me-2'></i>Database connection successful
    </div>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
        <i class='bi bi-x-circle-fill me-2'></i>Database connection failed: " . $e->getMessage() . "
    </div>";
    $errors[] = "Database connection failed";
}

// Fix 2: Check and create admin user if needed
if (isset($db)) {
    try {
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
        $stmt->execute();
        $admin_count = $stmt->fetch()['count'];
        
        if ($admin_count == 0) {
            // Create default admin user
            $admin_username = 'admin';
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $admin_email = '<EMAIL>';
            
            $query = "INSERT INTO users (username, password, email, role, created_at) VALUES (?, ?, ?, 'admin', NOW())";
            $stmt = $db->prepare($query);
            $stmt->execute([$admin_username, $admin_password, $admin_email]);
            
            echo "<div class='alert alert-success'>
                <i class='bi bi-check-circle-fill me-2'></i>Created default admin user (username: admin, password: admin123)
            </div>";
            $fixes_applied[] = "Created default admin user";
        } else {
            echo "<div class='alert alert-info'>
                <i class='bi bi-info-circle-fill me-2'></i>Admin user already exists ($admin_count found)
            </div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-warning'>
            <i class='bi bi-exclamation-triangle-fill me-2'></i>Could not check/create admin user: " . $e->getMessage() . "
        </div>";
    }
}

// Fix 3: Check and create required directories
$required_dirs = ['uploads', 'uploads/covers', 'uploads/members', 'uploads/profiles', 'logs'];
foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "<div class='alert alert-success'>
                <i class='bi bi-check-circle-fill me-2'></i>Created directory: $dir
            </div>";
            $fixes_applied[] = "Created directory $dir";
        } else {
            echo "<div class='alert alert-warning'>
                <i class='bi bi-exclamation-triangle-fill me-2'></i>Could not create directory: $dir
            </div>";
        }
    }
}

// Fix 4: Test dashboard file accessibility
$dashboard_files = [
    'admin/dashboard.php' => 'Admin Dashboard',
    'librarian/dashboard.php' => 'Librarian Dashboard',
    'member/member_dashboard.php' => 'Member Dashboard'
];

echo "<div class='card mt-4'>
<div class='card-header'>
    <h5><i class='bi bi-speedometer2 me-2'></i>Dashboard File Status</h5>
</div>
<div class='card-body'>";

foreach ($dashboard_files as $file => $name) {
    if (file_exists($file)) {
        echo "<p class='text-success'><i class='bi bi-check-circle-fill me-2'></i>$name: Available</p>";
    } else {
        echo "<p class='text-danger'><i class='bi bi-x-circle-fill me-2'></i>$name: Missing</p>";
        $errors[] = "$name file is missing";
    }
}

echo "</div></div>";

// Fix 5: Auto-login for testing (temporary)
if (isset($db)) {
    echo "<div class='card mt-4'>
    <div class='card-header'>
        <h5><i class='bi bi-key me-2'></i>Quick Access Setup</h5>
    </div>
    <div class='card-body'>";
    
    try {
        // Get admin user for auto-login
        $stmt = $db->prepare("SELECT * FROM users WHERE role = 'admin' LIMIT 1");
        $stmt->execute();
        $admin = $stmt->fetch();
        
        if ($admin) {
            $_SESSION['user_id'] = $admin['id'];
            $_SESSION['username'] = $admin['username'];
            $_SESSION['role'] = $admin['role'];
            $_SESSION['logged_in'] = true;
            
            echo "<div class='alert alert-success'>
                <i class='bi bi-check-circle-fill me-2'></i>Auto-logged in as admin for testing
            </div>";
            $fixes_applied[] = "Auto-login setup for testing";
        }
        
        // Get member for testing
        $stmt = $db->prepare("SELECT * FROM members LIMIT 1");
        $stmt->execute();
        $member = $stmt->fetch();
        
        if ($member) {
            echo "<div class='alert alert-info'>
                <i class='bi bi-info-circle-fill me-2'></i>Member account available for testing: {$member['email']}
            </div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-warning'>
            <i class='bi bi-exclamation-triangle-fill me-2'></i>Could not setup auto-login: " . $e->getMessage() . "
        </div>";
    }
    
    echo "</div></div>";
}

// Summary
echo "<div class='card mt-4'>
<div class='card-header'>
    <h5><i class='bi bi-clipboard-check me-2'></i>Fix Summary</h5>
</div>
<div class='card-body'>";

if (!empty($fixes_applied)) {
    echo "<h6 class='text-success'>Fixes Applied:</h6><ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ul>";
}

if (!empty($errors)) {
    echo "<h6 class='text-danger'>Issues Found:</h6><ul>";
    foreach ($errors as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
}

if (empty($errors)) {
    echo "<div class='alert alert-success'>
        <h5><i class='bi bi-check-circle-fill me-2'></i>All Systems Ready!</h5>
        <p>Your dashboards should now be accessible. Use the links below to test them.</p>
    </div>";
}

echo "</div></div>";

// Quick access buttons
echo "<div class='row mt-4'>";
foreach ($dashboard_files as $file => $name) {
    if (file_exists($file)) {
        $btn_class = strpos($file, 'admin') !== false ? 'btn-primary' : 
                    (strpos($file, 'librarian') !== false ? 'btn-success' : 'btn-info');
        
        echo "<div class='col-md-4 mb-3'>
            <div class='card'>
                <div class='card-body text-center'>
                    <h6 class='card-title'>$name</h6>
                    <a href='$file' class='btn $btn_class' target='_blank'>
                        <i class='bi bi-box-arrow-up-right me-1'></i>Open Dashboard
                    </a>
                </div>
            </div>
        </div>";
    }
}
echo "</div>";

echo "<div class='text-center mt-4'>
    <a href='dashboard_access_verification.php' class='btn btn-secondary me-2'>
        <i class='bi bi-clipboard-check me-1'></i>Verify Access
    </a>
    <a href='system_comprehensive_fix.php' class='btn btn-warning'>
        <i class='bi bi-tools me-1'></i>Full System Check
    </a>
</div>";

echo "</div>
</body>
</html>";
?>
