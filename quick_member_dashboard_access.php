<?php
session_start();

// Quick access - simulate member login for testing
if (isset($_GET['quick_access']) && $_GET['quick_access'] === 'test') {
    try {
        require_once 'config/database.php';
        $database = new Database();
        $db = $database->getConnection();
        
        // Get any member from database for testing
        $query = "SELECT * FROM members LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $member = $stmt->fetch();
        
        if ($member) {
            $_SESSION['member_id'] = $member['id'];
            $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
            $_SESSION['member_email'] = $member['email'];
            
            // Redirect to member dashboard
            header('Location: member_dashboard.php');
            exit;
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Member Dashboard Access</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #28a745, #20c997); min-height: 100vh; }
        .access-container { max-width: 600px; margin: 3rem auto; }
        .access-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); overflow: hidden; }
        .access-header { background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 2rem; text-align: center; }
        .access-body { padding: 2rem; }
    </style>
</head>
<body>
    <div class="access-container">
        <div class="access-card">
            <div class="access-header">
                <h2><i class="bi bi-speedometer2 me-2"></i>Quick Member Dashboard Access</h2>
                <p class="mb-0">Multiple ways to access your member dashboard</p>
            </div>
            <div class="access-body">
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-circle me-2"></i>Error: <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <div class="row g-3">
                    <div class="col-12">
                        <h5><i class="bi bi-1-circle me-2"></i>Direct Dashboard Access</h5>
                        <p class="text-muted">Try these direct links to the member dashboard:</p>
                        <div class="d-grid gap-2">
                            <a href="member_dashboard.php" class="btn btn-primary">
                                <i class="bi bi-house me-2"></i>Root Member Dashboard
                            </a>
                            <a href="member/member_dashboard.php" class="btn btn-success">
                                <i class="bi bi-folder me-2"></i>Member Directory Dashboard
                            </a>
                        </div>
                    </div>

                    <div class="col-12">
                        <hr>
                        <h5><i class="bi bi-2-circle me-2"></i>Login First</h5>
                        <p class="text-muted">If the above doesn't work, you need to login first:</p>
                        <div class="d-grid gap-2">
                            <a href="simple_member_login.php" class="btn btn-info">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Simple Member Login
                            </a>
                            <a href="login.php" class="btn btn-outline-info">
                                <i class="bi bi-person-check me-2"></i>Regular Login Page
                            </a>
                        </div>
                    </div>

                    <div class="col-12">
                        <hr>
                        <h5><i class="bi bi-3-circle me-2"></i>Quick Test Access</h5>
                        <p class="text-muted">For testing purposes only:</p>
                        <div class="d-grid gap-2">
                            <a href="?quick_access=test" class="btn btn-warning">
                                <i class="bi bi-lightning me-2"></i>Quick Test Login (Auto-login with any member)
                            </a>
                        </div>
                    </div>

                    <div class="col-12">
                        <hr>
                        <h5><i class="bi bi-4-circle me-2"></i>Debug & Troubleshoot</h5>
                        <p class="text-muted">If nothing works, use these tools:</p>
                        <div class="d-grid gap-2">
                            <a href="debug_member_dashboard.php" class="btn btn-outline-danger">
                                <i class="bi bi-bug me-2"></i>Debug Tool
                            </a>
                            <a href="test_member_access.php" class="btn btn-outline-secondary">
                                <i class="bi bi-tools me-2"></i>Access Test Page
                            </a>
                        </div>
                    </div>
                </div>

                <div class="mt-4 p-3 bg-light rounded">
                    <h6><i class="bi bi-info-circle me-2"></i>Current Status</h6>
                    <?php if (isset($_SESSION['member_id'])): ?>
                        <div class="text-success">
                            ✅ You are logged in as: <?php echo $_SESSION['member_name']; ?>
                            <br><a href="member_dashboard.php" class="btn btn-sm btn-success mt-2">Go to Dashboard</a>
                        </div>
                    <?php else: ?>
                        <div class="text-warning">
                            ⚠️ You are not logged in as a member. Please login first.
                        </div>
                    <?php endif; ?>
                </div>

                <div class="mt-3 text-center">
                    <small class="text-muted">
                        <a href="index.php">← Back to Home</a> | 
                        <a href="admin/dashboard.php">Admin Dashboard</a> | 
                        <a href="librarian/dashboard.php">Librarian Dashboard</a>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
