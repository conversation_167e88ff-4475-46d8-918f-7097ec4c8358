<?php
/**
 * Reduce Member Count and Balance Dashboard
 * This script reduces the member count to around 1000 and rebalances loan data
 */

require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Set execution time limit
set_time_limit(300); // 5 minutes

echo "<h1>🔧 Reducing Members and Balancing Dashboard</h1>";
echo "<p><strong>⏱️ This may take a few minutes...</strong></p>";

// Get current statistics
echo "<h2>📊 Current Statistics</h2>";
$stats_query = "
    SELECT
        (SELECT COUNT(*) FROM members) as total_members,
        (SELECT COUNT(*) FROM books) as total_books,
        (SELECT SUM(available_quantity) FROM books) as available_books,
        (SELECT COUNT(*) FROM book_loans) as total_loans,
        (SELECT COUNT(*) FROM book_loans WHERE status = 'borrowed') as active_loans,
        (SELECT COUNT(*) FROM book_loans WHERE status = 'overdue') as overdue_loans,
        (SELECT COUNT(*) FROM book_loans WHERE status = 'returned') as returned_loans
";
$stats_stmt = $db->prepare($stats_query);
$stats_stmt->execute();
$current_stats = $stats_stmt->fetch();

echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>Before Reduction:</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;'>";
echo "<div><strong>👥 Members:</strong> {$current_stats['total_members']}</div>";
echo "<div><strong>📚 Books:</strong> {$current_stats['total_books']}</div>";
echo "<div><strong>📖 Available:</strong> {$current_stats['available_books']}</div>";
echo "<div><strong>📋 Total Loans:</strong> {$current_stats['total_loans']}</div>";
echo "<div><strong>✅ Active:</strong> {$current_stats['active_loans']}</div>";
echo "<div><strong>⚠️ Overdue:</strong> {$current_stats['overdue_loans']}</div>";
echo "<div><strong>📚 Returned:</strong> {$current_stats['returned_loans']}</div>";
echo "</div>";
echo "</div>";

// Target member count
$target_members = 1000;

if ($current_stats['total_members'] <= $target_members) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ No Reduction Needed</h3>";
    echo "<p>Current member count ({$current_stats['total_members']}) is already at or below target ({$target_members}).</p>";
    echo "</div>";
    exit;
}

echo "<h2>🎯 Target: Reduce to {$target_members} members</h2>";

// Start transaction
$db->beginTransaction();

try {
    // Step 1: Identify members to keep (first 1000 by ID to maintain consistency)
    echo "<h3>Step 1: Selecting members to keep...</h3>";
    
    $keep_members_query = "SELECT id FROM members ORDER BY id LIMIT {$target_members}";
    $keep_stmt = $db->prepare($keep_members_query);
    $keep_stmt->execute();
    $members_to_keep = $keep_stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p>✅ Selected {$target_members} members to keep (IDs: " . min($members_to_keep) . " to " . max($members_to_keep) . ")</p>";
    
    // Step 2: Delete loans for members that will be removed
    echo "<h3>Step 2: Removing loans for excess members...</h3>";
    
    $members_to_keep_list = implode(',', $members_to_keep);
    $delete_loans_query = "DELETE FROM book_loans WHERE member_id NOT IN ({$members_to_keep_list})";
    $delete_loans_stmt = $db->prepare($delete_loans_query);
    $delete_loans_stmt->execute();
    $deleted_loans = $delete_loans_stmt->rowCount();
    
    echo "<p>🗑️ Removed {$deleted_loans} loans from excess members</p>";
    
    // Step 3: Delete excess members
    echo "<h3>Step 3: Removing excess members...</h3>";
    
    $delete_members_query = "DELETE FROM members WHERE id NOT IN ({$members_to_keep_list})";
    $delete_members_stmt = $db->prepare($delete_members_query);
    $delete_members_stmt->execute();
    $deleted_members = $delete_members_stmt->rowCount();
    
    echo "<p>🗑️ Removed {$deleted_members} excess members</p>";
    
    // Step 4: Update overdue statuses
    echo "<h3>Step 4: Updating loan statuses...</h3>";
    
    $overdue_query = "UPDATE book_loans SET status = 'overdue' WHERE status = 'borrowed' AND due_date < CURDATE()";
    $overdue_stmt = $db->prepare($overdue_query);
    $overdue_stmt->execute();
    $overdue_updated = $overdue_stmt->rowCount();
    
    echo "<p>📅 Updated {$overdue_updated} loans to overdue status</p>";
    
    // Step 5: Calculate fines for overdue books
    echo "<h3>Step 5: Calculating fines...</h3>";
    
    $fine_query = "
        UPDATE book_loans
        SET fine = DATEDIFF(CURDATE(), due_date) * 1.00
        WHERE status = 'overdue' AND due_date < CURDATE()
    ";
    $fine_stmt = $db->prepare($fine_query);
    $fine_stmt->execute();
    $fines_calculated = $fine_stmt->rowCount();
    
    echo "<p>💰 Calculated fines for {$fines_calculated} overdue books</p>";
    
    // Commit transaction
    $db->commit();
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ Reduction Complete!</h3>";
    echo "<p>Successfully reduced member count and rebalanced the dashboard.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    $db->rollBack();
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error During Reduction</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
    exit;
}

// Get final statistics
echo "<h2>📊 Final Statistics</h2>";
$final_stats_stmt = $db->prepare($stats_query);
$final_stats_stmt->execute();
$final_stats = $final_stats_stmt->fetch();

echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
echo "<h3>After Reduction:</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;'>";
echo "<div><strong>👥 Members:</strong> {$final_stats['total_members']}</div>";
echo "<div><strong>📚 Books:</strong> {$final_stats['total_books']}</div>";
echo "<div><strong>📖 Available:</strong> {$final_stats['available_books']}</div>";
echo "<div><strong>📋 Total Loans:</strong> {$final_stats['total_loans']}</div>";
echo "<div><strong>✅ Active:</strong> {$final_stats['active_loans']}</div>";
echo "<div><strong>⚠️ Overdue:</strong> {$final_stats['overdue_loans']}</div>";
echo "<div><strong>📚 Returned:</strong> {$final_stats['returned_loans']}</div>";
echo "</div>";
echo "</div>";

// Show improvement summary
echo "<h2>📈 Improvement Summary</h2>";
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px;'>";
echo "<h3>Changes Made:</h3>";
echo "<ul>";
echo "<li><strong>Members:</strong> {$current_stats['total_members']} → {$final_stats['total_members']} (-" . ($current_stats['total_members'] - $final_stats['total_members']) . ")</li>";
echo "<li><strong>Total Loans:</strong> {$current_stats['total_loans']} → {$final_stats['total_loans']} (-" . ($current_stats['total_loans'] - $final_stats['total_loans']) . ")</li>";
echo "<li><strong>Active Loans:</strong> {$current_stats['active_loans']} → {$final_stats['active_loans']} (-" . ($current_stats['active_loans'] - $final_stats['active_loans']) . ")</li>";
echo "<li><strong>Overdue Books:</strong> {$current_stats['overdue_loans']} → {$final_stats['overdue_loans']} (" . ($final_stats['overdue_loans'] - $current_stats['overdue_loans']) . ")</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🎉 Dashboard is Now Balanced!</h3>";
echo "<p>Your dashboard should now show more realistic and balanced statistics. You can refresh your admin dashboard to see the updated numbers.</p>";
echo "<p><a href='admin/dashboard.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Updated Dashboard</a></p>";
echo "</div>";
?>
