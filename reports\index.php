<?php
session_start();

// Include required files with error handling
try {
    require_once '../config/database.php';
    require_once '../config/config.php';
    require_once '../includes/functions.php';
} catch (Exception $e) {
    die('Error loading required files: ' . $e->getMessage());
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Initialize variables
$report_type = isset($_GET['type']) ? sanitize($_GET['type']) : '';
$date_range_type = isset($_GET['date_range_type']) ? sanitize($_GET['date_range_type']) : 'last_30_days';
$start_date = isset($_GET['start_date']) ? sanitize($_GET['start_date']) : '';
$end_date = isset($_GET['end_date']) ? sanitize($_GET['end_date']) : '';
$report_data = [];

// Calculate date ranges based on type
function calculateDateRange($type) {
    $today = new DateTime();
    $start = new DateTime();
    $end = new DateTime();

    switch ($type) {
        case 'this_week':
            $start->setISODate($today->format('Y'), $today->format('W'), 1);
            $end->setISODate($today->format('Y'), $today->format('W'), 7);
            break;
        case 'last_week':
            $start->setISODate($today->format('Y'), $today->format('W') - 1, 1);
            $end->setISODate($today->format('Y'), $today->format('W') - 1, 7);
            break;
        case 'this_month':
            $start->setDate($today->format('Y'), $today->format('m'), 1);
            $end->setDate($today->format('Y'), $today->format('m'), $today->format('t'));
            break;
        case 'last_month':
            $start->setDate($today->format('Y'), $today->format('m') - 1, 1);
            $end->setDate($today->format('Y'), $today->format('m') - 1, date('t', strtotime('last month')));
            break;
        case 'this_quarter':
            $quarter = ceil($today->format('m') / 3);
            $start->setDate($today->format('Y'), ($quarter - 1) * 3 + 1, 1);
            $end->setDate($today->format('Y'), $quarter * 3, date('t', mktime(0, 0, 0, $quarter * 3, 1, $today->format('Y'))));
            break;
        case 'last_quarter':
            $quarter = ceil($today->format('m') / 3) - 1;
            if ($quarter <= 0) {
                $quarter = 4;
                $year = $today->format('Y') - 1;
            } else {
                $year = $today->format('Y');
            }
            $start->setDate($year, ($quarter - 1) * 3 + 1, 1);
            $end->setDate($year, $quarter * 3, date('t', mktime(0, 0, 0, $quarter * 3, 1, $year)));
            break;
        case 'this_year':
            $start->setDate($today->format('Y'), 1, 1);
            $end->setDate($today->format('Y'), 12, 31);
            break;
        case 'last_year':
            $start->setDate($today->format('Y') - 1, 1, 1);
            $end->setDate($today->format('Y') - 1, 12, 31);
            break;
        case 'last_30_days':
            $start->modify('-30 days');
            $end = $today;
            break;
        case 'last_90_days':
            $start->modify('-90 days');
            $end = $today;
            break;
        default:
            // Default to last 30 days
            $start->modify('-30 days');
            $end = $today;
            break;
    }

    return ['start' => $start->format('Y-m-d'), 'end' => $end->format('Y-m-d')];
}

// Set date range based on type
$calculated_dates = calculateDateRange($date_range_type);
$start_date = $calculated_dates['start'];
$end_date = $calculated_dates['end'];

// Generate report based on type
if (!empty($report_type)) {
    switch ($report_type) {
        case 'overdue':
            // Overdue books report (both explicitly marked as overdue AND borrowed books past due)
            $query = "SELECT bl.*, b.title as book_title, b.isbn, m.first_name, m.last_name, m.email, m.phone
                      FROM book_loans bl
                      JOIN books b ON bl.book_id = b.id
                      JOIN members m ON bl.member_id = m.id
                      WHERE (bl.status = 'overdue' OR (bl.due_date < CURDATE() AND bl.status = 'borrowed'))
                      ORDER BY bl.due_date ASC";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $report_data = $stmt->fetchAll();
            break;

        case 'popular_books':
            // Popular books report
            $query = "SELECT b.id, b.title, b.author, b.isbn, COUNT(bl.id) as loan_count
                      FROM books b
                      JOIN book_loans bl ON b.id = bl.book_id
                      WHERE bl.issue_date BETWEEN :start_date AND :end_date
                      GROUP BY b.id
                      ORDER BY loan_count DESC
                      LIMIT 10";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':start_date', $start_date);
            $stmt->bindParam(':end_date', $end_date);
            $stmt->execute();
            $report_data = $stmt->fetchAll();
            break;

        case 'active_members':
            // Active members report
            $query = "SELECT m.id, m.first_name, m.last_name, m.email, COUNT(bl.id) as loan_count
                      FROM members m
                      JOIN book_loans bl ON m.id = bl.member_id
                      WHERE bl.issue_date BETWEEN :start_date AND :end_date
                      GROUP BY m.id
                      ORDER BY loan_count DESC
                      LIMIT 10";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':start_date', $start_date);
            $stmt->bindParam(':end_date', $end_date);
            $stmt->execute();
            $report_data = $stmt->fetchAll();
            break;

        case 'fines_collected':
            // Fines collected report
            $query = "SELECT bl.id, bl.return_date, bl.fine, b.title as book_title,
                      m.first_name, m.last_name
                      FROM book_loans bl
                      JOIN books b ON bl.book_id = b.id
                      JOIN members m ON bl.member_id = m.id
                      WHERE bl.status = 'returned' AND bl.fine > 0
                      AND bl.return_date BETWEEN :start_date AND :end_date
                      ORDER BY bl.return_date DESC";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':start_date', $start_date);
            $stmt->bindParam(':end_date', $end_date);
            $stmt->execute();
            $report_data = $stmt->fetchAll();

            // Calculate total fines
            $total_fines = 0;
            foreach ($report_data as $row) {
                $total_fines += $row['fine'];
            }
            break;

        case 'inventory':
            // Inventory report
            $query = "SELECT b.*,
                      (b.quantity - b.available_quantity) as borrowed_count
                      FROM books b
                      ORDER BY b.title ASC";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $report_data = $stmt->fetchAll();
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Reports</h1>
                    <?php if (!empty($report_type)): ?>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button id="printReport" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-printer"></i> Print Report
                        </button>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Report Selection Form -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" method="get" class="row g-3" id="reportForm">
                            <div class="col-md-3">
                                <label for="type" class="form-label">Report Type</label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="">Select Report Type</option>
                                    <option value="overdue" <?php echo $report_type === 'overdue' ? 'selected' : ''; ?>>Overdue Books</option>
                                    <option value="popular_books" <?php echo $report_type === 'popular_books' ? 'selected' : ''; ?>>Popular Books</option>
                                    <option value="active_members" <?php echo $report_type === 'active_members' ? 'selected' : ''; ?>>Active Members</option>
                                    <option value="fines_collected" <?php echo $report_type === 'fines_collected' ? 'selected' : ''; ?>>Fines Collected</option>
                                    <option value="inventory" <?php echo $report_type === 'inventory' ? 'selected' : ''; ?>>Inventory Status</option>
                                </select>
                            </div>

                            <div class="col-md-3">
                                <label for="date_range_type" class="form-label">Date Range</label>
                                <select class="form-select" id="date_range_type" name="date_range_type">
                                    <option value="this_week" <?php echo $date_range_type === 'this_week' ? 'selected' : ''; ?>>This Week</option>
                                    <option value="last_week" <?php echo $date_range_type === 'last_week' ? 'selected' : ''; ?>>Last Week</option>
                                    <option value="this_month" <?php echo $date_range_type === 'this_month' ? 'selected' : ''; ?>>This Month</option>
                                    <option value="last_month" <?php echo $date_range_type === 'last_month' ? 'selected' : ''; ?>>Last Month</option>
                                    <option value="this_quarter" <?php echo $date_range_type === 'this_quarter' ? 'selected' : ''; ?>>This Quarter</option>
                                    <option value="last_quarter" <?php echo $date_range_type === 'last_quarter' ? 'selected' : ''; ?>>Last Quarter</option>
                                    <option value="this_year" <?php echo $date_range_type === 'this_year' ? 'selected' : ''; ?>>This Year</option>
                                    <option value="last_year" <?php echo $date_range_type === 'last_year' ? 'selected' : ''; ?>>Last Year</option>
                                    <option value="last_30_days" <?php echo $date_range_type === 'last_30_days' ? 'selected' : ''; ?>>Last 30 Days</option>
                                    <option value="last_90_days" <?php echo $date_range_type === 'last_90_days' ? 'selected' : ''; ?>>Last 90 Days</option>
                                </select>
                            </div>



                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">Generate Report</button>
                            </div>

                            <!-- Date Range Display -->
                            <?php if (!empty($report_type)): ?>
                            <div class="col-12">
                                <div class="alert alert-info mb-0">
                                    <i class="bi bi-calendar-range me-2"></i>
                                    <strong>Selected Range:</strong> <?php echo formatDate($start_date) . ' to ' . formatDate($end_date); ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </form>
                    </div>
                </div>

                <!-- Report Results -->
                <?php if (!empty($report_type)): ?>
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <?php
                            // Get friendly range name
                            function getFriendlyRangeName($type) {
                                $ranges = [
                                    'this_week' => 'This Week',
                                    'last_week' => 'Last Week',
                                    'this_month' => 'This Month',
                                    'last_month' => 'Last Month',
                                    'this_quarter' => 'This Quarter',
                                    'last_quarter' => 'Last Quarter',
                                    'this_year' => 'This Year',
                                    'last_year' => 'Last Year',
                                    'last_30_days' => 'Last 30 Days',
                                    'last_90_days' => 'Last 90 Days',
                                    'custom' => 'Custom Range'
                                ];
                                return isset($ranges[$type]) ? $ranges[$type] : 'Custom Range';
                            }

                            $range_display = ' (' . getFriendlyRangeName($date_range_type) . ': ' . formatDate($start_date) . ' to ' . formatDate($end_date) . ')';

                            switch ($report_type) {
                                case 'overdue':
                                    echo 'Overdue Books Report';
                                    break;
                                case 'popular_books':
                                    echo 'Popular Books Report' . $range_display;
                                    break;
                                case 'active_members':
                                    echo 'Active Members Report' . $range_display;
                                    break;
                                case 'fines_collected':
                                    echo 'Fines Collected Report' . $range_display;
                                    break;
                                case 'inventory':
                                    echo 'Inventory Status Report';
                                    break;
                            }
                            ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($report_data)): ?>
                            <div class="alert alert-info">No data found for the selected report criteria.</div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <?php if ($report_type === 'overdue'): ?>
                                    <!-- Overdue Books Report -->
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Book Title</th>
                                                <th>ISBN</th>
                                                <th>Member</th>
                                                <th>Contact</th>
                                                <th>Due Date</th>
                                                <th>Days Overdue</th>
                                                <th>Estimated Fine</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($report_data as $row): ?>
                                                <tr>
                                                    <td><?php echo $row['book_title']; ?></td>
                                                    <td><?php echo $row['isbn']; ?></td>
                                                    <td><?php echo $row['first_name'] . ' ' . $row['last_name']; ?></td>
                                                    <td><?php echo $row['email'] . '<br>' . $row['phone']; ?></td>
                                                    <td><?php echo formatDate($row['due_date']); ?></td>
                                                    <td>
                                                        <?php
                                                            $due = new DateTime($row['due_date']);
                                                            $today = new DateTime();
                                                            echo $due->diff($today)->days;
                                                        ?>
                                                    </td>
                                                    <td>$<?php echo number_format(calculateFine($row['due_date']), 2); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                <?php elseif ($report_type === 'popular_books'): ?>
                                    <!-- Popular Books Report -->
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Rank</th>
                                                <th>Book Title</th>
                                                <th>Author</th>
                                                <th>ISBN</th>
                                                <th>Times Borrowed</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($report_data as $index => $row): ?>
                                                <tr>
                                                    <td><?php echo $index + 1; ?></td>
                                                    <td><?php echo $row['title']; ?></td>
                                                    <td><?php echo $row['author']; ?></td>
                                                    <td><?php echo $row['isbn']; ?></td>
                                                    <td><?php echo $row['loan_count']; ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                <?php elseif ($report_type === 'active_members'): ?>
                                    <!-- Active Members Report -->
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Rank</th>
                                                <th>Member Name</th>
                                                <th>Email</th>
                                                <th>Books Borrowed</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($report_data as $index => $row): ?>
                                                <tr>
                                                    <td><?php echo $index + 1; ?></td>
                                                    <td><?php echo $row['first_name'] . ' ' . $row['last_name']; ?></td>
                                                    <td><?php echo $row['email']; ?></td>
                                                    <td><?php echo $row['loan_count']; ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                <?php elseif ($report_type === 'fines_collected'): ?>
                                    <!-- Fines Collected Report -->
                                    <div class="alert alert-info mb-3">
                                        <strong>Total Fines Collected:</strong> $<?php echo number_format($total_fines, 2); ?>
                                    </div>
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Return Date</th>
                                                <th>Book Title</th>
                                                <th>Member</th>
                                                <th>Fine Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($report_data as $row): ?>
                                                <tr>
                                                    <td><?php echo formatDate($row['return_date']); ?></td>
                                                    <td><?php echo $row['book_title']; ?></td>
                                                    <td><?php echo $row['first_name'] . ' ' . $row['last_name']; ?></td>
                                                    <td>$<?php echo number_format($row['fine'], 2); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                <?php elseif ($report_type === 'inventory'): ?>
                                    <!-- Inventory Status Report -->
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Book Title</th>
                                                <th>Author</th>
                                                <th>ISBN</th>
                                                <th>Category</th>
                                                <th>Total Quantity</th>
                                                <th>Available</th>
                                                <th>Borrowed</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($report_data as $row): ?>
                                                <tr>
                                                    <td><?php echo $row['title']; ?></td>
                                                    <td><?php echo $row['author']; ?></td>
                                                    <td><?php echo $row['isbn']; ?></td>
                                                    <td><?php echo $row['category']; ?></td>
                                                    <td><?php echo $row['quantity']; ?></td>
                                                    <td><?php echo $row['available_quantity']; ?></td>
                                                    <td><?php echo $row['borrowed_count']; ?></td>
                                                    <td>
                                                        <?php if ($row['available_quantity'] == 0): ?>
                                                            <span class="badge bg-danger">Not Available</span>
                                                        <?php elseif ($row['available_quantity'] < $row['quantity']): ?>
                                                            <span class="badge bg-warning text-dark">Partially Available</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-success">Fully Available</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
</body>
</html>
