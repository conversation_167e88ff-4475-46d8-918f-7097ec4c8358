<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get search query
$search_query = isset($_GET['q']) ? trim($_GET['q']) : '';

// Initialize results arrays
$books = [];
$members = [];
$loans = [];

if (!empty($search_query)) {
    // Search books
    $query = "SELECT * FROM books
              WHERE title LIKE :search
              OR author LIKE :search
              OR isbn LIKE :search
              OR category LIKE :search
              ORDER BY title ASC
              LIMIT 20";
    $stmt = $db->prepare($query);
    $search_param = "%{$search_query}%";
    $stmt->bindParam(':search', $search_param);
    $stmt->execute();
    $books = $stmt->fetchAll();

    // Search members (only for admin and librarian)
    if (isAdmin() || isLibrarian()) {
        $query = "SELECT * FROM members
                  WHERE first_name LIKE :search
                  OR last_name LIKE :search
                  OR email LIKE :search
                  OR phone LIKE :search
                  ORDER BY last_name, first_name ASC
                  LIMIT 20";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':search', $search_param);
        $stmt->execute();
        $members = $stmt->fetchAll();
    }

    // Search loans (only for admin and librarian)
    if (isAdmin() || isLibrarian()) {
        $query = "SELECT bl.*, b.title as book_title, m.first_name, m.last_name
                  FROM book_loans bl
                  JOIN books b ON bl.book_id = b.id
                  JOIN members m ON bl.member_id = m.id
                  WHERE b.title LIKE :search
                  OR m.first_name LIKE :search
                  OR m.last_name LIKE :search
                  ORDER BY bl.issue_date DESC
                  LIMIT 20";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':search', $search_param);
        $stmt->execute();
        $loans = $stmt->fetchAll();
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Results - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Search Results for "<?php echo h($search_query); ?>"</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button id="darkModeToggle" class="btn btn-outline-secondary me-2" title="Toggle Dark Mode">
                            <i id="darkModeIcon" class="bi bi-moon"></i>
                        </button>
                        <form class="d-flex">
                            <input class="form-control me-2" type="search" name="q" placeholder="Search again..." value="<?php echo h($search_query); ?>" aria-label="Search">
                            <button class="btn btn-primary" type="submit"><i class="bi bi-search"></i></button>
                        </form>
                    </div>
                </div>

                <?php if (empty($search_query)): ?>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i> Please enter a search term to find books, members, or loans.
                    </div>
                <?php elseif (empty($books) && empty($members) && empty($loans)): ?>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i> No results found for "<?php echo h($search_query); ?>".
                    </div>
                <?php else: ?>
                    <!-- Books Results -->
                    <?php if (!empty($books)): ?>
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-book me-2"></i> Books (<?php echo count($books); ?>)</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Cover</th>
                                                <th>Title</th>
                                                <th>Author</th>
                                                <th>Category</th>
                                                <th>Available</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($books as $book): ?>
                                                <tr>
                                                    <td class="text-center" style="width: 60px;">
                                                        <?php if (!empty($book['cover_image'])): ?>
                                                            <?php
                                                            // Check if the cover_image is a URL or a local file
                                                            if (strpos($book['cover_image'], 'http') === 0) {
                                                                $image_src = $book['cover_image'];
                                                            } else {
                                                                $image_src = 'uploads/covers/' . $book['cover_image'];
                                                            }
                                                            ?>
                                                            <a href="#" class="cover-preview" data-bs-toggle="modal" data-bs-target="#coverModal"
                                                               data-img-src="<?php echo h($image_src); ?>"
                                                               data-title="<?php echo h($book['title']); ?>">
                                                                <img src="<?php echo h($image_src); ?>" alt="<?php echo h($book['title']); ?>"
                                                                     class="img-thumbnail" style="width: 40px; height: 60px; object-fit: cover;"
                                                                     onerror="this.style.display='none';">
                                                            </a>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary"><i class="bi bi-book"></i></span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?php echo h($book['title']); ?></td>
                                                    <td><?php echo h($book['author']); ?></td>
                                                    <td><?php echo h($book['category']); ?></td>
                                                    <td><?php echo h($book['available_quantity']); ?> / <?php echo h($book['quantity']); ?></td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="books/view.php?id=<?php echo h($book['id']); ?>" class="btn btn-outline-primary">
                                                                <i class="bi bi-eye"></i> View
                                                            </a>
                                                            <?php if (isAdmin() || isLibrarian()): ?>
                                                                <a href="books/edit.php?id=<?php echo h($book['id']); ?>" class="btn btn-outline-secondary">
                                                                    <i class="bi bi-pencil"></i> Edit
                                                                </a>
                                                                <a href="loans/issue.php?book_id=<?php echo h($book['id']); ?>" class="btn btn-outline-success">
                                                                    <i class="bi bi-journal-arrow-up"></i> Issue
                                                                </a>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="card-footer text-end">
                                <a href="books/index.php" class="btn btn-sm btn-primary">View All Books</a>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Members Results (Admin/Librarian Only) -->
                    <?php if ((isAdmin() || isLibrarian()) && !empty($members)): ?>
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="bi bi-people me-2"></i> Members (<?php echo count($members); ?>)</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Phone</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($members as $member): ?>
                                                <tr>
                                                    <td><?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></td>
                                                    <td><?php echo h($member['email']); ?></td>
                                                    <td><?php echo h($member['phone']); ?></td>
                                                    <td>
                                                        <?php if ($member['status'] === 'active'): ?>
                                                            <span class="badge bg-success">Active</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-danger">Inactive</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="members/view.php?id=<?php echo h($member['id']); ?>" class="btn btn-outline-primary">
                                                                <i class="bi bi-eye"></i> View
                                                            </a>
                                                            <a href="members/edit.php?id=<?php echo h($member['id']); ?>" class="btn btn-outline-secondary">
                                                                <i class="bi bi-pencil"></i> Edit
                                                            </a>
                                                            <a href="loans/issue.php?member_id=<?php echo h($member['id']); ?>" class="btn btn-outline-success">
                                                                <i class="bi bi-journal-arrow-up"></i> Issue Book
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="card-footer text-end">
                                <a href="members/index.php" class="btn btn-sm btn-success">View All Members</a>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Loans Results (Admin/Librarian Only) -->
                    <?php if ((isAdmin() || isLibrarian()) && !empty($loans)): ?>
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="bi bi-journal-text me-2"></i> Loans (<?php echo count($loans); ?>)</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Book</th>
                                                <th>Member</th>
                                                <th>Issue Date</th>
                                                <th>Due Date</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($loans as $loan): ?>
                                                <tr>
                                                    <td><?php echo h($loan['book_title']); ?></td>
                                                    <td><?php echo h($loan['first_name'] . ' ' . $loan['last_name']); ?></td>
                                                    <td><?php echo formatDate($loan['issue_date']); ?></td>
                                                    <td><?php echo formatDate($loan['due_date']); ?></td>
                                                    <td>
                                                        <?php if ($loan['status'] === 'borrowed'): ?>
                                                            <?php if (strtotime($loan['due_date']) < time()): ?>
                                                                <span class="badge bg-danger">Overdue</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-primary">Borrowed</span>
                                                            <?php endif; ?>
                                                        <?php elseif ($loan['status'] === 'returned'): ?>
                                                            <span class="badge bg-success">Returned</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary"><?php echo h(ucfirst($loan['status'])); ?></span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="loans/view.php?id=<?php echo h($loan['id']); ?>" class="btn btn-outline-primary">
                                                                <i class="bi bi-eye"></i> View
                                                            </a>
                                                            <?php if ($loan['status'] === 'borrowed'): ?>
                                                                <a href="loans/return.php?id=<?php echo h($loan['id']); ?>" class="btn btn-outline-success">
                                                                    <i class="bi bi-journal-arrow-down"></i> Return
                                                                </a>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="card-footer text-end">
                                <a href="loans/index.php" class="btn btn-sm btn-info text-white">View All Loans</a>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <!-- Book Cover Modal -->
    <div class="modal fade" id="coverModal" tabindex="-1" aria-labelledby="coverModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="coverModalLabel">Book Cover</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalCoverImage" src="" class="img-fluid" alt="Book Cover">
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/dark-mode.js"></script>
    <script>
        // Function to show book cover in modal
        document.addEventListener('DOMContentLoaded', function() {
            const coverLinks = document.querySelectorAll('.cover-preview');
            coverLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const imgSrc = this.getAttribute('data-img-src');
                    const title = this.getAttribute('data-title');
                    document.getElementById('modalCoverImage').src = imgSrc;
                    document.getElementById('coverModalLabel').textContent = title;
                });
            });
        });
    </script>
</body>
</html>
