<?php
/**
 * Simple Diagnostic Test - No dependencies
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Simple Diagnostic Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

echo "<div class='section'>";
echo "<h2>✅ Basic PHP Test</h2>";
echo "<div class='success'>PHP is working! Version: " . phpversion() . "</div>";
echo "<div class='info'>Current time: " . date('Y-m-d H:i:s') . "</div>";
echo "<div class='info'>Server: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</div>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>📁 File System Test</h2>";

$test_files = [
    'config/database.php' => 'Database Config',
    'admin/dashboard.php' => 'Admin Dashboard',
    'index.php' => 'Main Index'
];

foreach ($test_files as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ $description exists</div>";
    } else {
        echo "<div class='error'>❌ $description missing</div>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🔗 Quick Database Test</h2>";

try {
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';
        
        $database = new Database();
        $db = $database->getConnection();
        
        if ($db) {
            echo "<div class='success'>✅ Database connection successful</div>";
            
            // Simple query test
            $stmt = $db->query("SELECT 1 as test");
            $result = $stmt->fetch();
            echo "<div class='success'>✅ Database query successful</div>";
            
        } else {
            echo "<div class='error'>❌ Database connection failed</div>";
        }
    } else {
        echo "<div class='error'>❌ Database config file not found</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🎯 Access Information</h2>";
echo "<div class='info'>";
echo "<p><strong>This file URL:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
echo "<p><strong>Script Path:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Unknown') . "</p>";
echo "</div>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>🔗 Test Links</h2>";
echo "<p>Try these direct links:</p>";
echo "<ul>";
echo "<li><a href='diagnostic.php' target='_blank'>Main Diagnostic</a></li>";
echo "<li><a href='database_status.php' target='_blank'>Database Status</a></li>";
echo "<li><a href='troubleshoot.php' target='_blank'>Troubleshooting</a></li>";
echo "<li><a href='admin/dashboard.php' target='_blank'>Admin Dashboard</a></li>";
echo "</ul>";
echo "</div>";
