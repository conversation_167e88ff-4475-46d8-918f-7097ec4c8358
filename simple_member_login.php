<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

$error_message = '';
$success_message = '';

// Handle login
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    
    if (empty($email) || empty($password)) {
        $error_message = 'Please enter both email and password.';
    } else {
        try {
            $database = new Database();
            $db = $database->getConnection();
            
            $query = "SELECT * FROM members WHERE email = :email LIMIT 1";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':email', $email);
            $stmt->execute();
            $member = $stmt->fetch();
            
            if ($member && password_verify($password, $member['password'])) {
                $_SESSION['member_id'] = $member['id'];
                $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
                $_SESSION['member_email'] = $member['email'];
                
                // Redirect to member dashboard
                header('Location: member_dashboard.php');
                exit;
            } else {
                $error_message = 'Invalid email or password.';
            }
        } catch (Exception $e) {
            $error_message = 'Login error: ' . $e->getMessage();
        }
    }
}

// Create test member if needed
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_test'])) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        // Check if test member already exists
        $query = "SELECT id FROM members WHERE email = '<EMAIL>'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        if (!$stmt->fetch()) {
            // Create test member
            $query = "INSERT INTO members (first_name, last_name, email, password, phone, address, membership_date, membership_status) 
                      VALUES ('Test', 'Member', '<EMAIL>', :password, '************', '123 Test St', NOW(), 'active')";
            $stmt = $db->prepare($query);
            $hashed_password = password_hash('password123', PASSWORD_DEFAULT);
            $stmt->bindParam(':password', $hashed_password);
            $stmt->execute();
            
            $success_message = 'Test member created! Email: <EMAIL>, Password: password123';
        } else {
            $success_message = 'Test member already exists! Email: <EMAIL>, Password: password123';
        }
    } catch (Exception $e) {
        $error_message = 'Error creating test member: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Member Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .login-container { max-width: 400px; margin: 5rem auto; }
        .login-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); overflow: hidden; }
        .login-header { background: linear-gradient(135deg, #4361ee, #3a0ca3); color: white; padding: 2rem; text-align: center; }
        .login-body { padding: 2rem; }
        .btn-primary { background: linear-gradient(135deg, #4361ee, #3a0ca3); border: none; }
        .btn-primary:hover { background: linear-gradient(135deg, #3a0ca3, #4361ee); }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h3><i class="bi bi-person-circle me-2"></i>Member Login</h3>
                <p class="mb-0">Access your library account</p>
            </div>
            <div class="login-body">
                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle me-2"></i><?php echo $success_message; ?>
                    </div>
                <?php endif; ?>

                <form method="POST">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <button type="submit" name="login" class="btn btn-primary w-100 mb-3">
                        <i class="bi bi-box-arrow-in-right me-2"></i>Login
                    </button>
                </form>

                <hr>
                
                <div class="text-center">
                    <h6>Need a test account?</h6>
                    <form method="POST" class="d-inline">
                        <button type="submit" name="create_test" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-plus-circle me-1"></i>Create Test Member
                        </button>
                    </form>
                </div>

                <hr>

                <div class="text-center">
                    <h6>Quick Access</h6>
                    <div class="d-grid gap-2">
                        <a href="debug_member_dashboard.php" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-bug me-1"></i>Debug Tool
                        </a>
                        <a href="register.php" class="btn btn-outline-success btn-sm">
                            <i class="bi bi-person-plus me-1"></i>Register New Account
                        </a>
                        <a href="index.php" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-house me-1"></i>Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-3">
            <small class="text-white">
                Having trouble? Try the <a href="debug_member_dashboard.php" class="text-white"><u>debug tool</u></a> to diagnose issues.
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
