<?php
session_start();
require_once 'config/config.php';

// Create the directories if they don't exist
if (!file_exists(UPLOADS_PATH)) {
    mkdir(UPLOADS_PATH, 0777, true);
}

if (!file_exists(COVERS_PATH)) {
    mkdir(COVERS_PATH, 0777, true);
}

$message = '';
$success = false;
$uploaded_file = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if image was uploaded
    if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 5 * 1024 * 1024; // 5MB
        
        if (in_array($_FILES['image']['type'], $allowed_types) && $_FILES['image']['size'] <= $max_size) {
            $filename = "1984_cover.jpg"; // Fixed filename for 1984 cover
            $target_file = COVERS_PATH . $filename;
            
            if (move_uploaded_file($_FILES['image']['tmp_name'], $target_file)) {
                $message = "✅ Image uploaded successfully to: " . $target_file;
                $success = true;
                $uploaded_file = $target_file;
            } else {
                $message = "❌ Failed to upload image.";
            }
        } else {
            $message = "❌ Invalid image file. Only JPG, PNG, and GIF files under 5MB are allowed.";
        }
    } else {
        $message = "❌ No image was uploaded or there was an error.";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Image Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .success {
            color: green;
            padding: 10px;
            background-color: #e8f5e9;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .error {
            color: red;
            padding: 10px;
            background-color: #ffebee;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .instructions {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .btn {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #45a049;
        }
        input[type=file] {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>Simple Image Upload for 1984 Book Cover</h1>
    
    <div class="instructions">
        <h2>Instructions:</h2>
        <ol>
            <li>Click the "Choose File" button below</li>
            <li>Select the 1984 book cover image from your computer</li>
            <li>Click the "Upload Image" button</li>
            <li>After successful upload, go to <a href="update_manual_image.php">this page</a> to update the database</li>
        </ol>
    </div>
    
    <?php if (!empty($message)): ?>
        <div class="<?php echo $success ? 'success' : 'error'; ?>">
            <?php echo $message; ?>
        </div>
    <?php endif; ?>
    
    <div class="card">
        <form action="" method="post" enctype="multipart/form-data">
            <div>
                <label for="image">Select Image File:</label><br>
                <input type="file" id="image" name="image" required>
            </div>
            <button type="submit" class="btn">Upload Image</button>
        </form>
    </div>
    
    <?php if ($success && !empty($uploaded_file)): ?>
        <div class="card">
            <h3>Uploaded Image:</h3>
            <img src="uploads/covers/1984_cover.jpg" alt="Uploaded Image" style="max-width: 100%; max-height: 400px;">
            <p>Now go to <a href="update_manual_image.php">this page</a> to update the database with this image.</p>
        </div>
    <?php endif; ?>
    
    <div class="card">
        <h3>Directory Information:</h3>
        <p><strong>Uploads Path:</strong> <?php echo UPLOADS_PATH; ?></p>
        <p><strong>Covers Path:</strong> <?php echo COVERS_PATH; ?></p>
    </div>
    
    <p>
        <a href="books/index.php">Back to Books List</a> | 
        <a href="update_manual_image.php">Update Database with Uploaded Image</a>
    </p>
</body>
</html>
