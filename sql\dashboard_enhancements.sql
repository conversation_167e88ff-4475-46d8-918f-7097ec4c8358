-- SQL file for dashboard enhancements

-- Create table for librarian tasks
CREATE TABLE IF NOT EXISTS `librarian_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `librarian_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `due_date` date DEFAULT NULL,
  `priority` enum('low','medium','high') NOT NULL DEFAULT 'medium',
  `category` varchar(50) DEFAULT 'general',
  `completed` tinyint(1) NOT NULL DEFAULT 0,
  `completed_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `librarian_id` (`librarian_id`),
  CONSTRAINT `librarian_tasks_ibfk_1` FOREIGN KEY (`librarian_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create table for saved filters
CREATE TABLE IF NOT EXISTS `saved_filters` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `librarian_id` int(11) NOT NULL,
  `form_id` varchar(50) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `filter_data` json NOT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `librarian_id` (`librarian_id`),
  CONSTRAINT `saved_filters_ibfk_1` FOREIGN KEY (`librarian_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create table for book condition tracking
CREATE TABLE IF NOT EXISTS `book_conditions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `book_id` int(11) NOT NULL,
  `loan_id` int(11) DEFAULT NULL,
  `librarian_id` int(11) NOT NULL,
  `overall_rating` int(1) NOT NULL DEFAULT 3,
  `cover_condition` enum('new','good','fair','poor','damaged') NOT NULL DEFAULT 'good',
  `binding_condition` enum('new','good','fair','poor','damaged') NOT NULL DEFAULT 'good',
  `pages_condition` enum('new','good','fair','poor','damaged') NOT NULL DEFAULT 'good',
  `condition_notes` text DEFAULT NULL,
  `needs_repair` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `book_id` (`book_id`),
  KEY `loan_id` (`loan_id`),
  KEY `librarian_id` (`librarian_id`),
  CONSTRAINT `book_conditions_ibfk_1` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  CONSTRAINT `book_conditions_ibfk_2` FOREIGN KEY (`loan_id`) REFERENCES `book_loans` (`id`) ON DELETE SET NULL,
  CONSTRAINT `book_conditions_ibfk_3` FOREIGN KEY (`librarian_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create table for book condition images
CREATE TABLE IF NOT EXISTS `book_condition_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `condition_id` int(11) NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `condition_id` (`condition_id`),
  CONSTRAINT `book_condition_images_ibfk_1` FOREIGN KEY (`condition_id`) REFERENCES `book_conditions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create table for communication templates
CREATE TABLE IF NOT EXISTS `communication_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `communication_templates_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create table for communication log
CREATE TABLE IF NOT EXISTS `communication_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sender_id` int(11) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `method` enum('email','sms','notification') NOT NULL DEFAULT 'email',
  `sent_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sender_id` (`sender_id`),
  CONSTRAINT `communication_log_ibfk_1` FOREIGN KEY (`sender_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create table for communication recipients
CREATE TABLE IF NOT EXISTS `communication_recipients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `communication_id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `read_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `communication_id` (`communication_id`),
  KEY `member_id` (`member_id`),
  CONSTRAINT `communication_recipients_ibfk_1` FOREIGN KEY (`communication_id`) REFERENCES `communication_log` (`id`) ON DELETE CASCADE,
  CONSTRAINT `communication_recipients_ibfk_2` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create table for book acquisition suggestions
CREATE TABLE IF NOT EXISTS `book_acquisition_suggestions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `author` varchar(255) DEFAULT NULL,
  `isbn` varchar(20) DEFAULT NULL,
  `reason` text DEFAULT NULL,
  `request_count` int(11) NOT NULL DEFAULT 0,
  `search_count` int(11) NOT NULL DEFAULT 0,
  `avg_rating` decimal(3,2) DEFAULT NULL,
  `status` enum('pending','approved','rejected','acquired') NOT NULL DEFAULT 'pending',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create table for book requests
CREATE TABLE IF NOT EXISTS `book_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) DEFAULT NULL,
  `book_title` varchar(255) NOT NULL,
  `book_author` varchar(255) DEFAULT NULL,
  `isbn` varchar(20) DEFAULT NULL,
  `reason` text DEFAULT NULL,
  `status` enum('pending','approved','rejected','acquired') NOT NULL DEFAULT 'pending',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `member_id` (`member_id`),
  CONSTRAINT `book_requests_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create table for search log
CREATE TABLE IF NOT EXISTS `searches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `search_term` varchar(255) NOT NULL,
  `search_type` varchar(50) DEFAULT NULL,
  `results_count` int(11) DEFAULT 0,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `searches_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create table for book ratings
CREATE TABLE IF NOT EXISTS `book_ratings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `book_id` int(11) DEFAULT NULL,
  `book_title` varchar(255) NOT NULL,
  `member_id` int(11) DEFAULT NULL,
  `rating` int(1) NOT NULL,
  `review` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `book_id` (`book_id`),
  KEY `member_id` (`member_id`),
  CONSTRAINT `book_ratings_ibfk_1` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE SET NULL,
  CONSTRAINT `book_ratings_ibfk_2` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default communication templates
INSERT INTO `communication_templates` (`name`, `description`, `subject`, `message`, `created_at`) VALUES
('Overdue Notice', 'For books past due date', 'Overdue Book Notice', 'Dear {member_name},\n\nThis is a reminder that the following book(s) are overdue:\n{book_list}\n\nPlease return them at your earliest convenience or renew online.\n\nThank you,\nLibrary Staff', NOW()),
('Reservation Ready', 'Book is ready for pickup', 'Your Reserved Book is Ready', 'Dear {member_name},\n\nThe book you reserved, {book_title}, is now available for pickup. Please visit the library within the next 3 days to check out this item.\n\nThank you,\nLibrary Staff', NOW()),
('New Arrivals', 'Notify about new books', 'New Books at the Library', 'Dear {member_name},\n\nWe are pleased to inform you that the following new books have arrived at our library:\n{book_list}\n\nWe hope you will find something of interest!\n\nBest regards,\nLibrary Staff', NOW()),
('Membership Renewal', 'Membership expiring soon', 'Your Library Membership is Expiring Soon', 'Dear {member_name},\n\nYour library membership will expire on {expiry_date}. To continue enjoying our services without interruption, please renew your membership before this date.\n\nThank you for being a valued member of our library.\n\nBest regards,\nLibrary Staff', NOW());
