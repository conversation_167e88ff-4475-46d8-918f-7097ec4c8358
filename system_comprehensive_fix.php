<?php
/**
 * Comprehensive System Fix
 * This file will identify and fix all potential issues in the LMS system
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>LMS System Comprehensive Fix</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css' rel='stylesheet'>
    <style>
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .fix-section {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
        }
        .code-block {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
<div class='container mt-4'>
    <h1><i class='bi bi-tools me-2'></i>LMS System Comprehensive Fix</h1>
    <p class='text-muted'>Checking and fixing all potential issues in your Library Management System...</p>";

// Start session safely
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$fixes_applied = [];
$issues_found = [];

// Fix 1: Check and fix core configuration files
echo "<div class='fix-section'>
<h3><i class='bi bi-gear me-2'></i>Fix 1: Core Configuration Files</h3>";

$config_files = [
    'config/database.php' => 'Database configuration',
    'config/config.php' => 'Main configuration',
    'config/google_oauth.php' => 'Google OAuth configuration',
    'includes/functions.php' => 'Core functions'
];

foreach ($config_files as $file => $description) {
    if (file_exists($file)) {
        // Check for syntax errors
        $output = shell_exec("php -l $file 2>&1");
        if (strpos($output, 'No syntax errors') !== false) {
            echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>$file - $description (OK)</p>";
        } else {
            echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>$file - Syntax Error: $output</p>";
            $issues_found[] = "$file has syntax errors";
        }
    } else {
        echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>$file - Missing file</p>";
        $issues_found[] = "$file is missing";
    }
}
echo "</div>";

// Fix 2: Database connection and table verification
echo "<div class='fix-section'>
<h3><i class='bi bi-database me-2'></i>Fix 2: Database Connection & Tables</h3>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Database connection successful</p>";

    // Check required tables
    $required_tables = [
        'users' => 'System users (admin, librarian)',
        'members' => 'Library members',
        'books' => 'Book catalog',
        'book_loans' => 'Book borrowing records',
        'book_reservations' => 'Book reservations',
        'activity_log' => 'System activity log',
        'notifications' => 'System notifications'
    ];

    $missing_tables = [];
    foreach ($required_tables as $table => $description) {
        try {
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM $table");
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Table '$table' - $count records ($description)</p>";
        } catch (Exception $e) {
            echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>Table '$table' - Missing or Error</p>";
            $missing_tables[] = $table;
            $issues_found[] = "Table $table is missing or has issues";
        }
    }

    // Fix missing tables if any
    if (!empty($missing_tables)) {
        echo "<div class='alert alert-warning mt-3'>
        <h5>Missing Tables Detected</h5>
        <p>The following tables are missing: " . implode(', ', $missing_tables) . "</p>
        <p>You can fix this by running the database setup: <a href='setup_database.php' class='btn btn-warning btn-sm'>Setup Database</a></p>
        </div>";
    }

} catch (Exception $e) {
    echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>Database connection failed: " . $e->getMessage() . "</p>";
    $issues_found[] = "Database connection failed: " . $e->getMessage();
}
echo "</div>";

// Fix 3: Authentication system verification
echo "<div class='fix-section'>
<h3><i class='bi bi-shield-check me-2'></i>Fix 3: Authentication System</h3>";

try {
    require_once 'includes/functions.php';

    // Test authentication functions
    $auth_functions = [
        'isLoggedIn' => 'User login check',
        'isAdmin' => 'Admin role check',
        'isLibrarian' => 'Librarian role check',
        'isMemberLoggedIn' => 'Member login check'
    ];

    foreach ($auth_functions as $function => $description) {
        if (function_exists($function)) {
            echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Function '$function' - $description (Available)</p>";
        } else {
            echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>Function '$function' - Missing</p>";
            $issues_found[] = "Authentication function $function is missing";
        }
    }

    // Check if admin user exists
    if (isset($db)) {
        try {
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
            $stmt->execute();
            $admin_count = $stmt->fetch()['count'];

            if ($admin_count > 0) {
                echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Admin users found: $admin_count</p>";
            } else {
                echo "<p class='status-warning'><i class='bi bi-exclamation-triangle-fill me-2'></i>No admin users found</p>";
                echo "<div class='alert alert-warning mt-2'>
                <p>No admin users found. You can create one: <a href='create_admin.php' class='btn btn-warning btn-sm'>Create Admin</a></p>
                </div>";
            }
        } catch (Exception $e) {
            echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>Error checking admin users: " . $e->getMessage() . "</p>";
        }
    }

} catch (Exception $e) {
    echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>Error loading authentication functions: " . $e->getMessage() . "</p>";
    $issues_found[] = "Authentication system error: " . $e->getMessage();
}
echo "</div>";

// Fix 4: Dashboard files verification
echo "<div class='fix-section'>
<h3><i class='bi bi-speedometer2 me-2'></i>Fix 4: Dashboard Files</h3>";

$dashboard_files = [
    'admin/dashboard.php' => 'Admin dashboard',
    'librarian/dashboard.php' => 'Librarian dashboard',
    'member/member_dashboard.php' => 'Member dashboard',
    'member_dashboard.php' => 'Alternative member dashboard'
];

foreach ($dashboard_files as $file => $description) {
    if (file_exists($file)) {
        // Check for syntax errors
        $output = shell_exec("php -l $file 2>&1");
        if (strpos($output, 'No syntax errors') !== false) {
            echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>$file - $description (OK)</p>";
        } else {
            echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>$file - Syntax Error</p>";
            $issues_found[] = "$file has syntax errors";
        }
    } else {
        echo "<p class='status-warning'><i class='bi bi-exclamation-triangle-fill me-2'></i>$file - Not found (may be optional)</p>";
    }
}
echo "</div>";

// Fix 5: File permissions and directories
echo "<div class='fix-section'>
<h3><i class='bi bi-folder-check me-2'></i>Fix 5: File Permissions & Directories</h3>";

$required_dirs = [
    'uploads' => 'File uploads directory',
    'uploads/covers' => 'Book cover images',
    'uploads/members' => 'Member profile images',
    'uploads/profiles' => 'Profile images',
    'logs' => 'System logs'
];

foreach ($required_dirs as $dir => $description) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>$dir - $description (Writable)</p>";
        } else {
            echo "<p class='status-warning'><i class='bi bi-exclamation-triangle-fill me-2'></i>$dir - $description (Not writable)</p>";
            $issues_found[] = "Directory $dir is not writable";
        }
    } else {
        echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>$dir - Missing directory</p>";
        // Try to create the directory
        if (mkdir($dir, 0755, true)) {
            echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>$dir - Created successfully</p>";
            $fixes_applied[] = "Created directory $dir";
        } else {
            $issues_found[] = "Could not create directory $dir";
        }
    }
}
echo "</div>";

// Fix 6: Session and security checks
echo "<div class='fix-section'>
<h3><i class='bi bi-shield-lock me-2'></i>Fix 6: Session & Security</h3>";

// Check session configuration
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Session is active</p>";
} else {
    echo "<p class='status-warning'><i class='bi bi-exclamation-triangle-fill me-2'></i>Session not active</p>";
}

// Check PHP configuration
$php_checks = [
    'file_uploads' => 'File upload capability',
    'session.auto_start' => 'Session auto start (should be off)',
    'display_errors' => 'Error display (for debugging)'
];

foreach ($php_checks as $setting => $description) {
    $value = ini_get($setting);
    if ($setting === 'session.auto_start') {
        if ($value == 0) {
            echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>$setting - $description (Correct: Off)</p>";
        } else {
            echo "<p class='status-warning'><i class='bi bi-exclamation-triangle-fill me-2'></i>$setting - $description (Should be off)</p>";
        }
    } else {
        echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>$setting - $description (Value: " . ($value ? 'On' : 'Off') . ")</p>";
    }
}
echo "</div>";

// Fix 7: Test dashboard accessibility
echo "<div class='fix-section'>
<h3><i class='bi bi-globe me-2'></i>Fix 7: Dashboard Accessibility Test</h3>";

// Create test URLs
$base_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
$test_urls = [
    $base_url . '/admin/dashboard.php' => 'Admin Dashboard',
    $base_url . '/librarian/dashboard.php' => 'Librarian Dashboard',
    $base_url . '/member/member_dashboard.php' => 'Member Dashboard'
];

foreach ($test_urls as $url => $name) {
    echo "<p><i class='bi bi-link-45deg me-2'></i>$name: <a href='$url' target='_blank' class='btn btn-sm btn-outline-primary'>Test Access</a></p>";
}
echo "</div>";

// Summary and recommendations
echo "<div class='fix-section'>
<h3><i class='bi bi-clipboard-check me-2'></i>Summary & Recommendations</h3>";

if (empty($issues_found)) {
    echo "<div class='alert alert-success'>
    <h5><i class='bi bi-check-circle-fill me-2'></i>All Systems Operational!</h5>
    <p>No critical issues found. Your LMS system appears to be working correctly.</p>
    </div>";
} else {
    echo "<div class='alert alert-warning'>
    <h5><i class='bi bi-exclamation-triangle-fill me-2'></i>Issues Found</h5>
    <ul>";
    foreach ($issues_found as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>
    </div>";
}

if (!empty($fixes_applied)) {
    echo "<div class='alert alert-info'>
    <h5><i class='bi bi-tools me-2'></i>Fixes Applied</h5>
    <ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ul>
    </div>";
}

// Quick access links
echo "<div class='row mt-4'>
<div class='col-md-4'>
    <div class='card'>
        <div class='card-body text-center'>
            <h5 class='card-title'><i class='bi bi-person-gear me-2'></i>Admin Dashboard</h5>
            <a href='admin/dashboard.php' class='btn btn-primary'>Access Admin</a>
        </div>
    </div>
</div>
<div class='col-md-4'>
    <div class='card'>
        <div class='card-body text-center'>
            <h5 class='card-title'><i class='bi bi-book me-2'></i>Librarian Dashboard</h5>
            <a href='librarian/dashboard.php' class='btn btn-success'>Access Librarian</a>
        </div>
    </div>
</div>
<div class='col-md-4'>
    <div class='card'>
        <div class='card-body text-center'>
            <h5 class='card-title'><i class='bi bi-people me-2'></i>Member Dashboard</h5>
            <a href='member/member_dashboard.php' class='btn btn-info'>Access Member</a>
        </div>
    </div>
</div>
</div>";

echo "</div>
</div>
</body>
</html>";
?>