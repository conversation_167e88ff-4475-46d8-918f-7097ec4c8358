<?php
/**
 * Comprehensive System Status Check
 * This script checks all major components of the LMS system
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session safely
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>LMS System Status Check</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css'>
    <style>
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; }
    </style>
</head>
<body>
<div class='container mt-4'>
    <h1 class='mb-4'><i class='bi bi-gear-fill me-2'></i>LMS System Status Check</h1>";

// Test 1: Core Files
echo "<div class='test-section'>
<h3><i class='bi bi-file-earmark-code me-2'></i>Core Files Check</h3>";

$core_files = [
    'index.php' => 'Main entry point',
    'home.php' => 'Public homepage',
    'login.php' => 'Authentication page',
    'logout.php' => 'Logout handler',
    'register.php' => 'Member registration',
    'catalog.php' => 'Book catalog',
    'member_dashboard.php' => 'Member dashboard',
    'config/config.php' => 'Main configuration',
    'config/database.php' => 'Database configuration',
    'config/google_oauth.php' => 'Google OAuth config',
    'includes/functions.php' => 'Core functions',
    'includes/header.php' => 'Header template',
    'includes/footer.php' => 'Footer template',
    'admin/dashboard.php' => 'Admin dashboard'
];

$missing_files = [];
foreach ($core_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>$file - $description</p>";
    } else {
        echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>$file - $description (MISSING)</p>";
        $missing_files[] = $file;
    }
}
echo "</div>";

// Test 2: Database Connection
echo "<div class='test-section'>
<h3><i class='bi bi-database me-2'></i>Database Connection</h3>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Database connection successful</p>";
    
    // Test required tables
    $required_tables = ['users', 'members', 'books', 'book_loans', 'book_reservations', 'activity_log', 'notifications'];
    foreach ($required_tables as $table) {
        try {
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM $table");
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Table '$table' - $count records</p>";
        } catch (Exception $e) {
            echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>Table '$table' - Error: " . $e->getMessage() . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>Database connection failed: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 3: Configuration
echo "<div class='test-section'>
<h3><i class='bi bi-gear me-2'></i>Configuration Check</h3>";

try {
    require_once 'config/config.php';
    echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Main configuration loaded</p>";
    echo "<p class='status-pass'><i class='bi bi-info-circle me-2'></i>Base URL: " . (defined('BASE_URL') ? BASE_URL : 'Not defined') . "</p>";
    
    require_once 'config/google_oauth.php';
    echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Google OAuth configuration loaded</p>";
    
    if (function_exists('isGoogleOAuthConfigured')) {
        if (isGoogleOAuthConfigured()) {
            echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Google OAuth is configured</p>";
        } else {
            echo "<p class='status-warning'><i class='bi bi-exclamation-triangle-fill me-2'></i>Google OAuth not configured (optional)</p>";
        }
    }
} catch (Exception $e) {
    echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>Configuration error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 4: Functions
echo "<div class='test-section'>
<h3><i class='bi bi-code-square me-2'></i>Core Functions Check</h3>";

try {
    require_once 'includes/functions.php';
    echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Functions file loaded</p>";
    
    $functions_to_test = [
        'isLoggedIn' => 'User authentication check',
        'isAdmin' => 'Admin role check',
        'isLibrarian' => 'Librarian role check',
        'isMemberLoggedIn' => 'Member authentication check',
        'sanitize' => 'Input sanitization',
        'redirect' => 'Page redirection',
        'formatDate' => 'Date formatting',
        'calculateFine' => 'Fine calculation'
    ];
    
    foreach ($functions_to_test as $function => $description) {
        if (function_exists($function)) {
            echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>$function() - $description</p>";
        } else {
            echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>$function() - $description (MISSING)</p>";
        }
    }
} catch (Exception $e) {
    echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>Functions error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 5: Directory Structure
echo "<div class='test-section'>
<h3><i class='bi bi-folder me-2'></i>Directory Structure</h3>";

$required_dirs = [
    'config' => 'Configuration files',
    'includes' => 'Include files',
    'admin' => 'Admin interface',
    'uploads' => 'File uploads',
    'uploads/covers' => 'Book covers',
    'assets' => 'Static assets',
    'assets/css' => 'Stylesheets',
    'assets/js' => 'JavaScript files'
];

foreach ($required_dirs as $dir => $description) {
    if (is_dir($dir)) {
        echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>$dir/ - $description</p>";
    } else {
        echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>$dir/ - $description (MISSING)</p>";
    }
}
echo "</div>";

// Test 6: URL Routing
echo "<div class='test-section'>
<h3><i class='bi bi-link me-2'></i>URL Routing Test</h3>";

if (function_exists('url')) {
    $test_urls = [
        'home.php' => url('home.php'),
        'login.php' => url('login.php'),
        'admin/dashboard.php' => url('admin/dashboard.php')
    ];
    
    foreach ($test_urls as $path => $generated_url) {
        echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>$path → $generated_url</p>";
    }
} else {
    echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>URL function not available</p>";
}
echo "</div>";

// Summary
echo "<div class='test-section bg-light'>
<h3><i class='bi bi-clipboard-check me-2'></i>System Status Summary</h3>";

if (empty($missing_files)) {
    echo "<div class='alert alert-success'>
        <h5><i class='bi bi-check-circle-fill me-2'></i>System Status: OPERATIONAL</h5>
        <p>All core components are present and functional. Your LMS system is ready to use!</p>
    </div>";
} else {
    echo "<div class='alert alert-warning'>
        <h5><i class='bi bi-exclamation-triangle-fill me-2'></i>System Status: NEEDS ATTENTION</h5>
        <p>Some files are missing. Please check the issues above.</p>
    </div>";
}

echo "<div class='mt-3'>
    <h6>Quick Access Links:</h6>
    <a href='home.php' class='btn btn-primary me-2'><i class='bi bi-house me-1'></i>Home</a>
    <a href='login.php' class='btn btn-secondary me-2'><i class='bi bi-box-arrow-in-right me-1'></i>Login</a>
    <a href='register.php' class='btn btn-success me-2'><i class='bi bi-person-plus me-1'></i>Register</a>
    <a href='catalog.php' class='btn btn-info'><i class='bi bi-book me-1'></i>Catalog</a>
</div>";

echo "</div>";

echo "</div>
</body>
</html>";
?>
