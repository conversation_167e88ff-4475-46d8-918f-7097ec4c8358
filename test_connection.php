<?php
/**
 * Test Database Connection
 */
require_once 'config/database.php';

echo "<h2>Testing Database Connection</h2>";

try {
    // Create database instance
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<p>✅ Successfully connected to the database!</p>";
        
        // Test a simple query
        $query = "SELECT COUNT(*) as book_count FROM books";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        
        echo "<p>✅ Successfully executed a test query.</p>";
        echo "<p>Total books in the database: " . $result['book_count'] . "</p>";
        
        // Get database server info
        $server_info = $db->getAttribute(PDO::ATTR_SERVER_INFO);
        $server_version = $db->getAttribute(PDO::ATTR_SERVER_VERSION);
        
        echo "<h3>Database Server Information:</h3>";
        echo "<ul>";
        echo "<li><strong>Server Version:</strong> " . $server_version . "</li>";
        if ($server_info) {
            echo "<li><strong>Server Info:</strong> " . $server_info . "</li>";
        }
        echo "</ul>";
        
        echo "<p>Your Library Management System is ready to use!</p>";
    } else {
        echo "<p>❌ Failed to connect to the database.</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
