<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Enhanced Features Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-4'>";
echo "<h1 class='mb-4'><i class='bi bi-check-circle text-success me-2'></i>Enhanced Member Dashboard Features Test</h1>";

// Test 1: Database Tables
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h5><i class='bi bi-database me-2'></i>Database Tables Test</h5>";
echo "</div>";
echo "<div class='card-body'>";

$tables_to_check = [
    'member_wishlist' => 'Member Wishlist',
    'book_reviews' => 'Book Reviews',
    'reading_goals' => 'Reading Goals',
    'notifications' => 'Notifications'
];

foreach ($tables_to_check as $table => $name) {
    try {
        $query = "SELECT COUNT(*) as count FROM $table";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $count = $stmt->fetch()['count'];
        echo "<p class='text-success'><i class='bi bi-check-circle me-2'></i><strong>$name:</strong> Table exists with $count records</p>";
    } catch (PDOException $e) {
        echo "<p class='text-danger'><i class='bi bi-x-circle me-2'></i><strong>$name:</strong> Table missing or error - " . $e->getMessage() . "</p>";
    }
}

echo "</div></div>";

// Test 2: Functions
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h5><i class='bi bi-code me-2'></i>Functions Test</h5>";
echo "</div>";
echo "<div class='card-body'>";

$functions_to_test = [
    'timeAgo' => 'Time Ago Formatting',
    'formatDate' => 'Date Formatting',
    'formatDateTime' => 'DateTime Formatting',
    'h' => 'HTML Sanitization'
];

foreach ($functions_to_test as $func => $name) {
    if (function_exists($func)) {
        echo "<p class='text-success'><i class='bi bi-check-circle me-2'></i><strong>$name:</strong> Function available</p>";
        
        // Test the function
        if ($func === 'timeAgo') {
            $test_time = date('Y-m-d H:i:s', strtotime('-2 hours'));
            $result = timeAgo($test_time);
            echo "<p class='ms-4 text-muted'>Test: timeAgo('$test_time') = '$result'</p>";
        } elseif ($func === 'formatDate') {
            $result = formatDate(date('Y-m-d'));
            echo "<p class='ms-4 text-muted'>Test: formatDate(today) = '$result'</p>";
        }
    } else {
        echo "<p class='text-danger'><i class='bi bi-x-circle me-2'></i><strong>$name:</strong> Function missing</p>";
    }
}

echo "</div></div>";

// Test 3: File Structure
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h5><i class='bi bi-files me-2'></i>Enhanced Files Test</h5>";
echo "</div>";
echo "<div class='card-body'>";

$files_to_check = [
    'member/wishlist.php' => 'Wishlist Management',
    'member/book_reviews.php' => 'Book Reviews System',
    'member/reading_goals.php' => 'Reading Goals Tracker',
    'member/member_dashboard.php' => 'Enhanced Dashboard',
    'database/setup_enhanced_features.php' => 'Database Setup Script'
];

foreach ($files_to_check as $file => $name) {
    if (file_exists($file)) {
        echo "<p class='text-success'><i class='bi bi-check-circle me-2'></i><strong>$name:</strong> File exists</p>";
    } else {
        echo "<p class='text-danger'><i class='bi bi-x-circle me-2'></i><strong>$name:</strong> File missing</p>";
    }
}

echo "</div></div>";

// Test 4: Sample Data
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h5><i class='bi bi-data me-2'></i>Sample Data Test</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    // Check for sample notifications
    $query = "SELECT COUNT(*) as count FROM notifications";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $notification_count = $stmt->fetch()['count'];
    
    if ($notification_count > 0) {
        echo "<p class='text-success'><i class='bi bi-check-circle me-2'></i><strong>Notifications:</strong> $notification_count sample notifications found</p>";
        
        // Show sample notifications
        $query = "SELECT * FROM notifications LIMIT 3";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $notifications = $stmt->fetchAll();
        
        echo "<div class='ms-4'>";
        foreach ($notifications as $notification) {
            echo "<div class='alert alert-{$notification['type']} alert-sm'>";
            echo "<small><strong>{$notification['type']}:</strong> {$notification['message']}</small>";
            echo "</div>";
        }
        echo "</div>";
    } else {
        echo "<p class='text-warning'><i class='bi bi-exclamation-triangle me-2'></i><strong>Notifications:</strong> No sample data found</p>";
    }
} catch (PDOException $e) {
    echo "<p class='text-danger'><i class='bi bi-x-circle me-2'></i><strong>Notifications:</strong> Error - " . $e->getMessage() . "</p>";
}

echo "</div></div>";

// Test 5: Dashboard Features
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-secondary text-white'>";
echo "<h5><i class='bi bi-speedometer2 me-2'></i>Dashboard Features Summary</h5>";
echo "</div>";
echo "<div class='card-body'>";

$features = [
    'Enhanced Navigation Bar' => 'Quick search, notification bell, improved user menu',
    'Smart Alert System' => 'Overdue and due-soon book alerts',
    'Enhanced Statistics' => '6 statistics cards with reading analytics',
    'Wishlist Feature' => 'Save books for future reading',
    'Book Reviews & Ratings' => '5-star rating system with written reviews',
    'Reading Goals Tracking' => 'Annual goals with progress charts',
    'Enhanced Quick Actions' => '8 action buttons with visual icons',
    'Recent Activity Timeline' => 'Visual timeline of recent book returns',
    'Modern Visual Design' => 'Gradient cards, hover effects, responsive layout'
];

foreach ($features as $feature => $description) {
    echo "<div class='row mb-2'>";
    echo "<div class='col-md-4'><strong>$feature:</strong></div>";
    echo "<div class='col-md-8 text-muted'>$description</div>";
    echo "</div>";
}

echo "</div></div>";

// Test 6: Next Steps
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-dark text-white'>";
echo "<h5><i class='bi bi-arrow-right me-2'></i>Next Steps</h5>";
echo "</div>";
echo "<div class='card-body'>";

echo "<ol>";
echo "<li><strong>Access the Enhanced Dashboard:</strong> <a href='member/member_dashboard.php' class='btn btn-primary btn-sm ms-2'>Go to Member Dashboard</a></li>";
echo "<li><strong>Test Wishlist Feature:</strong> <a href='member/wishlist.php' class='btn btn-danger btn-sm ms-2'>View Wishlist</a></li>";
echo "<li><strong>Try Book Reviews:</strong> <a href='member/book_reviews.php' class='btn btn-warning btn-sm ms-2'>Book Reviews</a></li>";
echo "<li><strong>Set Reading Goals:</strong> <a href='member/reading_goals.php' class='btn btn-success btn-sm ms-2'>Reading Goals</a></li>";
echo "<li><strong>Browse Enhanced Catalog:</strong> <a href='catalog.php' class='btn btn-info btn-sm ms-2'>Book Catalog</a></li>";
echo "</ol>";

echo "<div class='alert alert-info mt-3'>";
echo "<h6><i class='bi bi-info-circle me-2'></i>Important Notes:</h6>";
echo "<ul class='mb-0'>";
echo "<li>Make sure you're logged in as a member to access all features</li>";
echo "<li>Some features require existing book loans or reading history</li>";
echo "<li>The notification system works best with sample data</li>";
echo "<li>All features are mobile-responsive and work on all devices</li>";
echo "</ul>";
echo "</div>";

echo "</div></div>";

echo "</div>"; // container

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
