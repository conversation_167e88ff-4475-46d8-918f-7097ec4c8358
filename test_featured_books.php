<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get featured books (newest additions)
$query = "SELECT * FROM books ORDER BY created_at DESC LIMIT 3";
$stmt = $db->prepare($query);
$stmt->execute();
$featured_books = $stmt->fetchAll();

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Featured Books</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .book-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
        }
        .book-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        .book-cover {
            height: 250px;
            overflow: hidden;
            position: relative;
        }
        .book-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        .book-card:hover .book-cover img {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>Featured Books Test</h2>
        <p class="text-muted">Testing the featured books display with cover images</p>
        
        <div class="row">
            <?php foreach ($featured_books as $book): ?>
                <div class="col-md-4 mb-4">
                    <div class="book-card">
                        <div class="book-cover">
                            <?php if (!empty($book['cover_image'])): ?>
                                <?php
                                // Check if the cover_image is a URL or a local file
                                if (strpos($book['cover_image'], 'http') === 0) {
                                    $image_src = $book['cover_image'];
                                } else {
                                    // Try different paths to find the image
                                    $possible_paths = [
                                        'uploads/covers/' . $book['cover_image'],
                                        './uploads/covers/' . $book['cover_image']
                                    ];
                                    
                                    $image_src = 'uploads/covers/' . $book['cover_image']; // Default
                                    foreach ($possible_paths as $path) {
                                        if (file_exists($path)) {
                                            $image_src = $path;
                                            break;
                                        }
                                    }
                                }
                                ?>
                                <img src="<?php echo h($image_src); ?>" alt="<?php echo h($book['title']); ?>"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div style="display: none; height: 100%; align-items: center; justify-content: center; background-color: #f8f9fa;">
                                    <i class="bi bi-book fs-1 text-muted"></i>
                                </div>
                            <?php else: ?>
                                <div style="height: 100%; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa;">
                                    <i class="bi bi-book fs-1 text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="p-3">
                            <h5 class="card-title"><?php echo h($book['title']); ?></h5>
                            <p class="card-text text-muted">by <?php echo h($book['author']); ?></p>
                            <?php if (!empty($book['category'])): ?>
                                <span class="badge bg-secondary mb-2"><?php echo h($book['category']); ?></span>
                            <?php endif; ?>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-<?php echo ($book['available_quantity'] > 0) ? 'success' : 'danger'; ?>">
                                    <i class="bi <?php echo ($book['available_quantity'] > 0) ? 'bi-check-circle' : 'bi-x-circle'; ?>"></i>
                                    <?php echo ($book['available_quantity'] > 0) ? 'Available' : 'Unavailable'; ?>
                                </small>
                                <a href="book_details.php?id=<?php echo h($book['id']); ?>" class="btn btn-sm btn-primary">View Details</a>
                            </div>
                            
                            <!-- Debug info -->
                            <div class="mt-2">
                                <small class="text-muted">
                                    Cover: <?php echo h($book['cover_image']); ?><br>
                                    Path: <?php echo h($image_src ?? 'N/A'); ?><br>
                                    Exists: <?php echo isset($image_src) && file_exists($image_src) ? 'Yes' : 'No'; ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="mt-4">
            <h3>Actions</h3>
            <a href="home.php" class="btn btn-primary">View Home Page</a>
            <a href="catalog.php" class="btn btn-secondary">View Catalog</a>
            <a href="final_cover_fix.php" class="btn btn-warning">Fix Covers</a>
        </div>
        
        <div class="mt-4">
            <h3>Debug Information</h3>
            <div class="alert alert-info">
                <p><strong>Total featured books:</strong> <?php echo count($featured_books); ?></p>
                <p><strong>Current directory:</strong> <?php echo getcwd(); ?></p>
                <p><strong>Script name:</strong> <?php echo $_SERVER['SCRIPT_NAME']; ?></p>
            </div>
        </div>
    </div>
</body>
</html>
