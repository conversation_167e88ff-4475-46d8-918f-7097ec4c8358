<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Display current session information
echo "<h2>Librarian Dashboard Test</h2>";
echo "<p>This script helps test the librarian dashboard functionality.</p>";

echo "<h3>Current Session Data:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>Authentication Status:</h3>";
echo "isLoggedIn(): " . (isLoggedIn() ? "true" : "false") . "<br>";
echo "isAdmin(): " . (isAdmin() ? "true" : "false") . "<br>";
echo "isLibrarian(): " . (isLibrarian() ? "true" : "false") . "<br>";
echo "isMemberLoggedIn(): " . (isMemberLoggedIn() ? "true" : "false") . "<br>";

// Set up librarian session for testing
echo "<h3>Set Up Librarian Session:</h3>";
echo "<form method='post' action=''>";
echo "<button type='submit' name='set_librarian' class='btn btn-primary'>Set Up Librarian Session</button>";
echo "</form>";

// Process form submission
if (isset($_POST['set_librarian'])) {
    // Clear session first
    session_unset();
    
    // Set up librarian session
    $_SESSION['user_id'] = 2;
    $_SESSION['username'] = 'librarian';
    $_SESSION['email'] = '<EMAIL>';
    $_SESSION['role'] = 'librarian';
    $_SESSION['full_name'] = 'Test Librarian';
    
    echo "<p>Session set to Librarian role.</p>";
    echo "<p>Page will refresh in 2 seconds...</p>";
    echo "<script>setTimeout(function() { window.location.reload(); }, 2000);</script>";
}

echo "<h3>Links:</h3>";
echo "<a href='librarian/dashboard.php'>Go to Librarian Dashboard</a><br>";
echo "<a href='login.php'>Go to Login</a><br>";
echo "<a href='logout.php'>Logout</a><br>";
echo "<a href='test_session.php'>Go to Session Test</a><br>";
?>
