<?php
/**
 * Test Librarian Payment Access
 * Verify that librarians can access payment features
 */

session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h1>🧪 Testing Librarian Payment Access</h1>";

// Connect to database
try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>📋 Current Session Status</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Session Data:</strong><br>";
    echo "<pre>";
    print_r($_SESSION);
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>🔐 Authentication Check</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "isLoggedIn(): " . (isLoggedIn() ? "✅ Yes" : "❌ No") . "<br>";
    echo "isAdmin(): " . (isAdmin() ? "✅ Yes" : "❌ No") . "<br>";
    echo "isLibrarian(): " . (isLibrarian() ? "✅ Yes" : "❌ No") . "<br>";
    echo "isStaffWithFinancialAccess(): " . (isStaffWithFinancialAccess() ? "✅ Yes" : "❌ No") . "<br>";
    echo "</div>";
    
    // Auto-login as librarian for testing
    if (!isLoggedIn()) {
        echo "<h2>🔄 Auto-Login as Librarian</h2>";
        
        $query = "SELECT * FROM users WHERE role = 'librarian' LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $librarian = $stmt->fetch();
        
        if ($librarian) {
            $_SESSION['user_id'] = $librarian['id'];
            $_SESSION['username'] = $librarian['username'];
            $_SESSION['email'] = $librarian['email'];
            $_SESSION['role'] = $librarian['role'];
            $_SESSION['full_name'] = $librarian['full_name'] ?: $librarian['username'];
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "✅ Successfully logged in as librarian: " . htmlspecialchars($librarian['username']);
            echo "</div>";
            
            // Refresh the page to show updated status
            echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "❌ No librarian user found in database. Please create a librarian user first.";
            echo "</div>";
        }
    }
    
    if (isLoggedIn()) {
        echo "<h2>🎯 Payment Feature Access Test</h2>";
        
        $access_tests = [
            'Payment Processing' => 'admin/payment_processing.php',
            'Payment Reports' => 'admin/payment_reports.php',
            'Payment Receipt' => 'admin/payment_receipt.php?receipt=test',
            'Financial Management' => 'admin/financial_management.php'
        ];
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>Access Test Results:</strong><br><br>";
        
        foreach ($access_tests as $feature => $url) {
            $can_access = isStaffWithFinancialAccess();
            $status = $can_access ? "✅ ALLOWED" : "❌ DENIED";
            $color = $can_access ? "green" : "red";
            
            echo "<div style='margin: 5px 0;'>";
            echo "<strong>$feature:</strong> ";
            echo "<span style='color: $color;'>$status</span> ";
            if ($can_access) {
                echo "- <a href='$url' target='_blank'>Test Access</a>";
            }
            echo "</div>";
        }
        echo "</div>";
        
        echo "<h2>📊 Sample Data Check</h2>";
        
        // Check for sample fines
        $fines_query = "SELECT COUNT(*) as count FROM fines WHERE status = 'unpaid'";
        $stmt = $db->prepare($fines_query);
        $stmt->execute();
        $unpaid_fines = $stmt->fetch()['count'];
        
        $payments_query = "SELECT COUNT(*) as count FROM payment_transactions";
        $stmt = $db->prepare($payments_query);
        $stmt->execute();
        $total_payments = $stmt->fetch()['count'];
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>Database Status:</strong><br>";
        echo "Unpaid Fines: $unpaid_fines<br>";
        echo "Total Payment Transactions: $total_payments<br>";
        echo "</div>";
        
        if ($unpaid_fines == 0) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "⚠️ <strong>No unpaid fines found.</strong> Run the setup script to create sample data:<br>";
            echo "<a href='setup_payment_features.php'>setup_payment_features.php</a>";
            echo "</div>";
        }
    }
    
    echo "<h2>🔗 Quick Access Links</h2>";
    echo "<div style='background: #cce7ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Librarian Dashboard Links:</strong><br>";
    echo "<a href='librarian/dashboard.php'>📊 Librarian Dashboard</a><br><br>";
    
    if (isStaffWithFinancialAccess()) {
        echo "<strong>Payment Features:</strong><br>";
        echo "<a href='admin/payment_processing.php'>💳 Payment Processing</a><br>";
        echo "<a href='admin/payment_reports.php'>📈 Payment Reports</a><br>";
        echo "<a href='admin/financial_management.php'>💰 Financial Overview</a><br><br>";
    }
    
    echo "<strong>Setup & Testing:</strong><br>";
    echo "<a href='setup_payment_features.php'>⚙️ Setup Payment Features</a><br>";
    echo "<a href='test_librarian_payment_access.php'>🧪 Refresh This Test</a><br>";
    echo "</div>";
    
    echo "<h2>📝 Instructions</h2>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ol>";
    echo "<li><strong>Login as Librarian:</strong> Use your librarian credentials or the auto-login above</li>";
    echo "<li><strong>Access Dashboard:</strong> Go to the librarian dashboard</li>";
    echo "<li><strong>Find Payment Features:</strong> Look for 'Financial Management' section in the sidebar</li>";
    echo "<li><strong>Test Features:</strong> Click on Payment Processing, Payment Reports, or Financial Overview</li>";
    echo "<li><strong>Process Payments:</strong> Try processing a fine payment with different payment methods</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "Error: " . htmlspecialchars($e->getMessage());
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2 {
    color: #333;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 3px;
    overflow-x: auto;
}
</style>
