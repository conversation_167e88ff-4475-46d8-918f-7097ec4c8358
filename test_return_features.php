<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Return & Rating Features Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-4'>";
echo "<h1 class='mb-4'><i class='bi bi-arrow-return-left text-success me-2'></i>Book Return & Rating Features Test</h1>";

// Test 1: Return Feature Files
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h5><i class='bi bi-files me-2'></i>Return Feature Files</h5>";
echo "</div>";
echo "<div class='card-body'>";

$return_files = [
    'member/return_book.php' => 'Book Return Interface',
    'member/member_dashboard.php' => 'Enhanced Dashboard with Return Buttons'
];

foreach ($return_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='text-success'><i class='bi bi-check-circle me-2'></i><strong>$description:</strong> File exists</p>";
    } else {
        echo "<p class='text-danger'><i class='bi bi-x-circle me-2'></i><strong>$description:</strong> File missing</p>";
    }
}

echo "</div></div>";

// Test 2: Database Tables for Return & Rating
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h5><i class='bi bi-database me-2'></i>Database Tables for Return & Rating</h5>";
echo "</div>";
echo "<div class='card-body'>";

$tables_for_return = [
    'book_loans' => 'Book Loans (for return tracking)',
    'book_reviews' => 'Book Reviews (for ratings)',
    'books' => 'Books (for updating availability)'
];

foreach ($tables_for_return as $table => $description) {
    try {
        $query = "DESCRIBE $table";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $columns = $stmt->fetchAll();
        echo "<p class='text-success'><i class='bi bi-check-circle me-2'></i><strong>$description:</strong> Table exists with " . count($columns) . " columns</p>";
        
        // Show important columns for each table
        if ($table === 'book_loans') {
            $important_cols = ['id', 'member_id', 'book_id', 'status', 'due_date', 'return_date', 'fine'];
            echo "<div class='ms-4'><small class='text-muted'>Key columns: " . implode(', ', $important_cols) . "</small></div>";
        } elseif ($table === 'book_reviews') {
            $important_cols = ['id', 'book_id', 'member_id', 'rating', 'review_text', 'review_date'];
            echo "<div class='ms-4'><small class='text-muted'>Key columns: " . implode(', ', $important_cols) . "</small></div>";
        }
        
    } catch (PDOException $e) {
        echo "<p class='text-danger'><i class='bi bi-x-circle me-2'></i><strong>$description:</strong> Table missing or error</p>";
    }
}

echo "</div></div>";

// Test 3: Sample Data for Testing
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h5><i class='bi bi-data me-2'></i>Sample Data for Testing</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    // Check for active loans
    $query = "SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $active_loans = $stmt->fetch()['count'];
    
    if ($active_loans > 0) {
        echo "<p class='text-success'><i class='bi bi-check-circle me-2'></i><strong>Active Loans:</strong> $active_loans books currently borrowed (available for return testing)</p>";
        
        // Show sample active loans
        $query = "SELECT bl.id, bl.member_id, b.title, b.author, bl.due_date 
                  FROM book_loans bl 
                  JOIN books b ON bl.book_id = b.id 
                  WHERE bl.status = 'borrowed' 
                  LIMIT 3";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $sample_loans = $stmt->fetchAll();
        
        echo "<div class='ms-4'>";
        echo "<small class='text-muted'>Sample active loans:</small><br>";
        foreach ($sample_loans as $loan) {
            echo "<small>• Member {$loan['member_id']}: '{$loan['title']}' by {$loan['author']} (Due: {$loan['due_date']})</small><br>";
        }
        echo "</div>";
    } else {
        echo "<p class='text-warning'><i class='bi bi-exclamation-triangle me-2'></i><strong>Active Loans:</strong> No active loans found (create some test loans for testing)</p>";
    }
    
    // Check for existing reviews
    $query = "SELECT COUNT(*) as count FROM book_reviews";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $review_count = $stmt->fetch()['count'];
    
    echo "<p class='text-info'><i class='bi bi-star me-2'></i><strong>Book Reviews:</strong> $review_count reviews in database</p>";
    
} catch (PDOException $e) {
    echo "<p class='text-danger'><i class='bi bi-x-circle me-2'></i><strong>Sample Data:</strong> Error checking data - " . $e->getMessage() . "</p>";
}

echo "</div></div>";

// Test 4: Return & Rating Features
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h5><i class='bi bi-star-half me-2'></i>Return & Rating Features</h5>";
echo "</div>";
echo "<div class='card-body'>";

$features = [
    'Self-Return System' => 'Members can return books themselves with automatic processing',
    'Integrated Rating' => 'Rate books (1-5 stars) during the return process',
    'Written Reviews' => 'Optional text reviews when returning books',
    'Quick Return from Dashboard' => 'Return books directly from the dashboard with modal',
    'Automatic Fine Calculation' => 'Overdue fines calculated automatically',
    'Inventory Update' => 'Book availability updated when returned',
    'Activity Logging' => 'Return activities logged for tracking',
    'Success Notifications' => 'Confirmation messages after successful returns'
];

foreach ($features as $feature => $description) {
    echo "<div class='row mb-2'>";
    echo "<div class='col-md-4'><strong>$feature:</strong></div>";
    echo "<div class='col-md-8 text-muted'>$description</div>";
    echo "</div>";
}

echo "</div></div>";

// Test 5: How to Test
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-dark text-white'>";
echo "<h5><i class='bi bi-play-circle me-2'></i>How to Test the Features</h5>";
echo "</div>";
echo "<div class='card-body'>";

echo "<h6>Testing Steps:</h6>";
echo "<ol>";
echo "<li><strong>Login as a Member:</strong> Use any existing member account</li>";
echo "<li><strong>Check Current Loans:</strong> Ensure you have some borrowed books</li>";
echo "<li><strong>Access Return Features:</strong>";
echo "<ul>";
echo "<li>Click 'Return Books' from quick actions on dashboard</li>";
echo "<li>Or click 'Return' button next to any book in Current Loans table</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Test Return Process:</strong>";
echo "<ul>";
echo "<li>Select a book to return</li>";
echo "<li>Rate the book (1-5 stars)</li>";
echo "<li>Write an optional review</li>";
echo "<li>Click 'Return Book'</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Verify Results:</strong>";
echo "<ul>";
echo "<li>Book should be removed from current loans</li>";
echo "<li>Book availability should increase</li>";
echo "<li>Review should appear in book reviews</li>";
echo "<li>Success message should be displayed</li>";
echo "</ul>";
echo "</li>";
echo "</ol>";

echo "<div class='alert alert-info mt-3'>";
echo "<h6><i class='bi bi-info-circle me-2'></i>Quick Test Links:</h6>";
echo "<div class='d-flex flex-wrap gap-2'>";
echo "<a href='member/member_dashboard.php' class='btn btn-primary btn-sm'>Member Dashboard</a>";
echo "<a href='member/return_book.php' class='btn btn-success btn-sm'>Return Books Page</a>";
echo "<a href='member/book_reviews.php' class='btn btn-warning btn-sm'>Book Reviews</a>";
echo "<a href='catalog.php' class='btn btn-info btn-sm'>Book Catalog</a>";
echo "</div>";
echo "</div>";

echo "</div></div>";

// Test 6: Expected Benefits
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-secondary text-white'>";
echo "<h5><i class='bi bi-trophy me-2'></i>Expected Benefits</h5>";
echo "</div>";
echo "<div class='card-body'>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h6>For Members:</h6>";
echo "<ul>";
echo "<li>Convenient self-service returns</li>";
echo "<li>Immediate feedback through ratings</li>";
echo "<li>No need to visit library for returns</li>";
echo "<li>Track reading history with reviews</li>";
echo "<li>Quick return from dashboard</li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h6>For Library:</h6>";
echo "<ul>";
echo "<li>Reduced staff workload</li>";
echo "<li>Automatic inventory management</li>";
echo "<li>Member engagement through reviews</li>";
echo "<li>Better book recommendations</li>";
echo "<li>Detailed usage analytics</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div></div>";

echo "</div>"; // container

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
