<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Display current session information
echo "<h2>Session Test</h2>";
echo "<p>This script displays current session information to help debug redirection issues.</p>";

echo "<h3>Current Session Data:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>Authentication Status:</h3>";
echo "isLoggedIn(): " . (isLoggedIn() ? "true" : "false") . "<br>";
echo "isAdmin(): " . (isAdmin() ? "true" : "false") . "<br>";
echo "isLibrarian(): " . (isLibrarian() ? "true" : "false") . "<br>";
echo "isMemberLoggedIn(): " . (isMemberLoggedIn() ? "true" : "false") . "<br>";

echo "<h3>Links:</h3>";
echo "<a href='index.php'>Go to Index</a><br>";
echo "<a href='admin/dashboard.php'>Go to Admin Dashboard</a><br>";
echo "<a href='librarian/dashboard.php'>Go to Librarian Dashboard</a><br>";
echo "<a href='login.php'>Go to Login</a><br>";
echo "<a href='logout.php'>Logout</a><br>";

// Add a form to simulate different user roles for testing
echo "<h3>Test Different Roles:</h3>";
echo "<form method='post' action=''>";
echo "<select name='role'>";
echo "<option value=''>Select a role to simulate</option>";
echo "<option value='admin'>Admin</option>";
echo "<option value='librarian'>Librarian</option>";
echo "<option value='other'>Other Staff</option>";
echo "<option value='member'>Member</option>";
echo "<option value='clear'>Clear Session</option>";
echo "</select>";
echo "<button type='submit'>Set Role</button>";
echo "</form>";

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['role'])) {
    $role = $_POST['role'];
    
    // Clear session first
    session_unset();
    
    if ($role === 'admin') {
        $_SESSION['user_id'] = 1;
        $_SESSION['username'] = 'admin';
        $_SESSION['email'] = '<EMAIL>';
        $_SESSION['role'] = 'admin';
        echo "<p>Session set to Admin role.</p>";
    } elseif ($role === 'librarian') {
        $_SESSION['user_id'] = 2;
        $_SESSION['username'] = 'librarian';
        $_SESSION['email'] = '<EMAIL>';
        $_SESSION['role'] = 'librarian';
        $_SESSION['full_name'] = 'Test Librarian';
        echo "<p>Session set to Librarian role.</p>";
    } elseif ($role === 'other') {
        $_SESSION['user_id'] = 3;
        $_SESSION['username'] = 'staff';
        $_SESSION['email'] = '<EMAIL>';
        $_SESSION['role'] = 'staff';
        echo "<p>Session set to Other Staff role.</p>";
    } elseif ($role === 'member') {
        $_SESSION['member_id'] = 1;
        $_SESSION['member_name'] = 'Test Member';
        $_SESSION['member_email'] = '<EMAIL>';
        echo "<p>Session set to Member role.</p>";
    } elseif ($role === 'clear') {
        echo "<p>Session cleared.</p>";
    }
    
    // Refresh the page to show updated session info
    echo "<p>Page will refresh in 2 seconds...</p>";
    echo "<script>setTimeout(function() { window.location.reload(); }, 2000);</script>";
}
?>
