<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h1>View Details Test</h1>";

// Test database connection
echo "<h2>1. Database Connection Test</h2>";
try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<p style='color: green;'>✅ Database connected successfully</p>";
    } else {
        echo "<p style='color: red;'>❌ Database connection failed</p>";
        exit;
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
    exit;
}

// Test books table
echo "<h2>2. Books Table Test</h2>";
try {
    $query = "SELECT COUNT(*) as count FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $count = $stmt->fetch()['count'];
    echo "<p style='color: green;'>✅ Books table accessible. Found $count books.</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Books table error: " . $e->getMessage() . "</p>";
    exit;
}

// Get a sample book
echo "<h2>3. Sample Book Test</h2>";
try {
    $query = "SELECT * FROM books LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $book = $stmt->fetch();
    
    if ($book) {
        echo "<p style='color: green;'>✅ Sample book found:</p>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> " . $book['id'] . "</li>";
        echo "<li><strong>Title:</strong> " . htmlspecialchars($book['title']) . "</li>";
        echo "<li><strong>Author:</strong> " . htmlspecialchars($book['author']) . "</li>";
        echo "</ul>";
        
        // Test the book_details.php link
        $book_details_url = "book_details.php?id=" . $book['id'];
        echo "<p><a href='$book_details_url' class='btn btn-primary' target='_blank'>Test View Details Link</a></p>";
        
    } else {
        echo "<p style='color: red;'>❌ No books found in database</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Sample book error: " . $e->getMessage() . "</p>";
}

// Test functions
echo "<h2>4. Functions Test</h2>";
$functions_to_test = ['isLoggedIn', 'isMemberLoggedIn', 'getCurrentUrl', 'h'];
foreach ($functions_to_test as $func) {
    if (function_exists($func)) {
        echo "<p style='color: green;'>✅ Function $func exists</p>";
    } else {
        echo "<p style='color: red;'>❌ Function $func missing</p>";
    }
}

// Test getCurrentUrl specifically
if (function_exists('getCurrentUrl')) {
    try {
        $current_url = getCurrentUrl();
        echo "<p style='color: green;'>✅ getCurrentUrl() works: $current_url</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ getCurrentUrl() error: " . $e->getMessage() . "</p>";
    }
}

// Test file paths
echo "<h2>5. File Paths Test</h2>";
$files_to_check = [
    'book_details.php',
    'catalog.php',
    'config/database.php',
    'config/config.php',
    'includes/functions.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ File exists: $file</p>";
    } else {
        echo "<p style='color: red;'>❌ File missing: $file</p>";
    }
}

echo "<h2>6. Catalog Test</h2>";
echo "<p><a href='catalog.php' class='btn btn-secondary' target='_blank'>Test Catalog Page</a></p>";

echo "<style>
.btn { 
    display: inline-block; 
    padding: 8px 16px; 
    background: #007bff; 
    color: white; 
    text-decoration: none; 
    border-radius: 4px; 
    margin: 5px;
}
.btn-secondary { background: #6c757d; }
</style>";
?>
