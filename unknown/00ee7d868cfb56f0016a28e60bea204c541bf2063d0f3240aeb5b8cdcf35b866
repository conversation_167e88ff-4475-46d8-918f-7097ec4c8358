<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect(url('../auth/login.php'));
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    setMessage('Invalid book ID', 'danger');
    redirect(url('books/index.php'));
}

$book_id = (int)$_GET['id'];

// Get book details
$query = "SELECT * FROM books WHERE id = :id";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $book_id);
$stmt->execute();

if ($stmt->rowCount() == 0) {
    setMessage('Book not found', 'danger');
    redirect(url('books/index.php'));
}

$book = $stmt->fetch();

// Get loan history for this book
$query = "SELECT bl.*, m.first_name, m.last_name
          FROM book_loans bl
          JOIN members m ON bl.member_id = m.id
          WHERE bl.book_id = :book_id
          ORDER BY bl.issue_date DESC
          LIMIT 10";
$stmt = $db->prepare($query);
$stmt->bindParam(':book_id', $book_id);
$stmt->execute();
$loan_history = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $book['title']; ?> - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Book Details</h1>
                    <div>
                        <a href="<?php echo url('books/edit.php?id=' . $book_id); ?>" class="btn btn-sm btn-warning">
                            <i class="bi bi-pencil"></i> Edit
                        </a>
                        <a href="<?php echo url('books/index.php'); ?>" class="btn btn-sm btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Books
                        </a>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <?php if (!empty($book['cover_image'])): ?>
                                    <?php
                                    // Try different paths to find the image
                                    $possible_paths = [
                                        '../uploads/covers/' . $book['cover_image'],
                                        'uploads/covers/' . $book['cover_image']
                                    ];

                                    $image_src = '../uploads/covers/' . $book['cover_image']; // Default
                                    foreach ($possible_paths as $path) {
                                        if (file_exists($path)) {
                                            $image_src = $path;
                                            break;
                                        }
                                    }
                                    ?>
                                    <img src="<?php echo $image_src; ?>" alt="<?php echo $book['title']; ?>" class="img-fluid mb-3" style="max-height: 300px;"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <div class="bg-light p-5 mb-3 d-flex align-items-center justify-content-center" style="display: none;">
                                        <i class="bi bi-book fs-1 text-secondary"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="bg-light p-5 mb-3 d-flex align-items-center justify-content-center">
                                        <i class="bi bi-book fs-1 text-secondary"></i>
                                    </div>
                                <?php endif; ?>

                                <h4 class="card-title"><?php echo $book['title']; ?></h4>
                                <h6 class="card-subtitle mb-2 text-muted"><?php echo $book['author']; ?></h6>

                                <div class="d-grid gap-2 mt-3">
                                    <?php if ($book['available_quantity'] > 0): ?>
                                        <a href="<?php echo url('loans/issue.php?book_id=' . $book_id); ?>" class="btn btn-success">
                                            <i class="bi bi-journal-arrow-up"></i> Issue Book
                                        </a>
                                    <?php else: ?>
                                        <button class="btn btn-secondary" disabled>
                                            <i class="bi bi-journal-x"></i> Not Available
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-8 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Book Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-md-3 fw-bold">ISBN:</div>
                                    <div class="col-md-9"><?php echo $book['isbn'] ?: 'N/A'; ?></div>
                                </div>

                                <div class="row mb-2">
                                    <div class="col-md-3 fw-bold">Category:</div>
                                    <div class="col-md-9"><?php echo $book['category'] ?: 'N/A'; ?></div>
                                </div>

                                <div class="row mb-2">
                                    <div class="col-md-3 fw-bold">Publication Year:</div>
                                    <div class="col-md-9"><?php echo $book['publication_year'] ?: 'N/A'; ?></div>
                                </div>

                                <div class="row mb-2">
                                    <div class="col-md-3 fw-bold">Publisher:</div>
                                    <div class="col-md-9"><?php echo $book['publisher'] ?: 'N/A'; ?></div>
                                </div>

                                <div class="row mb-2">
                                    <div class="col-md-3 fw-bold">Total Copies:</div>
                                    <div class="col-md-9"><?php echo $book['quantity']; ?></div>
                                </div>

                                <div class="row mb-2">
                                    <div class="col-md-3 fw-bold">Available Copies:</div>
                                    <div class="col-md-9">
                                        <?php if ($book['available_quantity'] == 0): ?>
                                            <span class="badge bg-danger">Not Available</span>
                                        <?php elseif ($book['available_quantity'] < $book['quantity']): ?>
                                            <span class="badge bg-warning text-dark"><?php echo $book['available_quantity']; ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-success"><?php echo $book['available_quantity']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="row mb-2">
                                    <div class="col-md-3 fw-bold">Shelf Location:</div>
                                    <div class="col-md-9"><?php echo $book['shelf_location'] ?: 'N/A'; ?></div>
                                </div>

                                <div class="row mb-2">
                                    <div class="col-md-3 fw-bold">Added On:</div>
                                    <div class="col-md-9"><?php echo formatDate($book['created_at']); ?></div>
                                </div>

                                <?php if (!empty($book['description'])): ?>
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h6 class="fw-bold">Description:</h6>
                                        <p><?php echo nl2br($book['description']); ?></p>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Loan History -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Loan History</h5>
                            </div>
                            <div class="card-body">
                                <?php if (count($loan_history) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Member</th>
                                                <th>Issue Date</th>
                                                <th>Due Date</th>
                                                <th>Return Date</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($loan_history as $loan): ?>
                                            <tr>
                                                <td><?php echo $loan['first_name'] . ' ' . $loan['last_name']; ?></td>
                                                <td><?php echo formatDate($loan['issue_date']); ?></td>
                                                <td><?php echo formatDate($loan['due_date']); ?></td>
                                                <td><?php echo $loan['return_date'] ? formatDate($loan['return_date']) : '-'; ?></td>
                                                <td>
                                                    <?php if ($loan['status'] === 'borrowed'): ?>
                                                        <span class="badge bg-primary">Borrowed</span>
                                                    <?php elseif ($loan['status'] === 'returned'): ?>
                                                        <span class="badge bg-success">Returned</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Overdue</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php else: ?>
                                <p class="text-muted">No loan history available for this book.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
</body>
</html>
