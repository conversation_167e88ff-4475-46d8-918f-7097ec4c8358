<?php
/**
 * Member Analytics API
 * Provides detailed member statistics and analytics data
 */

header('Content-Type: application/json');
session_start();

require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();

    $analytics = [];

    // Enhanced member statistics
    $member_stats_query = "
        SELECT
            (SELECT COUNT(*) FROM members) as total_members,
            (SELECT COUNT(DISTINCT member_id) FROM book_loans WHERE status = 'borrowed') as members_with_active_loans,
            (SELECT COUNT(DISTINCT member_id) FROM book_loans WHERE status = 'returned') as members_who_returned,
            (SELECT COUNT(DISTINCT member_id) FROM book_loans WHERE status = 'overdue') as members_with_overdue,
            (SELECT COUNT(DISTINCT member_id) FROM book_loans) as members_who_borrowed,
            (SELECT SUM(fine) FROM book_loans WHERE fine > 0) as total_fines,
            (SELECT COUNT(*) FROM book_loans WHERE fine > 0) as loans_with_fines,
            (SELECT COUNT(DISTINCT member_id) FROM book_loans WHERE fine > 0) as members_with_fines,
            (SELECT COUNT(*) FROM members WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) as new_members_this_month,
            (SELECT COUNT(*) FROM members WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)) as new_members_this_week,
            (SELECT ROUND((SELECT COUNT(*) FROM book_loans) / (SELECT COUNT(*) FROM members), 1)) as avg_loans_per_member
    ";
    $member_stats_stmt = $db->prepare($member_stats_query);
    $member_stats_stmt->execute();
    $analytics['member_stats'] = $member_stats_stmt->fetch();

    // Member activity levels
    $activity_levels_query = "
        SELECT
            CASE
                WHEN loan_count = 0 THEN 'Inactive'
                WHEN loan_count BETWEEN 1 AND 2 THEN 'Light Reader'
                WHEN loan_count BETWEEN 3 AND 5 THEN 'Regular Reader'
                WHEN loan_count BETWEEN 6 AND 10 THEN 'Active Reader'
                WHEN loan_count > 10 THEN 'Power Reader'
            END as activity_level,
            COUNT(*) as member_count
        FROM (
            SELECT m.id, COUNT(bl.id) as loan_count
            FROM members m
            LEFT JOIN book_loans bl ON m.id = bl.member_id
            GROUP BY m.id
        ) as member_activity
        GROUP BY activity_level
        ORDER BY
            CASE activity_level
                WHEN 'Inactive' THEN 1
                WHEN 'Light Reader' THEN 2
                WHEN 'Regular Reader' THEN 3
                WHEN 'Active Reader' THEN 4
                WHEN 'Power Reader' THEN 5
            END
    ";
    $activity_stmt = $db->prepare($activity_levels_query);
    $activity_stmt->execute();
    $analytics['activity_levels'] = $activity_stmt->fetchAll();

    // Top borrowers
    $top_borrowers_query = "
        SELECT m.id, m.first_name, m.last_name, m.email,
               COUNT(bl.id) as total_loans,
               SUM(CASE WHEN bl.status IN ('borrowed', 'overdue') THEN 1 ELSE 0 END) as active_loans,
               SUM(CASE WHEN bl.status = 'returned' THEN 1 ELSE 0 END) as returned_loans,
               SUM(CASE WHEN bl.status = 'overdue' THEN 1 ELSE 0 END) as overdue_loans,
               COALESCE(SUM(bl.fine), 0) as total_fines
        FROM members m
        LEFT JOIN book_loans bl ON m.id = bl.member_id
        GROUP BY m.id
        HAVING total_loans > 0
        ORDER BY total_loans DESC
        LIMIT 10
    ";
    $top_borrowers_stmt = $db->prepare($top_borrowers_query);
    $top_borrowers_stmt->execute();
    $analytics['top_borrowers'] = $top_borrowers_stmt->fetchAll();

    // Recent member activity (last 7 days)
    $recent_activity_query = "
        SELECT m.first_name, m.last_name, bl.issue_date, b.title,
               bl.status, bl.due_date
        FROM book_loans bl
        JOIN members m ON bl.member_id = m.id
        JOIN books b ON bl.book_id = b.id
        WHERE bl.issue_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        ORDER BY bl.issue_date DESC
        LIMIT 15
    ";
    $recent_activity_stmt = $db->prepare($recent_activity_query);
    $recent_activity_stmt->execute();
    $analytics['recent_activity'] = $recent_activity_stmt->fetchAll();

    // Member risk analysis
    $risk_analysis_query = "
        SELECT
            SUM(CASE WHEN overdue_count > 3 THEN 1 ELSE 0 END) as high_risk_members,
            SUM(CASE WHEN overdue_count BETWEEN 1 AND 3 THEN 1 ELSE 0 END) as medium_risk_members,
            SUM(CASE WHEN total_fine > 50 THEN 1 ELSE 0 END) as high_fine_members,
            AVG(overdue_count) as avg_overdue_per_member
        FROM (
            SELECT m.id,
                   COUNT(CASE WHEN bl.status = 'overdue' THEN 1 END) as overdue_count,
                   COALESCE(SUM(bl.fine), 0) as total_fine
            FROM members m
            LEFT JOIN book_loans bl ON m.id = bl.member_id
            GROUP BY m.id
        ) as member_risk
    ";
    $risk_stmt = $db->prepare($risk_analysis_query);
    $risk_stmt->execute();
    $analytics['risk_analysis'] = $risk_stmt->fetch();

    // Monthly registration trend (last 12 months)
    $monthly_trend_query = "
        SELECT
            DATE_FORMAT(membership_date, '%Y-%m') as month,
            DATE_FORMAT(membership_date, '%M %Y') as month_name,
            COUNT(*) as new_members
        FROM members
        WHERE membership_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(membership_date, '%Y-%m')
        ORDER BY month
    ";
    $monthly_trend_stmt = $db->prepare($monthly_trend_query);
    $monthly_trend_stmt->execute();
    $analytics['monthly_trend'] = $monthly_trend_stmt->fetchAll();

    // Member engagement metrics
    $engagement_query = "
        SELECT
            COUNT(DISTINCT CASE WHEN bl.issue_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN bl.member_id END) as active_last_30_days,
            COUNT(DISTINCT CASE WHEN bl.issue_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN bl.member_id END) as active_last_7_days,
            COUNT(DISTINCT CASE WHEN bl.issue_date >= CURDATE() THEN bl.member_id END) as active_today
        FROM book_loans bl
    ";
    $engagement_stmt = $db->prepare($engagement_query);
    $engagement_stmt->execute();
    $analytics['engagement'] = $engagement_stmt->fetch();

    // Calculate derived metrics
    $analytics['derived_metrics'] = [
        'members_never_borrowed' => $analytics['member_stats']['total_members'] - $analytics['member_stats']['members_who_borrowed'],
        'avg_fine_per_member' => $analytics['member_stats']['members_with_fines'] > 0 ?
            round($analytics['member_stats']['total_fines'] / $analytics['member_stats']['members_with_fines'], 2) : 0,
        'engagement_rate_30_days' => $analytics['member_stats']['total_members'] > 0 ?
            round(($analytics['engagement']['active_last_30_days'] / $analytics['member_stats']['total_members']) * 100, 1) : 0,
        'engagement_rate_7_days' => $analytics['member_stats']['total_members'] > 0 ?
            round(($analytics['engagement']['active_last_7_days'] / $analytics['member_stats']['total_members']) * 100, 1) : 0
    ];

    echo json_encode([
        'success' => true,
        'analytics' => $analytics,
        'timestamp' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
