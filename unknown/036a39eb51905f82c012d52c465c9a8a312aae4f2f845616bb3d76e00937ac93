<?php
require_once '../config/database.php';

// Create database connection
$database = new Database();
$db = $database->getConnection();

// SQL to create activity_log table
$sql = "CREATE TABLE IF NOT EXISTS activity_log (
    id INT(11) NOT NULL AUTO_INCREMENT,
    user_id INT(11) DEFAULT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    entity_type VARCHAR(50) DEFAULT NULL,
    entity_id INT(11) DEFAULT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_action (action),
    KEY idx_timestamp (timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

// Execute query
try {
    $db->exec($sql);
    echo "Activity log table created successfully!";
    
    // Add some sample activity logs
    $sample_activities = [
        [
            'user_id' => 1, // Admin user
            'action' => 'login',
            'description' => 'Admin logged in to the system',
            'ip_address' => '127.0.0.1'
        ],
        [
            'user_id' => 1,
            'action' => 'add',
            'description' => 'Added new book: "The Great Gatsby"',
            'entity_type' => 'book',
            'entity_id' => 1,
            'ip_address' => '127.0.0.1'
        ],
        [
            'user_id' => 1,
            'action' => 'edit',
            'description' => 'Updated member information for John Doe',
            'entity_type' => 'member',
            'entity_id' => 1,
            'ip_address' => '127.0.0.1'
        ],
        [
            'user_id' => 1,
            'action' => 'issue',
            'description' => 'Issued book "To Kill a Mockingbird" to member Sarah Johnson',
            'entity_type' => 'loan',
            'entity_id' => 1,
            'ip_address' => '127.0.0.1'
        ],
        [
            'user_id' => 1,
            'action' => 'return',
            'description' => 'Returned book "1984" from member Michael Brown',
            'entity_type' => 'loan',
            'entity_id' => 2,
            'ip_address' => '127.0.0.1'
        ]
    ];
    
    // Insert sample activities
    $query = "INSERT INTO activity_log (user_id, action, description, entity_type, entity_id, ip_address) 
              VALUES (:user_id, :action, :description, :entity_type, :entity_id, :ip_address)";
    $stmt = $db->prepare($query);
    
    foreach ($sample_activities as $activity) {
        $stmt->bindValue(':user_id', $activity['user_id']);
        $stmt->bindValue(':action', $activity['action']);
        $stmt->bindValue(':description', $activity['description']);
        $stmt->bindValue(':entity_type', $activity['entity_type'] ?? NULL);
        $stmt->bindValue(':entity_id', $activity['entity_id'] ?? NULL);
        $stmt->bindValue(':ip_address', $activity['ip_address']);
        $stmt->execute();
    }
    
    echo "\nSample activity logs added successfully!";
    
} catch(PDOException $e) {
    echo "Error creating activity_log table: " . $e->getMessage();
}
?>
