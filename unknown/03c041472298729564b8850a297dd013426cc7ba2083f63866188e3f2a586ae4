<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get library statistics
$stats = array();

// Total books
$query = "SELECT COUNT(*) as total FROM books";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['total_books'] = $stmt->fetch()['total'];

// Available books
$query = "SELECT SUM(available_quantity) as available FROM books";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['available_books'] = $stmt->fetch()['available'];

// Total members
$query = "SELECT COUNT(*) as total FROM members";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['total_members'] = $stmt->fetch()['total'];

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        /* Modern Hero Section */
        .hero-section {
            background: linear-gradient(135deg, rgba(5, 5, 20, 0.92) 0%, rgba(10, 20, 40, 0.85) 100%);
            position: relative;
            color: white;
            padding: 40px 0 20px;
            margin-bottom: 20px;
            overflow: hidden;
            min-height: 100vh;
            height: 100vh;
            max-height: 100vh;
            display: flex;
            align-items: center;
            box-shadow: inset 0 0 150px rgba(0, 0, 0, 0.8);
        }

        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('uploads/images/library.jpg') center/cover no-repeat;
            opacity: 1;
            z-index: 1;
        }

        .hero-overlay::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to right,
                rgba(0,0,0,0.7) 0%,
                rgba(0,0,0,0.6) 30%,
                rgba(0,0,0,0.5) 60%,
                rgba(0,0,0,0.4) 100%);
            z-index: 1;
            box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.5);
        }

        .hero-section .container {
            z-index: 5;
            position: relative;
        }

        .hero-content {
            padding: 1.5rem;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4), 0 0 20px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.15);
            position: relative;
            overflow: hidden;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 900;
            line-height: 1;
            margin-bottom: 0.5rem;
            letter-spacing: -0.5px;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8), 0 0 1px rgba(0, 0, 0, 1);
            color: #ffffff;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            font-weight: 500;
            text-shadow: 1px 1px 5px rgba(0, 0, 0, 0.9), 0 0 2px rgba(0, 0, 0, 1);
            line-height: 1.4;
            max-width: 90%;
            margin-bottom: 1rem;
        }

        .search-form .form-control {
            border-radius: 50px;
            padding: 10px 20px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            font-size: 1rem;
            background-color: rgba(255, 255, 255, 0.95);
            height: 45px;
        }

        .search-form .btn {
            border-radius: 0 50px 50px 0;
            padding: 10px 20px;
            background-color: #00d4ff;
            border-color: #00d4ff;
            transition: all 0.3s ease;
            font-size: 1rem;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            height: 45px;
        }

        .hero-stats {
            margin-top: 0.5rem;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .stat-item {
            text-align: center;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 8px;
            padding: 6px 15px;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-number {
            font-size: 1.3rem;
            font-weight: 700;
            color: #00d4ff;
            text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
            line-height: 1.1;
        }

        .stat-label {
            font-size: 0.7rem;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            font-weight: 600;
            line-height: 1.1;
        }

        .hero-buttons .btn {
            border-radius: 50px;
            padding: 6px 15px;
            font-weight: 600;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .hero-buttons .btn-primary {
            background-color: #00d4ff;
            border-color: #00d4ff;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 212, 255, 0.3);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            font-size: 1.1rem;
            letter-spacing: 0.5px;
        }

        .hero-buttons .btn-outline-light {
            border-width: 2px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            font-size: 1.1rem;
            letter-spacing: 0.5px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="home.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <div class="d-flex align-items-center">
                <a href="login.php" class="btn btn-outline-light me-2">
                    <i class="bi bi-person me-1"></i>Login
                </a>
                <a href="register.php" class="btn btn-primary">Register</a>
            </div>
        </div>
    </nav>

    <!-- Modern Hero Section -->
    <section class="hero-section">
        <div class="hero-overlay"></div>
        <div class="container position-relative">
            <div class="row align-items-center min-vh-75">
                <div class="col-lg-6 hero-content text-white">
                    <h1 class="hero-title mb-1">Welcome to Our Library</h1>
                    <p class="hero-subtitle mb-2">A sanctuary of knowledge where books inspire minds and stories transform lives</p>

                    <!-- Quick Search Bar -->
                    <div class="search-container mb-2">
                        <form action="catalog.php" method="get" class="search-form">
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" placeholder="What story awaits you today?" aria-label="Search books">
                                <button class="btn btn-primary" type="submit">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <div class="hero-stats d-flex flex-wrap mb-2">
                        <div class="stat-item me-2 mb-2">
                            <div class="stat-number"><?php echo number_format($stats['total_books']); ?>+</div>
                            <div class="stat-label">Volumes</div>
                        </div>
                        <div class="stat-item me-2 mb-2">
                            <div class="stat-number"><?php echo number_format($stats['available_books']); ?>+</div>
                            <div class="stat-label">Ready</div>
                        </div>
                        <div class="stat-item mb-2">
                            <div class="stat-number"><?php echo number_format($stats['total_members']); ?>+</div>
                            <div class="stat-label">Readers</div>
                        </div>
                    </div>

                    <div class="hero-buttons">
                        <a href="catalog.php" class="btn btn-primary me-2 mb-2">
                            <i class="bi bi-book-half me-1"></i>Explore Our Shelves
                        </a>
                        <a href="register.php" class="btn btn-outline-light mb-2">
                            <i class="bi bi-person-plus me-1"></i>Become a Reader
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
