<?php
// This script checks and fixes database issues

// Include necessary files
require_once 'config/database.php';
require_once 'config/config.php';

// Create database connection
$database = new Database();
$db = $database->getConnection();

echo "<h1>Database Fix Utility</h1>";

// Check if the users table has a full_name column
$query = "SHOW COLUMNS FROM users LIKE 'full_name'";
$stmt = $db->prepare($query);
$stmt->execute();
$hasFullNameColumn = $stmt->rowCount() > 0;

echo "<p>Checking for full_name column in users table: " . ($hasFullNameColumn ? "Found" : "Not found") . "</p>";

// Add full_name column if it doesn't exist
if (!$hasFullNameColumn) {
    echo "<p>Adding full_name column to users table...</p>";
    try {
        $query = "ALTER TABLE users ADD COLUMN full_name VARCHAR(100) AFTER role";
        $stmt = $db->prepare($query);
        $stmt->execute();
        echo "<p style='color:green'>full_name column added successfully.</p>";
    } catch (PDOException $e) {
        echo "<p style='color:red'>Error adding full_name column: " . $e->getMessage() . "</p>";
    }
}

// Update librarians with a default full_name if not set
$query = "SELECT id, username, full_name FROM users WHERE role = 'librarian'";
$stmt = $db->prepare($query);
$stmt->execute();
$librarians = $stmt->fetchAll();

echo "<p>Found " . count($librarians) . " librarian users.</p>";

if (count($librarians) > 0) {
    echo "<ul>";
    foreach ($librarians as $librarian) {
        echo "<li>Librarian ID: " . $librarian['id'] . ", Username: " . $librarian['username'];
        
        if (empty($librarian['full_name'])) {
            echo " - Setting default full_name...";
            try {
                $defaultName = "Librarian " . $librarian['username'];
                $query = "UPDATE users SET full_name = :full_name WHERE id = :id";
                $updateStmt = $db->prepare($query);
                $updateStmt->bindValue(':full_name', $defaultName);
                $updateStmt->bindValue(':id', $librarian['id']);
                $updateStmt->execute();
                echo " <span style='color:green'>Set to '" . $defaultName . "'</span>";
            } catch (PDOException $e) {
                echo " <span style='color:red'>Error: " . $e->getMessage() . "</span>";
            }
        } else {
            echo " - Full Name: " . $librarian['full_name'];
        }
        
        echo "</li>";
    }
    echo "</ul>";
}

// Check for notifications table
$query = "SHOW TABLES LIKE 'notifications'";
$stmt = $db->prepare($query);
$stmt->execute();
$hasNotificationsTable = $stmt->rowCount() > 0;

echo "<p>Checking for notifications table: " . ($hasNotificationsTable ? "Found" : "Not found") . "</p>";

// Create notifications table if it doesn't exist
if (!$hasNotificationsTable) {
    echo "<p>Creating notifications table...</p>";
    try {
        $query = "CREATE TABLE notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NULL,
            message TEXT NOT NULL,
            type VARCHAR(20) DEFAULT 'info',
            is_read TINYINT(1) DEFAULT 0,
            entity_type VARCHAR(50) NULL,
            entity_id INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )";
        $stmt = $db->prepare($query);
        $stmt->execute();
        echo "<p style='color:green'>notifications table created successfully.</p>";
    } catch (PDOException $e) {
        echo "<p style='color:red'>Error creating notifications table: " . $e->getMessage() . "</p>";
    }
}

echo "<p>Database check and fix completed.</p>";
echo "<p><a href='login.php'>Return to login page</a></p>";
?>
