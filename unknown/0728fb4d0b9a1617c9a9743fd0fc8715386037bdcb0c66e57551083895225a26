/**
 * Enhanced Notifications Page JavaScript
 */
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    const statusFilter = document.getElementById('statusFilter');
    const typeFilter = document.getElementById('typeFilter');
    const sortOrder = document.getElementById('sortOrder');
    const notificationItems = document.querySelectorAll('.notification-item');
    const notificationsContainer = document.querySelector('.notifications-container');
    
    // Apply filters when any filter changes
    if (statusFilter && typeFilter && sortOrder) {
        statusFilter.addEventListener('change', applyFilters);
        typeFilter.addEventListener('change', applyFilters);
        sortOrder.addEventListener('change', applyFilters);
    }
    
    function applyFilters() {
        const status = statusFilter.value;
        const type = typeFilter.value;
        const sort = sortOrder.value;
        
        // First hide all notifications
        notificationItems.forEach(item => {
            item.style.display = 'none';
        });
        
        // Filter by status and type
        let filteredItems = Array.from(notificationItems).filter(item => {
            const itemStatus = item.getAttribute('data-status');
            const itemType = item.getAttribute('data-type');
            
            const statusMatch = status === 'all' || itemStatus === status;
            const typeMatch = type === 'all' || itemType === type;
            
            return statusMatch && typeMatch;
        });
        
        // Sort the filtered items
        filteredItems.sort((a, b) => {
            const aTime = a.querySelector('.notification-time').textContent;
            const bTime = b.querySelector('.notification-time').textContent;
            
            // Convert relative time to a sortable value (rough approximation)
            const getTimeValue = (timeText) => {
                if (timeText.includes('just now')) return Date.now();
                if (timeText.includes('minute')) {
                    const minutes = parseInt(timeText) || 1;
                    return Date.now() - (minutes * 60 * 1000);
                }
                if (timeText.includes('hour')) {
                    const hours = parseInt(timeText) || 1;
                    return Date.now() - (hours * 60 * 60 * 1000);
                }
                if (timeText.includes('day')) {
                    const days = parseInt(timeText) || 1;
                    return Date.now() - (days * 24 * 60 * 60 * 1000);
                }
                if (timeText.includes('week')) {
                    const weeks = parseInt(timeText) || 1;
                    return Date.now() - (weeks * 7 * 24 * 60 * 60 * 1000);
                }
                // Default to a month ago
                return Date.now() - (30 * 24 * 60 * 60 * 1000);
            };
            
            const aValue = getTimeValue(aTime);
            const bValue = getTimeValue(bTime);
            
            return sort === 'newest' ? bValue - aValue : aValue - bValue;
        });
        
        // Show filtered and sorted items
        if (filteredItems.length > 0) {
            filteredItems.forEach(item => {
                item.style.display = 'block';
                notificationsContainer.appendChild(item); // Re-append to change order
            });
        } else {
            // Show "no results" message
            const noResults = document.createElement('div');
            noResults.className = 'alert alert-info';
            noResults.innerHTML = '<i class="bi bi-info-circle me-2"></i> No notifications match your filters.';
            notificationsContainer.appendChild(noResults);
        }
    }
    
    // Batch selection functionality
    const selectAllCheckbox = document.getElementById('selectAllNotifications');
    const notificationCheckboxes = document.querySelectorAll('.notification-checkbox');
    const markSelectedReadBtn = document.getElementById('markSelectedRead');
    
    if (selectAllCheckbox && markSelectedReadBtn) {
        // Select all checkbox
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            
            notificationCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            
            updateMarkSelectedButton();
        });
        
        // Individual checkboxes
        notificationCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateMarkSelectedButton);
        });
        
        // Mark selected as read button
        markSelectedReadBtn.addEventListener('click', function() {
            const selectedIds = Array.from(notificationCheckboxes)
                .filter(checkbox => checkbox.checked)
                .map(checkbox => checkbox.value);
            
            if (selectedIds.length > 0) {
                // Create a form to submit the selected IDs
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'mark_selected_read.php';
                
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'notification_ids';
                input.value = JSON.stringify(selectedIds);
                
                form.appendChild(input);
                document.body.appendChild(form);
                form.submit();
            }
        });
    }
    
    function updateMarkSelectedButton() {
        const selectedCount = Array.from(notificationCheckboxes).filter(checkbox => checkbox.checked).length;
        
        if (selectedCount > 0) {
            markSelectedReadBtn.removeAttribute('disabled');
            markSelectedReadBtn.textContent = `Mark ${selectedCount} Selected as Read`;
        } else {
            markSelectedReadBtn.setAttribute('disabled', 'disabled');
            markSelectedReadBtn.innerHTML = '<i class="bi bi-check-all me-1"></i> Mark Selected as Read';
        }
        
        // Update select all checkbox state
        if (notificationCheckboxes.length > 0) {
            selectAllCheckbox.checked = selectedCount === notificationCheckboxes.length;
            selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < notificationCheckboxes.length;
        }
    }
    
    // Add animation to notification cards
    notificationItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        
        setTimeout(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, 50 * index);
    });
});
