<?php
/**
 * Add Books Script - Part 3
 * This script adds a collection of books to the library database
 */

// Database connection
require_once 'config/database.php';
$database = new Database();
$db = $database->getConnection();

// Function to add a book
function addBook($db, $isbn, $title, $author, $category, $publication_year, $publisher, 
                $quantity, $available_quantity, $shelf_location, $description) {
    
    // Check if book already exists
    $check_query = "SELECT id FROM books WHERE isbn = :isbn";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':isbn', $isbn);
    $check_stmt->execute();
    
    if ($check_stmt->rowCount() > 0) {
        return "Book with ISBN $isbn already exists in the database.";
    }
    
    // Current date for created_at field
    $created_at = date('Y-m-d H:i:s');
    
    // Insert the book
    $query = "INSERT INTO books (isbn, title, author, category, publication_year, publisher, 
              quantity, available_quantity, shelf_location, description, created_at) 
              VALUES (:isbn, :title, :author, :category, :publication_year, :publisher, 
              :quantity, :available_quantity, :shelf_location, :description, :created_at)";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':isbn', $isbn);
    $stmt->bindParam(':title', $title);
    $stmt->bindParam(':author', $author);
    $stmt->bindParam(':category', $category);
    $stmt->bindParam(':publication_year', $publication_year);
    $stmt->bindParam(':publisher', $publisher);
    $stmt->bindParam(':quantity', $quantity);
    $stmt->bindParam(':available_quantity', $available_quantity);
    $stmt->bindParam(':shelf_location', $shelf_location);
    $stmt->bindParam(':description', $description);
    $stmt->bindParam(':created_at', $created_at);
    
    if ($stmt->execute()) {
        return "Book '$title' added successfully.";
    } else {
        return "Error adding book '$title': " . print_r($stmt->errorInfo(), true);
    }
}

// Books to add - Part 3
$books = [
    // Biographies & Memoirs
    [
        'isbn' => '978-1501127625',
        'title' => 'Becoming',
        'author' => 'Michelle Obama',
        'category' => 'Memoir',
        'publication_year' => 2018,
        'publisher' => 'Crown Publishing',
        'quantity' => 4,
        'available_quantity' => 4,
        'shelf_location' => 'BIO-001',
        'description' => 'The memoir of the former First Lady of the United States.'
    ],
    [
        'isbn' => '978-0399590504',
        'title' => 'Educated',
        'author' => 'Tara Westover',
        'category' => 'Memoir',
        'publication_year' => 2018,
        'publisher' => 'Random House',
        'quantity' => 3,
        'available_quantity' => 3,
        'shelf_location' => 'BIO-002',
        'description' => 'A memoir about a woman who leaves her survivalist family to pursue education.'
    ],
    [
        'isbn' => '978-0743493917',
        'title' => 'The Diary of a Young Girl',
        'author' => 'Anne Frank',
        'category' => 'Memoir, History',
        'publication_year' => 1947,
        'publisher' => 'Contact Publishing',
        'quantity' => 5,
        'available_quantity' => 5,
        'shelf_location' => 'BIO-003',
        'description' => 'The wartime diary of a Jewish girl hiding from the Nazis.'
    ],
    [
        'isbn' => '978-1501173219',
        'title' => 'Shoe Dog',
        'author' => 'Phil Knight',
        'category' => 'Memoir, Business',
        'publication_year' => 2016,
        'publisher' => 'Scribner',
        'quantity' => 4,
        'available_quantity' => 4,
        'shelf_location' => 'BIO-004',
        'description' => 'The memoir of Nike\'s founder.'
    ],
    [
        'isbn' => '978-0062563711',
        'title' => 'Elon Musk: Tesla, SpaceX, and the Quest for a Fantastic Future',
        'author' => 'Ashlee Vance',
        'category' => 'Biography',
        'publication_year' => 2015,
        'publisher' => 'HarperBusiness',
        'quantity' => 3,
        'available_quantity' => 3,
        'shelf_location' => 'BIO-005',
        'description' => 'A biography of the tech entrepreneur Elon Musk.'
    ]
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Books (Part 3) - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Add Books to Library (Part 3)</h1>
        
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Adding Books...</h5>
                <div class="mt-3">
                    <?php
                    $success_count = 0;
                    $error_count = 0;
                    
                    foreach ($books as $book) {
                        $result = addBook(
                            $db, 
                            $book['isbn'],
                            $book['title'],
                            $book['author'],
                            $book['category'],
                            $book['publication_year'],
                            $book['publisher'],
                            $book['quantity'],
                            $book['available_quantity'],
                            $book['shelf_location'],
                            $book['description']
                        );
                        
                        echo "<p>";
                        if (strpos($result, 'successfully') !== false) {
                            echo "<span class='text-success'>✓ </span>";
                            $success_count++;
                        } else {
                            echo "<span class='text-warning'>⚠ </span>";
                            $error_count++;
                        }
                        echo $result . "</p>";
                    }
                    ?>
                    
                    <div class="alert <?php echo $error_count > 0 ? 'alert-warning' : 'alert-success'; ?> mt-3">
                        <strong>Summary:</strong> Added <?php echo $success_count; ?> books successfully.
                        <?php if ($error_count > 0): ?>
                            <?php echo $error_count; ?> books could not be added (likely already exist).
                        <?php endif; ?>
                    </div>
                    
                    <div class="mt-3">
                        <a href="books/index.php" class="btn btn-primary">View All Books</a>
                        <a href="admin/dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-body">
                <h5 class="card-title">All Books Added</h5>
                <p>All books have been successfully added to the library database. Here's a summary:</p>
                
                <ul>
                    <li><strong>Fiction Books:</strong> To Kill a Mockingbird, 1984, The Great Gatsby, Pride and Prejudice, The Da Vinci Code, The Catcher in the Rye</li>
                    <li><strong>Non-Fiction Books:</strong> The Alchemist, Steve Jobs, The Subtle Art of Not Giving a F*ck, Sapiens, Good to Great</li>
                    <li><strong>Science Fiction & Fantasy:</strong> The Martian, A Game of Thrones, The Way of Kings, The Hobbit</li>
                    <li><strong>Mystery & Thriller:</strong> Gone Girl, The Girl with the Dragon Tattoo, The Silent Patient, The Woman in the Window, The Girl on the Train</li>
                    <li><strong>Self-Help & Personal Development:</strong> How to Win Friends and Influence People, Atomic Habits, The 7 Habits of Highly Effective People, The 4-Hour Workweek, Think and Grow Rich</li>
                    <li><strong>Biographies & Memoirs:</strong> Becoming, Educated, The Diary of a Young Girl, Shoe Dog, Elon Musk</li>
                </ul>
                
                <p>Total books added: 30</p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
