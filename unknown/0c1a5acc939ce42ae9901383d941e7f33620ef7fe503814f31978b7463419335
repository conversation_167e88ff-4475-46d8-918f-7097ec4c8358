<?php
/**
 * Test .htaccess and add_books.php fixes
 */

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>File Fixes Verification</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css' rel='stylesheet'>
    <style>
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .status-warning { color: #ffc107; }
    </style>
</head>
<body>
<div class='container mt-4'>
    <h1><i class='bi bi-tools me-2'></i>File Fixes Verification</h1>
    <p class='text-muted'>Checking the fixes applied to .htaccess and add_books.php files...</p>";

// Test 1: Check .htaccess file
echo "<div class='card mb-4'>
<div class='card-header'>
    <h5><i class='bi bi-file-earmark-text me-2'></i>.htaccess File Check</h5>
</div>
<div class='card-body'>";

if (file_exists('.htaccess')) {
    echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>.htaccess file exists</p>";
    
    $htaccess_content = file_get_contents('.htaccess');
    
    // Check for modern Apache syntax
    if (strpos($htaccess_content, 'Require all granted') !== false) {
        echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Modern Apache 2.4+ syntax detected</p>";
    } else {
        echo "<p class='status-warning'><i class='bi bi-exclamation-triangle-fill me-2'></i>Old Apache syntax detected</p>";
    }
    
    // Check for deprecated directives
    if (strpos($htaccess_content, 'Order allow,deny') !== false) {
        echo "<p class='status-warning'><i class='bi bi-exclamation-triangle-fill me-2'></i>Deprecated 'Order allow,deny' found</p>";
    } else {
        echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>No deprecated Order directives found</p>";
    }
    
    // Check PHP module reference
    if (strpos($htaccess_content, 'mod_php7.c') !== false) {
        echo "<p class='status-warning'><i class='bi bi-exclamation-triangle-fill me-2'></i>Specific PHP 7 module reference found (may cause issues with PHP 8)</p>";
    } elseif (strpos($htaccess_content, 'mod_php.c') !== false) {
        echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Generic PHP module reference found (compatible)</p>";
    }
    
} else {
    echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>.htaccess file not found</p>";
}

echo "</div></div>";

// Test 2: Check add_books.php file
echo "<div class='card mb-4'>
<div class='card-header'>
    <h5><i class='bi bi-file-earmark-code me-2'></i>add_books.php File Check</h5>
</div>
<div class='card-body'>";

if (file_exists('add_books.php')) {
    echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>add_books.php file exists</p>";
    
    // Check PHP syntax
    $output = shell_exec("php -l add_books.php 2>&1");
    if (strpos($output, 'No syntax errors') !== false) {
        echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>PHP syntax is valid</p>";
    } else {
        echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>PHP syntax errors found: $output</p>";
    }
    
    // Check HTML structure
    $content = file_get_contents('add_books.php');
    
    // Count opening and closing div tags
    $opening_divs = substr_count($content, '<div');
    $closing_divs = substr_count($content, '</div>');
    
    if ($opening_divs === $closing_divs) {
        echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>HTML div tags are balanced ($opening_divs opening, $closing_divs closing)</p>";
    } else {
        echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>HTML div tags are unbalanced ($opening_divs opening, $closing_divs closing)</p>";
    }
    
    // Check for specific issues that were fixed
    if (strpos($content, '<div class="card-body"></div>') !== false) {
        echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>Empty div tag found (not fixed)</p>";
    } else {
        echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>No empty div tags found</p>";
    }
    
    if (strpos($content, 'alert-success\'; ?> mt-3"></div>') !== false) {
        echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>Malformed alert div found (not fixed)</p>";
    } else {
        echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Alert div structure is correct</p>";
    }
    
} else {
    echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>add_books.php file not found</p>";
}

echo "</div></div>";

// Test 3: Test database connection for add_books.php
echo "<div class='card mb-4'>
<div class='card-header'>
    <h5><i class='bi bi-database me-2'></i>Database Connection Test</h5>
</div>
<div class='card-body'>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Database connection successful</p>";
    
    // Check if books table exists
    $stmt = $db->prepare("SHOW TABLES LIKE 'books'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Books table exists</p>";
        
        // Check current book count
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM books");
        $stmt->execute();
        $result = $stmt->fetch();
        echo "<p class='status-pass'><i class='bi bi-check-circle-fill me-2'></i>Current books in database: {$result['count']}</p>";
    } else {
        echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>Books table does not exist</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='status-fail'><i class='bi bi-x-circle-fill me-2'></i>Database connection failed: " . $e->getMessage() . "</p>";
}

echo "</div></div>";

// Summary and next steps
echo "<div class='card mb-4'>
<div class='card-header'>
    <h5><i class='bi bi-clipboard-check me-2'></i>Summary & Next Steps</h5>
</div>
<div class='card-body'>
    <h6>Files Fixed:</h6>
    <ul>
        <li><strong>.htaccess</strong> - Updated to modern Apache 2.4+ syntax</li>
        <li><strong>add_books.php</strong> - Fixed HTML structure and div tag issues</li>
    </ul>
    
    <h6>Test the Fixed Files:</h6>
    <div class='row mt-3'>
        <div class='col-md-6'>
            <div class='card'>
                <div class='card-body text-center'>
                    <h6 class='card-title'>Test add_books.php</h6>
                    <a href='add_books.php' class='btn btn-primary' target='_blank'>
                        <i class='bi bi-plus-circle me-1'></i>Add Books
                    </a>
                </div>
            </div>
        </div>
        <div class='col-md-6'>
            <div class='card'>
                <div class='card-body text-center'>
                    <h6 class='card-title'>View Books</h6>
                    <a href='books/index.php' class='btn btn-success' target='_blank'>
                        <i class='bi bi-book me-1'></i>View Catalog
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
</div>";

echo "<div class='text-center mt-4'>
    <a href='dashboard_access_verification.php' class='btn btn-secondary me-2'>
        <i class='bi bi-speedometer2 me-1'></i>Dashboard Verification
    </a>
    <a href='system_comprehensive_fix.php' class='btn btn-warning'>
        <i class='bi bi-tools me-1'></i>Full System Check
    </a>
</div>";

echo "</div>
</body>
</html>";
?>
