<?php
/**
 * Complete Email System Fix
 * This script fixes all issues with the email system and settings page
 */

// Include database connection
require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Complete Email System Fix - Library Management System</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { padding: 40px 0; background-color: #f8f9fa; }
        .fix-card { max-width: 1000px; margin: 0 auto; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .step { margin-bottom: 20px; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='card fix-card'>
            <div class='card-header bg-success text-white'>
                <h4 class='mb-0'>🔧 Complete Email System Fix</h4>
            </div>
            <div class='card-body'>";

$success = true;
$steps = [];

// Step 1: Create reminder_logs table
echo "<div class='step'>";
echo "<h5>Step 1: Creating reminder_logs table</h5>";
try {
    $query = "CREATE TABLE IF NOT EXISTS reminder_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        loan_id INT NOT NULL,
        reminder_type ENUM('due_date', 'overdue') NOT NULL,
        days_before INT,
        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (loan_id) REFERENCES book_loans(id) ON DELETE CASCADE
    )";
    $db->exec($query);
    echo "<p class='text-success'>✅ reminder_logs table created/verified</p>";
} catch (PDOException $e) {
    echo "<p class='text-danger'>❌ Error: " . $e->getMessage() . "</p>";
    $success = false;
}
echo "</div>";

// Step 2: Create email_logs table
echo "<div class='step'>";
echo "<h5>Step 2: Creating email_logs table</h5>";
try {
    $query = "CREATE TABLE IF NOT EXISTS email_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        recipient_email VARCHAR(100) NOT NULL,
        subject VARCHAR(255) NOT NULL,
        status ENUM('pending', 'sent', 'failed') NOT NULL DEFAULT 'pending',
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $db->exec($query);
    echo "<p class='text-success'>✅ email_logs table created/verified</p>";
} catch (PDOException $e) {
    echo "<p class='text-danger'>❌ Error: " . $e->getMessage() . "</p>";
    $success = false;
}
echo "</div>";

// Step 3: Create settings table
echo "<div class='step'>";
echo "<h5>Step 3: Creating settings table</h5>";
try {
    $query = "CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_group VARCHAR(50) NOT NULL,
        setting_key VARCHAR(50) NOT NULL,
        setting_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY group_key (setting_group, setting_key)
    )";
    $db->exec($query);
    echo "<p class='text-success'>✅ settings table created/verified</p>";
    
    // Insert default settings
    $default_settings = [
        // Email settings
        ['email', 'from_email', '<EMAIL>'],
        ['email', 'from_name', 'Library Management System'],
        ['email', 'reply_to', '<EMAIL>'],
        ['email', 'smtp_enabled', 'false'],
        ['email', 'smtp_host', 'smtp.example.com'],
        ['email', 'smtp_port', '587'],
        ['email', 'smtp_username', ''],
        ['email', 'smtp_password', ''],
        ['email', 'smtp_secure', 'tls'],
        
        // Notification settings
        ['notifications', 'due_date_reminder_days', '3'],
        ['notifications', 'send_overdue_notifications', 'true'],
        ['notifications', 'overdue_notification_frequency', '7'],
        
        // Fine settings
        ['fines', 'fine_rate_per_day', '0.25'],
        ['fines', 'grace_period_days', '3'],
        ['fines', 'max_fine_per_book', '25.00']
    ];
    
    $insert_query = "INSERT IGNORE INTO settings (setting_group, setting_key, setting_value) VALUES (?, ?, ?)";
    $insert_stmt = $db->prepare($insert_query);
    
    $inserted_count = 0;
    foreach ($default_settings as $setting) {
        if ($insert_stmt->execute($setting)) {
            $inserted_count++;
        }
    }
    
    echo "<p class='text-success'>✅ Inserted/verified $inserted_count default settings</p>";
} catch (PDOException $e) {
    echo "<p class='text-danger'>❌ Error: " . $e->getMessage() . "</p>";
    $success = false;
}
echo "</div>";

// Step 4: Create users table if needed
echo "<div class='step'>";
echo "<h5>Step 4: Verifying users table</h5>";
try {
    $query = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(100),
        role ENUM('admin', 'librarian') DEFAULT 'librarian',
        status ENUM('active', 'inactive') DEFAULT 'active',
        remember_token VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $db->exec($query);
    echo "<p class='text-success'>✅ users table created/verified</p>";
    
    // Check if admin user exists
    $check_query = "SELECT COUNT(*) as count FROM users WHERE role = 'admin'";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->execute();
    $admin_count = $check_stmt->fetch()['count'];
    
    if ($admin_count == 0) {
        // Create default admin user
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $admin_query = "INSERT INTO users (username, email, password, full_name, role, status) VALUES (?, ?, ?, ?, 'admin', 'active')";
        $admin_stmt = $db->prepare($admin_query);
        $admin_stmt->execute(['admin', '<EMAIL>', $admin_password, 'System Administrator']);
        
        echo "<p class='text-warning'>⚠️ Created default admin user: admin / admin123</p>";
    } else {
        echo "<p class='text-info'>ℹ️ Admin users already exist ($admin_count found)</p>";
    }
} catch (PDOException $e) {
    echo "<p class='text-danger'>❌ Error: " . $e->getMessage() . "</p>";
    $success = false;
}
echo "</div>";

// Step 5: Test email settings page access
echo "<div class='step'>";
echo "<h5>Step 5: Testing Email Settings Page</h5>";
if (file_exists('admin/email_settings.php')) {
    echo "<p class='text-success'>✅ Email settings file exists</p>";
    echo "<p class='text-info'>ℹ️ File size: " . filesize('admin/email_settings.php') . " bytes</p>";
} else {
    echo "<p class='text-danger'>❌ Email settings file not found</p>";
    $success = false;
}

// Check include files
$include_files = ['includes/head.php', 'includes/header.php', 'includes/sidebar.php', 'includes/footer.php', 'includes/functions.php'];
foreach ($include_files as $file) {
    if (file_exists($file)) {
        echo "<p class='text-success'>✅ $file exists</p>";
    } else {
        echo "<p class='text-danger'>❌ $file missing</p>";
        $success = false;
    }
}
echo "</div>";

// Final status
if ($success) {
    echo "<div class='alert alert-success mt-4'>
            <h5>🎉 Email System Fix Completed Successfully!</h5>
            <p>All components are now properly configured:</p>
            <ul>
                <li>✅ Database tables created</li>
                <li>✅ Default settings inserted</li>
                <li>✅ Admin user available</li>
                <li>✅ Email settings page ready</li>
            </ul>
            <p><strong>Next Steps:</strong></p>
            <ol>
                <li>Login as admin (username: admin, password: admin123)</li>
                <li>Access Email Settings from the admin menu</li>
                <li>Configure your email settings</li>
                <li>Test the reminder system</li>
            </ol>
          </div>";
} else {
    echo "<div class='alert alert-danger mt-4'>
            <h5>❌ Some Issues Remain</h5>
            <p>Please check the error messages above and resolve them.</p>
          </div>";
}

echo "        </div>
            <div class='card-footer'>
                <div class='d-flex justify-content-between'>
                    <a href='index.php' class='btn btn-secondary'>Dashboard</a>
                    <a href='check_admin_users.php' class='btn btn-warning'>Check Admin Users</a>
                    <a href='admin/email_settings.php' class='btn btn-success'>Email Settings</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
