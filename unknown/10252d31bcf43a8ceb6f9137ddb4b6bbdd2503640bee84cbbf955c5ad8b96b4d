/* Custom styles for Library Management System */

/* Enhanced Date Range Selection Styles */
.quick-date-btn {
    transition: all 0.2s ease;
    border-radius: 20px;
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

.quick-date-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.quick-date-btn.active {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

#custom_dates_container,
#custom_dates_container2 {
    transition: all 0.3s ease;
}

.date-range-display {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-left: 4px solid var(--bs-primary);
}

/* Report form enhancements */
#reportForm .form-select,
#reportForm .form-control {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#reportForm .form-select:focus,
#reportForm .form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

body {
    font-size: .875rem;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: .75rem 1rem;
}

.sidebar .nav-link.active {
    color: #2470dc;
}

.sidebar .nav-link:hover {
    color: #007bff;
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
}

/* Navbar */
.navbar-brand {
    padding-top: .75rem;
    padding-bottom: .75rem;
    font-size: 1rem;
    background-color: rgba(0, 0, 0, .25);
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);
}

/* Content */
main {
    margin-top: 10px;
}

/* Cards */
.card {
    margin-bottom: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* Tables */
.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

/* Forms */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Login page */
.login-page {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Book cover thumbnails */
.book-cover {
    width: 100px;
    height: 150px;
    object-fit: cover;
    border: 1px solid #ddd;
}

/* Member profile image */
.member-image {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 50%;
}

/* Dashboard stats */
.stats-card {
    transition: transform 0.3s;
}

.stats-card:hover {
    transform: translateY(-5px);
}

/* Pagination */
.pagination {
    margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
    }
}

/* Footer styles */
footer {
    margin-top: 2rem;
}

footer h5 {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 1rem;
}

footer .text-muted {
    color: #adb5bd !important;
}

footer a.text-muted:hover {
    color: #ffffff !important;
    text-decoration: underline !important;
}

footer hr {
    border-color: rgba(255, 255, 255, 0.1);
}

/* Print styles */
@media print {
    .sidebar {
        display: none;
    }

    .navbar {
        display: none;
    }

    main {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    .no-print {
        display: none;
    }

    footer {
        display: none;
    }
}
