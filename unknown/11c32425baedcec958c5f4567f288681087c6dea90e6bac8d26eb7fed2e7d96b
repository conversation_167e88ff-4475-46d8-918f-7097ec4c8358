/**
 * Enhanced Notifications Dropdown JavaScript
 * Handles the dropdown notification functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize notification dropdown functionality
    initializeNotificationDropdown();
    
    // Auto-refresh notifications every 30 seconds
    setInterval(refreshNotifications, 30000);
});

function initializeNotificationDropdown() {
    const notificationDropdown = document.getElementById('notificationDropdown');
    const notificationBadge = document.getElementById('notificationBadge');
    const notificationCount = document.getElementById('notificationCount');
    const markAllReadBtn = document.getElementById('markAllReadBtn');
    
    if (!notificationDropdown) return;
    
    // Handle bell icon animation
    if (notificationBadge) {
        animateBellPeriodically();
    }
    
    // Handle mark all as read button
    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', function() {
            markAllNotificationsAsRead();
        });
    }
    
    // Handle individual mark as read buttons
    initializeMarkReadButtons();
    
    // Handle dropdown show/hide events
    const dropdownElement = notificationDropdown.closest('.dropdown');
    if (dropdownElement) {
        dropdownElement.addEventListener('shown.bs.dropdown', function() {
            // Dropdown opened - could mark notifications as seen here
            console.log('Notifications dropdown opened');
        });
        
        dropdownElement.addEventListener('hidden.bs.dropdown', function() {
            // Dropdown closed
            console.log('Notifications dropdown closed');
        });
    }
}

function initializeMarkReadButtons() {
    const markReadButtons = document.querySelectorAll('.mark-read-btn');
    
    markReadButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const notificationId = this.getAttribute('data-notification-id');
            if (notificationId) {
                markNotificationAsRead(notificationId, this);
            }
        });
    });
}

function markNotificationAsRead(notificationId, buttonElement) {
    // Disable button and show loading state
    buttonElement.disabled = true;
    buttonElement.innerHTML = '<i class="bi bi-hourglass-split"></i>';
    
    // Send AJAX request to mark notification as read
    fetch('../notifications/mark_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'notification_id=' + encodeURIComponent(notificationId)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI
            const notificationItem = buttonElement.closest('.notification-item');
            if (notificationItem) {
                notificationItem.classList.remove('unread');
                buttonElement.remove(); // Remove the mark as read button
            }
            
            // Update badge count
            updateNotificationBadge(-1);
            
            // Show success feedback
            showNotificationFeedback('Notification marked as read', 'success');
        } else {
            // Show error and restore button
            buttonElement.disabled = false;
            buttonElement.innerHTML = '<i class="bi bi-check"></i>';
            showNotificationFeedback('Failed to mark notification as read', 'error');
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
        buttonElement.disabled = false;
        buttonElement.innerHTML = '<i class="bi bi-check"></i>';
        showNotificationFeedback('Network error occurred', 'error');
    });
}

function markAllNotificationsAsRead() {
    const markAllBtn = document.getElementById('markAllReadBtn');
    if (!markAllBtn) return;
    
    // Show loading state
    markAllBtn.disabled = true;
    markAllBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Marking...';
    
    // Send AJAX request to mark all notifications as read
    fetch('../notifications/mark_all_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI - remove all unread classes and mark read buttons
            const unreadItems = document.querySelectorAll('.notification-item.unread');
            unreadItems.forEach(item => {
                item.classList.remove('unread');
                const markReadBtn = item.querySelector('.mark-read-btn');
                if (markReadBtn) {
                    markReadBtn.remove();
                }
            });
            
            // Update badge and count
            updateNotificationBadge(0, true);
            
            // Hide mark all button and show success message
            markAllBtn.style.display = 'none';
            const footer = markAllBtn.closest('.notifications-footer');
            if (footer) {
                const successMsg = document.createElement('span');
                successMsg.className = 'text-muted small';
                successMsg.textContent = 'All caught up!';
                footer.insertBefore(successMsg, footer.firstChild);
            }
            
            showNotificationFeedback('All notifications marked as read', 'success');
        } else {
            // Restore button state
            markAllBtn.disabled = false;
            markAllBtn.innerHTML = '<i class="bi bi-check-all me-1"></i>Mark all as read';
            showNotificationFeedback('Failed to mark all notifications as read', 'error');
        }
    })
    .catch(error => {
        console.error('Error marking all notifications as read:', error);
        markAllBtn.disabled = false;
        markAllBtn.innerHTML = '<i class="bi bi-check-all me-1"></i>Mark all as read';
        showNotificationFeedback('Network error occurred', 'error');
    });
}

function updateNotificationBadge(change, reset = false) {
    const badge = document.getElementById('notificationBadge');
    const count = document.getElementById('notificationCount');
    
    if (!badge) return;
    
    let currentCount = reset ? 0 : parseInt(badge.textContent.replace('+', '')) || 0;
    
    if (!reset) {
        currentCount = Math.max(0, currentCount + change);
    }
    
    if (currentCount === 0) {
        badge.style.display = 'none';
        if (count) count.textContent = '0 new';
    } else {
        badge.style.display = 'inline-block';
        badge.textContent = currentCount > 9 ? '9+' : currentCount;
        if (count) count.textContent = currentCount + ' new';
    }
}

function refreshNotifications() {
    // Fetch latest notifications
    fetch('../notifications/get_recent.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateNotificationsList(data.notifications);
                updateNotificationBadge(data.unread_count, true);
            }
        })
        .catch(error => {
            console.log('Failed to refresh notifications:', error);
        });
}

function updateNotificationsList(notifications) {
    const notificationsList = document.getElementById('notificationsList');
    if (!notificationsList) return;
    
    // Update the notifications list with new data
    // This would require server-side implementation
    console.log('Updating notifications list with:', notifications);
}

function animateBellPeriodically() {
    const bellIcon = document.querySelector('.bell-icon');
    if (!bellIcon) return;
    
    // Animate bell every 15 seconds if there are unread notifications
    setInterval(function() {
        const badge = document.getElementById('notificationBadge');
        if (badge && badge.style.display !== 'none') {
            bellIcon.classList.add('bell-animate');
            
            setTimeout(function() {
                bellIcon.classList.remove('bell-animate');
            }, 1000);
        }
    }, 15000);
}

function showNotificationFeedback(message, type) {
    // Create a small toast notification for feedback
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 3000);
}

// Export functions for external use
window.NotificationDropdown = {
    markAsRead: markNotificationAsRead,
    markAllAsRead: markAllNotificationsAsRead,
    refresh: refreshNotifications,
    updateBadge: updateNotificationBadge
};
