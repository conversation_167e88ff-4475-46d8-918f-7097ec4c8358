<?php
/**
 * Test Dashboard Access
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Dashboard Access Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .test-link { display: inline-block; margin: 5px; padding: 8px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
</style>";

echo "<div class='section'>";
echo "<h2>🔍 File Path Tests</h2>";

// Test file paths
$files_to_check = [
    'admin/dashboard.php' => 'Admin Dashboard',
    'config/database.php' => 'Database Config',
    'config/config.php' => 'Main Config',
    'includes/functions.php' => 'Functions File',
    'includes/head.php' => 'Head Include'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ $description ($file) - File exists</div>";
    } else {
        echo "<div class='error'>❌ $description ($file) - File missing</div>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🔧 Path Resolution Test</h2>";

// Test path resolution from admin directory
$admin_dir = __DIR__ . '/admin';
$config_from_admin = $admin_dir . '/../config/database.php';
$includes_from_admin = $admin_dir . '/../includes/functions.php';

echo "<div class='info'>";
echo "<p><strong>Current directory:</strong> " . __DIR__ . "</p>";
echo "<p><strong>Admin directory:</strong> " . $admin_dir . "</p>";
echo "<p><strong>Config from admin:</strong> " . $config_from_admin . "</p>";
echo "<p><strong>Config exists:</strong> " . (file_exists($config_from_admin) ? '✅ Yes' : '❌ No') . "</p>";
echo "<p><strong>Includes from admin:</strong> " . $includes_from_admin . "</p>";
echo "<p><strong>Includes exists:</strong> " . (file_exists($includes_from_admin) ? '✅ Yes' : '❌ No') . "</p>";
echo "</div>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>📊 Database Connection Test</h2>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<div class='success'>✅ Database connection successful</div>";
        
        // Test basic query
        $stmt = $db->query("SELECT 1 as test");
        $result = $stmt->fetch();
        echo "<div class='success'>✅ Database query test successful</div>";
        
    } else {
        echo "<div class='error'>❌ Database connection failed</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🎯 Quick Actions</h2>";
echo "<p><a href='admin/dashboard.php' class='test-link'>📊 Test Admin Dashboard</a></p>";
echo "<p><a href='test_diagnostics.php' class='test-link'>🔧 Test Diagnostics</a></p>";
echo "<p><a href='index.php' class='test-link'>🏠 Go to Home</a></p>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>📝 Summary</h2>";
echo "<div class='info'>";
echo "<p><strong>If all tests pass:</strong></p>";
echo "<ul>";
echo "<li>All required files exist and are accessible</li>";
echo "<li>Database connection is working</li>";
echo "<li>Admin dashboard should load without errors</li>";
echo "</ul>";
echo "<p><strong>If you see errors:</strong></p>";
echo "<ul>";
echo "<li>Check file permissions</li>";
echo "<li>Verify XAMPP/WAMP is running</li>";
echo "<li>Check database configuration</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
