<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terms and Policies - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .terms-container {
            max-width: 900px;
            margin: 50px auto;
        }
        .card {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .card-header {
            background-color: #343a40;
            color: white;
            font-weight: bold;
        }
        h3 {
            color: #343a40;
            margin-top: 1.5rem;
            margin-bottom: 1rem;
        }
        .policy-section {
            margin-bottom: 2rem;
        }
        .policy-section:last-child {
            margin-bottom: 0;
        }
        .last-updated {
            font-style: italic;
            color: #6c757d;
            margin-bottom: 1.5rem;
        }
        .print-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 100;
        }
        @media print {
            .navbar, .card-header, .card-footer, .print-button, .btn, footer {
                display: none !important;
            }
            .card {
                box-shadow: none !important;
                border: none !important;
            }
            .card-body {
                padding: 0 !important;
            }
            body {
                background-color: white !important;
            }
            .terms-container {
                margin: 0 !important;
                max-width: 100% !important;
            }
            .policy-section {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="home.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="home.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="catalog.php">Book Catalog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">About Us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">Contact</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <?php if (isLoggedIn()): ?>
                        <a href="admin/dashboard.php" class="btn btn-outline-light me-2">Admin Dashboard</a>
                        <a href="logout.php" class="btn btn-outline-danger">Logout</a>
                    <?php elseif (isMemberLoggedIn()): ?>
                        <a href="member_dashboard.php" class="btn btn-outline-light me-2">My Dashboard</a>
                        <a href="logout.php" class="btn btn-outline-danger">Logout</a>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-outline-light me-2">Login</a>
                        <a href="register.php" class="btn btn-primary">Register</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <div class="terms-container">
        <div class="card">
            <div class="card-header py-3">
                <h1 class="mb-0 text-center">Library Terms and Policies</h1>
            </div>
            <div class="card-body p-4">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    These terms and policies govern your use of the library services. By registering as a member, you agree to abide by these terms.
                </div>

                <p class="last-updated">Last Updated: <?php echo date('F j, Y'); ?></p>

                <!-- Table of Contents -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h3 class="mb-0">Quick Reference</h3>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item"><a href="#membership">Membership Terms</a></li>
                            <li class="list-group-item"><a href="#borrowing">Borrowing Policies</a> - Loan period: 14 days, 5 books maximum</li>
                            <li class="list-group-item"><a href="#privacy">Privacy Policy</a></li>
                            <li class="list-group-item"><a href="#facility">Library Facility Use</a></li>
                            <li class="list-group-item"><a href="#online">Online Services</a></li>
                            <li class="list-group-item"><a href="#conduct">Code of Conduct</a></li>
                            <li class="list-group-item"><a href="#changes">Changes to Terms</a></li>
                        </ul>
                    </div>
                </div>

                <div id="membership" class="policy-section">
                    <h3><i class="bi bi-card-checklist me-2"></i>Membership Terms</h3>
                    <ol>
                        <li>Library membership is available to all residents of the community.</li>
                        <li>Members must be at least 13 years of age. Those under 13 require a parent or guardian to register on their behalf.</li>
                        <li>Membership is valid for one year from the date of registration and must be renewed annually.</li>
                        <li>Members are responsible for notifying the library of any changes to their contact information.</li>
                        <li>The library reserves the right to suspend or terminate membership for violation of library policies.</li>
                    </ol>
                </div>

                <div id="borrowing" class="policy-section">
                    <h3><i class="bi bi-book me-2"></i>Borrowing Policies</h3>
                    <ol>
                        <li>Members may borrow up to 5 books at a time.</li>
                        <li>The standard loan period is 14 days, with the option to renew once if the item is not reserved by another member.</li>
                        <li>Reference materials and rare books may not be borrowed and must be used within the library.</li>
                        <li>Members are responsible for returning borrowed items by the due date.</li>
                        <li>Late returns will incur a fine of $1.00 per day per item.</li>
                        <li>Members will be charged for lost or damaged items based on the replacement cost plus a processing fee.</li>
                    </ol>
                </div>

                <div id="privacy" class="policy-section">
                    <h3><i class="bi bi-shield-check me-2"></i>Privacy Policy</h3>
                    <p>The library is committed to protecting your privacy. We collect personal information solely for the purpose of providing library services and will not share your information with third parties except as required by law.</p>
                    <ol>
                        <li>We collect your name, address, email, and phone number for communication purposes.</li>
                        <li>Your borrowing history is maintained for service improvement and recommendation purposes.</li>
                        <li>You may request to view or delete your personal information at any time by contacting the library administration.</li>
                        <li>We use cookies on our website to enhance your browsing experience. You may disable cookies in your browser settings.</li>
                    </ol>
                </div>

                <div id="facility" class="policy-section">
                    <h3><i class="bi bi-building me-2"></i>Library Facility Use</h3>
                    <ol>
                        <li>Library facilities are available to all members during operating hours.</li>
                        <li>Users are expected to maintain a quiet and respectful environment.</li>
                        <li>Food and drinks are not permitted in the reading areas or near computer stations.</li>
                        <li>The use of mobile phones should be minimized, and calls should be taken outside the reading areas.</li>
                        <li>Library computers are available on a first-come, first-served basis for a maximum of 1 hour when others are waiting.</li>
                        <li>The library is not responsible for personal belongings left unattended.</li>
                    </ol>
                </div>

                <div id="online" class="policy-section">
                    <h3><i class="bi bi-laptop me-2"></i>Online Services</h3>
                    <ol>
                        <li>Online account access is provided for the convenience of members.</li>
                        <li>Members are responsible for maintaining the confidentiality of their account credentials.</li>
                        <li>Unauthorized access to another member's account is strictly prohibited.</li>
                        <li>The library reserves the right to monitor online activity to ensure compliance with these terms.</li>
                        <li>E-resources and digital content are subject to licensing agreements and copyright restrictions.</li>
                    </ol>
                </div>

                <div id="conduct" class="policy-section">
                    <h3><i class="bi bi-exclamation-triangle me-2"></i>Code of Conduct</h3>
                    <ol>
                        <li>Respect for all library users, staff, and property is expected at all times.</li>
                        <li>Harassment, discrimination, or disruptive behavior will not be tolerated.</li>
                        <li>Theft, vandalism, or damage to library materials or property may result in legal action.</li>
                        <li>The library is a shared community space; please be considerate of others.</li>
                    </ol>
                </div>

                <div id="changes" class="policy-section">
                    <h3><i class="bi bi-pencil me-2"></i>Changes to Terms</h3>
                    <p>The library reserves the right to modify these terms and policies at any time. Members will be notified of significant changes via email or notices posted in the library and on our website.</p>
                </div>

                <!-- Print Button -->
                <button class="btn btn-primary print-button" onclick="window.print()">
                    <i class="bi bi-printer me-2"></i>Print Terms
                </button>

                <div class="mt-4 text-center">
                    <a href="register.php" class="btn btn-primary">Return to Registration</a>
                </div>
            </div>
            <div class="card-footer text-center py-3">
                <p class="mb-0">Library Management System &copy; <?php echo date('Y'); ?> | All Rights Reserved</p>
            </div>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
