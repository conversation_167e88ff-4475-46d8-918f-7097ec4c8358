<?php
/**
 * Check Admin Users and Create Test Admin
 */

// Include database connection
require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Check Admin Users - Library Management System</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { padding: 40px 0; background-color: #f8f9fa; }
        .admin-card { max-width: 900px; margin: 0 auto; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
    </style>
</head>
<body>
    <div class='container'>
        <div class='card admin-card'>
            <div class='card-header bg-warning text-dark'>
                <h4 class='mb-0'>👤 Admin Users Check & Setup</h4>
            </div>
            <div class='card-body'>";

// Check if users table exists
try {
    $query = "SHOW TABLES LIKE 'users'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        echo "<div class='alert alert-danger'>❌ Users table does not exist!</div>";
        
        // Create users table
        echo "<p>Creating users table...</p>";
        $create_query = "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100),
            role ENUM('admin', 'librarian') DEFAULT 'librarian',
            status ENUM('active', 'inactive') DEFAULT 'active',
            remember_token VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $db->exec($create_query);
        echo "<div class='alert alert-success'>✅ Users table created successfully!</div>";
    } else {
        echo "<div class='alert alert-success'>✅ Users table exists</div>";
    }
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>❌ Error with users table: " . $e->getMessage() . "</div>";
    exit;
}

// Check existing admin users
echo "<h5>Current Admin Users:</h5>";
try {
    $query = "SELECT id, username, email, role, status FROM users WHERE role = 'admin'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $admin_users = $stmt->fetchAll();
    
    if (count($admin_users) > 0) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>ID</th><th>Username</th><th>Email</th><th>Role</th><th>Status</th><th>Action</th></tr></thead>";
        echo "<tbody>";
        foreach ($admin_users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['username'] . "</td>";
            echo "<td>" . $user['email'] . "</td>";
            echo "<td><span class='badge bg-danger'>" . $user['role'] . "</span></td>";
            echo "<td><span class='badge bg-" . ($user['status'] == 'active' ? 'success' : 'secondary') . "'>" . $user['status'] . "</span></td>";
            echo "<td><a href='direct_user_access.php?user_id=" . $user['id'] . "' class='btn btn-sm btn-primary'>Login as This User</a></td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>⚠️ No admin users found!</div>";
        
        // Create a default admin user
        echo "<h5>Creating Default Admin User:</h5>";
        $admin_username = 'admin';
        $admin_email = '<EMAIL>';
        $admin_password = 'admin123'; // Default password
        $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
        
        try {
            $insert_query = "INSERT INTO users (username, email, password, full_name, role, status) VALUES (?, ?, ?, ?, 'admin', 'active')";
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->execute([$admin_username, $admin_email, $hashed_password, 'System Administrator']);
            
            $admin_id = $db->lastInsertId();
            
            echo "<div class='alert alert-success'>";
            echo "<h6>✅ Default Admin User Created!</h6>";
            echo "<p><strong>Username:</strong> $admin_username</p>";
            echo "<p><strong>Email:</strong> $admin_email</p>";
            echo "<p><strong>Password:</strong> $admin_password</p>";
            echo "<p><strong>User ID:</strong> $admin_id</p>";
            echo "</div>";
            
            echo "<div class='d-grid gap-2'>";
            echo "<a href='direct_user_access.php?user_id=$admin_id' class='btn btn-success'>Login as Admin</a>";
            echo "</div>";
            
        } catch (PDOException $e) {
            echo "<div class='alert alert-danger'>❌ Error creating admin user: " . $e->getMessage() . "</div>";
        }
    }
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>❌ Error checking admin users: " . $e->getMessage() . "</div>";
}

// Quick login form
echo "<hr><h5>Quick Admin Login:</h5>";
echo "<form method='post' action=''>
        <div class='row'>
            <div class='col-md-4'>
                <input type='text' name='username' class='form-control' placeholder='Username' value='admin'>
            </div>
            <div class='col-md-4'>
                <input type='password' name='password' class='form-control' placeholder='Password' value='admin123'>
            </div>
            <div class='col-md-4'>
                <button type='submit' name='quick_login' class='btn btn-primary w-100'>Quick Login</button>
            </div>
        </div>
      </form>";

// Process quick login
if (isset($_POST['quick_login'])) {
    $username = $_POST['username'];
    $password = $_POST['password'];
    
    try {
        $query = "SELECT id, username, email, password, role FROM users WHERE username = ? AND role = 'admin'";
        $stmt = $db->prepare($query);
        $stmt->execute([$username]);
        
        if ($stmt->rowCount() > 0) {
            $user = $stmt->fetch();
            
            if (password_verify($password, $user['password'])) {
                // Start session and login
                session_start();
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['role'] = $user['role'];
                
                echo "<div class='alert alert-success mt-3'>";
                echo "<h6>✅ Login Successful!</h6>";
                echo "<p>You are now logged in as: " . $user['username'] . "</p>";
                echo "<div class='d-grid gap-2'>";
                echo "<a href='admin/email_settings.php' class='btn btn-success'>Go to Email Settings</a>";
                echo "</div>";
                echo "</div>";
            } else {
                echo "<div class='alert alert-danger mt-3'>❌ Invalid password</div>";
            }
        } else {
            echo "<div class='alert alert-danger mt-3'>❌ Admin user not found</div>";
        }
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger mt-3'>❌ Login error: " . $e->getMessage() . "</div>";
    }
}

echo "        </div>
            <div class='card-footer'>
                <div class='d-flex justify-content-between'>
                    <a href='index.php' class='btn btn-secondary'>Dashboard</a>
                    <a href='auth/login.php' class='btn btn-primary'>Regular Login</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
