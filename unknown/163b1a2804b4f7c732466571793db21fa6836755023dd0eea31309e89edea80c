<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="assets/css/home.css">
    <style>
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 9999;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="debug-info">
        <div>CSS Test Page</div>
        <div id="cssStatus">Checking CSS...</div>
        <div id="imageStatus">Checking Image...</div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="home.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <div class="d-flex align-items-center">
                <a href="login.php" class="btn btn-outline-light me-2">
                    <i class="bi bi-person me-1"></i>Login
                </a>
                <a href="register.php" class="btn btn-primary">Register</a>
            </div>
        </div>
    </nav>

    <!-- Modern Hero Section -->
    <section class="hero-section">
        <div class="hero-overlay"></div>
        <div class="container position-relative">
            <div class="row align-items-center min-vh-75">
                <div class="col-lg-6 hero-content text-white">
                    <h1 class="hero-title mb-1">Welcome to Our Library</h1>
                    <p class="hero-subtitle mb-2">A sanctuary of knowledge where books inspire minds and stories transform lives</p>

                    <!-- Quick Search Bar -->
                    <div class="search-container mb-2">
                        <div class="d-flex align-items-center">
                            <form action="catalog.php" method="get" class="search-form flex-grow-1 me-2">
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control" placeholder="What story awaits you today?" aria-label="Search books">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </form>
                            <button id="darkModeToggle" class="btn btn-dark theme-toggle-btn" title="Toggle Dark Mode">
                                <i id="darkModeIcon" class="bi bi-moon"></i>
                            </button>
                        </div>
                    </div>

                    <div class="hero-stats d-flex flex-wrap mb-2">
                        <div class="stat-item me-2 mb-2">
                            <div class="stat-number">150+</div>
                            <div class="stat-label">Volumes</div>
                        </div>
                        <div class="stat-item me-2 mb-2">
                            <div class="stat-number">750+</div>
                            <div class="stat-label">Ready</div>
                        </div>
                        <div class="stat-item mb-2">
                            <div class="stat-number">1000+</div>
                            <div class="stat-label">Readers</div>
                        </div>
                    </div>

                    <div class="hero-buttons">
                        <a href="catalog.php" class="btn btn-primary me-2 mb-2">
                            <i class="bi bi-book-half me-1"></i>Explore Our Shelves
                        </a>
                        <a href="register.php" class="btn btn-outline-light mb-2">
                            <i class="bi bi-person-plus me-1"></i>Become a Reader
                        </a>
                    </div>
                </div>
            </div>

            <!-- Scroll Down Indicator -->
            <div class="scroll-indicator">
                <a href="#featured-books" class="text-white">
                    <div class="scroll-text">Discover More</div>
                    <div class="scroll-icon"><i class="bi bi-chevron-down"></i></div>
                </a>
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Check if CSS is loaded
        function checkCSS() {
            const heroSection = document.querySelector('.hero-section');
            const computedStyle = window.getComputedStyle(heroSection);
            const background = computedStyle.background;
            const height = computedStyle.height;
            
            document.getElementById('cssStatus').innerHTML = 
                `CSS: ${background.includes('gradient') ? '✅ Loaded' : '❌ Not Loaded'}<br>
                Height: ${height}`;
        }
        
        // Check if background image is loaded
        function checkImage() {
            const img = new Image();
            img.onload = function() {
                document.getElementById('imageStatus').innerHTML = '✅ Image Loaded';
            };
            img.onerror = function() {
                document.getElementById('imageStatus').innerHTML = '❌ Image Failed';
            };
            img.src = 'uploads/images/library.jpg';
        }
        
        // Run checks when page loads
        window.addEventListener('load', function() {
            checkCSS();
            checkImage();
        });
        
        // Add dark mode toggle functionality
        document.getElementById('darkModeToggle').addEventListener('click', function() {
            console.log('Dark mode toggle clicked');
        });
    </script>
</body>
</html>
