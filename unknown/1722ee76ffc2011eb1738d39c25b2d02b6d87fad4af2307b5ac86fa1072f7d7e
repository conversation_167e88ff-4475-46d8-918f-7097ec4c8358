<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    echo "Please log in first.";
    exit;
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Find the 1984 book
$query = "SELECT * FROM books WHERE title LIKE '%1984%' AND author LIKE '%Orwell%'";
$stmt = $db->prepare($query);
$stmt->execute();

if ($stmt->rowCount() == 0) {
    echo "Book '1984' not found in the database.";
    exit;
}

$book = $stmt->fetch();
$book_id = $book['id'];

echo "<h2>Update Cover Image for '1984' by <PERSON></h2>";
echo "<p>Book ID: " . $book_id . "</p>";

// Use a sample image URL
$image_url = "https://m.media-amazon.com/images/I/71kxa1-0mfL._AC_UF1000,1000_QL80_.jpg";

// Update the book in the database with the image URL
$query = "UPDATE books SET cover_image = :cover_image WHERE id = :id";
$stmt = $db->prepare($query);
$stmt->bindParam(':cover_image', $image_url);
$stmt->bindParam(':id', $book_id);

if ($stmt->execute()) {
    echo "<div style='color: green; margin: 20px 0;'>✅ Cover image URL updated successfully!</div>";
    echo "<p>You can now view the book details: <a href='books/view.php?id=" . $book_id . "'>View Book Details</a></p>";
    echo "<p>Or go back to the books list: <a href='books/index.php'>Books List</a></p>";
    
    // Display the image
    echo "<div style='margin: 20px 0;'>";
    echo "<h3>Cover Image:</h3>";
    echo "<img src='" . $image_url . "' alt='1984 Cover' style='max-width: 300px;'>";
    echo "</div>";
} else {
    echo "<div style='color: red; margin: 20px 0;'>❌ Failed to update the database.</div>";
}
?>
