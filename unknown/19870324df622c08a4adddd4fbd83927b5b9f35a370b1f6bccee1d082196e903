<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Debug Paths</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h2>Path Debugging</h2>";

echo "<h3>Server Information</h3>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Script Name:</strong> " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p><strong>Request URI:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p><strong>Current Working Directory:</strong> " . getcwd() . "</p>";

echo "<h3>Configuration</h3>";
echo "<p><strong>BASE_URL:</strong> " . BASE_URL . "</p>";
echo "<p><strong>COVERS_PATH:</strong> " . COVERS_PATH . "</p>";
echo "<p><strong>UPLOADS_PATH:</strong> " . UPLOADS_PATH . "</p>";

echo "<h3>Directory Tests</h3>";
$test_paths = [
    'uploads/covers/',
    '../uploads/covers/',
    './uploads/covers/',
    'lms/uploads/covers/',
    __DIR__ . '/uploads/covers/',
    __DIR__ . '/../uploads/covers/',
];

foreach ($test_paths as $path) {
    $exists = is_dir($path);
    $readable = $exists ? is_readable($path) : false;
    echo "<p><strong>{$path}:</strong> " . ($exists ? '✅ Exists' : '❌ Not found') . ($readable ? ' & Readable' : '') . "</p>";
    
    if ($exists) {
        $files = scandir($path);
        $image_count = 0;
        foreach ($files as $file) {
            if ($file != '.' && $file != '..' && in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif'])) {
                $image_count++;
            }
        }
        echo "<p style='margin-left: 20px;'>Contains {$image_count} image files</p>";
    }
}

echo "<h3>URL Tests</h3>";
$test_urls = [
    url('uploads/covers/1984_cover.jpg'),
    BASE_URL . 'uploads/covers/1984_cover.jpg',
    BASE_URL . 'lms/uploads/covers/1984_cover.jpg',
];

foreach ($test_urls as $url) {
    echo "<p><strong>URL:</strong> " . htmlspecialchars($url) . "</p>";
    echo "<p style='margin-left: 20px;'><img src='" . htmlspecialchars($url) . "' style='max-width: 100px; height: auto;' alt='Test' onerror='this.style.border=\"2px solid red\"; this.alt=\"Failed\";'></p>";
}

echo "<h3>File System Check</h3>";
$image_file = '1984_cover.jpg';
$possible_paths = [
    'uploads/covers/' . $image_file,
    '../uploads/covers/' . $image_file,
    './uploads/covers/' . $image_file,
    'lms/uploads/covers/' . $image_file,
    __DIR__ . '/uploads/covers/' . $image_file,
];

foreach ($possible_paths as $path) {
    $exists = file_exists($path);
    echo "<p><strong>{$path}:</strong> " . ($exists ? '✅ Found' : '❌ Not found') . "</p>";
    if ($exists) {
        echo "<p style='margin-left: 20px;'>Size: " . filesize($path) . " bytes</p>";
    }
}

echo "</div></body></html>";
?>
