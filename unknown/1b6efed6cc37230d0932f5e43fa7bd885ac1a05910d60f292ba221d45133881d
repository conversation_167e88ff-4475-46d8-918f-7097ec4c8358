/**
 * Dashboard Customization JavaScript
 * Handles saved filters, task management, and other dashboard features
 * Dashboard customization functionality has been completely removed
 */

document.addEventListener('DOMContentLoaded', function() {
    // Dashboard customization functionality completely removed
    initSavedFilters();
    initTaskManagement();
    initBulkOperations();
    initBookConditionTracking();
    initMemberCommunication();
});

/**
 * Initialize saved filters functionality
 */
function initSavedFilters() {
    // Add saved filters UI to search forms
    const searchForms = document.querySelectorAll('form[data-supports-saved-filters="true"]');
    searchForms.forEach(form => {
        const formControls = document.createElement('div');
        formControls.className = 'saved-filter-controls mt-2';
        formControls.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="dropdown me-2">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-bookmark me-1"></i>Saved Filters
                    </button>
                    <ul class="dropdown-menu saved-filters-list">
                        <li><a class="dropdown-item" href="#" data-action="save-current">Save Current Filter</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><span class="dropdown-item-text text-muted">No saved filters</span></li>
                    </ul>
                </div>
            </div>
        `;

        form.appendChild(formControls);

        // Add event listener for saving current filter
        const saveFilterLink = formControls.querySelector('[data-action="save-current"]');
        saveFilterLink.addEventListener('click', function(e) {
            e.preventDefault();
            // Show the save filter modal
            const modal = new bootstrap.Modal(document.getElementById('saveFilterModal'));
            modal.show();
        });
    });
}

/**
 * Initialize task management functionality
 */
function initTaskManagement() {
    // Implementation will be added in the next phase
}

/**
 * Initialize bulk operations functionality
 */
function initBulkOperations() {
    // Implementation will be added in the next phase
}

/**
 * Initialize book condition tracking
 */
function initBookConditionTracking() {
    // Implementation will be added in the next phase
}

/**
 * Initialize member communication center
 */
function initMemberCommunication() {
    // Implementation will be added in the next phase
}

/**
 * Show a notification message
 */
function showNotification(message, type = 'info') {
    const notificationContainer = document.getElementById('notificationContainer');
    if (!notificationContainer) return;

    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    notificationContainer.appendChild(notification);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 150);
    }, 5000);
}
