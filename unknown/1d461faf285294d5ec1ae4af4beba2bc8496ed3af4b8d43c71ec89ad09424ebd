<?php
/**
 * Generate Members and Book Loans Script
 * This script generates realistic members and automatically assigns book loans to them
 */

require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<h1>📚 Generating Members and Book Loans</h1>";

// Sample member data with realistic profiles
$sample_members = [
    [
        'first_name' => '<PERSON>',
        'last_name' => '<PERSON>',
        'email' => '<EMAIL>',
        'phone' => '555-0101',
        'address' => '123 Oak Street, Springfield, IL 62701',
        'membership_date' => '2024-01-15'
    ],
    [
        'first_name' => '<PERSON>',
        'last_name' => '<PERSON>',
        'email' => '<EMAIL>',
        'phone' => '555-0102',
        'address' => '456 Maple Avenue, Springfield, IL 62702',
        'membership_date' => '2024-02-20'
    ],
    [
        'first_name' => '<PERSON>',
        'last_name' => '<PERSON>',
        'email' => 'maria.rod<PERSON><PERSON><PERSON>@email.com',
        'phone' => '555-0103',
        'address' => '789 Pine Road, Springfield, IL 62703',
        'membership_date' => '2024-03-10'
    ],
    [
        'first_name' => '<PERSON>',
        'last_name' => '<PERSON>',
        'email' => '<EMAIL>',
        'phone' => '555-0104',
        'address' => '321 <PERSON> Drive, Springfield, IL 62704',
        'membership_date' => '2024-04-05'
    ],
    [
        'first_name' => 'Sarah',
        'last_name' => 'Thompson',
        'email' => '<EMAIL>',
        'phone' => '555-0105',
        'address' => '654 Cedar Lane, Springfield, IL 62705',
        'membership_date' => '2024-05-12'
    ],
    [
        'first_name' => 'David',
        'last_name' => 'Kim',
        'email' => '<EMAIL>',
        'phone' => '555-0106',
        'address' => '987 Birch Street, Springfield, IL 62706',
        'membership_date' => '2024-06-18'
    ],
    [
        'first_name' => 'Emily',
        'last_name' => 'Davis',
        'email' => '<EMAIL>',
        'phone' => '555-0107',
        'address' => '147 Walnut Avenue, Springfield, IL 62707',
        'membership_date' => '2024-07-22'
    ],
    [
        'first_name' => 'Michael',
        'last_name' => 'Brown',
        'email' => '<EMAIL>',
        'phone' => '555-0108',
        'address' => '258 Spruce Road, Springfield, IL 62708',
        'membership_date' => '2024-08-14'
    ],
    [
        'first_name' => 'Jessica',
        'last_name' => 'Miller',
        'email' => '<EMAIL>',
        'phone' => '555-0109',
        'address' => '369 Ash Drive, Springfield, IL 62709',
        'membership_date' => '2024-09-03'
    ],
    [
        'first_name' => 'Christopher',
        'last_name' => 'Garcia',
        'email' => '<EMAIL>',
        'phone' => '555-0110',
        'address' => '741 Hickory Lane, Springfield, IL 62710',
        'membership_date' => '2024-10-11'
    ],
    [
        'first_name' => 'Amanda',
        'last_name' => 'Martinez',
        'email' => '<EMAIL>',
        'phone' => '555-0111',
        'address' => '852 Poplar Street, Springfield, IL 62711',
        'membership_date' => '2024-11-07'
    ],
    [
        'first_name' => 'Daniel',
        'last_name' => 'Anderson',
        'email' => '<EMAIL>',
        'phone' => '555-0112',
        'address' => '963 Willow Avenue, Springfield, IL 62712',
        'membership_date' => '2024-12-01'
    ]
];

// Function to add a member to the database
function addMember($db, $member_data) {
    // Check if member already exists
    $check_query = "SELECT id FROM members WHERE email = :email";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':email', $member_data['email']);
    $check_stmt->execute();

    if ($check_stmt->rowCount() > 0) {
        return ['success' => false, 'message' => "Member with email {$member_data['email']} already exists"];
    }

    // Generate default password based on first name (lowercase)
    $default_password = strtolower($member_data['first_name']) . '123';
    $hashed_password = password_hash($default_password, PASSWORD_DEFAULT);

    // Insert new member with password
    $query = "INSERT INTO members (first_name, last_name, email, phone, address, membership_date, membership_status, password)
              VALUES (:first_name, :last_name, :email, :phone, :address, :membership_date, 'active', :password)";

    $stmt = $db->prepare($query);
    $stmt->bindParam(':first_name', $member_data['first_name']);
    $stmt->bindParam(':last_name', $member_data['last_name']);
    $stmt->bindParam(':email', $member_data['email']);
    $stmt->bindParam(':phone', $member_data['phone']);
    $stmt->bindParam(':address', $member_data['address']);
    $stmt->bindParam(':membership_date', $member_data['membership_date']);
    $stmt->bindParam(':password', $hashed_password);

    if ($stmt->execute()) {
        return [
            'success' => true,
            'member_id' => $db->lastInsertId(),
            'message' => "Member added successfully",
            'password' => $default_password
        ];
    } else {
        return ['success' => false, 'message' => "Failed to add member"];
    }
}

// Function to get available books
function getAvailableBooks($db) {
    $query = "SELECT id, title, author, available_quantity FROM books WHERE available_quantity > 0 ORDER BY title";
    $stmt = $db->prepare($query);
    $stmt->execute();
    return $stmt->fetchAll();
}

// Function to create a book loan
function createBookLoan($db, $book_id, $member_id, $issue_date = null, $due_date = null, $return_date = null, $status = 'borrowed') {
    if (!$issue_date) {
        $issue_date = date('Y-m-d');
    }
    if (!$due_date) {
        $due_date = date('Y-m-d', strtotime($issue_date . ' +14 days'));
    }

    // Check if book is available (only for new borrows, not returns)
    if ($status === 'borrowed') {
        $check_query = "SELECT available_quantity FROM books WHERE id = :book_id";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->bindParam(':book_id', $book_id);
        $check_stmt->execute();
        $book = $check_stmt->fetch();

        if (!$book || $book['available_quantity'] <= 0) {
            return ['success' => false, 'message' => "Book not available"];
        }
    }

    // Calculate fine for returned books if overdue
    $fine = 0;
    if ($status === 'returned' && $return_date && strtotime($return_date) > strtotime($due_date)) {
        $days_overdue = (strtotime($return_date) - strtotime($due_date)) / (60 * 60 * 24);
        $fine = $days_overdue * 1.00; // $1 per day fine
    }

    // Begin transaction
    $db->beginTransaction();

    try {
        // Insert loan record
        $loan_query = "INSERT INTO book_loans (book_id, member_id, issue_date, due_date, return_date, status, fine)
                       VALUES (:book_id, :member_id, :issue_date, :due_date, :return_date, :status, :fine)";
        $loan_stmt = $db->prepare($loan_query);
        $loan_stmt->bindParam(':book_id', $book_id);
        $loan_stmt->bindParam(':member_id', $member_id);
        $loan_stmt->bindParam(':issue_date', $issue_date);
        $loan_stmt->bindParam(':due_date', $due_date);
        $loan_stmt->bindParam(':return_date', $return_date);
        $loan_stmt->bindParam(':status', $status);
        $loan_stmt->bindParam(':fine', $fine);
        $loan_stmt->execute();

        // Update book available quantity
        if ($status === 'borrowed') {
            // Decrease available quantity for new borrows
            $update_query = "UPDATE books SET available_quantity = available_quantity - 1 WHERE id = :book_id";
        } else {
            // Don't change quantity for returned books (they're already returned in this scenario)
            $update_query = null;
        }

        if ($update_query) {
            $update_stmt = $db->prepare($update_query);
            $update_stmt->bindParam(':book_id', $book_id);
            $update_stmt->execute();
        }

        // Commit transaction
        $db->commit();

        $fine_message = $fine > 0 ? " (Fine: $" . number_format($fine, 2) . ")" : "";
        return ['success' => true, 'loan_id' => $db->lastInsertId(), 'message' => "Book loan created successfully" . $fine_message];
    } catch (Exception $e) {
        // Rollback transaction on error
        $db->rollBack();
        return ['success' => false, 'message' => "Failed to create loan: " . $e->getMessage()];
    }
}

// Start processing
echo "<h2>Step 1: Adding Members to Database</h2>";

$added_members = [];
$member_count = 0;
$skipped_count = 0;

foreach ($sample_members as $member) {
    $result = addMember($db, $member);

    if ($result['success']) {
        $added_members[] = [
            'id' => $result['member_id'],
            'name' => $member['first_name'] . ' ' . $member['last_name'],
            'email' => $member['email'],
            'password' => $result['password']
        ];
        echo "<p style='color: green;'>✅ Added: {$member['first_name']} {$member['last_name']} (ID: {$result['member_id']}) - Password: <strong>{$result['password']}</strong></p>";
        $member_count++;
    } else {
        echo "<p style='color: orange;'>⚠️ Skipped: {$member['first_name']} {$member['last_name']} - {$result['message']}</p>";
        $skipped_count++;
    }
}

echo "<h3>Members Summary</h3>";
echo "<p><strong>Members added:</strong> $member_count</p>";
echo "<p><strong>Members skipped:</strong> $skipped_count</p>";

// Step 2: Create Book Loans
echo "<h2>Step 2: Creating Book Loans</h2>";

// Get all available books
$available_books = getAvailableBooks($db);
echo "<p>Found " . count($available_books) . " available books for borrowing.</p>";

if (empty($available_books)) {
    echo "<p style='color: red;'>❌ No books available for borrowing. Please add books to the library first.</p>";
} else {
    // Get all members (including existing ones)
    $all_members_query = "SELECT id, first_name, last_name, email FROM members WHERE membership_status = 'active' ORDER BY id";
    $all_members_stmt = $db->prepare($all_members_query);
    $all_members_stmt->execute();
    $all_members = $all_members_stmt->fetchAll();

    echo "<p>Found " . count($all_members) . " active members for book borrowing.</p>";

    // Create realistic borrowing patterns with both active and returned books
    $loan_patterns = [
        // Pattern: [active_books, returned_books, days_ago_for_active, days_ago_for_returned]
        ['active_books' => 2, 'returned_books' => 1, 'active_days_ago' => 5, 'returned_days_ago' => 20],
        ['active_books' => 1, 'returned_books' => 2, 'active_days_ago' => 10, 'returned_days_ago' => 25],
        ['active_books' => 1, 'returned_books' => 0, 'active_days_ago' => 3, 'returned_days_ago' => 0],
        ['active_books' => 0, 'returned_books' => 3, 'active_days_ago' => 0, 'returned_days_ago' => 30],
        ['active_books' => 1, 'returned_books' => 1, 'active_days_ago' => 1, 'returned_days_ago' => 15],
        ['active_books' => 2, 'returned_books' => 2, 'active_days_ago' => 12, 'returned_days_ago' => 35],
        ['active_books' => 1, 'returned_books' => 1, 'active_days_ago' => 8, 'returned_days_ago' => 18],
        ['active_books' => 0, 'returned_books' => 2, 'active_days_ago' => 0, 'returned_days_ago' => 22],
    ];

    $loans_created = 0;
    $loans_failed = 0;
    $returned_loans_created = 0;
    $book_index = 0;

    // Assign loans to members
    foreach ($all_members as $index => $member) {
        if ($index >= count($loan_patterns)) break; // Don't exceed our pattern array

        $pattern = $loan_patterns[$index];
        $active_books = $pattern['active_books'];
        $returned_books = $pattern['returned_books'];
        $active_days_ago = $pattern['active_days_ago'];
        $returned_days_ago = $pattern['returned_days_ago'];

        echo "<h4>📚 Assigning books to {$member['first_name']} {$member['last_name']}</h4>";

        // Create returned book loans first (older history)
        for ($i = 0; $i < $returned_books; $i++) {
            if ($book_index >= count($available_books)) {
                echo "<p style='color: orange;'>⚠️ No more books available for loan history</p>";
                break;
            }

            $book = $available_books[$book_index];
            $issue_date = date('Y-m-d', strtotime("-{$returned_days_ago} days"));
            $due_date = date('Y-m-d', strtotime($issue_date . ' +14 days'));

            // Some books returned on time, some late
            $return_delay = rand(0, 5); // 0-5 days after due date (some on time, some late)
            $return_date = date('Y-m-d', strtotime($due_date . " +{$return_delay} days"));

            $loan_result = createBookLoan($db, $book['id'], $member['id'], $issue_date, $due_date, $return_date, 'returned');

            if ($loan_result['success']) {
                $status_text = $return_delay > 0 ? "returned late" : "returned on time";
                echo "<p style='color: blue;'>📖 Returned: \"{$book['title']}\" by {$book['author']} ({$status_text}) {$loan_result['message']}</p>";
                $returned_loans_created++;
            } else {
                echo "<p style='color: red;'>❌ Failed to create return record for \"{$book['title']}\": {$loan_result['message']}</p>";
                $loans_failed++;
            }
            $book_index++;
        }

        // Create active book loans (currently borrowed)
        for ($i = 0; $i < $active_books; $i++) {
            if ($book_index >= count($available_books)) {
                echo "<p style='color: orange;'>⚠️ No more books available for borrowing</p>";
                break;
            }

            $book = $available_books[$book_index];
            $issue_date = date('Y-m-d', strtotime("-{$active_days_ago} days"));
            $due_date = date('Y-m-d', strtotime($issue_date . ' +14 days'));

            $loan_result = createBookLoan($db, $book['id'], $member['id'], $issue_date, $due_date);

            if ($loan_result['success']) {
                // Check if book is overdue
                $is_overdue = strtotime($due_date) < strtotime(date('Y-m-d'));
                $status_text = $is_overdue ? "(OVERDUE)" : "(Active)";
                $color = $is_overdue ? "orange" : "green";

                echo "<p style='color: {$color};'>✅ Currently Borrowed: \"{$book['title']}\" by {$book['author']} {$status_text} (Loan ID: {$loan_result['loan_id']})</p>";
                $loans_created++;

                // Update the available quantity in our local array
                $available_books[$book_index]['available_quantity']--;

                // Move to next book if this one is no longer available
                if ($available_books[$book_index]['available_quantity'] <= 0) {
                    $book_index++;
                }
            } else {
                echo "<p style='color: red;'>❌ Failed to borrow \"{$book['title']}\": {$loan_result['message']}</p>";
                $loans_failed++;
                $book_index++; // Move to next book on failure
            }
        }

        if ($active_books == 0 && $returned_books == 0) {
            echo "<p style='color: gray;'>📝 No books assigned to this member</p>";
        }
    }

    echo "<h3>Book Loans Summary</h3>";
    echo "<p><strong>Active loans created:</strong> $loans_created</p>";
    echo "<p><strong>Returned loans created:</strong> $returned_loans_created</p>";
    echo "<p><strong>Total loans created:</strong> " . ($loans_created + $returned_loans_created) . "</p>";
    echo "<p><strong>Loans failed:</strong> $loans_failed</p>";

    // Update overdue loan statuses
    echo "<h3>📅 Updating Overdue Loan Statuses</h3>";
    $overdue_query = "UPDATE book_loans SET status = 'overdue' WHERE status = 'borrowed' AND due_date < CURDATE()";
    $overdue_stmt = $db->prepare($overdue_query);
    $overdue_stmt->execute();
    $overdue_count = $overdue_stmt->rowCount();

    if ($overdue_count > 0) {
        echo "<p style='color: orange;'>⚠️ Updated $overdue_count loan(s) to overdue status</p>";
    } else {
        echo "<p style='color: green;'>✅ No overdue loans found</p>";
    }
}

// Step 3: Display Final Summary
echo "<h2>Step 3: Final Summary</h2>";

// Get total counts from database
$total_members_query = "SELECT COUNT(*) as total FROM members";
$total_members_stmt = $db->prepare($total_members_query);
$total_members_stmt->execute();
$total_members = $total_members_stmt->fetch()['total'];

$total_loans_query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'borrowed'";
$total_loans_stmt = $db->prepare($total_loans_query);
$total_loans_stmt->execute();
$total_active_loans = $total_loans_stmt->fetch()['total'];

$total_books_query = "SELECT COUNT(*) as total FROM books";
$total_books_stmt = $db->prepare($total_books_query);
$total_books_stmt->execute();
$total_books = $total_books_stmt->fetch()['total'];

echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🎉 SUCCESS! Members and Loans Generated!</h3>";
echo "<p><strong>Database Summary:</strong></p>";
echo "<ul>";
echo "<li><strong>Total Members:</strong> $total_members</li>";
echo "<li><strong>Total Books:</strong> $total_books</li>";
echo "<li><strong>Active Loans:</strong> $total_active_loans</li>";
echo "</ul>";
echo "<p><strong>What you can do now:</strong></p>";
echo "<ul>";
echo "<li><a href='members/index.php'>View all members</a></li>";
echo "<li><a href='books/index.php'>View all books</a></li>";
echo "<li><a href='loans/index.php'>View all loans</a></li>";
echo "<li><a href='librarian/dashboard.php'>Go to Librarian Dashboard</a></li>";
echo "</ul>";
echo "</div>";

// Show member login credentials
if (!empty($added_members)) {
    echo "<h3>🔐 Member Login Credentials</h3>";
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>⚠️ Important:</strong> Save these login credentials for the newly created members!</p>";
    echo "</div>";

    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Member ID</th><th>Name</th><th>Email</th><th>Password</th><th>Login URL</th>";
    echo "</tr>";

    foreach ($added_members as $member) {
        echo "<tr>";
        echo "<td>{$member['id']}</td>";
        echo "<td>{$member['name']}</td>";
        echo "<td>{$member['email']}</td>";
        echo "<td style='font-family: monospace; background: #f8f9fa;'><strong>{$member['password']}</strong></td>";
        echo "<td><a href='member_login.php' target='_blank'>Member Login</a></td>";
        echo "</tr>";
    }
    echo "</table>";

    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>📝 Password Pattern:</h4>";
    echo "<p>All passwords follow the pattern: <code>[firstname]123</code></p>";
    echo "<p><strong>Examples:</strong></p>";
    echo "<ul>";
    echo "<li>Alice Johnson → Password: <code>alice123</code></li>";
    echo "<li>Robert Chen → Password: <code>robert123</code></li>";
    echo "<li>Maria Rodriguez → Password: <code>maria123</code></li>";
    echo "</ul>";
    echo "<p><strong>Note:</strong> Members can change their passwords after logging in through their profile page.</p>";
    echo "</div>";
}

// Show sample of recent loans
echo "<h3>Recent Book Loans:</h3>";
$recent_loans_query = "SELECT bl.id, bl.issue_date, bl.due_date, bl.status,
                       b.title as book_title, b.author as book_author,
                       CONCAT(m.first_name, ' ', m.last_name) as member_name
                       FROM book_loans bl
                       JOIN books b ON bl.book_id = b.id
                       JOIN members m ON bl.member_id = m.id
                       ORDER BY bl.created_at DESC
                       LIMIT 10";
$recent_loans_stmt = $db->prepare($recent_loans_query);
$recent_loans_stmt->execute();
$recent_loans = $recent_loans_stmt->fetchAll();

if (!empty($recent_loans)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Loan ID</th><th>Member</th><th>Book</th><th>Author</th><th>Issue Date</th><th>Due Date</th><th>Status</th>";
    echo "</tr>";

    foreach ($recent_loans as $loan) {
        $status_color = $loan['status'] === 'borrowed' ? 'green' : ($loan['status'] === 'overdue' ? 'red' : 'blue');
        echo "<tr>";
        echo "<td>{$loan['id']}</td>";
        echo "<td>{$loan['member_name']}</td>";
        echo "<td>{$loan['book_title']}</td>";
        echo "<td>{$loan['book_author']}</td>";
        echo "<td>{$loan['issue_date']}</td>";
        echo "<td>{$loan['due_date']}</td>";
        echo "<td style='color: $status_color; font-weight: bold;'>{$loan['status']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No loans found in the database.</p>";
}

?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h1, h2, h3, h4 { color: #333; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    a { color: #007bff; text-decoration: none; }
    a:hover { text-decoration: underline; }
    .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0; }
</style>
