<?php
/**
 * Final System Fix Script
 * This script performs comprehensive fixes to make the LMS system fully functional
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Final System Fix - LMS</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css'>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .fix-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: white; }
    </style>
</head>
<body>";

echo "<div class='container'>";
echo "<h1 class='mb-4'><i class='bi bi-tools'></i> Final System Fix</h1>";

$fixes_applied = 0;
$errors_found = 0;

// Fix 1: Create missing directories
echo "<div class='fix-section'>";
echo "<h3>1. Creating Missing Directories</h3>";

$directories = [
    'uploads',
    'uploads/covers',
    'uploads/members', 
    'uploads/profiles',
    'uploads/images',
    'logs'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "<p class='success'><i class='bi bi-check-circle'></i> Created directory: $dir</p>";
            $fixes_applied++;
        } else {
            echo "<p class='error'><i class='bi bi-x-circle'></i> Failed to create directory: $dir</p>";
            $errors_found++;
        }
    } else {
        echo "<p class='info'><i class='bi bi-info-circle'></i> Directory exists: $dir</p>";
    }
}
echo "</div>";

// Fix 2: Database Connection and Setup
echo "<div class='fix-section'>";
echo "<h3>2. Database Connection and Setup</h3>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    echo "<p class='success'><i class='bi bi-check-circle'></i> Database connection successful</p>";
    
    // Check and create missing tables
    $required_tables = [
        'users' => 'CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(100) NOT NULL UNIQUE,
            role ENUM(\'admin\', \'librarian\') NOT NULL,
            full_name VARCHAR(100) NULL,
            remember_token VARCHAR(255) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )',
        'members' => 'CREATE TABLE IF NOT EXISTS members (
            id INT AUTO_INCREMENT PRIMARY KEY,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NULL,
            phone VARCHAR(20),
            address TEXT,
            membership_date DATE NOT NULL,
            membership_status ENUM(\'active\', \'inactive\', \'suspended\') DEFAULT \'active\',
            remember_token VARCHAR(255) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )',
        'books' => 'CREATE TABLE IF NOT EXISTS books (
            id INT AUTO_INCREMENT PRIMARY KEY,
            isbn VARCHAR(20) UNIQUE,
            title VARCHAR(255) NOT NULL,
            author VARCHAR(100) NOT NULL,
            category VARCHAR(50),
            publication_year INT,
            publisher VARCHAR(100),
            quantity INT NOT NULL DEFAULT 1,
            available_quantity INT NOT NULL DEFAULT 1,
            shelf_location VARCHAR(50),
            description TEXT,
            cover_image VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )',
        'book_loans' => 'CREATE TABLE IF NOT EXISTS book_loans (
            id INT AUTO_INCREMENT PRIMARY KEY,
            book_id INT NOT NULL,
            member_id INT NOT NULL,
            issue_date DATE NOT NULL,
            due_date DATE NOT NULL,
            return_date DATE,
            fine DECIMAL(10, 2) DEFAULT 0.00,
            status ENUM(\'borrowed\', \'returned\', \'overdue\') DEFAULT \'borrowed\',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
        )'
    ];
    
    foreach ($required_tables as $table_name => $create_sql) {
        try {
            $db->exec($create_sql);
            echo "<p class='success'><i class='bi bi-check-circle'></i> Table '$table_name' ready</p>";
            $fixes_applied++;
        } catch (Exception $e) {
            echo "<p class='error'><i class='bi bi-x-circle'></i> Error with table '$table_name': " . $e->getMessage() . "</p>";
            $errors_found++;
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'><i class='bi bi-x-circle'></i> Database connection failed: " . $e->getMessage() . "</p>";
    $errors_found++;
}
echo "</div>";

// Fix 3: Create Default Admin User
echo "<div class='fix-section'>";
echo "<h3>3. Default Admin User</h3>";

if (isset($db)) {
    try {
        // Check if admin exists
        $query = "SELECT COUNT(*) as count FROM users WHERE role = 'admin'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['count'] == 0) {
            // Create default admin
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $insert_query = "INSERT INTO users (username, email, password, role, full_name) VALUES ('admin', '<EMAIL>', ?, 'admin', 'System Administrator')";
            $stmt = $db->prepare($insert_query);
            
            if ($stmt->execute([$admin_password])) {
                echo "<p class='success'><i class='bi bi-check-circle'></i> Default admin user created</p>";
                echo "<p class='info'><i class='bi bi-info-circle'></i> Username: admin | Password: admin123</p>";
                $fixes_applied++;
            } else {
                echo "<p class='error'><i class='bi bi-x-circle'></i> Failed to create admin user</p>";
                $errors_found++;
            }
        } else {
            echo "<p class='info'><i class='bi bi-info-circle'></i> Admin user already exists</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'><i class='bi bi-x-circle'></i> Error checking admin user: " . $e->getMessage() . "</p>";
        $errors_found++;
    }
}
echo "</div>";

// Fix 4: Sample Data
echo "<div class='fix-section'>";
echo "<h3>4. Sample Data</h3>";

if (isset($db)) {
    try {
        // Check if books exist
        $query = "SELECT COUNT(*) as count FROM books";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['count'] == 0) {
            // Insert sample books
            $books = [
                ['9780132350884', 'Clean Code', 'Robert C. Martin', 'Programming', 2008, 'Prentice Hall', 3, 3, 'A1'],
                ['9780262033848', 'Introduction to Algorithms', 'Thomas H. Cormen', 'Computer Science', 2009, 'MIT Press', 2, 2, 'B2'],
                ['9780134685991', 'Effective Java', 'Joshua Bloch', 'Programming', 2018, 'Addison-Wesley', 4, 4, 'A3']
            ];
            
            $insert_query = "INSERT INTO books (isbn, title, author, category, publication_year, publisher, quantity, available_quantity, shelf_location) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $db->prepare($insert_query);
            
            foreach ($books as $book) {
                $stmt->execute($book);
            }
            
            echo "<p class='success'><i class='bi bi-check-circle'></i> Sample books added</p>";
            $fixes_applied++;
        } else {
            echo "<p class='info'><i class='bi bi-info-circle'></i> Books already exist ($result[count] found)</p>";
        }
        
        // Check if members exist
        $query = "SELECT COUNT(*) as count FROM members";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['count'] == 0) {
            // Insert sample members
            $members = [
                ['John', 'Doe', '<EMAIL>', '************', '123 Main St', date('Y-m-d')],
                ['Jane', 'Smith', '<EMAIL>', '************', '456 Oak Ave', date('Y-m-d')]
            ];
            
            $insert_query = "INSERT INTO members (first_name, last_name, email, phone, address, membership_date) VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $db->prepare($insert_query);
            
            foreach ($members as $member) {
                $stmt->execute($member);
            }
            
            echo "<p class='success'><i class='bi bi-check-circle'></i> Sample members added</p>";
            $fixes_applied++;
        } else {
            echo "<p class='info'><i class='bi bi-info-circle'></i> Members already exist ($result[count] found)</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'><i class='bi bi-x-circle'></i> Error adding sample data: " . $e->getMessage() . "</p>";
        $errors_found++;
    }
}
echo "</div>";

// Summary
echo "<div class='fix-section'>";
echo "<h3>Fix Summary</h3>";
echo "<p class='success'><i class='bi bi-check-circle'></i> Fixes Applied: $fixes_applied</p>";
echo "<p class='error'><i class='bi bi-x-circle'></i> Errors Found: $errors_found</p>";

if ($errors_found == 0) {
    echo "<div class='alert alert-success'>";
    echo "<h4><i class='bi bi-check-circle'></i> System Ready!</h4>";
    echo "<p>Your LMS system has been successfully configured and is ready to use.</p>";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h4><i class='bi bi-exclamation-triangle'></i> Some Issues Remain</h4>";
    echo "<p>Please check the errors above and resolve them manually.</p>";
    echo "</div>";
}
echo "</div>";

// Quick Access Links
echo "<div class='fix-section'>";
echo "<h3>Quick Access</h3>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<a href='access.php' class='btn btn-primary me-2 mb-2'><i class='bi bi-house'></i> Access Portal</a>";
echo "<a href='login.php' class='btn btn-success me-2 mb-2'><i class='bi bi-box-arrow-in-right'></i> Login</a>";
echo "<a href='admin/dashboard.php' class='btn btn-warning me-2 mb-2'><i class='bi bi-speedometer2'></i> Admin Dashboard</a>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<a href='diagnostic.php' class='btn btn-info me-2 mb-2'><i class='bi bi-tools'></i> Diagnostic</a>";
echo "<a href='index.php' class='btn btn-secondary me-2 mb-2'><i class='bi bi-house'></i> Home</a>";
echo "<a href='catalog.php' class='btn btn-outline-primary me-2 mb-2'><i class='bi bi-book'></i> Catalog</a>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>"; // container
echo "</body></html>";
?>
