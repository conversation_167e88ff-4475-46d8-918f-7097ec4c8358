<?php
/**
 * Library Management System Database Verification Script
 */

// Database connection parameters
$host = 'localhost';
$dbname = 'lms_db';
$username = 'root';
$password = '';

try {
    // Connect to the database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Library Management System - Database Verification</h2>";
    
    // Check if tables exist
    $tables = ['users', 'members', 'books', 'book_loans', 'book_reservations', 'email_logs'];
    $missing_tables = [];
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() == 0) {
            $missing_tables[] = $table;
        }
    }
    
    if (empty($missing_tables)) {
        echo "<p>✅ All required tables exist.</p>";
    } else {
        echo "<p>❌ Missing tables: " . implode(', ', $missing_tables) . "</p>";
    }
    
    // Check if sample data exists
    $data_checks = [
        'users' => "SELECT COUNT(*) FROM users",
        'members' => "SELECT COUNT(*) FROM members",
        'books' => "SELECT COUNT(*) FROM books",
        'book_loans' => "SELECT COUNT(*) FROM book_loans",
        'book_reservations' => "SELECT COUNT(*) FROM book_reservations"
    ];
    
    foreach ($data_checks as $table => $query) {
        $stmt = $pdo->query($query);
        $count = $stmt->fetchColumn();
        echo "<p>✅ Table '$table' contains $count records.</p>";
    }
    
    // Display sample data
    echo "<h3>Sample Users</h3>";
    $stmt = $pdo->query("SELECT id, username, email, role FROM users");
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Role</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['username'] . "</td>";
        echo "<td>" . $row['email'] . "</td>";
        echo "<td>" . $row['role'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Sample Books</h3>";
    $stmt = $pdo->query("SELECT id, isbn, title, author, category, available_quantity FROM books LIMIT 5");
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>ISBN</th><th>Title</th><th>Author</th><th>Category</th><th>Available</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['isbn'] . "</td>";
        echo "<td>" . $row['title'] . "</td>";
        echo "<td>" . $row['author'] . "</td>";
        echo "<td>" . $row['category'] . "</td>";
        echo "<td>" . $row['available_quantity'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Sample Members</h3>";
    $stmt = $pdo->query("SELECT id, first_name, last_name, email, membership_status FROM members LIMIT 5");
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>First Name</th><th>Last Name</th><th>Email</th><th>Status</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['first_name'] . "</td>";
        echo "<td>" . $row['last_name'] . "</td>";
        echo "<td>" . $row['email'] . "</td>";
        echo "<td>" . $row['membership_status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Sample Book Loans</h3>";
    $stmt = $pdo->query("SELECT bl.id, b.title, CONCAT(m.first_name, ' ', m.last_name) as member_name, 
                         bl.issue_date, bl.due_date, bl.return_date, bl.status 
                         FROM book_loans bl
                         JOIN books b ON bl.book_id = b.id
                         JOIN members m ON bl.member_id = m.id");
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>Book</th><th>Member</th><th>Issue Date</th><th>Due Date</th><th>Return Date</th><th>Status</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['title'] . "</td>";
        echo "<td>" . $row['member_name'] . "</td>";
        echo "<td>" . $row['issue_date'] . "</td>";
        echo "<td>" . $row['due_date'] . "</td>";
        echo "<td>" . ($row['return_date'] ? $row['return_date'] : 'N/A') . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Database verification completed successfully! 🎉</h3>";
    echo "<p>Your Library Management System database is set up correctly with sample data.</p>";
    
} catch (PDOException $e) {
    echo "<h2>Error!</h2>";
    echo "<p>An error occurred during database verification:</p>";
    echo "<pre>" . $e->getMessage() . "</pre>";
}
?>
