/**
 * Enhanced Notifications JavaScript
 */
document.addEventListener('DOMContentLoaded', function() {
    // Bell animation when there are unread notifications
    const bellIcon = document.querySelector('.bell-icon');
    const unreadBadge = document.querySelector('.notification-badge');
    const staticNotificationsContainer = document.querySelector('.static-notifications-container');
    const closeNotificationsBtn = document.getElementById('closeNotifications');
    const minimizeNotificationsBtn = document.getElementById('minimizeNotifications');
    const refreshNotificationsBtn = document.getElementById('refreshNotifications');

    // Check if notifications should be hidden (from localStorage)
    const notificationsHidden = localStorage.getItem('notificationsHidden') === 'true';
    const notificationsMinimized = localStorage.getItem('notificationsMinimized') === 'true';

    if (staticNotificationsContainer) {
        if (notificationsHidden) {
            staticNotificationsContainer.style.display = 'none';
        } else {
            staticNotificationsContainer.style.display = 'block';
            staticNotificationsContainer.classList.add('show');

            // Apply minimized state if it was minimized before
            if (notificationsMinimized) {
                staticNotificationsContainer.classList.add('minimized');
                if (minimizeNotificationsBtn) {
                    minimizeNotificationsBtn.innerHTML = '<i class="bi bi-arrows-angle-expand"></i>';
                    minimizeNotificationsBtn.title = 'Expand';
                }
            }
        }
    }

    // Handle close button click
    if (closeNotificationsBtn) {
        closeNotificationsBtn.addEventListener('click', function() {
            if (staticNotificationsContainer) {
                staticNotificationsContainer.classList.remove('show');
                // Use setTimeout to allow the animation to complete before hiding
                setTimeout(() => {
                    staticNotificationsContainer.style.display = 'none';
                }, 300);
                localStorage.setItem('notificationsHidden', 'true');
            }
        });
    }

    // Handle refresh button click
    if (refreshNotificationsBtn) {
        refreshNotificationsBtn.addEventListener('click', function() {
            // Add loading animation
            this.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
            this.disabled = true;

            // Reload the page after a short delay to show the loading animation
            setTimeout(() => {
                window.location.reload();
            }, 500);
        });
    }

    // Handle minimize button click
    if (minimizeNotificationsBtn) {
        minimizeNotificationsBtn.addEventListener('click', function() {
            if (staticNotificationsContainer) {
                if (staticNotificationsContainer.classList.contains('minimized')) {
                    // Expand
                    staticNotificationsContainer.classList.remove('minimized');
                    this.innerHTML = '<i class="bi bi-dash"></i>';
                    this.title = 'Minimize';
                    localStorage.setItem('notificationsMinimized', 'false');
                } else {
                    // Minimize
                    staticNotificationsContainer.classList.add('minimized');
                    this.innerHTML = '<i class="bi bi-arrows-angle-expand"></i>';
                    this.title = 'Expand';
                    localStorage.setItem('notificationsMinimized', 'true');
                }
            }
        });
    }

    // Handle bell icon click to toggle notifications
    if (bellIcon) {
        bellIcon.parentElement.addEventListener('click', function(e) {
            if (staticNotificationsContainer) {
                e.preventDefault(); // Prevent navigation to notifications page

                if (staticNotificationsContainer.style.display === 'none') {
                    staticNotificationsContainer.style.display = 'block';
                    staticNotificationsContainer.classList.add('show');
                    localStorage.setItem('notificationsHidden', 'false');
                } else {
                    staticNotificationsContainer.classList.remove('show');
                    // Use setTimeout to allow the animation to complete before hiding
                    setTimeout(() => {
                        staticNotificationsContainer.style.display = 'none';
                    }, 300);
                    localStorage.setItem('notificationsHidden', 'true');
                }
            }
        });
    }

    if (bellIcon && unreadBadge) {
        // Add pulse animation to badge
        unreadBadge.classList.add('badge-pulse');

        // Animate bell every 15 seconds
        setInterval(function() {
            bellIcon.classList.add('bell-animate');

            // Remove animation class after animation completes
            setTimeout(function() {
                bellIcon.classList.remove('bell-animate');
            }, 1000);
        }, 15000);
    }

    // Staggered fade-in for notification items
    const notificationItems = document.querySelectorAll('.notification-item');

    notificationItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(10px)';
        item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

        setTimeout(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, 100 * index);
    });

    // Filter notifications by type
    const filterButtons = document.querySelectorAll('.notifications-filter-bar .btn');

    if (filterButtons.length > 0) {
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                filterButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to clicked button
                this.classList.add('active');

                const filterType = this.getAttribute('data-filter');

                // Show/hide notifications based on filter
                let visibleCount = 0;
                notificationItems.forEach(item => {
                    if (filterType === 'all') {
                        item.style.display = 'block';
                        visibleCount++;
                    } else {
                        const itemType = item.getAttribute('data-notification-type');
                        if (itemType === filterType) {
                            item.style.display = 'block';
                            visibleCount++;
                        } else {
                            item.style.display = 'none';
                        }
                    }
                });

                // Update the count display
                const countDisplay = document.querySelector('.notification-count-display');
                if (countDisplay) {
                    if (filterType === 'all') {
                        countDisplay.textContent = `${notificationItems.length} total`;
                    } else {
                        countDisplay.textContent = `${visibleCount} ${filterType}`;
                    }
                }
            });
        });
    }

    // Mark as read functionality
    const markAllReadBtn = document.querySelector('.mark-all-read');

    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Add loading state
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Marking...';
            this.disabled = true;

            // Redirect after a short delay to show the loading state
            setTimeout(() => {
                window.location.href = this.getAttribute('href');
            }, 500);
        });
    }

    // Mark individual notification as read
    const markReadBtns = document.querySelectorAll('.mark-read-btn');

    if (markReadBtns.length > 0) {
        markReadBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();

                const notificationId = this.getAttribute('data-id');
                const notificationItem = this.closest('.notification-item');

                // Add loading state
                this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
                this.disabled = true;

                // Create a form to submit the request
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '../notifications/mark_read.php';
                form.style.display = 'none';

                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'notification_id';
                idInput.value = notificationId;

                form.appendChild(idInput);
                document.body.appendChild(form);

                // Use fetch API to submit the form asynchronously
                fetch('../notifications/mark_read.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'notification_id=' + notificationId
                })
                .then(response => {
                    if (response.ok) {
                        // Update UI to show notification as read
                        notificationItem.classList.remove('unread');
                        this.remove();

                        // Update unread count badge
                        const badge = document.querySelector('.notification-badge');
                        const counter = document.querySelector('.notification-counter');

                        if (badge && badge.textContent) {
                            let count = parseInt(badge.textContent);
                            if (count > 0) {
                                count--;
                                if (count === 0) {
                                    badge.style.display = 'none';
                                    if (counter) counter.style.display = 'none';

                                    // Remove mark all as read button if no unread notifications
                                    const markAllReadBtn = document.querySelector('.mark-all-read');
                                    if (markAllReadBtn) markAllReadBtn.style.display = 'none';
                                } else {
                                    badge.textContent = count > 9 ? '9+' : count;
                                    if (counter) counter.textContent = count;
                                }
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('Error marking notification as read:', error);
                    this.innerHTML = 'Mark as read';
                    this.disabled = false;
                });
            });
        });
    }

    // Hover effect for notification items
    notificationItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(3px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
});
