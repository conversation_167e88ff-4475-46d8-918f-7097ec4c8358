<?php
require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<h2>Current Database Statistics</h2>";

// Get current member count
$query = "SELECT COUNT(*) as total FROM members";
$stmt = $db->prepare($query);
$stmt->execute();
$current_members = $stmt->fetch()['total'];

// Get other stats
$query = "SELECT COUNT(*) as total FROM books";
$stmt = $db->prepare($query);
$stmt->execute();
$total_books = $stmt->fetch()['total'];

$query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'borrowed'";
$stmt = $db->prepare($query);
$stmt->execute();
$active_loans = $stmt->fetch()['total'];

$query = "SELECT COUNT(*) as total FROM book_loans WHERE due_date < CURDATE() AND status = 'borrowed'";
$stmt = $db->prepare($query);
$stmt->execute();
$overdue_books = $stmt->fetch()['total'];

// Also check for explicitly marked overdue books
$query2 = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'overdue'";
$stmt2 = $db->prepare($query2);
$stmt2->execute();
$explicitly_overdue = $stmt2->fetch()['total'];

// Check sample due dates
$query3 = "SELECT due_date, status FROM book_loans WHERE status IN ('borrowed', 'overdue') ORDER BY due_date LIMIT 5";
$stmt3 = $db->prepare($query3);
$stmt3->execute();
$sample_dates = $stmt3->fetchAll();

$query = "SELECT SUM(available_quantity) as available FROM books";
$stmt = $db->prepare($query);
$stmt->execute();
$available_books = $stmt->fetch()['available'];

echo "<div style='background: #e9ecef; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>📊 Current Dashboard Statistics</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>";
echo "<div><strong>👥 Total Members:</strong> {$current_members}</div>";
echo "<div><strong>📚 Total Books:</strong> {$total_books}</div>";
echo "<div><strong>📖 Available Books:</strong> {$available_books}</div>";
echo "<div><strong>✅ Active Loans:</strong> {$active_loans}</div>";
echo "<div><strong>⚠️ Overdue Books (past due):</strong> {$overdue_books}</div>";
echo "<div><strong>🔴 Explicitly Overdue:</strong> {$explicitly_overdue}</div>";
echo "</div>";

echo "<h4>📅 Sample Due Dates:</h4>";
echo "<table style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'><th style='border: 1px solid #ddd; padding: 8px;'>Due Date</th><th style='border: 1px solid #ddd; padding: 8px;'>Status</th><th style='border: 1px solid #ddd; padding: 8px;'>Days from Today</th></tr>";
foreach ($sample_dates as $loan) {
    $due_date = new DateTime($loan['due_date']);
    $today = new DateTime();
    $diff = $today->diff($due_date);
    $days_diff = $diff->invert ? -$diff->days : $diff->days;
    $color = $days_diff < 0 ? 'color: red;' : 'color: green;';
    echo "<tr><td style='border: 1px solid #ddd; padding: 8px;'>{$loan['due_date']}</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$loan['status']}</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px; {$color}'>{$days_diff} days</td></tr>";
}
echo "</table>";
echo "</div>";

if ($current_members > 1500) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>⚠️ High Member Count Detected</h3>";
    echo "<p>Current member count ({$current_members}) is quite high. You may want to reset the database or reduce the member count for a more balanced dashboard.</p>";
    echo "</div>";
} elseif ($current_members < 500) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px;'>";
    echo "<h3>💡 Suggestion</h3>";
    echo "<p>Current member count ({$current_members}) is relatively low. You could generate more members to reach around 1000+ for a more realistic dashboard.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Good Balance</h3>";
    echo "<p>Current member count ({$current_members}) looks good for dashboard display!</p>";
    echo "</div>";
}
?>
