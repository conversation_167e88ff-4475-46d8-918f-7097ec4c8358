<?php
/**
 * Update Member Passwords Script
 * This script adds passwords to existing members who don't have them
 */

require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<h1>🔐 Update Member Passwords</h1>";

// Check if password column exists
$check_column_query = "SHOW COLUMNS FROM members LIKE 'password'";
$check_stmt = $db->prepare($check_column_query);
$check_stmt->execute();
$has_password_column = $check_stmt->rowCount() > 0;

if (!$has_password_column) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ Password Column Missing</h3>";
    echo "<p>The members table doesn't have a password column. Please run the database update script first.</p>";
    echo "<p><a href='update_members_table.php'>Update Members Table</a></p>";
    echo "</div>";
    exit;
}

// Find members without passwords
$query = "SELECT id, first_name, last_name, email FROM members WHERE password IS NULL OR password = ''";
$stmt = $db->prepare($query);
$stmt->execute();
$members_without_passwords = $stmt->fetchAll();

echo "<h2>📊 Members Without Passwords</h2>";
echo "<p>Found " . count($members_without_passwords) . " member(s) without passwords.</p>";

if (empty($members_without_passwords)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ All Members Have Passwords!</h3>";
    echo "<p>All members in the database already have passwords set.</p>";
    echo "</div>";
} else {
    echo "<h3>🔧 Updating Member Passwords</h3>";
    
    $updated_count = 0;
    $failed_count = 0;
    $updated_members = [];
    
    foreach ($members_without_passwords as $member) {
        // Generate password based on first name
        $default_password = strtolower($member['first_name']) . '123';
        $hashed_password = password_hash($default_password, PASSWORD_DEFAULT);
        
        // Update member password
        $update_query = "UPDATE members SET password = :password WHERE id = :id";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':password', $hashed_password);
        $update_stmt->bindParam(':id', $member['id']);
        
        if ($update_stmt->execute()) {
            $updated_members[] = [
                'id' => $member['id'],
                'name' => $member['first_name'] . ' ' . $member['last_name'],
                'email' => $member['email'],
                'password' => $default_password
            ];
            echo "<p style='color: green;'>✅ Updated: {$member['first_name']} {$member['last_name']} - Password: <strong>{$default_password}</strong></p>";
            $updated_count++;
        } else {
            echo "<p style='color: red;'>❌ Failed to update: {$member['first_name']} {$member['last_name']}</p>";
            $failed_count++;
        }
    }
    
    echo "<h3>📈 Update Summary</h3>";
    echo "<p><strong>Members updated:</strong> $updated_count</p>";
    echo "<p><strong>Updates failed:</strong> $failed_count</p>";
    
    if (!empty($updated_members)) {
        echo "<h3>🔐 Updated Member Credentials</h3>";
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<p><strong>⚠️ Important:</strong> Save these login credentials!</p>";
        echo "</div>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>Member ID</th><th>Name</th><th>Email</th><th>New Password</th>";
        echo "</tr>";
        
        foreach ($updated_members as $member) {
            echo "<tr>";
            echo "<td>{$member['id']}</td>";
            echo "<td>{$member['name']}</td>";
            echo "<td>{$member['email']}</td>";
            echo "<td style='font-family: monospace; background: #f8f9fa;'><strong>{$member['password']}</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}

// Show all members with their password status
echo "<h2>👥 All Members Password Status</h2>";

$all_members_query = "SELECT id, first_name, last_name, email, 
                      CASE 
                          WHEN password IS NULL OR password = '' THEN 'No Password' 
                          ELSE 'Has Password' 
                      END as password_status
                      FROM members 
                      ORDER BY id";
$all_stmt = $db->prepare($all_members_query);
$all_stmt->execute();
$all_members = $all_stmt->fetchAll();

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f0f0f0;'>";
echo "<th>ID</th><th>Name</th><th>Email</th><th>Password Status</th>";
echo "</tr>";

foreach ($all_members as $member) {
    $status_color = $member['password_status'] === 'Has Password' ? 'green' : 'red';
    echo "<tr>";
    echo "<td>{$member['id']}</td>";
    echo "<td>{$member['first_name']} {$member['last_name']}</td>";
    echo "<td>{$member['email']}</td>";
    echo "<td style='color: $status_color; font-weight: bold;'>{$member['password_status']}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<div style='margin: 30px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;'>";
echo "<h3>🔗 Quick Links</h3>";
echo "<ul>";
echo "<li><a href='generate_members_and_loans.php'>Generate More Members & Loans</a></li>";
echo "<li><a href='verify_members_and_loans.php'>Verify Members & Loans</a></li>";
echo "<li><a href='members/index.php'>Manage Members</a></li>";
echo "<li><a href='member_login.php'>Member Login Page</a></li>";
echo "</ul>";
echo "</div>";

?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h1, h2, h3 { color: #333; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    a { color: #007bff; text-decoration: none; }
    a:hover { text-decoration: underline; }
    code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
</style>
