<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Images</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .image-container {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        h2 {
            margin-top: 0;
        }
        img {
            max-width: 100%;
            height: auto;
            display: block;
            margin-bottom: 10px;
        }
        .hero-section {
            position: relative;
            height: 400px;
            overflow: hidden;
            margin-bottom: 30px;
            border-radius: 5px;
        }
        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('uploads/images/library.jpg') center/cover no-repeat;
            opacity: 0.85;
            z-index: 1;
        }
        .hero-circle {
            position: absolute;
            width: 250px;
            height: 250px;
            border-radius: 50%;
            background: rgba(76, 201, 240, 0.1);
            border: 2px solid rgba(76, 201, 240, 0.3);
            z-index: 2;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .floating-book {
            position: absolute;
            z-index: 3;
            filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.3));
        }
        .book-1 {
            width: 100px;
            top: 30%;
            left: 40%;
        }
        .book-2 {
            width: 100px;
            top: 50%;
            right: 30%;
        }
        .book-3 {
            width: 100px;
            bottom: 30%;
            left: 45%;
        }
    </style>
</head>
<body>
    <h1>Image Test Page</h1>
    
    <div class="image-container">
        <h2>Library Background Image</h2>
        <img src="uploads/images/library.jpg" alt="Library Background">
        <p>Path: uploads/images/library.jpg</p>
    </div>
    
    <div class="image-container">
        <h2>Book Images</h2>
        <img src="assets/img/book1.png" alt="Book 1">
        <p>Path: assets/img/book1.png</p>
        
        <img src="assets/img/book2.png" alt="Book 2">
        <p>Path: assets/img/book2.png</p>
        
        <img src="assets/img/book3.png" alt="Book 3">
        <p>Path: assets/img/book3.png</p>
    </div>
    
    <div class="image-container">
        <h2>Hero Section with Circle and Books</h2>
        <div class="hero-section">
            <div class="hero-overlay"></div>
            <div class="hero-circle"></div>
            <div class="floating-book book-1">
                <img src="assets/img/book1.png" alt="Book 1">
            </div>
            <div class="floating-book book-2">
                <img src="assets/img/book2.png" alt="Book 2">
            </div>
            <div class="floating-book book-3">
                <img src="assets/img/book3.png" alt="Book 3">
            </div>
        </div>
    </div>
</body>
</html>
