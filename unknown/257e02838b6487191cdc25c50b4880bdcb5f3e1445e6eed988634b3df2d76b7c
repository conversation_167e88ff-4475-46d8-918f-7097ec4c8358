<?php
require_once 'config/database.php';

// Create database connection
$database = new Database();
$db = $database->getConnection();

// Check if the users table has a full_name column
$query = "DESCRIBE users";
$stmt = $db->prepare($query);
$stmt->execute();
$columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

echo "Users table columns: " . implode(', ', $columns) . "\n";

// Check if there are any librarian users
$query = "SELECT * FROM users WHERE role = 'librarian'";
$stmt = $db->prepare($query);
$stmt->execute();
$librarians = $stmt->fetchAll();

echo "Number of librarians: " . count($librarians) . "\n";
if (count($librarians) > 0) {
    foreach ($librarians as $librarian) {
        echo "Librarian ID: " . $librarian['id'] . ", Username: " . $librarian['username'];
        if (isset($librarian['full_name'])) {
            echo ", Full Name: " . $librarian['full_name'];
        } else {
            echo ", Full Name: Not set";
        }
        echo "\n";
    }
}

// Check if the full_name column exists
$hasFullNameColumn = in_array('full_name', $columns);
if (!$hasFullNameColumn) {
    echo "Adding full_name column to users table...\n";
    try {
        $query = "ALTER TABLE users ADD COLUMN full_name VARCHAR(100) AFTER role";
        $stmt = $db->prepare($query);
        $stmt->execute();
        echo "full_name column added successfully.\n";
    } catch (PDOException $e) {
        echo "Error adding full_name column: " . $e->getMessage() . "\n";
    }
}

// Update librarians with a default full_name if not set
if (count($librarians) > 0) {
    foreach ($librarians as $librarian) {
        if (!isset($librarian['full_name']) || empty($librarian['full_name'])) {
            echo "Setting default full_name for librarian ID: " . $librarian['id'] . "\n";
            try {
                $query = "UPDATE users SET full_name = :full_name WHERE id = :id";
                $stmt = $db->prepare($query);
                $stmt->bindValue(':full_name', 'Librarian ' . $librarian['username']);
                $stmt->bindValue(':id', $librarian['id']);
                $stmt->execute();
                echo "Default full_name set successfully.\n";
            } catch (PDOException $e) {
                echo "Error setting default full_name: " . $e->getMessage() . "\n";
            }
        }
    }
}

echo "Database check completed.\n";
?>
