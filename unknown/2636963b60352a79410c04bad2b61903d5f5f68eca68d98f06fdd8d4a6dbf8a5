<?php
// Simple working dashboard - minimal version
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Auto-login as admin for testing
try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!isLoggedIn() || !isAdmin()) {
        $query = "SELECT * FROM users WHERE role = 'admin' LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $admin = $stmt->fetch();
        
        if ($admin) {
            $_SESSION['user_id'] = $admin['id'];
            $_SESSION['username'] = $admin['username'];
            $_SESSION['role'] = $admin['role'];
            $_SESSION['logged_in'] = true;
        }
    }
} catch (Exception $e) {
    die("Setup error: " . $e->getMessage());
}

// Get basic statistics with error handling
$stats = [
    'total_books' => 0,
    'available_books' => 0,
    'total_members' => 0,
    'active_loans' => 0,
    'overdue_books' => 0
];

try {
    // Total books
    $query = "SELECT COUNT(*) as total FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['total_books'] = $stmt->fetch()['total'] ?? 0;

    // Available books
    $query = "SELECT SUM(COALESCE(available_quantity, 0)) as available FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['available_books'] = $stmt->fetch()['available'] ?? 0;

    // Total members
    $query = "SELECT COUNT(*) as total FROM members";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['total_members'] = $stmt->fetch()['total'] ?? 0;

    // Active loans
    $query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'borrowed'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['active_loans'] = $stmt->fetch()['total'] ?? 0;

    // Overdue books
    $query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'overdue'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['overdue_books'] = $stmt->fetch()['total'] ?? 0;

} catch (Exception $e) {
    $error_message = "Error loading statistics: " . $e->getMessage();
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Simple</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/dashboard-fixes.css">
    <style>
        .stats-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 10px;
            overflow: hidden;
        }
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .view-details-link {
            z-index: 1050 !important;
            position: relative !important;
            pointer-events: auto !important;
            cursor: pointer !important;
            text-decoration: none !important;
        }
        .view-details-link:hover {
            opacity: 0.8 !important;
            text-decoration: underline !important;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-book me-2"></i>Library Management System - Admin
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../logout.php">
                    <i class="bi bi-box-arrow-right me-1"></i>Sign Out
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="bi bi-speedometer2 me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../books/index.php">
                                <i class="bi bi-book me-2"></i>Books
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../members/index.php">
                                <i class="bi bi-people me-2"></i>Members
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../loans/index.php">
                                <i class="bi bi-journal-arrow-up me-2"></i>Loans
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/index.php">
                                <i class="bi bi-file-earmark-bar-graph me-2"></i>Reports
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Dashboard</h1>
                </div>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i><?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-white bg-primary stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Total Books</h6>
                                        <h3 class="mb-0"><?php echo h($stats['total_books']); ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-book fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a href="<?php echo url('books/index.php'); ?>" class="text-white-50 text-decoration-none view-details-link">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                <i class="bi bi-chevron-right text-white-50"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card text-white bg-success stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Total Members</h6>
                                        <h3 class="mb-0"><?php echo h($stats['total_members']); ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-people fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a href="<?php echo url('members/index.php'); ?>" class="text-white-50 text-decoration-none view-details-link">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                <i class="bi bi-chevron-right text-white-50"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card text-white bg-warning stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Active Loans</h6>
                                        <h3 class="mb-0"><?php echo h($stats['active_loans']); ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-journal-arrow-up fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a href="<?php echo url('loans/index.php'); ?>" class="text-white-50 text-decoration-none view-details-link">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                <i class="bi bi-chevron-right text-white-50"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card text-white bg-danger stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Overdue Books</h6>
                                        <h3 class="mb-0"><?php echo h($stats['overdue_books']); ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-exclamation-triangle fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a href="<?php echo url('loans/overdue.php'); ?>" class="text-white-50 text-decoration-none view-details-link">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                <i class="bi bi-chevron-right text-white-50"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <a href="../books/add.php" class="btn btn-primary w-100">
                                            <i class="bi bi-plus-circle me-2"></i>Add New Book
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="../members/add.php" class="btn btn-success w-100">
                                            <i class="bi bi-person-plus me-2"></i>Register Member
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="../loans/issue.php" class="btn btn-info w-100">
                                            <i class="bi bi-journal-arrow-up me-2"></i>Issue Book
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="../loans/return.php" class="btn btn-warning w-100">
                                            <i class="bi bi-journal-arrow-down me-2"></i>Return Book
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="dashboard.php" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left me-2"></i>Back to Full Dashboard
                    </a>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Ensure View Details links work
        document.addEventListener('DOMContentLoaded', function() {
            const viewDetailsLinks = document.querySelectorAll('.view-details-link');
            
            viewDetailsLinks.forEach(function(link) {
                link.style.pointerEvents = 'auto';
                link.style.cursor = 'pointer';
                link.style.zIndex = '1050';
                link.style.position = 'relative';
                
                link.addEventListener('click', function(e) {
                    console.log('View Details link clicked:', this.href);
                });
            });
            
            console.log('Simple dashboard loaded successfully');
        });
    </script>
</body>
</html>
