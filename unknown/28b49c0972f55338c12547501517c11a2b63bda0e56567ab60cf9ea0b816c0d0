<?php
/**
 * Common utility functions for the Library Management System
 */

/**
 * Sanitize user input
 * @param string $data
 * @return string
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Redirect to a specific page
 * @param string $location
 */
function redirect($location) {
    // If the location is already a full URL, use it as is
    if (strpos($location, 'http://') === 0 || strpos($location, 'https://') === 0) {
        header("Location: $location");
    } else {
        // Otherwise, assume it's a relative path
        header("Location: $location");
    }
    exit;
}

/**
 * Check if user is logged in
 * @return bool
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Check if user is admin
 * @return bool
 */
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

/**
 * Check if user is librarian
 * @return bool
 */
function isLibrarian() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'librarian';
}

/**
 * Check if user is admin or librarian (staff with financial access)
 * @return bool
 */
function isStaffWithFinancialAccess() {
    return isset($_SESSION['role']) && ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'librarian');
}

/**
 * Check if member is logged in
 * @return bool
 */
function isMemberLoggedIn() {
    return isset($_SESSION['member_id']);
}

/**
 * Get member details by ID
 * @param PDO $db
 * @param int $member_id
 * @return array|false
 */
function getMemberById($db, $member_id) {
    $query = "SELECT * FROM members WHERE id = :member_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':member_id', $member_id);
    $stmt->execute();
    return $stmt->fetch();
}

/**
 * Get member's current loans
 * @param PDO $db
 * @param int $member_id
 * @return array
 */
function getMemberLoans($db, $member_id) {
    $query = "SELECT bl.*, b.title, b.author, b.isbn, b.cover_image
              FROM book_loans bl
              JOIN books b ON bl.book_id = b.id
              WHERE bl.member_id = :member_id AND bl.status != 'returned'
              ORDER BY bl.due_date ASC";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':member_id', $member_id);
    $stmt->execute();
    return $stmt->fetchAll();
}

/**
 * Check if member has overdue books
 * @param PDO $db
 * @param int $member_id
 * @return bool
 */
function memberHasOverdueBooks($db, $member_id) {
    $query = "SELECT COUNT(*) as count FROM book_loans
              WHERE member_id = :member_id AND status = 'overdue'";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':member_id', $member_id);
    $stmt->execute();
    return $stmt->fetch()['count'] > 0;
}

/**
 * Calculate fine for overdue books
 * @param string $due_date
 * @param string $return_date (optional)
 * @return float
 */
function calculateFine($due_date, $return_date = null) {
    $fine_per_day = 1.00; // $1 per day

    if ($return_date === null) {
        $return_date = date('Y-m-d');
    }

    $due = new DateTime($due_date);
    $return = new DateTime($return_date);

    $days_overdue = $return->diff($due)->days;

    // Only calculate fine if book is actually overdue
    if ($return > $due) {
        return $days_overdue * $fine_per_day;
    }

    return 0;
}

/**
 * Format date for display
 * @param string $date
 * @return string
 */
function formatDate($date) {
    return date('F j, Y', strtotime($date));
}

/**
 * Format date and time for display
 * @param string $datetime
 * @return string
 */
function formatDateTime($datetime) {
    return date('F j, Y g:i A', strtotime($datetime));
}

/**
 * Get time ago string (e.g., "2 hours ago")
 * @param string $datetime
 * @return string
 */
function timeAgo($datetime) {
    $time = strtotime($datetime);
    $now = time();
    $diff = $now - $time;

    if ($diff < 60) {
        return 'Just now';
    } elseif ($diff < 3600) {
        $mins = floor($diff / 60);
        return $mins . ' minute' . ($mins > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 604800) {
        $days = floor($diff / 86400);
        return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 2592000) {
        $weeks = floor($diff / 604800);
        return $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
    } else {
        return formatDate($datetime);
    }
}

/**
 * Format file size for display
 * @param int $bytes
 * @return string
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

/**
 * Generate a random string
 * @param int $length
 * @return string
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

/**
 * Display flash messages
 */
function displayMessage() {
    if (isset($_SESSION['message'])) {
        echo '<div class="alert alert-' . $_SESSION['message_type'] . ' alert-dismissible fade show" role="alert">
                ' . $_SESSION['message'] . '
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
              </div>';
        unset($_SESSION['message']);
        unset($_SESSION['message_type']);
    }
}

/**
 * Set flash message
 * @param string $message
 * @param string $type
 */
function setMessage($message, $type = 'success') {
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
}

/**
 * Get the current URL
 * @return string
 */
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $uri = $_SERVER['REQUEST_URI'];
    return $protocol . '://' . $host . $uri;
}

/**
 * Log user activity
 * @param PDO $db
 * @param string $action
 * @param string $description
 * @param string $entity_type (optional)
 * @param int $entity_id (optional)
 * @return bool
 */
function logActivity($db, $action, $description, $entity_type = null, $entity_id = null) {
    try {
        $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
        $ip_address = $_SERVER['REMOTE_ADDR'];
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;

        $query = "INSERT INTO activity_log (user_id, action, description, entity_type, entity_id, ip_address, user_agent)
                  VALUES (:user_id, :action, :description, :entity_type, :entity_id, :ip_address, :user_agent)";
        $stmt = $db->prepare($query);

        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':action', $action);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':entity_type', $entity_type);
        $stmt->bindParam(':entity_id', $entity_id);
        $stmt->bindParam(':ip_address', $ip_address);
        $stmt->bindParam(':user_agent', $user_agent);

        return $stmt->execute();
    } catch (Exception $e) {
        // Silently fail - don't let activity logging disrupt the application
        return false;
    }
}

/**
 * Add a notification
 * @param PDO $db
 * @param string $message
 * @param string $type (info, warning, success, danger)
 * @param int $user_id (optional - null for all users)
 * @param string $entity_type (optional)
 * @param int $entity_id (optional)
 * @return bool
 */
function addNotification($db, $message, $type = 'info', $user_id = null, $entity_type = null, $entity_id = null) {
    try {
        $query = "INSERT INTO notifications (user_id, message, type, entity_type, entity_id)
                  VALUES (:user_id, :message, :type, :entity_type, :entity_id)";
        $stmt = $db->prepare($query);

        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':message', $message);
        $stmt->bindParam(':type', $type);
        $stmt->bindParam(':entity_type', $entity_type);
        $stmt->bindParam(':entity_id', $entity_id);

        return $stmt->execute();
    } catch (Exception $e) {
        // Silently fail - don't let notification creation disrupt the application
        return false;
    }
}

/**
 * Get unread notifications count for a user
 * @param PDO $db
 * @param int $user_id
 * @return int
 */
function getUnreadNotificationsCount($db, $user_id) {
    try {
        $query = "SELECT COUNT(*) as count FROM notifications
                  WHERE (user_id = :user_id OR user_id IS NULL) AND is_read = 0";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();

        return $stmt->fetch()['count'];
    } catch (Exception $e) {
        return 0;
    }
}
