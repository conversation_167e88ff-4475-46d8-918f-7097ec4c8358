<?php
/**
 * Send Email Notifications Script
 * 
 * This script is designed to be run as a cron job to send automated email notifications
 * such as due date reminders and overdue book notices.
 * 
 * Recommended cron schedule: Daily at midnight
 * Example cron entry: 0 0 * * * php /path/to/lms/cron/send_notifications.php
 */

// Set script execution time limit to 5 minutes
set_time_limit(300);

// Define the base path
define('BASE_PATH', dirname(__DIR__));

// Include required files
require_once BASE_PATH . '/config/database.php';
require_once BASE_PATH . '/includes/email_service.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Create email service instance
$emailService = new EmailService($db);

// Get notification settings
$settings = getNotificationSettings($db);

// Log script start
logToFile("Notification script started at " . date('Y-m-d H:i:s'));

// Send due date reminders
if ($settings['send_due_date_reminders']) {
    $days_before = (int)$settings['due_date_reminder_days'];
    logToFile("Sending due date reminders for books due in {$days_before} days...");
    
    $results = $emailService->sendDueDateReminders($days_before);
    
    logToFile("Due date reminders sent: {$results['success']} successful, {$results['failure']} failed");
}

// Send overdue notifications
if ($settings['send_overdue_notifications']) {
    // Check if we should send overdue notifications today based on frequency
    $frequency = (int)$settings['overdue_notification_frequency'];
    
    if ($frequency > 0 && $frequency % 7 === 0) {
        // For weekly frequency, only send on Mondays
        if (date('N') != 1) {
            logToFile("Skipping overdue notifications - configured to send only on Mondays");
            exit;
        }
    } elseif ($frequency > 1) {
        // For other frequencies, check if today is a multiple of the frequency
        $days_since_epoch = floor(time() / 86400);
        if ($days_since_epoch % $frequency !== 0) {
            logToFile("Skipping overdue notifications - not scheduled for today based on frequency {$frequency}");
            exit;
        }
    }
    
    logToFile("Sending overdue book notifications...");
    
    $results = $emailService->sendOverdueNotifications();
    
    logToFile("Overdue notifications sent: {$results['success']} successful, {$results['failure']} failed");
}

// Log script end
logToFile("Notification script completed at " . date('Y-m-d H:i:s'));

/**
 * Get notification settings from database
 * 
 * @param PDO $db Database connection
 * @return array Settings
 */
function getNotificationSettings($db) {
    $settings = [
        'send_due_date_reminders' => true,
        'due_date_reminder_days' => 3,
        'send_overdue_notifications' => true,
        'overdue_notification_frequency' => 7
    ];
    
    try {
        $query = "SELECT setting_key, setting_value FROM settings WHERE setting_group = 'notifications'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        while ($row = $stmt->fetch()) {
            switch ($row['setting_key']) {
                case 'due_date_reminder_days':
                    $settings['due_date_reminder_days'] = (int)$row['setting_value'];
                    break;
                case 'send_overdue_notifications':
                    $settings['send_overdue_notifications'] = $row['setting_value'] === 'true';
                    break;
                case 'overdue_notification_frequency':
                    $settings['overdue_notification_frequency'] = (int)$row['setting_value'];
                    break;
            }
        }
    } catch (Exception $e) {
        logToFile("Error getting notification settings: " . $e->getMessage());
    }
    
    return $settings;
}

/**
 * Log message to file
 * 
 * @param string $message Message to log
 */
function logToFile($message) {
    $log_file = BASE_PATH . '/logs/notifications.log';
    $log_dir = dirname($log_file);
    
    // Create logs directory if it doesn't exist
    if (!file_exists($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    // Append message to log file
    file_put_contents(
        $log_file,
        date('Y-m-d H:i:s') . ' - ' . $message . PHP_EOL,
        FILE_APPEND
    );
}
