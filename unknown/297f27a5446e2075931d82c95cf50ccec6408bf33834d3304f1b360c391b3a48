<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>PHP Error Test</h1>";

// Test database connection
echo "<h2>Testing Database Connection...</h2>";
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    echo "<p style='color: green;'>✓ Database connection successful!</p>";
    
    // Test a simple query
    $query = "SELECT COUNT(*) as count FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch();
    echo "<p style='color: green;'>✓ Database query successful! Found " . $result['count'] . " books.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
}

// Test config file
echo "<h2>Testing Config File...</h2>";
try {
    require_once 'config/config.php';
    echo "<p style='color: green;'>✓ Config file loaded successfully!</p>";
    echo "<p>Base URL: " . BASE_URL . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Config error: " . $e->getMessage() . "</p>";
}

// Test functions file
echo "<h2>Testing Functions File...</h2>";
try {
    require_once 'includes/functions.php';
    echo "<p style='color: green;'>✓ Functions file loaded successfully!</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Functions error: " . $e->getMessage() . "</p>";
}

// Test session
echo "<h2>Testing Session...</h2>";
session_start();
echo "<p style='color: green;'>✓ Session started successfully!</p>";
echo "<p>Session ID: " . session_id() . "</p>";

// Test file paths
echo "<h2>Testing File Paths...</h2>";
echo "<p>Current directory: " . __DIR__ . "</p>";
echo "<p>Config directory exists: " . (is_dir(__DIR__ . '/config') ? 'Yes' : 'No') . "</p>";
echo "<p>Includes directory exists: " . (is_dir(__DIR__ . '/includes') ? 'Yes' : 'No') . "</p>";

echo "<h2>All tests completed!</h2>";
?>
