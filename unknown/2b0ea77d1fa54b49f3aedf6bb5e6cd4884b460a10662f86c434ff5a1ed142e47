<?php
/**
 * Add Librarian <PERSON>t
 * This script adds a librarian user to the database
 */

// Database connection
require_once 'config/database.php';
$database = new Database();
$db = $database->getConnection();

// Function to add a librarian
function addLibrarian($db, $username, $email, $password, $full_name) {
    // Check if username or email already exists
    $check_query = "SELECT id FROM users WHERE username = :username OR email = :email";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':username', $username);
    $check_stmt->bindParam(':email', $email);
    $check_stmt->execute();
    
    if ($check_stmt->rowCount() > 0) {
        return "User with this username or email already exists.";
    }
    
    // Hash the password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    // Current date for created_at field
    $created_at = date('Y-m-d H:i:s');
    
    // Insert the librarian
    $query = "INSERT INTO users (username, email, password, full_name, role, status, created_at) 
              VALUES (:username, :email, :password, :full_name, 'librarian', 'active', :created_at)";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':username', $username);
    $stmt->bindParam(':email', $email);
    $stmt->bindParam(':password', $hashed_password);
    $stmt->bindParam(':full_name', $full_name);
    $stmt->bindParam(':created_at', $created_at);
    
    if ($stmt->execute()) {
        return "Librarian added successfully.";
    } else {
        return "Error adding librarian: " . print_r($stmt->errorInfo(), true);
    }
}

// Librarian details
$username = 'librarian';
$email = '<EMAIL>';
$password = 'librarian123';
$full_name = 'Library Staff';

// Add the librarian
$result = addLibrarian($db, $username, $email, $password, $full_name);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Librarian - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Add Librarian</h1>
        
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Adding Librarian User</h5>
                
                <div class="mt-3">
                    <?php if (strpos($result, 'successfully') !== false): ?>
                        <div class="alert alert-success">
                            <strong>Success!</strong> <?php echo $result; ?>
                        </div>
                        
                        <div class="card mt-4">
                            <div class="card-header bg-primary text-white">
                                Librarian Login Details
                            </div>
                            <div class="card-body">
                                <p><strong>Username:</strong> <?php echo $username; ?></p>
                                <p><strong>Email:</strong> <?php echo $email; ?></p>
                                <p><strong>Password:</strong> <?php echo $password; ?></p>
                                <p><strong>Role:</strong> Librarian</p>
                                <p><strong>Status:</strong> Active</p>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <strong>Warning!</strong> <?php echo $result; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="mt-4">
                        <a href="login.php" class="btn btn-primary">Go to Login Page</a>
                        <a href="admin/dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
