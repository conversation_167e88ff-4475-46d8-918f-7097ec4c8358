<?php
// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
} else {
    session_regenerate_id(true);
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'config/google_oauth.php';
require_once 'includes/functions.php';
require_once 'includes/google_auth.php';

// Include Google auto-login script
require_once 'includes/google_auto_login.php';

// Check if user is already logged in
if (isLoggedIn()) {
    // Redirect based on role
    if (isAdmin()) {
        header('Location: admin/dashboard.php');
        exit;
    } elseif (isLibrarian()) {
        header('Location: librarian/dashboard.php');
        exit;
    } else {
        header('Location: index.php');
        exit;
    }
} elseif (isMemberLoggedIn()) {
    header('Location: member_dashboard.php');
    exit;
}

$error = '';

// Check if there's an error message in the session
if (isset($_SESSION['error'])) {
    $error = $_SESSION['error'];
    unset($_SESSION['error']);
}

// Get Google login URLs (regular and direct)
$google_login_url = getGoogleLoginUrl();
$google_direct_url = getGoogleLoginUrl('', true);


// Check if user has a remember cookie
if (!isset($_SESSION['user_id']) && !isset($_SESSION['member_id']) && isset($_COOKIE['remember_user'])) {
    $remember_user = $_COOKIE['remember_user'];

    // Connect to database
    $database = new Database();
    $db = $database->getConnection();

    // Check if the remember token exists in the database
    $query = "SELECT id, username, email, role, full_name FROM users WHERE remember_token = :token";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':token', $remember_user);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $row = $stmt->fetch();
        // Auto login the user
        $_SESSION['user_id'] = $row['id'];
        $_SESSION['username'] = $row['username'];
        $_SESSION['email'] = $row['email'];
        $_SESSION['role'] = $row['role'];

        // Redirect based on role
        if ($row['role'] === 'admin') {
            header('Location: admin/dashboard.php');
            exit;
        } elseif ($row['role'] === 'librarian') {
            // Store full name in session for librarian
            $_SESSION['full_name'] = !empty($row['full_name']) ? $row['full_name'] : $_SESSION['username'];
            header('Location: librarian/dashboard.php');
            exit;
        } else {
            header('Location: index.php');
            exit;
        }
    }
}

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email_or_username = trim($_POST['email_or_username']);
    $password = $_POST['password'];
    $remember = isset($_POST['remember']) ? true : false;

    // Validate input
    if (empty($email_or_username) || empty($password)) {
        $error = 'Please enter both email/username and password';
    } else {
        // Connect to database
        $database = new Database();
        $db = $database->getConnection();

        // First, try to authenticate as admin/staff
        $is_admin = false;

        // Check if input is email or username
        if (filter_var($email_or_username, FILTER_VALIDATE_EMAIL)) {
            // Input is an email
            $query = "SELECT id, username, password, email, role, full_name FROM users WHERE email = :email";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':email', $email_or_username);
        } else {
            // Input is a username
            $query = "SELECT id, username, password, email, role, full_name FROM users WHERE username = :username";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':username', $email_or_username);
        }

        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch();
            $id = $row['id'];
            $hashed_password = $row['password'];
            $email = $row['email'];
            $username = $row['username'];
            $role = $row['role'];

            // Verify password
            if (password_verify($password, $hashed_password)) {
                // Password is correct, create session for admin/staff
                $_SESSION['user_id'] = $id;
                $_SESSION['username'] = $username;
                $_SESSION['email'] = $email;
                $_SESSION['role'] = $role;

                // Handle "Remember Me" functionality
                if ($remember) {
                    // Generate a unique token
                    $remember_token = bin2hex(random_bytes(32));

                    // Store the token in the database
                    $update_query = "UPDATE users SET remember_token = :token WHERE id = :id";
                    $update_stmt = $db->prepare($update_query);
                    $update_stmt->bindParam(':token', $remember_token);
                    $update_stmt->bindParam(':id', $id);
                    $update_stmt->execute();

                    // Set a cookie that expires in 30 days
                    setcookie('remember_user', $remember_token, time() + 86400 * 30, '/');
                }

                // Redirect based on role
                if ($role === 'admin') {
                    header('Location: admin/dashboard.php');
                    exit;
                } elseif ($role === 'librarian') {
                    // Store full name in session for librarian
                    $_SESSION['full_name'] = !empty($row['full_name']) ? $row['full_name'] : $_SESSION['username'];

                    // Debug information
                    error_log("Librarian login: User ID: " . $_SESSION['user_id'] . ", Username: " . $_SESSION['username'] . ", Full Name: " . $_SESSION['full_name']);

                    header('Location: librarian/dashboard.php');
                    exit;
                } else {
                    header('Location: index.php');
                    exit;
                }
            }
        }

        // If not admin, try to authenticate as member
        if (!$is_admin) {
            $query = "SELECT * FROM members WHERE email = :email AND membership_status = 'active'";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':email', $email_or_username);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $member = $stmt->fetch();

                // Verify member password
                if (password_verify($password, $member['password'])) {
                    // Password is correct, create session for member
                    $_SESSION['member_id'] = $member['id'];
                    $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
                    $_SESSION['member_email'] = $member['email'];

                    // Handle "Remember Me" functionality for members
                    if ($remember) {
                        // Generate a unique token
                        $remember_token = bin2hex(random_bytes(32));

                        // Store the token in the database
                        $update_query = "UPDATE members SET remember_token = :token WHERE id = :id";
                        $update_stmt = $db->prepare($update_query);
                        $update_stmt->bindParam(':token', $remember_token);
                        $update_stmt->bindParam(':id', $member['id']);
                        $update_stmt->execute();

                        // Set a cookie that expires in 30 days
                        setcookie('remember_user', $remember_token, time() + 86400 * 30, '/');
                    }

                    // Redirect to member dashboard
                    header('Location: member_dashboard.php');
                    exit;
                } else {
                    $error = 'Invalid email or password';
                }
            } else {
                $error = 'Invalid email or password';
            }
        }
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    <style>
        body {
            background: #f5f7fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            font-family: 'Roboto', sans-serif;
        }

        .login-container {
            max-width: 420px;
            width: 100%;
            margin: 0 auto;
        }

        .card {
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }

        .card-header {
            background: #1a73e8;
            color: white;
            font-weight: 500;
            padding: 18px 28px;
            text-align: left;
        }

        .card-header h4 {
            margin: 0;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-body {
            padding: 28px;
        }

        .form-label {
            font-weight: 400;
            color: #5f6368;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-control {
            padding: 12px 16px;
            border-radius: 4px;
            border: 1px solid #dadce0;
            font-size: 16px;
            transition: border-color 0.2s ease;
            background: #fff;
        }

        .form-control:focus {
            border-color: #1a73e8;
            box-shadow: none;
            outline: none;
        }

        .btn-primary {
            background: #1a73e8;
            border: none;
            padding: 12px 24px;
            font-weight: 500;
            border-radius: 4px;
            width: 100%;
            font-size: 14px;
            transition: background-color 0.2s ease;
        }

        .btn-primary:hover {
            background: #1557b0;
        }
        .password-toggle {
            cursor: pointer;
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
            color: #5f6368;
            padding: 4px;
        }

        .password-toggle:hover {
            color: #1a73e8;
        }

        .password-field {
            position: relative;
        }

        .form-check {
            margin: 12px 0;
        }

        .form-check-input {
            margin-top: 0.25em;
        }

        .form-check-input:checked {
            background-color: #1a73e8;
            border-color: #1a73e8;
        }

        .form-check-label {
            font-size: 14px;
            color: #3c4043;
            margin-left: 4px;
        }

        .alert-danger {
            background: #ea4335;
            border: none;
            border-radius: 4px;
            color: white;
            padding: 12px 16px;
            margin-bottom: 16px;
            font-size: 14px;
        }
        /* Google Button */
        .btn-google {
            background: white;
            color: #3c4043;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-weight: 500;
            font-size: 14px;
            padding: 10px 24px;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            transition: box-shadow 0.2s ease;
        }

        .btn-google:hover {
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
            color: #3c4043;
        }

        .btn-google img {
            margin-right: 8px;
        }

        /* Separator */
        .separator {
            text-align: center;
            color: #5f6368;
            font-size: 14px;
            margin: 18px 0 12px 0;
        }

        .google-text {
            color: #5f6368;
            font-size: 14px;
            text-align: center;
            margin-bottom: 12px;
        }

        /* Register link */
        .register-link {
            text-align: center;
            margin-top: 16px;
            font-size: 14px;
            color: #5f6368;
        }

        .register-link a {
            color: #1a73e8;
            text-decoration: none;
        }

        .register-link a:hover {
            text-decoration: underline;
        }

        /* Loading state */
        .btn-loading {
            position: relative;
            color: transparent !important;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            top: 50%;
            left: 50%;
            margin-left: -8px;
            margin-top: -8px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 480px) {
            body { padding: 16px; }
            .login-container { max-width: 100%; }
            .card-body { padding: 20px; }
            .card-header { padding: 16px 20px; }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="card">
            <div class="card-header">
                <h4><i class="bi bi-box-arrow-in-right me-2"></i>Login</h4>
            </div>
            <div class="card-body">
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <?php echo h($error); ?>
                    </div>
                <?php endif; ?>

                <form method="post" action="<?php echo h($_SERVER['PHP_SELF']); ?>">
                    <div class="mb-3">
                        <label for="email_or_username" class="form-label">Username or Email</label>
                        <input type="text" class="form-control" id="email_or_username" name="email_or_username"
                            value="<?php echo isset($_POST['email_or_username']) ? h($_POST['email_or_username']) : ''; ?>"
                            required autofocus>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="password-field">
                            <input type="password" class="form-control" id="password" name="password" required>
                            <span class="password-toggle" id="togglePassword">
                                <i class="bi bi-eye"></i>
                            </span>
                        </div>
                    </div>

                    <div class="form-check mb-3">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember"
                            <?php echo (isset($_COOKIE['remember_user']) && $_COOKIE['remember_user'] != '') ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="remember">Remember me</label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Login</button>
                    </div>
                </form>



                <div class="separator">Or login with</div>
                <div class="google-text">Sign in with Google for a faster, password-free experience</div>

                <div class="d-grid">
                    <?php if (isGoogleOAuthConfigured()): ?>
                        <a href="<?php echo h($google_login_url); ?>" class="btn btn-google" id="googleLoginBtn">
                            <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="18" height="18">
                            <span>Sign in with Google</span>
                            <span class="badge bg-success ms-2" style="font-size: 10px;">OAUTH</span>
                        </a>
                    <?php else: ?>
                        <a href="setup_google.php" class="btn btn-google" id="googleSetupBtn">
                            <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="18" height="18">
                            <span>Setup Google Sign-In</span>
                            <span class="badge bg-warning ms-2" style="font-size: 10px;">SETUP</span>
                        </a>
                    <?php endif; ?>
                </div>

                <div class="register-link">
                    <p>Don't have an account? <a href="register.php">Register</a></p>
                    <p><small>Debug: <a href="<?php echo url('register.php'); ?>">Register (url function)</a> | <a href="/Library/lms/register.php">Register (full path)</a></small></p>
                </div>
            </div>
        </div>
    </div>



    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo url('assets/js/google-direct-login.js'); ?>"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Password toggle
            const togglePassword = document.getElementById('togglePassword');
            const passwordField = document.getElementById('password');

            if (togglePassword && passwordField) {
                togglePassword.addEventListener('click', function() {
                    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordField.setAttribute('type', type);

                    const eyeIcon = this.querySelector('i');
                    eyeIcon.classList.toggle('bi-eye');
                    eyeIcon.classList.toggle('bi-eye-slash');
                });
            }

            // Form loading state
            const loginForm = document.querySelector('form');
            const loginButton = document.querySelector('.btn-primary');

            if (loginForm && loginButton) {
                loginForm.addEventListener('submit', function() {
                    loginButton.classList.add('btn-loading');
                    loginButton.disabled = true;
                });
            }

            // Google button loading
            const googleButton = document.querySelector('.btn-google');
            if (googleButton) {
                googleButton.addEventListener('click', function() {
                    this.classList.add('btn-loading');
                });
            }
        });
    </script>
</body>
</html>
