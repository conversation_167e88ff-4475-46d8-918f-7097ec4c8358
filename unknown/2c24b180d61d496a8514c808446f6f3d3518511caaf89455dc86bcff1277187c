<?php
// Simple test page to verify notification bell functionality
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Bell Test - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/dashboard-fixes.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .test-header {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
        }
        .test-section {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .demo-navbar {
            background: #343a40;
            padding: 1rem;
            border-radius: 8px;
            position: relative;
        }
        .status-indicator {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            margin: 0.5rem 0;
            font-weight: 500;
        }
        .status-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body class="bg-light">
    <div class="test-container">
        <div class="test-header">
            <h1><i class="bi bi-bell-fill me-2"></i>Notification Bell Fix Test</h1>
            <p class="mb-0">Testing the fixed notification bell functionality</p>
        </div>

        <div class="test-section">
            <h3>🔔 Interactive Notification Bell Demo</h3>
            <p>Click the bell below to test the notification toggle functionality:</p>

            <div class="demo-navbar">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-white">Admin Dashboard Header Simulation</span>
                    <div class="d-flex align-items-center">
                        <!-- Fixed Notification Bell -->
                        <div class="nav-item me-3">
                            <button class="nav-link position-relative border-0 bg-transparent" type="button" id="notificationBell" style="cursor: pointer;">
                                <span class="bell-icon"><i class="bi bi-bell fs-5 text-white"></i></span>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge">3</span>
                            </button>
                        </div>
                        <span class="text-white">Welcome, Admin</span>
                    </div>
                </div>

                <!-- Notification Container -->
                <div class="static-notifications-container" id="staticNotifications" style="position: relative; top: 10px; right: 0; width: 100%; max-width: 400px; margin-top: 1rem;">
                    <div class="notifications-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">Notifications</h6>
                            <button type="button" class="btn-close btn-close-white btn-sm" id="closeNotifications" aria-label="Close"></button>
                        </div>
                    </div>
                    <div class="notifications-body">
                        <div class="notification-item unread type-info">
                            <div class="d-flex">
                                <div class="notification-icon bg-info">
                                    <i class="bi bi-info-circle" style="color: #4361ee;"></i>
                                </div>
                                <div class="notification-content">
                                    <p class="notification-message">Notification bell has been fixed! Click functionality is now working.</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="notification-time">Just now</span>
                                        <button class="btn btn-sm btn-link p-0 mark-read-btn">Mark as read</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="notification-item type-success">
                            <div class="d-flex">
                                <div class="notification-icon bg-success">
                                    <i class="bi bi-check-circle" style="color: #2a9d8f;"></i>
                                </div>
                                <div class="notification-content">
                                    <p class="notification-message">Button element replaced anchor tag for proper click handling.</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="notification-time">5 minutes ago</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="notification-item type-warning">
                            <div class="d-flex">
                                <div class="notification-icon bg-warning">
                                    <i class="bi bi-exclamation-triangle" style="color: #fb8500;"></i>
                                </div>
                                <div class="notification-content">
                                    <p class="notification-message">CSS z-index conflicts have been resolved.</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="notification-time">1 hour ago</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="notifications-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="../notifications/index.php">View all notifications</a>
                            <span class="text-muted">3 total</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>✅ Test Results</h3>
            <div id="testResults">
                <p class="text-muted">Click the notification bell above to see the results...</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 What Was Fixed</h3>
            <div class="status-success">
                <strong>✅ Fixed:</strong> Changed notification bell from anchor tag to button element
            </div>
            <div class="status-success">
                <strong>✅ Fixed:</strong> Removed href attribute that was causing page navigation
            </div>
            <div class="status-success">
                <strong>✅ Fixed:</strong> Updated CSS z-index values to prevent conflicts
            </div>
            <div class="status-success">
                <strong>✅ Fixed:</strong> Improved JavaScript click handler with proper event handling
            </div>
            <div class="status-success">
                <strong>✅ Fixed:</strong> Enhanced notification container styling for better visibility
            </div>
        </div>

        <div class="test-section">
            <h3>🚀 Next Steps</h3>
            <p>If this test works correctly, the same fixes have been applied to your main admin dashboard. You can now:</p>
            <ul>
                <li>Go back to the <a href="dashboard.php" class="btn btn-sm btn-primary">Admin Dashboard</a></li>
                <li>Test the notification bell functionality there</li>
                <li>Click the bell to show/hide notifications</li>
                <li>Use the "View all notifications" link to access the full notifications page</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let clickCount = 0;

        document.addEventListener('DOMContentLoaded', function() {
            const notificationBell = document.getElementById('notificationBell');
            const staticNotificationsContainer = document.getElementById('staticNotifications');
            const closeButton = document.getElementById('closeNotifications');
            const testResults = document.getElementById('testResults');

            // Initialize container as hidden
            if (staticNotificationsContainer) {
                staticNotificationsContainer.style.display = 'none';
            }

            // Bell click handler
            if (notificationBell && staticNotificationsContainer) {
                notificationBell.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    clickCount++;

                    const isHidden = staticNotificationsContainer.style.display === 'none' || 
                                   staticNotificationsContainer.style.display === '';

                    if (isHidden) {
                        staticNotificationsContainer.style.display = 'block';
                        staticNotificationsContainer.style.opacity = '1';
                        staticNotificationsContainer.style.transform = 'translateY(0)';
                        staticNotificationsContainer.classList.add('show');
                        testResults.innerHTML = '<div class="status-success">✅ Click #' + clickCount + ': Notifications opened successfully!</div>';
                    } else {
                        staticNotificationsContainer.classList.remove('show');
                        staticNotificationsContainer.style.opacity = '0';
                        staticNotificationsContainer.style.transform = 'translateY(-10px)';
                        setTimeout(() => {
                            staticNotificationsContainer.style.display = 'none';
                        }, 300);
                        testResults.innerHTML = '<div class="status-info">ℹ️ Click #' + clickCount + ': Notifications closed successfully!</div>';
                    }
                });
            } else {
                testResults.innerHTML = '<div class="status-error">❌ Error: Could not find notification bell or container elements!</div>';
            }

            // Close button handler
            if (closeButton && staticNotificationsContainer) {
                closeButton.addEventListener('click', function() {
                    staticNotificationsContainer.classList.remove('show');
                    staticNotificationsContainer.style.opacity = '0';
                    staticNotificationsContainer.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        staticNotificationsContainer.style.display = 'none';
                    }, 300);
                    testResults.innerHTML = '<div class="status-info">ℹ️ Notifications closed via close button!</div>';
                });
            }
        });
    </script>
</body>
</html>
