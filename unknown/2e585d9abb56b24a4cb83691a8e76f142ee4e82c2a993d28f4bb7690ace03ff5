<?php
/**
 * Admin Settings Page
 *
 * This page allows administrators to manage system settings.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include required files with error handling
try {
    require_once '../config/database.php';
    require_once '../config/config.php';
    require_once '../includes/functions.php';
} catch (Exception $e) {
    die('Error loading required files: ' . $e->getMessage());
}

// Check if user is logged in and is an admin
// TEMPORARILY DISABLED - UNCOMMENT TO RE-ENABLE AUTHENTICATION
/*
if (!isLoggedIn() || !isAdmin()) {
    redirect(url('login.php'));
    exit;
}
*/

// Initialize variables
$success = '';
$error = '';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Create settings table if it doesn't exist
try {
    $create_settings_table = "CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_group VARCHAR(50) NOT NULL,
        setting_key VARCHAR(100) NOT NULL,
        setting_value TEXT,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_setting (setting_group, setting_key)
    )";
    $db->exec($create_settings_table);
} catch (PDOException $e) {
    $error = 'Failed to create settings table: ' . $e->getMessage();
}

// Function to get setting value
function getSetting($db, $group, $key, $default = '') {
    try {
        $query = "SELECT setting_value FROM settings WHERE setting_group = :group AND setting_key = :key";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':group', $group);
        $stmt->bindParam(':key', $key);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch();
            return $row['setting_value'];
        }
        return $default;
    } catch (PDOException $e) {
        return $default;
    }
}

// Function to update setting
function updateSetting($db, $group, $key, $value, $description = '') {
    try {
        $query = "INSERT INTO settings (setting_group, setting_key, setting_value, description)
                  VALUES (:group, :key, :value, :description)
                  ON DUPLICATE KEY UPDATE
                  setting_value = VALUES(setting_value),
                  description = VALUES(description),
                  updated_at = CURRENT_TIMESTAMP";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':group', $group);
        $stmt->bindParam(':key', $key);
        $stmt->bindParam(':value', $value);
        $stmt->bindParam(':description', $description);
        return $stmt->execute();
    } catch (PDOException $e) {
        return false;
    }
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'update_general') {
        // Update general settings
        $library_name = trim($_POST['library_name'] ?? '');
        $library_address = trim($_POST['library_address'] ?? '');
        $library_phone = trim($_POST['library_phone'] ?? '');
        $library_email = trim($_POST['library_email'] ?? '');
        $loan_period = intval($_POST['loan_period'] ?? 14);
        $fine_per_day = floatval($_POST['fine_per_day'] ?? 1.00);
        $max_books_per_member = intval($_POST['max_books_per_member'] ?? 5);

        $updates = [
            ['general', 'library_name', $library_name, 'Library name displayed throughout the system'],
            ['general', 'library_address', $library_address, 'Library physical address'],
            ['general', 'library_phone', $library_phone, 'Library contact phone number'],
            ['general', 'library_email', $library_email, 'Library contact email address'],
            ['general', 'loan_period', $loan_period, 'Default loan period in days'],
            ['general', 'fine_per_day', $fine_per_day, 'Fine amount per day for overdue books'],
            ['general', 'max_books_per_member', $max_books_per_member, 'Maximum books a member can borrow']
        ];

        $all_success = true;
        foreach ($updates as $update) {
            if (!updateSetting($db, $update[0], $update[1], $update[2], $update[3])) {
                $all_success = false;
                break;
            }
        }

        if ($all_success) {
            $success = 'General settings updated successfully!';
        } else {
            $error = 'Failed to update some settings. Please try again.';
        }
    }

    elseif ($action === 'update_system') {
        // Update system settings
        $items_per_page = intval($_POST['items_per_page'] ?? 10);
        $enable_notifications = isset($_POST['enable_notifications']) ? 'true' : 'false';
        $enable_email_reminders = isset($_POST['enable_email_reminders']) ? 'true' : 'false';
        $reminder_days_before = intval($_POST['reminder_days_before'] ?? 3);
        $enable_member_registration = isset($_POST['enable_member_registration']) ? 'true' : 'false';

        $updates = [
            ['system', 'items_per_page', $items_per_page, 'Number of items to display per page'],
            ['system', 'enable_notifications', $enable_notifications, 'Enable system notifications'],
            ['system', 'enable_email_reminders', $enable_email_reminders, 'Enable email reminders for due books'],
            ['system', 'reminder_days_before', $reminder_days_before, 'Days before due date to send reminders'],
            ['system', 'enable_member_registration', $enable_member_registration, 'Allow members to self-register']
        ];

        $all_success = true;
        foreach ($updates as $update) {
            if (!updateSetting($db, $update[0], $update[1], $update[2], $update[3])) {
                $all_success = false;
                break;
            }
        }

        if ($all_success) {
            $success = 'System settings updated successfully!';
        } else {
            $error = 'Failed to update some settings. Please try again.';
        }
    }
}

// Get current settings
$general_settings = [
    'library_name' => getSetting($db, 'general', 'library_name', 'Library Management System'),
    'library_address' => getSetting($db, 'general', 'library_address', ''),
    'library_phone' => getSetting($db, 'general', 'library_phone', ''),
    'library_email' => getSetting($db, 'general', 'library_email', ''),
    'loan_period' => getSetting($db, 'general', 'loan_period', '14'),
    'fine_per_day' => getSetting($db, 'general', 'fine_per_day', '1.00'),
    'max_books_per_member' => getSetting($db, 'general', 'max_books_per_member', '5')
];

$system_settings = [
    'items_per_page' => getSetting($db, 'system', 'items_per_page', '10'),
    'enable_notifications' => getSetting($db, 'system', 'enable_notifications', 'true'),
    'enable_email_reminders' => getSetting($db, 'system', 'enable_email_reminders', 'true'),
    'reminder_days_before' => getSetting($db, 'system', 'reminder_days_before', '3'),
    'enable_member_registration' => getSetting($db, 'system', 'enable_member_registration', 'false')
];

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Settings - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .settings-card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
        }
        .settings-card .card-header {
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            font-weight: 600;
        }
        .nav-pills .nav-link {
            border-radius: 0.375rem;
            margin-bottom: 0.5rem;
        }
        .nav-pills .nav-link.active {
            background-color: #0d6efd;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">System Settings</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="email_settings.php" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-envelope me-1"></i> Email Settings
                            </a>
                            <a href="backup.php" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-download me-1"></i> Backup
                            </a>
                        </div>
                    </div>
                </div>

                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle me-2"></i><?php echo h($success); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i><?php echo h($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-3">
                        <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                            <button class="nav-link active" id="v-pills-general-tab" data-bs-toggle="pill" data-bs-target="#v-pills-general" type="button" role="tab" aria-controls="v-pills-general" aria-selected="true">
                                <i class="bi bi-gear me-2"></i>General Settings
                            </button>
                            <button class="nav-link" id="v-pills-system-tab" data-bs-toggle="pill" data-bs-target="#v-pills-system" type="button" role="tab" aria-controls="v-pills-system" aria-selected="false">
                                <i class="bi bi-cpu me-2"></i>System Settings
                            </button>
                            <a href="email_settings.php" class="nav-link">
                                <i class="bi bi-envelope me-2"></i>Email Settings
                            </a>
                            <a href="users.php" class="nav-link">
                                <i class="bi bi-people me-2"></i>User Management
                            </a>
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="tab-content" id="v-pills-tabContent">
                            <!-- General Settings Tab -->
                            <div class="tab-pane fade show active" id="v-pills-general" role="tabpanel" aria-labelledby="v-pills-general-tab">
                                <div class="card settings-card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><i class="bi bi-building me-2"></i>Library Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <form action="" method="post">
                                            <input type="hidden" name="action" value="update_general">

                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="library_name" class="form-label">Library Name</label>
                                                    <input type="text" class="form-control" id="library_name" name="library_name" value="<?php echo h($general_settings['library_name']); ?>" required>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="library_email" class="form-label">Library Email</label>
                                                    <input type="email" class="form-control" id="library_email" name="library_email" value="<?php echo h($general_settings['library_email']); ?>">
                                                </div>
                                            </div>

                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="library_phone" class="form-label">Library Phone</label>
                                                    <input type="text" class="form-control" id="library_phone" name="library_phone" value="<?php echo h($general_settings['library_phone']); ?>">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="library_address" class="form-label">Library Address</label>
                                                    <textarea class="form-control" id="library_address" name="library_address" rows="2"><?php echo h($general_settings['library_address']); ?></textarea>
                                                </div>
                                            </div>

                                            <hr>
                                            <h6 class="text-muted mb-3">Loan Settings</h6>

                                            <div class="row mb-3">
                                                <div class="col-md-4">
                                                    <label for="loan_period" class="form-label">Default Loan Period (days)</label>
                                                    <input type="number" class="form-control" id="loan_period" name="loan_period" value="<?php echo h($general_settings['loan_period']); ?>" min="1" max="365" required>
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="fine_per_day" class="form-label">Fine Per Day ($)</label>
                                                    <input type="number" class="form-control" id="fine_per_day" name="fine_per_day" value="<?php echo h($general_settings['fine_per_day']); ?>" min="0" step="0.01" required>
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="max_books_per_member" class="form-label">Max Books Per Member</label>
                                                    <input type="number" class="form-control" id="max_books_per_member" name="max_books_per_member" value="<?php echo h($general_settings['max_books_per_member']); ?>" min="1" max="50" required>
                                                </div>
                                            </div>

                                            <div class="d-grid">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="bi bi-check-lg me-2"></i>Save General Settings
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- System Settings Tab -->
                            <div class="tab-pane fade" id="v-pills-system" role="tabpanel" aria-labelledby="v-pills-system-tab">
                                <div class="card settings-card">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0"><i class="bi bi-cpu me-2"></i>System Configuration</h5>
                                    </div>
                                    <div class="card-body">
                                        <form action="" method="post">
                                            <input type="hidden" name="action" value="update_system">

                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="items_per_page" class="form-label">Items Per Page</label>
                                                    <select class="form-select" id="items_per_page" name="items_per_page">
                                                        <option value="5" <?php echo $system_settings['items_per_page'] == '5' ? 'selected' : ''; ?>>5</option>
                                                        <option value="10" <?php echo $system_settings['items_per_page'] == '10' ? 'selected' : ''; ?>>10</option>
                                                        <option value="25" <?php echo $system_settings['items_per_page'] == '25' ? 'selected' : ''; ?>>25</option>
                                                        <option value="50" <?php echo $system_settings['items_per_page'] == '50' ? 'selected' : ''; ?>>50</option>
                                                        <option value="100" <?php echo $system_settings['items_per_page'] == '100' ? 'selected' : ''; ?>>100</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="reminder_days_before" class="form-label">Reminder Days Before Due</label>
                                                    <input type="number" class="form-control" id="reminder_days_before" name="reminder_days_before" value="<?php echo h($system_settings['reminder_days_before']); ?>" min="1" max="30">
                                                </div>
                                            </div>

                                            <hr>
                                            <h6 class="text-muted mb-3">Feature Settings</h6>

                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="enable_notifications" name="enable_notifications" <?php echo $system_settings['enable_notifications'] === 'true' ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="enable_notifications">
                                                            Enable System Notifications
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="enable_email_reminders" name="enable_email_reminders" <?php echo $system_settings['enable_email_reminders'] === 'true' ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="enable_email_reminders">
                                                            Enable Email Reminders
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="enable_member_registration" name="enable_member_registration" <?php echo $system_settings['enable_member_registration'] === 'true' ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="enable_member_registration">
                                                            Allow Member Self-Registration
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="d-grid">
                                                <button type="submit" class="btn btn-success">
                                                    <i class="bi bi-check-lg me-2"></i>Save System Settings
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
