<?php
/**
 * Google Authentication Debug
 *
 * This script helps diagnose issues with Google OAuth authentication.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/google_auth.php';

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// Check if Google OAuth is configured
$is_configured = isGoogleOAuthConfigured();

// Get Google login URL
$google_login_url = getGoogleLoginUrl();

// Check if cURL is enabled
$curl_enabled = function_exists('curl_init');

// Check if the redirect URI is valid
$redirect_uri = GOOGLE_REDIRECT_URI;
$redirect_uri_valid = filter_var($redirect_uri, FILTER_VALIDATE_URL) !== false;

// Check if the database has the required columns
$database = new Database();
$db = $database->getConnection();

function columnExists($db, $table, $column) {
    $query = "SHOW COLUMNS FROM $table LIKE '$column'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    return $stmt->rowCount() > 0;
}

$users_google_id_exists = columnExists($db, 'users', 'google_id');
$users_google_token_exists = columnExists($db, 'users', 'google_token');
$users_google_picture_exists = columnExists($db, 'users', 'google_picture');

$members_google_id_exists = columnExists($db, 'members', 'google_id');
$members_google_token_exists = columnExists($db, 'members', 'google_token');
$members_google_picture_exists = columnExists($db, 'members', 'google_picture');
$members_remember_token_exists = columnExists($db, 'members', 'remember_token');

// Check if the Google callback file exists
$callback_file_exists = file_exists(__DIR__ . '/google_callback.php');

// Check if the session is working
$session_working = isset($_SESSION);

// Check if the Google OAuth scopes are defined
$scopes_defined = defined('GOOGLE_SCOPES') && is_array(GOOGLE_SCOPES) && count(GOOGLE_SCOPES) > 0;

// Get the PHP version
$php_version = phpversion();

// Check if the Google OAuth client ID and client secret are valid
$client_id_valid = !empty(GOOGLE_CLIENT_ID) && strpos(GOOGLE_CLIENT_ID, '.apps.googleusercontent.com') !== false;
$client_secret_valid = !empty(GOOGLE_CLIENT_SECRET) && strpos(GOOGLE_CLIENT_SECRET, 'GOCSPX-') !== false;

// Check if the Google OAuth redirect URI matches the base URL
$redirect_uri_matches_base_url = strpos($redirect_uri, BASE_URL) === 0;

// Get the server information
$server_info = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';

// Check if the Google OAuth configuration file is writable
$config_file = __DIR__ . '/config/google_oauth.php';
$config_file_writable = is_writable($config_file);

// Check if the Google OAuth configuration file is readable
$config_file_readable = is_readable($config_file);

// Check if the Google OAuth configuration file exists
$config_file_exists = file_exists($config_file);

// Check if the Google OAuth configuration file is valid PHP
$config_file_valid = false;
if ($config_file_exists && $config_file_readable) {
    try {
        $config_content = file_get_contents($config_file);
        $config_file_valid = strpos($config_content, '<?php') === 0;
    } catch (Exception $e) {
        $config_file_valid = false;
    }
}

// Check if the Google OAuth configuration file has the required constants
$config_file_has_constants = false;
if ($config_file_exists && $config_file_readable) {
    try {
        $config_content = file_get_contents($config_file);
        $config_file_has_constants = strpos($config_content, "define('GOOGLE_CLIENT_ID'") !== false &&
                                    strpos($config_content, "define('GOOGLE_CLIENT_SECRET'") !== false &&
                                    strpos($config_content, "define('GOOGLE_REDIRECT_URI'") !== false;
    } catch (Exception $e) {
        $config_file_has_constants = false;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth Debug - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px;
        }
        .debug-container {
            max-width: 800px;
            margin: 40px auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            padding: 30px;
        }
        .code-block {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .status {
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .check-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .check-icon {
            margin-right: 10px;
            font-size: 1.2rem;
        }
        .check-text {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 class="mb-4"><i class="bi bi-google me-2"></i>Google OAuth Debug</h1>
        
        <div class="status <?php echo $is_configured ? 'status-success' : 'status-error'; ?>">
            <h4><i class="bi <?php echo $is_configured ? 'bi-check-circle-fill' : 'bi-exclamation-triangle-fill'; ?> me-2"></i>
                Configuration Status
            </h4>
            <p>
                <?php if ($is_configured): ?>
                    Google OAuth is properly configured.
                <?php else: ?>
                    Google OAuth is not configured. Please set up your Google OAuth credentials.
                <?php endif; ?>
            </p>
        </div>
        
        <h3 class="mt-4">Configuration Checks</h3>
        <div class="check-item">
            <div class="check-icon text-<?php echo $client_id_valid ? 'success' : 'danger'; ?>">
                <i class="bi <?php echo $client_id_valid ? 'bi-check-circle-fill' : 'bi-x-circle-fill'; ?>"></i>
            </div>
            <div class="check-text">
                Client ID: <?php echo $client_id_valid ? 'Valid' : 'Invalid or empty'; ?>
                <?php if (!$client_id_valid): ?>
                    <div class="small text-danger">Please set a valid Client ID in the Google OAuth configuration.</div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="check-item">
            <div class="check-icon text-<?php echo $client_secret_valid ? 'success' : 'danger'; ?>">
                <i class="bi <?php echo $client_secret_valid ? 'bi-check-circle-fill' : 'bi-x-circle-fill'; ?>"></i>
            </div>
            <div class="check-text">
                Client Secret: <?php echo $client_secret_valid ? 'Valid' : 'Invalid or empty'; ?>
                <?php if (!$client_secret_valid): ?>
                    <div class="small text-danger">Please set a valid Client Secret in the Google OAuth configuration.</div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="check-item">
            <div class="check-icon text-<?php echo $redirect_uri_valid ? 'success' : 'danger'; ?>">
                <i class="bi <?php echo $redirect_uri_valid ? 'bi-check-circle-fill' : 'bi-x-circle-fill'; ?>"></i>
            </div>
            <div class="check-text">
                Redirect URI: <?php echo $redirect_uri_valid ? 'Valid' : 'Invalid'; ?>
                <?php if (!$redirect_uri_valid): ?>
                    <div class="small text-danger">The redirect URI is not a valid URL.</div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="check-item">
            <div class="check-icon text-<?php echo $redirect_uri_matches_base_url ? 'success' : 'danger'; ?>">
                <i class="bi <?php echo $redirect_uri_matches_base_url ? 'bi-check-circle-fill' : 'bi-x-circle-fill'; ?>"></i>
            </div>
            <div class="check-text">
                Redirect URI matches Base URL: <?php echo $redirect_uri_matches_base_url ? 'Yes' : 'No'; ?>
                <?php if (!$redirect_uri_matches_base_url): ?>
                    <div class="small text-danger">The redirect URI does not match the base URL.</div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="check-item">
            <div class="check-icon text-<?php echo $curl_enabled ? 'success' : 'danger'; ?>">
                <i class="bi <?php echo $curl_enabled ? 'bi-check-circle-fill' : 'bi-x-circle-fill'; ?>"></i>
            </div>
            <div class="check-text">
                cURL: <?php echo $curl_enabled ? 'Enabled' : 'Disabled'; ?>
                <?php if (!$curl_enabled): ?>
                    <div class="small text-danger">cURL is required for Google OAuth authentication.</div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="check-item">
            <div class="check-icon text-<?php echo $callback_file_exists ? 'success' : 'danger'; ?>">
                <i class="bi <?php echo $callback_file_exists ? 'bi-check-circle-fill' : 'bi-x-circle-fill'; ?>"></i>
            </div>
            <div class="check-text">
                Callback File: <?php echo $callback_file_exists ? 'Exists' : 'Missing'; ?>
                <?php if (!$callback_file_exists): ?>
                    <div class="small text-danger">The Google callback file is missing.</div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="check-item">
            <div class="check-icon text-<?php echo $session_working ? 'success' : 'danger'; ?>">
                <i class="bi <?php echo $session_working ? 'bi-check-circle-fill' : 'bi-x-circle-fill'; ?>"></i>
            </div>
            <div class="check-text">
                Session: <?php echo $session_working ? 'Working' : 'Not working'; ?>
                <?php if (!$session_working): ?>
                    <div class="small text-danger">Sessions are required for Google OAuth authentication.</div>
                <?php endif; ?>
            </div>
        </div>
        
        <h3 class="mt-4">Database Checks</h3>
        <div class="check-item">
            <div class="check-icon text-<?php echo $users_google_id_exists ? 'success' : 'danger'; ?>">
                <i class="bi <?php echo $users_google_id_exists ? 'bi-check-circle-fill' : 'bi-x-circle-fill'; ?>"></i>
            </div>
            <div class="check-text">
                Users Google ID Column: <?php echo $users_google_id_exists ? 'Exists' : 'Missing'; ?>
                <?php if (!$users_google_id_exists): ?>
                    <div class="small text-danger">Run the add_google_auth.php script to add the required columns.</div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="check-item">
            <div class="check-icon text-<?php echo $members_google_id_exists ? 'success' : 'danger'; ?>">
                <i class="bi <?php echo $members_google_id_exists ? 'bi-check-circle-fill' : 'bi-x-circle-fill'; ?>"></i>
            </div>
            <div class="check-text">
                Members Google ID Column: <?php echo $members_google_id_exists ? 'Exists' : 'Missing'; ?>
                <?php if (!$members_google_id_exists): ?>
                    <div class="small text-danger">Run the add_google_auth.php script to add the required columns.</div>
                <?php endif; ?>
            </div>
        </div>
        
        <h3 class="mt-4">System Information</h3>
        <div class="code-block">
            <p>PHP Version: <?php echo h($php_version); ?></p>
            <p>Server: <?php echo h($server_info); ?></p>
            <p>Base URL: <?php echo h(BASE_URL); ?></p>
            <p>Redirect URI: <?php echo h($redirect_uri); ?></p>
        </div>
        
        <div class="mt-4 text-center">
            <a href="update_google_credentials.php" class="btn btn-primary">Update Google Credentials</a>
            <a href="test_google_auth.php" class="btn btn-success ms-2">Test Google Authentication</a>
            <a href="login.php" class="btn btn-outline-secondary ms-2">Go to Login Page</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
