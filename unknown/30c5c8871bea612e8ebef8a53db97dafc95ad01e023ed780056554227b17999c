<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and has appropriate permissions
if (!isLoggedIn() || (!isAdmin() && !isLibrarian())) {
    redirect('../auth/login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Initialize variables
$success_count = 0;
$error_count = 0;
$errors = [];
$imported_books = [];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if file was uploaded
    if (isset($_FILES['csvFile']) && $_FILES['csvFile']['error'] === UPLOAD_ERR_OK) {
        $file_tmp = $_FILES['csvFile']['tmp_name'];
        $file_name = $_FILES['csvFile']['name'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        
        // Check if file is a CSV
        if ($file_ext === 'csv') {
            // Open the file
            if (($handle = fopen($file_tmp, "r")) !== FALSE) {
                // Check if first row is header
                $has_header = isset($_POST['headerRow']) && $_POST['headerRow'] === 'on';
                $row_count = 0;
                
                // Process each row
                while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                    $row_count++;
                    
                    // Skip header row if specified
                    if ($has_header && $row_count === 1) {
                        continue;
                    }
                    
                    // Check if row has enough columns
                    if (count($data) < 3) {
                        $errors[] = "Row $row_count: Not enough columns. Each row must have at least title, author, and quantity.";
                        $error_count++;
                        continue;
                    }
                    
                    // Extract data
                    $isbn = sanitize($data[0] ?? '');
                    $title = sanitize($data[1] ?? '');
                    $author = sanitize($data[2] ?? '');
                    $category = sanitize($data[3] ?? '');
                    $publication_year = sanitize($data[4] ?? '');
                    $publisher = sanitize($data[5] ?? '');
                    $quantity = (int)sanitize($data[6] ?? '1');
                    $shelf_location = sanitize($data[7] ?? '');
                    $description = sanitize($data[8] ?? '');
                    
                    // Validate required fields
                    if (empty($title)) {
                        $errors[] = "Row $row_count: Book title is required";
                        $error_count++;
                        continue;
                    }
                    
                    if (empty($author)) {
                        $errors[] = "Row $row_count: Author name is required";
                        $error_count++;
                        continue;
                    }
                    
                    if ($quantity < 1) {
                        $errors[] = "Row $row_count: Quantity must be at least 1";
                        $error_count++;
                        continue;
                    }
                    
                    // Check if ISBN already exists
                    if (!empty($isbn)) {
                        $query = "SELECT id FROM books WHERE isbn = :isbn";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':isbn', $isbn);
                        $stmt->execute();
                        
                        if ($stmt->rowCount() > 0) {
                            $errors[] = "Row $row_count: ISBN '$isbn' already exists in the database";
                            $error_count++;
                            continue;
                        }
                    }
                    
                    // Insert book into database
                    try {
                        $available_quantity = $quantity; // Initially all books are available
                        
                        $query = "INSERT INTO books (isbn, title, author, category, publication_year, publisher, 
                                                    quantity, available_quantity, shelf_location, description) 
                                  VALUES (:isbn, :title, :author, :category, :publication_year, :publisher, 
                                         :quantity, :available_quantity, :shelf_location, :description)";
                        
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':isbn', $isbn);
                        $stmt->bindParam(':title', $title);
                        $stmt->bindParam(':author', $author);
                        $stmt->bindParam(':category', $category);
                        $stmt->bindParam(':publication_year', $publication_year);
                        $stmt->bindParam(':publisher', $publisher);
                        $stmt->bindParam(':quantity', $quantity);
                        $stmt->bindParam(':available_quantity', $available_quantity);
                        $stmt->bindParam(':shelf_location', $shelf_location);
                        $stmt->bindParam(':description', $description);
                        
                        if ($stmt->execute()) {
                            $success_count++;
                            $imported_books[] = [
                                'title' => $title,
                                'author' => $author,
                                'isbn' => $isbn
                            ];
                        } else {
                            $errors[] = "Row $row_count: Failed to add book '$title'";
                            $error_count++;
                        }
                    } catch (PDOException $e) {
                        $errors[] = "Row $row_count: Database error: " . $e->getMessage();
                        $error_count++;
                    }
                }
                
                fclose($handle);
                
                // Set message based on results
                if ($success_count > 0) {
                    setMessage("Successfully imported $success_count books" . ($error_count > 0 ? " with $error_count errors" : ""), 'success');
                } else {
                    setMessage("Failed to import any books. Please check the errors and try again.", 'danger');
                }
            } else {
                setMessage("Failed to open the CSV file", 'danger');
            }
        } else {
            setMessage("Invalid file format. Only CSV files are allowed", 'danger');
        }
    } else {
        setMessage("No file uploaded or an error occurred during upload", 'danger');
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import Books - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Import Books</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="index.php" class="btn btn-sm btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Books
                        </a>
                    </div>
                </div>
                
                <?php displayMessage(); ?>
                
                <?php if ($success_count > 0): ?>
                <div class="alert alert-success">
                    <h4><i class="bi bi-check-circle me-2"></i> Import Successful</h4>
                    <p>Successfully imported <?php echo $success_count; ?> books.</p>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Imported Books</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ISBN</th>
                                        <th>Title</th>
                                        <th>Author</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($imported_books as $book): ?>
                                    <tr>
                                        <td><?php echo $book['isbn'] ?: 'N/A'; ?></td>
                                        <td><?php echo $book['title']; ?></td>
                                        <td><?php echo $book['author']; ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if ($error_count > 0): ?>
                <div class="alert alert-danger">
                    <h4><i class="bi bi-exclamation-triangle me-2"></i> Import Errors</h4>
                    <p>Encountered <?php echo $error_count; ?> errors during import.</p>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Import More Books</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" method="post" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="csvFile" class="form-label">CSV File</label>
                                        <input type="file" class="form-control" id="csvFile" name="csvFile" accept=".csv" required>
                                        <small class="text-muted">Upload a CSV file with book data</small>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="headerRow" name="headerRow" checked>
                                        <label class="form-check-label" for="headerRow">
                                            First row contains column headers
                                        </label>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-upload me-1"></i> Upload and Import
                                    </button>
                                    <a href="../templates/book_import_template.csv" class="btn btn-outline-secondary">
                                        <i class="bi bi-download me-1"></i> Download Template
                                    </a>
                                </form>
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <h5><i class="bi bi-info-circle me-2"></i> CSV Format</h5>
                                    <p>Your CSV file should have the following columns:</p>
                                    <ol>
                                        <li><strong>ISBN</strong> (optional)</li>
                                        <li><strong>Title</strong> (required)</li>
                                        <li><strong>Author</strong> (required)</li>
                                        <li><strong>Category</strong> (optional)</li>
                                        <li><strong>Publication Year</strong> (optional)</li>
                                        <li><strong>Publisher</strong> (optional)</li>
                                        <li><strong>Quantity</strong> (required, default: 1)</li>
                                        <li><strong>Shelf Location</strong> (optional)</li>
                                        <li><strong>Description</strong> (optional)</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
</body>
</html>
