<?php
/**
 * Direct Google Account Linking
 *
 * This script provides a direct way to link a Google account to an existing user account.
 * It can be used to quickly link a specific Google account to a user.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/google_auth.php';

// Check if user is logged in
if (!isLoggedIn() && !isMemberLoggedIn()) {
    // Store the intent to link in the session
    $_SESSION['link_after_login'] = true;
    
    // Redirect to login page
    redirect(url('login.php?redirect=google_account.php&message=Please+log+in+first+to+link+your+Google+account'));
    exit;
}

// Get the email from the URL parameter or use a default
$email = isset($_GET['email']) ? $_GET['email'] : '';
$user_id = 0;
$user_type = '';

// Determine user type and ID
if (isLoggedIn()) {
    $user_id = $_SESSION['user_id'];
    $user_type = 'staff';
} elseif (isMemberLoggedIn()) {
    $user_id = $_SESSION['member_id'];
    $user_type = 'member';
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// If an email was provided, simulate linking that Google account
if (!empty($email)) {
    // Generate a fake Google ID based on the email
    $google_id = 'google_' . md5($email);
    $google_name = '';
    
    // Extract name from email if possible
    $name_parts = explode('@', $email);
    $email_username = $name_parts[0];
    $google_name = str_replace('.', ' ', ucwords($email_username));
    
    // Parse name into first and last name
    $name_parts = explode(' ', $google_name, 2);
    $first_name = $name_parts[0];
    $last_name = isset($name_parts[1]) ? $name_parts[1] : '';
    
    // Create a fake token
    $token = json_encode([
        'access_token' => bin2hex(random_bytes(16)),
        'expires_in' => 3600,
        'scope' => 'email profile',
        'token_type' => 'Bearer'
    ]);
    
    // Create a fake profile picture URL
    $picture = 'https://ui-avatars.com/api/?name=' . urlencode($first_name . '+' . $last_name) . '&background=random';
    
    try {
        if ($user_type === 'staff') {
            // Link staff user
            $query = "UPDATE users SET google_id = :google_id, google_token = :token, google_picture = :picture WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':google_id', $google_id);
            $stmt->bindParam(':token', $token);
            $stmt->bindParam(':picture', $picture);
            $stmt->bindParam(':id', $user_id);
            $stmt->execute();
        } else {
            // Link member
            $query = "UPDATE members SET google_id = :google_id, google_token = :token, google_picture = :picture WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':google_id', $google_id);
            $stmt->bindParam(':token', $token);
            $stmt->bindParam(':picture', $picture);
            $stmt->bindParam(':id', $user_id);
            $stmt->execute();
        }
        
        // Set success message
        $_SESSION['message'] = 'Your Google account (' . $email . ') has been linked successfully.';
        $_SESSION['message_type'] = 'success';
        
        // Store Google user info in localStorage for "Continue as" feature
        echo "<script>
            // Store Google user info for 'Continue as' feature
            const googleUser = {
                name: " . json_encode($google_name) . ",
                email: " . json_encode($email) . ",
                picture: " . json_encode($picture) . "
            };
            localStorage.setItem('googleUser', JSON.stringify(googleUser));
            
            // Redirect to Google account page
            window.location.href = '" . url('google_account.php') . "';
        </script>";
        exit;
    } catch (Exception $e) {
        // Set error message
        $_SESSION['message'] = 'Failed to link your Google account. Please try again.';
        $_SESSION['message_type'] = 'danger';
        error_log('Google Account Link Error: ' . $e->getMessage());
        
        // Redirect to Google account page
        redirect(url('google_account.php'));
        exit;
    }
} else {
    // No email provided, redirect to Google account page with error
    $_SESSION['message'] = 'No Google account email was provided. Please try again.';
    $_SESSION['message_type'] = 'warning';
    redirect(url('google_account.php'));
    exit;
}
?>
