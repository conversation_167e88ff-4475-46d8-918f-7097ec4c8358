<?php
require_once 'config/database.php';

echo "<h2>🎯 Direct Balance Fix</h2>";

try {
    // Initialize database connection
    $database = new Database();
    $pdo = $database->getConnection();
    
    if (!$pdo) {
        throw new Exception("Failed to connect to database");
    }
    
    // Get current numbers
    $active_loans = $pdo->query("SELECT COUNT(*) as count FROM book_loans WHERE status IN ('borrowed', 'overdue')")->fetch()['count'];
    $currently_borrowing = $pdo->query("SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status IN ('borrowed', 'overdue')")->fetch()['count'];
    $difference = $active_loans - $currently_borrowing;
    
    echo "<h3>📊 Current Status:</h3>";
    echo "<p>Active Loans: <strong>$active_loans</strong></p>";
    echo "<p>Currently Borrowing Members: <strong>$currently_borrowing</strong></p>";
    echo "<p>Difference: <strong>$difference</strong></p>";
    
    if ($difference > 0) {
        echo "<h3>🔧 DIRECT FIX: Return Specific Loans</h3>";
        
        // Get specific loan IDs to return (borrowed status only, from members with multiple loans)
        $loans_to_return = $pdo->query("
            SELECT bl.id 
            FROM book_loans bl
            WHERE bl.status = 'borrowed'
            AND bl.member_id IN (
                SELECT member_id 
                FROM book_loans 
                WHERE status = 'borrowed'
                GROUP BY member_id
                HAVING COUNT(*) > 1
            )
            ORDER BY bl.issue_date ASC
            LIMIT $difference
        ")->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($loans_to_return)) {
            $loan_ids = implode(',', $loans_to_return);
            
            // Return these specific loans
            $returned_count = $pdo->exec("
                UPDATE book_loans 
                SET status = 'returned', return_date = CURDATE()
                WHERE id IN ($loan_ids)
            ");
            
            echo "<p>✅ Successfully returned $returned_count specific loans.</p>";
        } else {
            echo "<p>⚠️ No suitable loans found to return (all members may have only 1 loan each).</p>";
            
            // Alternative: Return some oldest borrowed loans
            $oldest_loans = $pdo->query("
                SELECT id 
                FROM book_loans 
                WHERE status = 'borrowed'
                ORDER BY issue_date ASC
                LIMIT $difference
            ")->fetchAll(PDO::FETCH_COLUMN);
            
            if (!empty($oldest_loans)) {
                $loan_ids = implode(',', $oldest_loans);
                $returned_count = $pdo->exec("
                    UPDATE book_loans 
                    SET status = 'returned', return_date = CURDATE()
                    WHERE id IN ($loan_ids)
                ");
                
                echo "<p>✅ Returned $returned_count oldest loans as alternative.</p>";
            }
        }
        
        // Update book availability
        $books = $pdo->query("SELECT id, quantity FROM books")->fetchAll();
        
        foreach ($books as $book) {
            $active_for_book = $pdo->prepare("SELECT COUNT(*) as count FROM book_loans WHERE book_id = ? AND status IN ('borrowed', 'overdue')");
            $active_for_book->execute([$book['id']]);
            $active_count = $active_for_book->fetch()['count'];
            
            $available = max(0, $book['quantity'] - $active_count);
            
            $update_available = $pdo->prepare("UPDATE books SET available_quantity = ? WHERE id = ?");
            $update_available->execute([$available, $book['id']]);
        }
        
        echo "<p>✅ Updated book availability.</p>";
    }
    
    // FINAL VERIFICATION
    echo "<h3>🎯 FINAL VERIFICATION:</h3>";
    
    $final_stats = [
        'total_books' => $pdo->query("SELECT COUNT(*) as count FROM books")->fetch()['count'],
        'total_copies' => $pdo->query("SELECT SUM(quantity) as total FROM books")->fetch()['total'],
        'available_copies' => $pdo->query("SELECT SUM(available_quantity) as available FROM books")->fetch()['available'],
        'total_members' => $pdo->query("SELECT COUNT(*) as count FROM members")->fetch()['count'],
        'active_loans' => $pdo->query("SELECT COUNT(*) as count FROM book_loans WHERE status IN ('borrowed', 'overdue')")->fetch()['count'],
        'borrowed_loans' => $pdo->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed'")->fetch()['count'],
        'overdue_loans' => $pdo->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'overdue'")->fetch()['count'],
        'currently_borrowing' => $pdo->query("SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status IN ('borrowed', 'overdue')")->fetch()['count']
    ];
    
    $perfect_match = $final_stats['active_loans'] == $final_stats['currently_borrowing'];
    $new_difference = $final_stats['active_loans'] - $final_stats['currently_borrowing'];
    
    echo "<div style='background: " . ($perfect_match ? '#d4edda' : '#fff3cd') . "; padding: 20px; border-radius: 10px; border: 2px solid " . ($perfect_match ? '#28a745' : '#ffc107') . ";'>";
    echo "<h4>📊 FINAL DASHBOARD STATUS</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th style='padding: 10px; background: " . ($perfect_match ? '#28a745' : '#ffc107') . "; color: white;'>Metric</th><th style='padding: 10px; background: " . ($perfect_match ? '#28a745' : '#ffc107') . "; color: white;'>Value</th></tr>";
    
    echo "<tr><td style='padding: 8px;'>Total Books</td><td style='padding: 8px;'>{$final_stats['total_books']}</td></tr>";
    echo "<tr><td style='padding: 8px;'>Total Copies</td><td style='padding: 8px;'>{$final_stats['total_copies']}</td></tr>";
    echo "<tr><td style='padding: 8px;'>Available Copies</td><td style='padding: 8px;'>{$final_stats['available_copies']}</td></tr>";
    echo "<tr><td style='padding: 8px;'>Total Members</td><td style='padding: 8px;'>{$final_stats['total_members']}</td></tr>";
    echo "<tr style='background: #ffffcc;'><td style='padding: 8px;'><strong>Active Loans</strong></td><td style='padding: 8px;'><strong>{$final_stats['active_loans']}</strong></td></tr>";
    echo "<tr style='background: #ffffcc;'><td style='padding: 8px;'><strong>Currently Borrowing</strong></td><td style='padding: 8px;'><strong>{$final_stats['currently_borrowing']}</strong></td></tr>";
    echo "<tr><td style='padding: 8px;'>Difference</td><td style='padding: 8px;'><strong>$new_difference</strong></td></tr>";
    echo "<tr><td style='padding: 8px;'>Borrowed Loans</td><td style='padding: 8px;'>{$final_stats['borrowed_loans']}</td></tr>";
    echo "<tr><td style='padding: 8px;'>Overdue Loans</td><td style='padding: 8px;'>{$final_stats['overdue_loans']}</td></tr>";
    echo "</table>";
    echo "</div>";
    
    // Final status message
    if ($perfect_match) {
        echo "<h3 style='color: green; text-align: center; font-size: 24px;'>🎉 PERFECT BALANCE ACHIEVED! 🎉</h3>";
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; border: 2px solid #28a745; text-align: center;'>";
        echo "<h4 style='color: #155724;'>✅ Active Loans = Currently Borrowing Members</h4>";
        echo "<p style='color: #155724; font-size: 16px;'>Your dashboard calculations are now perfectly synchronized!</p>";
        echo "<p style='color: #0066cc; font-size: 18px;'>🔄 <strong>Please refresh your admin dashboard to see the perfect results!</strong></p>";
        echo "</div>";
    } else {
        echo "<h3 style='color: orange;'>📊 Progress Report</h3>";
        echo "<p style='color: orange;'>Remaining difference: <strong>$new_difference</strong></p>";
        
        if ($new_difference < 50) {
            echo "<p style='color: green;'>✅ Great progress! The difference is now much smaller.</p>";
            echo "<p style='color: blue;'>This small difference might be acceptable for a realistic LMS system.</p>";
        }
        
        echo "<p style='color: blue;'>🔄 <strong>Refresh your dashboard to see the improved balance!</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
