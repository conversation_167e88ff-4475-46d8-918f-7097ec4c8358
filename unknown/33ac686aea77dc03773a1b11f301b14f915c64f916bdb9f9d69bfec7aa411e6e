<?php
/**
 * User Access Management
 *
 * This page allows administrators to generate direct access links for users.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is an admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(url('login.php'));
    exit;
}

// Initialize variables
$success = '';
$error = '';
$members = [];
$staff = [];

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get all active members
$query = "SELECT id, first_name, last_name, email, membership_date FROM members WHERE membership_status = 'active' ORDER BY first_name, last_name";
$stmt = $db->prepare($query);
$stmt->execute();
$members = $stmt->fetchAll();

// Get all staff users
$query = "SELECT id, username, email, role, full_name FROM users ORDER BY role, username";
$stmt = $db->prepare($query);
$stmt->execute();
$staff = $stmt->fetchAll();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// Function to generate a direct access link
function generateDirectAccessLink($email, $type) {
    return url('direct_user_access.php?email=' . urlencode($email) . '&type=' . urlencode($type));
}

// Function to generate a direct Google link
function generateDirectGoogleLink($email) {
    return url('direct_google_login.php?email=' . urlencode($email));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Access Management - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .access-link {
            word-break: break-all;
            font-size: 0.85rem;
        }
        .copy-btn {
            cursor: pointer;
        }
        .copy-btn:hover {
            color: #0d6efd;
        }
        .user-table th {
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <?php include 'includes/admin_navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/admin_sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-link me-2"></i>User Access Management</h1>
                </div>
                
                <?php if (!empty($success)): ?>
                    <div class="alert alert-success">
                        <?php echo h($success); ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <?php echo h($error); ?>
                    </div>
                <?php endif; ?>
                
                <div class="alert alert-info">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    <strong>About Direct Access Links</strong>
                    <p class="mb-0">These links allow users to access their accounts directly without entering their password. Share these links only with the intended users.</p>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-people me-2"></i>Member Access Links</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover user-table">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Membership Date</th>
                                        <th>Direct Access Link</th>
                                        <th>Google Link</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($members as $member): ?>
                                        <tr>
                                            <td><?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></td>
                                            <td><?php echo h($member['email']); ?></td>
                                            <td><?php echo h($member['membership_date']); ?></td>
                                            <td>
                                                <?php $direct_link = generateDirectAccessLink($member['email'], 'member'); ?>
                                                <div class="d-flex align-items-center">
                                                    <span class="access-link me-2"><?php echo h($direct_link); ?></span>
                                                    <i class="bi bi-clipboard copy-btn" data-link="<?php echo h($direct_link); ?>" title="Copy to clipboard"></i>
                                                </div>
                                            </td>
                                            <td>
                                                <?php $google_link = generateDirectGoogleLink($member['email']); ?>
                                                <div class="d-flex align-items-center">
                                                    <span class="access-link me-2"><?php echo h($google_link); ?></span>
                                                    <i class="bi bi-clipboard copy-btn" data-link="<?php echo h($google_link); ?>" title="Copy to clipboard"></i>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-person-badge me-2"></i>Staff Access Links</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover user-table">
                                <thead>
                                    <tr>
                                        <th>Username</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Direct Access Link</th>
                                        <th>Google Link</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($staff as $user): ?>
                                        <tr>
                                            <td><?php echo h($user['username']); ?></td>
                                            <td><?php echo h(!empty($user['full_name']) ? $user['full_name'] : '-'); ?></td>
                                            <td><?php echo h($user['email']); ?></td>
                                            <td><span class="badge bg-<?php echo $user['role'] === 'admin' ? 'danger' : 'primary'; ?>"><?php echo h(ucfirst($user['role'])); ?></span></td>
                                            <td>
                                                <?php $direct_link = generateDirectAccessLink($user['email'], $user['role']); ?>
                                                <div class="d-flex align-items-center">
                                                    <span class="access-link me-2"><?php echo h($direct_link); ?></span>
                                                    <i class="bi bi-clipboard copy-btn" data-link="<?php echo h($direct_link); ?>" title="Copy to clipboard"></i>
                                                </div>
                                            </td>
                                            <td>
                                                <?php $google_link = generateDirectGoogleLink($user['email']); ?>
                                                <div class="d-flex align-items-center">
                                                    <span class="access-link me-2"><?php echo h($google_link); ?></span>
                                                    <i class="bi bi-clipboard copy-btn" data-link="<?php echo h($google_link); ?>" title="Copy to clipboard"></i>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Copy to clipboard functionality
        document.addEventListener('DOMContentLoaded', function() {
            const copyButtons = document.querySelectorAll('.copy-btn');
            
            copyButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const link = this.getAttribute('data-link');
                    navigator.clipboard.writeText(link).then(() => {
                        // Change icon temporarily to show success
                        const originalClass = this.className;
                        this.className = 'bi bi-check-circle text-success copy-btn';
                        
                        setTimeout(() => {
                            this.className = originalClass;
                        }, 1500);
                    });
                });
            });
        });
    </script>
</body>
</html>
