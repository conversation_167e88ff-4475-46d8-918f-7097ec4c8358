<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is librarian
if (!isLoggedIn() || !isLibrarian()) {
    redirect('../login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$records_per_page = 20;
$offset = ($page - 1) * $records_per_page;

// Filtering
$action_filter = isset($_GET['action']) ? $_GET['action'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';

// Build query - for librarian, only show relevant activities
$query = "SELECT al.*, u.username
          FROM activity_log al
          LEFT JOIN users u ON al.user_id = u.id
          WHERE 1=1";
$params = [];

// Add filter for current librarian's activities or general library activities
$query .= " AND (al.user_id = :current_user_id OR al.entity_type IN ('book', 'member', 'loan', 'reservation'))";
$params[':current_user_id'] = $_SESSION['user_id'];

if (!empty($action_filter)) {
    $query .= " AND al.action = :action";
    $params[':action'] = $action_filter;
}

if (!empty($date_from)) {
    $query .= " AND DATE(al.timestamp) >= :date_from";
    $params[':date_from'] = $date_from;
}

if (!empty($date_to)) {
    $query .= " AND DATE(al.timestamp) <= :date_to";
    $params[':date_to'] = $date_to;
}

// Count total records for pagination
$count_query = str_replace("al.*, u.username", "COUNT(*) as total", $query);
$stmt = $db->prepare($count_query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$total_records = $stmt->fetch()['total'];
$total_pages = ceil($total_records / $records_per_page);

// Get activities with pagination
$query .= " ORDER BY al.timestamp DESC LIMIT :offset, :limit";
$stmt = $db->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':limit', $records_per_page, PDO::PARAM_INT);
$stmt->execute();
$activities = $stmt->fetchAll();

// Get all action types for filter dropdown
$query = "SELECT DISTINCT action FROM activity_log
          WHERE user_id = :user_id OR entity_type IN ('book', 'member', 'loan', 'reservation')
          ORDER BY action";
$stmt = $db->prepare($query);
$stmt->bindValue(':user_id', $_SESSION['user_id']);
$stmt->execute();
$actions = $stmt->fetchAll();

// Log view activity
logActivity($db, 'view', 'Viewed activity log');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activity Log - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        /* Notification styles */
        .notification-dropdown {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: none;
            border-radius: 0.5rem;
        }

        .notification-item {
            transition: background-color 0.2s;
        }

        .notification-item:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .notification-item p {
            font-size: 0.85rem;
            line-height: 1.4;
        }

        .notification-item small {
            font-size: 0.75rem;
        }

        .notification-item .bi {
            opacity: 0.8;
        }

        .notification-item.unread {
            background-color: rgba(13, 110, 253, 0.05);
        }

        .notification-item.unread p {
            font-weight: 500;
        }

        @media (max-width: 767.98px) {
            .notification-dropdown {
                width: 280px !important;
            }
        }
    </style>
</head>
<body>
    <header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="#">Library MS Librarian</a>
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="w-100"></div>
        <div class="navbar-nav">
            <div class="nav-item text-nowrap d-flex align-items-center">
                <!-- Notification Bell -->
                <div class="dropdown me-2">
                    <button class="btn btn-sm btn-outline-light position-relative" type="button" id="notificationDropdown" data-bs-toggle="dropdown" aria-expanded="false" title="Notifications">
                        <i class="bi bi-bell"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            2 <span class="visually-hidden">unread notifications</span>
                        </span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end notification-dropdown p-0" aria-labelledby="notificationDropdown" style="width: 320px; max-height: 400px; overflow-y: auto;">
                        <div class="d-flex justify-content-between align-items-center bg-dark text-white p-2">
                            <h6 class="mb-0">Recent Notifications</h6>
                            <span class="badge bg-danger">2 new</span>
                        </div>
                        <div class="notification-list">
                            <!-- Info notification -->
                            <div class="notification-item p-2 border-bottom">
                                <div class="d-flex">
                                    <div class="flex-shrink-0 me-2">
                                        <i class="bi bi-info-circle text-primary fs-5"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between">
                                            <p class="mb-0 fw-bold">Welcome to the new Library Management System dashboard!</p>
                                            <small class="text-muted ms-2">May 21, 10:14 am</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Warning notification -->
                            <div class="notification-item p-2 border-bottom">
                                <div class="d-flex">
                                    <div class="flex-shrink-0 me-2">
                                        <i class="bi bi-exclamation-triangle text-warning fs-5"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between">
                                            <p class="mb-0 fw-bold">There are 3 books overdue today. Please check the overdue books report.</p>
                                            <small class="text-muted ms-2">May 21, 10:14 am</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Info notification -->
                            <div class="notification-item p-2 border-bottom">
                                <div class="d-flex">
                                    <div class="flex-shrink-0 me-2">
                                        <i class="bi bi-info-circle text-primary fs-5"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between">
                                            <p class="mb-0 fw-bold">New feature: You can now export reports to Excel and PDF formats.</p>
                                            <small class="text-muted ms-2">May 21, 10:14 am</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-center p-2 bg-light">
                            <a href="../notifications/index.php" class="btn btn-sm btn-primary me-2">
                                <i class="bi bi-bell me-1"></i> View All Notifications
                            </a>
                            <button class="btn btn-sm btn-secondary">
                                <i class="bi bi-check-all me-1"></i> Mark All as Read
                            </button>
                        </div>
                    </div>
                </div>
                <span class="nav-link px-3 text-white">Welcome, Librarian</span>
                <a class="btn btn-danger btn-sm mx-2" href="../logout.php">
                    <i class="bi bi-box-arrow-right me-1"></i>Sign out
                </a>
            </div>
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="bi bi-speedometer2 me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../books/index.php">
                                <i class="bi bi-book me-2"></i>
                                Books
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../members/index.php">
                                <i class="bi bi-people me-2"></i>
                                Members
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../loans/index.php">
                                <i class="bi bi-journal-arrow-up me-2"></i>
                                Book Loans
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/index.php">
                                <i class="bi bi-file-earmark-bar-graph me-2"></i>
                                Reports
                            </a>
                        </li>
                    </ul>

                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>Librarian Tasks</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="issue_book.php">
                                <i class="bi bi-arrow-right-circle me-2"></i>
                                Issue Book
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="return_book.php">
                                <i class="bi bi-arrow-left-circle me-2"></i>
                                Return Book
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="manage_reservations.php">
                                <i class="bi bi-bookmark me-2"></i>
                                Manage Reservations
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="activity_log.php">
                                <i class="bi bi-list-check me-2"></i>
                                Activity Log
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Activity Log</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button id="darkModeToggle" class="btn btn-sm btn-outline-secondary" title="Toggle Dark Mode">
                            <i id="darkModeIcon" class="bi bi-moon"></i>
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-funnel me-2"></i> Filter Activities</h5>
                    </div>
                    <div class="card-body">
                        <form method="get" action="" class="row g-3">
                            <div class="col-md-4">
                                <label for="action" class="form-label">Action Type</label>
                                <select name="action" id="action" class="form-select">
                                    <option value="">All Actions</option>
                                    <?php foreach ($actions as $action): ?>
                                        <option value="<?php echo h($action['action']); ?>" <?php echo $action_filter === $action['action'] ? 'selected' : ''; ?>>
                                            <?php echo h(ucfirst($action['action'])); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="date_from" class="form-label">Date From</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo h($date_from); ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="date_to" class="form-label">Date To</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo h($date_to); ?>">
                            </div>
                            <div class="col-12 text-end">
                                <a href="activity_log.php" class="btn btn-secondary me-2">Reset</a>
                                <button type="submit" class="btn btn-primary">Apply Filters</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Activity Log Table -->
                <div class="card mb-4">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i> Activity Log</h5>
                        <span class="badge bg-primary"><?php echo $total_records; ?> activities</span>
                    </div>
                    <div class="card-body">
                        <?php if (empty($activities)): ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i> No activities found matching your criteria.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date & Time</th>
                                            <th>User</th>
                                            <th>Action</th>
                                            <th>Description</th>
                                            <th>IP Address</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($activities as $activity): ?>
                                            <tr>
                                                <td><?php echo formatDateTime($activity['timestamp']); ?></td>
                                                <td><?php echo h($activity['username'] ?? 'System'); ?></td>
                                                <td>
                                                    <?php if ($activity['action'] === 'login'): ?>
                                                        <span class="badge bg-success">Login</span>
                                                    <?php elseif ($activity['action'] === 'logout'): ?>
                                                        <span class="badge bg-secondary">Logout</span>
                                                    <?php elseif ($activity['action'] === 'add'): ?>
                                                        <span class="badge bg-primary">Add</span>
                                                    <?php elseif ($activity['action'] === 'edit'): ?>
                                                        <span class="badge bg-warning text-dark">Edit</span>
                                                    <?php elseif ($activity['action'] === 'delete'): ?>
                                                        <span class="badge bg-danger">Delete</span>
                                                    <?php elseif ($activity['action'] === 'view'): ?>
                                                        <span class="badge bg-info text-dark">View</span>
                                                    <?php elseif ($activity['action'] === 'issue'): ?>
                                                        <span class="badge bg-primary">Issue</span>
                                                    <?php elseif ($activity['action'] === 'return'): ?>
                                                        <span class="badge bg-success">Return</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-dark"><?php echo h(ucfirst($activity['action'])); ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo h($activity['description']); ?></td>
                                                <td><?php echo h($activity['ip_address']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                                <nav aria-label="Activity log pagination">
                                    <ul class="pagination justify-content-center mt-4">
                                        <?php if ($page > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=1<?php echo !empty($action_filter) ? '&action=' . h($action_filter) : ''; ?><?php echo !empty($date_from) ? '&date_from=' . h($date_from) : ''; ?><?php echo !empty($date_to) ? '&date_to=' . h($date_to) : ''; ?>" aria-label="First">
                                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                                </a>
                                            </li>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($action_filter) ? '&action=' . h($action_filter) : ''; ?><?php echo !empty($date_from) ? '&date_from=' . h($date_from) : ''; ?><?php echo !empty($date_to) ? '&date_to=' . h($date_to) : ''; ?>" aria-label="Previous">
                                                    <span aria-hidden="true">&laquo;</span>
                                                </a>
                                            </li>
                                        <?php endif; ?>

                                        <?php
                                        $start_page = max(1, $page - 2);
                                        $end_page = min($total_pages, $page + 2);

                                        for ($i = $start_page; $i <= $end_page; $i++):
                                        ?>
                                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($action_filter) ? '&action=' . h($action_filter) : ''; ?><?php echo !empty($date_from) ? '&date_from=' . h($date_from) : ''; ?><?php echo !empty($date_to) ? '&date_to=' . h($date_to) : ''; ?>">
                                                    <?php echo $i; ?>
                                                </a>
                                            </li>
                                        <?php endfor; ?>

                                        <?php if ($page < $total_pages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($action_filter) ? '&action=' . h($action_filter) : ''; ?><?php echo !empty($date_from) ? '&date_from=' . h($date_from) : ''; ?><?php echo !empty($date_to) ? '&date_to=' . h($date_to) : ''; ?>" aria-label="Next">
                                                    <span aria-hidden="true">&raquo;</span>
                                                </a>
                                            </li>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $total_pages; ?><?php echo !empty($action_filter) ? '&action=' . h($action_filter) : ''; ?><?php echo !empty($date_from) ? '&date_from=' . h($date_from) : ''; ?><?php echo !empty($date_to) ? '&date_to=' . h($date_to) : ''; ?>" aria-label="Last">
                                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo url('assets/js/dark-mode.js'); ?>"></script>

    <script>
    // Handle notifications
    document.addEventListener('DOMContentLoaded', function() {
        const markAllAsReadBtn = document.querySelector('.notification-dropdown .btn-secondary');
        if (markAllAsReadBtn) {
            markAllAsReadBtn.addEventListener('click', function() {
                // Mark all notifications as read
                const notificationItems = document.querySelectorAll('.notification-item');
                notificationItems.forEach(item => {
                    item.classList.remove('unread');
                });

                // Update notification badge
                const badge = document.querySelector('#notificationDropdown .badge');
                if (badge) {
                    badge.textContent = '0';
                    badge.classList.add('d-none');
                }

                // Update header badge
                const headerBadge = document.querySelector('.notification-dropdown .badge.bg-danger');
                if (headerBadge) {
                    headerBadge.textContent = '0 new';
                }

                // In a real application, you would send an AJAX request to mark notifications as read in the database
                // For example:
                // fetch('../notifications/mark_all_read.php', {
                //     method: 'POST',
                //     headers: {
                //         'Content-Type': 'application/json',
                //     },
                //     body: JSON.stringify({
                //         user_id: <?php echo $_SESSION['user_id']; ?>
                //     })
                // })
                // .then(response => response.json())
                // .then(data => {
                //     console.log('Success:', data);
                // })
                // .catch((error) => {
                //     console.error('Error:', error);
                // });
            });
        }
    });
    </script>
</body>
</html>
