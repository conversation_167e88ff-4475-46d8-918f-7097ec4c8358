# 📚 Library Management System - Access Guide

## 🚀 Quick Start

### 1. **First Time Setup**
1. **Start your web server** (XAMPP, WAMP, or similar)
2. **Access the system** via: `http://localhost/Library/lms/access.php`
3. **Run database setup**: Click "Database Setup" button
4. **Create admin user**: Click "Create Admin" button

### 2. **System Access Points**

#### 🏠 **Main Entry Points**
- **Home Page**: `http://localhost/Library/lms/`
- **Access Portal**: `http://localhost/Library/lms/access.php`
- **Login Page**: `http://localhost/Library/lms/login.php`

#### 👨‍💼 **Admin Access**
- **Admin Dashboard**: `http://localhost/Library/lms/admin/dashboard.php`
- **Manage Books**: `http://localhost/Library/lms/books/index.php`
- **Manage Members**: `http://localhost/Library/lms/members/index.php`
- **System Settings**: `http://localhost/Library/lms/admin/settings.php`

#### 👥 **Member Access**
- **Member Dashboard**: `http://localhost/Library/lms/member_dashboard.php`
- **Book Catalog**: `http://localhost/Library/lms/catalog.php`
- **Member Registration**: `http://localhost/Library/lms/register.php`

## 🔧 System Setup & Maintenance

### **Database Setup**
1. **Automatic Setup**: `http://localhost/Library/lms/setup.php`
2. **Manual Setup**: `http://localhost/Library/lms/setup_database.php`
3. **Database Verification**: `http://localhost/Library/lms/verify_database.php`

### **Admin User Creation**
- **Create Admin**: `http://localhost/Library/lms/create_admin.php`
- **Default Admin**: Username: `admin`, Password: `admin123`

### **System Diagnostics**
- **Full Diagnostic**: `http://localhost/Library/lms/diagnostic.php`
- **System Fix**: `http://localhost/Library/lms/system_fix.php`
- **Database Status**: `http://localhost/Library/lms/database_status.php`

## 🔐 Default Login Credentials

### **Admin Access**
- **Username**: `admin`
- **Password**: `admin123`
- **Email**: `<EMAIL>`

### **Sample Members**
- **Email**: `<EMAIL>` (Password needs to be set)
- **Email**: `<EMAIL>` (Password needs to be set)

## 📋 System Features

### **For Administrators**
- ✅ Dashboard with statistics
- ✅ Book management (add, edit, delete)
- ✅ Member management
- ✅ Loan tracking
- ✅ Overdue book management
- ✅ Fine calculation
- ✅ Reports generation
- ✅ System settings

### **For Members**
- ✅ Book catalog browsing
- ✅ Book search and filtering
- ✅ Loan history
- ✅ Profile management
- ✅ Book reservations
- ✅ Fine tracking

### **For Librarians**
- ✅ Issue books
- ✅ Return books
- ✅ Member lookup
- ✅ Basic reporting

## 🛠️ Troubleshooting

### **Common Issues & Solutions**

#### **Database Connection Failed**
1. Check if XAMPP/WAMP is running
2. Verify MySQL service is started
3. Run: `http://localhost/Library/lms/system_fix.php`

#### **Admin Dashboard Not Accessible**
1. Create admin user: `http://localhost/Library/lms/create_admin.php`
2. Check authentication in `includes/functions.php`

#### **Missing Tables Error**
1. Run database setup: `http://localhost/Library/lms/setup.php`
2. Or manual setup: `http://localhost/Library/lms/setup_database.php`

#### **Styling Issues**
1. Check if CSS files exist in `assets/css/`
2. Verify Bootstrap CDN is accessible
3. Clear browser cache

### **System Health Check**
Run these URLs to check system health:
- `http://localhost/Library/lms/diagnostic.php`
- `http://localhost/Library/lms/system_fix.php`
- `http://localhost/Library/lms/verify_database.php`

## 📁 File Structure

```
lms/
├── admin/              # Admin panel
├── assets/             # CSS, JS, Images
├── auth/               # Authentication
├── books/              # Book management
├── config/             # Configuration files
├── database/           # Database scripts
├── includes/           # Common includes
├── members/            # Member management
├── uploads/            # File uploads
├── access.php          # System access portal
├── index.php           # Main entry point
├── login.php           # Login page
└── setup.php           # Database setup
```

## 🔄 Regular Maintenance

### **Daily Tasks**
- Check overdue books
- Process returns
- Update member status

### **Weekly Tasks**
- Generate reports
- Backup database
- Check system logs

### **Monthly Tasks**
- Update book inventory
- Review member accounts
- System performance check

## 📞 Support

If you encounter issues:
1. Check the **Access Portal**: `http://localhost/Library/lms/access.php`
2. Run **System Diagnostic**: `http://localhost/Library/lms/diagnostic.php`
3. Use **System Fix**: `http://localhost/Library/lms/system_fix.php`

## 🎯 Quick Actions

| Action | URL |
|--------|-----|
| **Start Here** | `/access.php` |
| **Login** | `/login.php` |
| **Admin Panel** | `/admin/dashboard.php` |
| **Setup Database** | `/setup.php` |
| **Create Admin** | `/create_admin.php` |
| **System Check** | `/diagnostic.php` |
| **Fix Issues** | `/system_fix.php` |

---

**📝 Note**: Replace `localhost/Library/lms/` with your actual server path if different.
