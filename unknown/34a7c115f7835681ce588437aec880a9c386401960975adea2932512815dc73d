<?php
// Test file to verify admin URL access
echo "<h1>Admin URL Test</h1>";
echo "<p><strong>Current URL:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p><strong>Script Name:</strong> " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";

echo "<h2>File System Check</h2>";
echo "<p><strong>Admin directory exists:</strong> " . (is_dir('admin') ? 'Yes' : 'No') . "</p>";
echo "<p><strong>Admin dashboard exists:</strong> " . (file_exists('admin/dashboard.php') ? 'Yes' : 'No') . "</p>";

echo "<h2>Correct URLs to Try</h2>";
echo "<ul>";
echo "<li><a href='admin/dashboard.php'>admin/dashboard.php</a></li>";
echo "<li><a href='admin/index.php'>admin/index.php</a></li>";
echo "<li><a href='admin/'>admin/</a></li>";
echo "</ul>";

echo "<h2>System Information</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server Software:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";

// Test database connection
echo "<h2>Database Connection Test</h2>";
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    echo "<p style='color: green;'>✅ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
}
?>
