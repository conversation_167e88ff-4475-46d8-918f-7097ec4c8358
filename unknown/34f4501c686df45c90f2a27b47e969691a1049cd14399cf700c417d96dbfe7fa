<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h2>Member Access Debug Information</h2>";

// Check session data
echo "<h3>Session Data:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Check if member is logged in
echo "<h3>Member Login Status:</h3>";
if (isMemberLoggedIn()) {
    echo "✅ Member is logged in<br>";
    echo "Member ID: " . $_SESSION['member_id'] . "<br>";
    echo "Member Name: " . $_SESSION['member_name'] . "<br>";
    echo "Member Email: " . $_SESSION['member_email'] . "<br>";
} else {
    echo "❌ Member is NOT logged in<br>";
}

// Check database connection
echo "<h3>Database Connection:</h3>";
try {
    $database = new Database();
    $db = $database->getConnection();
    echo "✅ Database connection successful<br>";
    
    // Check members table
    $query = "SELECT COUNT(*) as total FROM members WHERE membership_status = 'active'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch();
    echo "Active members in database: " . $result['total'] . "<br>";
    
    // Show first few members
    $query = "SELECT id, first_name, last_name, email FROM members WHERE membership_status = 'active' LIMIT 5";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $members = $stmt->fetchAll();
    
    echo "<h4>Sample Active Members:</h4>";
    foreach ($members as $member) {
        echo "ID: {$member['id']}, Name: {$member['first_name']} {$member['last_name']}, Email: {$member['email']}<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
}

// Check file paths
echo "<h3>File Paths:</h3>";
echo "Current directory: " . __DIR__ . "<br>";
echo "Member dashboard exists: " . (file_exists('member_dashboard.php') ? '✅ Yes' : '❌ No') . "<br>";
echo "Member login exists: " . (file_exists('member_login.php') ? '✅ Yes' : '❌ No') . "<br>";

// Check URL configuration
echo "<h3>URL Configuration:</h3>";
echo "Base URL: " . BASE_URL . "<br>";
echo "Current URL: " . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'Not available') . "<br>";

echo "<h3>Quick Actions:</h3>";
echo "<a href='member_login.php' class='btn btn-primary'>Go to Member Login</a> ";
echo "<a href='member_dashboard.php' class='btn btn-success'>Try Member Dashboard</a> ";
echo "<a href='direct_member_login.php' class='btn btn-warning'>Direct Member Login</a>";

?>
<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.btn { padding: 10px 15px; margin: 5px; text-decoration: none; color: white; border-radius: 5px; display: inline-block; }
.btn-primary { background-color: #007bff; }
.btn-success { background-color: #28a745; }
.btn-warning { background-color: #ffc107; color: black; }
</style>
