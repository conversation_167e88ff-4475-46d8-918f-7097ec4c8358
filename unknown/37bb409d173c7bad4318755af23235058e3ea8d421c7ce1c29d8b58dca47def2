<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Update Book Covers</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h2>Update Book Covers</h2>";

// Get all books
$query = "SELECT * FROM books ORDER BY id";
$stmt = $db->prepare($query);
$stmt->execute();
$books = $stmt->fetchAll();

// Get all cover images from the covers folder
$covers_dir = 'uploads/covers/';
$cover_files = [];
if (is_dir($covers_dir)) {
    $files = scandir($covers_dir);
    foreach ($files as $file) {
        if ($file != '.' && $file != '..' && in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif'])) {
            $cover_files[] = $file;
        }
    }
}

echo "<p>Found " . count($books) . " books and " . count($cover_files) . " cover images.</p>";

// Manual mapping for specific books
$book_cover_mapping = [
    'To Kill a Mockingbird' => '1984_cover.jpg', // You can update this
    '1984' => '1984_cover.jpg',
    'The Great Gatsby' => '1747725503_the-great-gatsby-and-other-works-9781645173519_hr.jpg',
    'Pride and Prejudice' => '1747724335_Pride and Prejudice.jpg',
    'A Game of Thrones' => '1747724281_AGameOfThrones.jpg',
    'Good to Great' => '1747724303_Good to Great.jpg',
    'Steve Jobs' => '1747725415_steve-jobs-9781451648553_hr.jpg',
    'The Alchemist' => '1747725431_the-alchemist-a-graphic-novel.jpg',
    'The Catcher in the Rye' => '1747725459_Book-Review-The-Catcher-in-the-Rye-by-J-D-Salinger.jpg',
    'The Martian' => '1747725673_The_Martian_(Weir_novel).jpg'
];

$updated_count = 0;

foreach ($books as $book) {
    $cover_image = null;
    
    // Check if we have a specific mapping for this book
    if (isset($book_cover_mapping[$book['title']])) {
        $cover_image = $book_cover_mapping[$book['title']];
    } else {
        // Try to find a cover image that matches the book title
        foreach ($cover_files as $file) {
            $filename_lower = strtolower($file);
            $title_lower = strtolower($book['title']);
            
            // Remove common words and special characters for better matching
            $title_clean = preg_replace('/[^a-z0-9]/', '', $title_lower);
            $filename_clean = preg_replace('/[^a-z0-9]/', '', $filename_lower);
            
            if (strpos($filename_clean, $title_clean) !== false || strpos($title_clean, $filename_clean) !== false) {
                $cover_image = $file;
                break;
            }
        }
    }
    
    // Update the book if we found a cover image
    if ($cover_image && file_exists($covers_dir . $cover_image)) {
        $update_query = "UPDATE books SET cover_image = :cover_image WHERE id = :id";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':cover_image', $cover_image);
        $update_stmt->bindParam(':id', $book['id']);
        
        if ($update_stmt->execute()) {
            echo "<div class='alert alert-success'>✅ Updated '{$book['title']}' with cover: {$cover_image}</div>";
            $updated_count++;
        } else {
            echo "<div class='alert alert-danger'>❌ Failed to update '{$book['title']}'</div>";
        }
    } else {
        echo "<div class='alert alert-warning'>⚠️ No cover found for '{$book['title']}'</div>";
    }
}

echo "<div class='alert alert-info mt-4'>
    <h4>Summary:</h4>
    <p>Updated {$updated_count} books with cover images.</p>
    <p><a href='home.php' class='btn btn-primary'>View Home Page</a></p>
    <p><a href='catalog.php' class='btn btn-secondary'>View Book Catalog</a></p>
</div>";

echo "</div></body></html>";
?>
