<?php
/**
 * Security Hardening Script
 * Implements security best practices and protections
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

$security_measures = [];
$errors = [];

// Function to create .htaccess file with security rules
function createSecureHtaccess() {
    $htaccess_content = '
# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<FilesMatch "\.(sql|log|md|txt|conf|ini)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to config and includes directories
<DirectoryMatch "^.*(config|includes|database).*$">
    Order allow,deny
    Deny from all
</DirectoryMatch>

# Prevent PHP execution in uploads directory
<Directory "uploads">
    <FilesMatch "\.php$">
        Order allow,deny
        Deny from all
    </FilesMatch>
</Directory>

# Disable server signature
ServerSignature Off

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>
';

    return file_put_contents('.htaccess', $htaccess_content) !== false;
}

// Function to create security functions
function createSecurityFunctions() {
    $security_functions = '<?php
/**
 * Security Functions
 * Additional security utilities for the LMS
 */

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION[\'csrf_token\'])) {
        $_SESSION[\'csrf_token\'] = bin2hex(random_bytes(32));
    }
    return $_SESSION[\'csrf_token\'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION[\'csrf_token\']) && hash_equals($_SESSION[\'csrf_token\'], $token);
}

/**
 * Rate limiting
 */
function checkRateLimit($action, $limit = 5, $window = 300) {
    $key = $action . \'_\' . $_SERVER[\'REMOTE_ADDR\'];
    
    if (!isset($_SESSION[\'rate_limit\'])) {
        $_SESSION[\'rate_limit\'] = [];
    }
    
    $now = time();
    
    // Clean old entries
    foreach ($_SESSION[\'rate_limit\'] as $k => $data) {
        if ($data[\'time\'] < ($now - $window)) {
            unset($_SESSION[\'rate_limit\'][$k]);
        }
    }
    
    // Count current attempts
    $attempts = 0;
    foreach ($_SESSION[\'rate_limit\'] as $data) {
        if (strpos($data[\'key\'], $key) === 0) {
            $attempts++;
        }
    }
    
    if ($attempts >= $limit) {
        return false;
    }
    
    // Record this attempt
    $_SESSION[\'rate_limit\'][] = [
        \'key\' => $key,
        \'time\' => $now
    ];
    
    return true;
}

/**
 * Input validation and sanitization
 */
function validateInput($input, $type = \'string\', $options = []) {
    switch ($type) {
        case \'email\':
            return filter_var($input, FILTER_VALIDATE_EMAIL);
        case \'int\':
            return filter_var($input, FILTER_VALIDATE_INT, $options);
        case \'float\':
            return filter_var($input, FILTER_VALIDATE_FLOAT, $options);
        case \'url\':
            return filter_var($input, FILTER_VALIDATE_URL);
        case \'string\':
        default:
            return htmlspecialchars(trim($input), ENT_QUOTES, \'UTF-8\');
    }
}

/**
 * Session security
 */
function secureSession() {
    // Regenerate session ID periodically
    if (!isset($_SESSION[\'last_regeneration\'])) {
        $_SESSION[\'last_regeneration\'] = time();
    } elseif (time() - $_SESSION[\'last_regeneration\'] > 300) {
        session_regenerate_id(true);
        $_SESSION[\'last_regeneration\'] = time();
    }
    
    return true;
}

/**
 * Log security events
 */
function logSecurityEvent($event, $details = \'\') {
    if (!is_dir(\'logs\')) {
        mkdir(\'logs\', 0755, true);
    }
    $log_entry = date(\'Y-m-d H:i:s\') . \' - \' . $_SERVER[\'REMOTE_ADDR\'] . \' - \' . $event . \' - \' . $details . PHP_EOL;
    file_put_contents(\'logs/security.log\', $log_entry, FILE_APPEND | LOCK_EX);
}
?>';

    return file_put_contents('includes/security.php', $security_functions) !== false;
}

// Function to update database with security enhancements
function addSecurityTables($db) {
    $tables = [
        // Login attempts tracking
        "CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL,
            username VARCHAR(100),
            attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            success BOOLEAN DEFAULT FALSE,
            user_agent TEXT,
            INDEX idx_ip_time (ip_address, attempt_time)
        )",
        
        // Security logs
        "CREATE TABLE IF NOT EXISTS security_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_type VARCHAR(50) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_id INT NULL,
            details TEXT,
            severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_event_time (event_type, created_at),
            INDEX idx_ip_address (ip_address)
        )"
    ];
    
    $created = 0;
    foreach ($tables as $table) {
        try {
            $db->exec($table);
            $created++;
        } catch (Exception $e) {
            // Table might already exist
        }
    }
    
    return $created;
}

// Apply security measures
try {
    if (createSecureHtaccess()) {
        $security_measures[] = "Created secure .htaccess file with security headers and rules";
    } else {
        $errors[] = "Failed to create .htaccess file";
    }
    
    if (createSecurityFunctions()) {
        $security_measures[] = "Created security functions library";
    } else {
        $errors[] = "Failed to create security functions";
    }
    
    // Connect to database for security tables
    $database = new Database();
    $db = $database->getConnection();
    
    $tables_created = addSecurityTables($db);
    if ($tables_created > 0) {
        $security_measures[] = "Created $tables_created security-related database tables";
    }
    
} catch (Exception $e) {
    $errors[] = "Error applying security measures: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Hardening - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header bg-warning text-dark">
                        <h1 class="h4 mb-0"><i class="bi bi-shield-check me-2"></i>Security Hardening Applied</h1>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($security_measures)): ?>
                            <div class="alert alert-success">
                                <h2 class="h5"><i class="bi bi-check-circle me-2"></i>Security Measures Applied Successfully!</h2>
                                <ul class="mb-0">
                                    <?php foreach ($security_measures as $measure): ?>
                                        <li><?php echo htmlspecialchars($measure); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <h2 class="h5"><i class="bi bi-exclamation-triangle me-2"></i>Errors Encountered</h2>
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <div class="mt-4">
                            <h2 class="h5">Security Features Added:</h2>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Security headers (.htaccess)</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>CSRF protection functions</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Rate limiting</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Input validation</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Session security</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Security logging</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Login attempt tracking</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>File upload protection</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Directory access prevention</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>XSS protection</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h2 class="h5">Next Steps:</h2>
                            <div class="d-grid gap-2 d-md-flex">
                                <a href="performance_optimization.php" class="btn btn-info">
                                    <i class="bi bi-speedometer2 me-2"></i>Performance Optimization
                                </a>
                                <a href="system_health_check.php" class="btn btn-success">
                                    <i class="bi bi-heart-pulse me-2"></i>System Health Check
                                </a>
                                <a href="final_system_test.php" class="btn btn-primary">
                                    <i class="bi bi-check-all me-2"></i>Final System Test
                                </a>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="bi bi-house me-2"></i>Back to Home
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
