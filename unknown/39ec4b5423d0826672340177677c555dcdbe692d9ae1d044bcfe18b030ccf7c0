<?php
/**
 * Google Account Management
 *
 * This page allows users to manage their linked Google accounts.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/google_auth.php';

// Check if user is logged in
if (!isLoggedIn() && !isMemberLoggedIn()) {
    redirect(url('login.php'));
}

// Initialize variables
$success = '';
$error = '';
$user_data = [];
$has_google_account = false;

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get user data
if (isLoggedIn()) {
    // Staff user
    $user_id = $_SESSION['user_id'];
    $query = "SELECT * FROM users WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $user_id);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $user_data = $stmt->fetch();
        $has_google_account = !empty($user_data['google_id']);
    }
} elseif (isMemberLoggedIn()) {
    // Member
    $user_id = $_SESSION['member_id'];
    $query = "SELECT * FROM members WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $user_id);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $user_data = $stmt->fetch();
        $has_google_account = !empty($user_data['google_id']);
    }
}

// Process unlink request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'unlink') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $error = 'Security verification failed. Please try again.';
    } else {
        try {
            if (isLoggedIn()) {
                // Unlink Google account for staff
                $query = "UPDATE users SET google_id = NULL, google_token = NULL, google_picture = NULL WHERE id = :id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':id', $user_id);
                $stmt->execute();
            } elseif (isMemberLoggedIn()) {
                // Unlink Google account for member
                $query = "UPDATE members SET google_id = NULL, google_token = NULL, google_picture = NULL WHERE id = :id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':id', $user_id);
                $stmt->execute();
            }

            $success = 'Your Google account has been unlinked successfully.';
            $has_google_account = false;

            // Clear Google user from localStorage
            echo "<script>localStorage.removeItem('googleUser');</script>";
        } catch (Exception $e) {
            $error = 'An error occurred while unlinking your Google account. Please try again.';
            error_log('Google Account Unlink Error: ' . $e->getMessage());
        }
    }
}

// Generate CSRF token
$csrf_token = bin2hex(random_bytes(32));
$_SESSION['csrf_token'] = $csrf_token;

// Get Google login URL for linking
$google_login_url = getGoogleLoginUrl('google_account.php');

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// Get user name
$user_name = '';
if (isLoggedIn()) {
    $user_name = !empty($user_data['full_name']) ? $user_data['full_name'] : $user_data['username'];
} elseif (isMemberLoggedIn()) {
    $user_name = $_SESSION['member_name'];
}

// Get return URL based on user type
$return_url = '';
if (isAdmin()) {
    $return_url = 'admin/dashboard.php';
} elseif (isLibrarian()) {
    $return_url = 'librarian/dashboard.php';
} elseif (isMemberLoggedIn()) {
    $return_url = 'member_dashboard.php';
} else {
    $return_url = 'index.php';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Account Management - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            padding: 20px 0;
        }
        .account-container {
            max-width: 800px;
            margin: 40px auto;
        }
        .card {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-radius: 12px;
            overflow: hidden;
        }
        .card-header {
            background-color: #343a40;
            color: white;
            font-weight: bold;
            padding: 1.2rem;
        }
        .google-account-card {
            border: 1px solid #dadce0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #fff;
            transition: all 0.3s ease;
        }
        .google-account-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .google-logo {
            width: 24px;
            height: 24px;
            margin-right: 10px;
        }
        .btn-google {
            background-color: #ffffff;
            color: #757575;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-family: 'Roboto', sans-serif;
            font-weight: 500;
            font-size: 14px;
            padding: 10px 12px;
            position: relative;
            text-align: center;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        }
        .btn-google:hover {
            background-color: #f8f9fa;
            box-shadow: 0 2px 4px rgba(0,0,0,0.12);
            border-color: #c6c6c6;
            color: #3c4043;
        }
        .btn-google img {
            vertical-align: middle;
        }
        .account-picture {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <div class="account-container">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="bi bi-google me-2"></i>Google Account Management</h4>
                <a href="<?php echo url($return_url); ?>" class="btn btn-sm btn-outline-light">
                    <i class="bi bi-arrow-left me-1"></i> Back
                </a>
            </div>
            <div class="card-body p-4">
                <h5 class="card-title mb-4">Manage Your Google Account</h5>

                <?php if (!empty($success)): ?>
                    <div class="alert alert-success">
                        <?php echo h($success); ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <?php echo h($error); ?>
                    </div>
                <?php endif; ?>

                <div class="mb-4">
                    <p>Hello, <strong><?php echo h($user_name); ?></strong>!</p>
                    <p>Here you can manage your Google account connection for easy sign-in to the Library Management System.</p>
                </div>

                <?php if ($has_google_account): ?>
                    <div class="google-account-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <?php if (!empty($user_data['google_picture'])): ?>
                                    <img src="<?php echo h($user_data['google_picture']); ?>" alt="Profile" class="account-picture me-3">
                                <?php else: ?>
                                    <div class="account-picture bg-secondary d-flex align-items-center justify-content-center me-3">
                                        <i class="bi bi-person-fill text-white"></i>
                                    </div>
                                <?php endif; ?>
                                <div>
                                    <h6 class="mb-1">Google Account Connected</h6>
                                    <p class="text-muted mb-0"><?php echo h($user_data['email']); ?></p>
                                </div>
                            </div>
                            <form method="post" action="<?php echo h($_SERVER['PHP_SELF']); ?>">
                                <input type="hidden" name="csrf_token" value="<?php echo h($csrf_token); ?>">
                                <input type="hidden" name="action" value="unlink">
                                <button type="submit" class="btn btn-outline-danger btn-sm" onclick="return confirm('Are you sure you want to unlink your Google account?');">
                                    <i class="bi bi-link-break me-1"></i> Unlink
                                </button>
                            </form>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        <strong>What does this mean?</strong>
                        <p class="mb-0">Your Google account is linked to your library account. You can sign in using Google without entering your password.</p>
                    </div>
                <?php else: ?>
                    <div class="google-account-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="account-picture bg-light d-flex align-items-center justify-content-center me-3">
                                    <i class="bi bi-google text-secondary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">No Google Account Connected</h6>
                                    <p class="text-muted mb-0">Link your Google account for easy sign-in</p>
                                </div>
                            </div>
                            <a href="<?php echo h($google_login_url); ?>" class="btn btn-google">
                                <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="18" height="18" class="me-2">
                                <span>Link Google Account</span>
                            </a>
                        </div>
                    </div>

                    <!-- Direct Google Account Linking Form -->
                    <div class="card mt-4 mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="bi bi-link me-2"></i>Direct Google Account Linking</h6>
                        </div>
                        <div class="card-body">
                            <p class="card-text">Enter a Google email address to link directly to your account:</p>
                            <form action="direct_google_link.php" method="get" class="mt-3">
                                <div class="input-group mb-3">
                                    <input type="email" class="form-control" name="email" placeholder="<EMAIL>" required>
                                    <button class="btn btn-primary" type="submit">
                                        <i class="bi bi-link me-1"></i> Link Account
                                    </button>
                                </div>
                                <div class="form-text">
                                    This will link the specified Google account to your library account.
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        <strong>Why link your Google account?</strong>
                        <ul class="mb-0 mt-2">
                            <li>Sign in quickly without entering your password</li>
                            <li>Enhanced security with Google's protection</li>
                            <li>No need to remember another password</li>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
            <div class="card-footer text-center py-3">
                <p class="mb-0">Library Management System &copy; <?php echo date('Y'); ?></p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo url('assets/js/google-auth.js'); ?>"></script>
</body>
</html>
