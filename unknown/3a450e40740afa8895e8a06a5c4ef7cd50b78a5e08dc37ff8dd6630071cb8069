<?php
/**
 * Google Authentication Test
 *
 * This script tests the Google OAuth configuration.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/google_auth.php';

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// Get Google login URL
$google_login_url = getGoogleLoginUrl();
$is_configured = isGoogleOAuthConfigured();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth Test - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 40px auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            padding: 30px;
        }
        .code-block {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .status {
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="mb-4"><i class="bi bi-google me-2"></i>Google OAuth Test</h1>
        
        <div class="status <?php echo $is_configured ? 'status-success' : 'status-error'; ?>">
            <h4><i class="bi <?php echo $is_configured ? 'bi-check-circle-fill' : 'bi-exclamation-triangle-fill'; ?> me-2"></i>
                Configuration Status
            </h4>
            <p>
                <?php if ($is_configured): ?>
                    Google OAuth is properly configured.
                <?php else: ?>
                    Google OAuth is not configured. Please set up your Google OAuth credentials.
                <?php endif; ?>
            </p>
        </div>
        
        <h3>Configuration Details</h3>
        <div class="code-block">
            <p>Client ID: <?php echo !empty(GOOGLE_CLIENT_ID) ? substr(GOOGLE_CLIENT_ID, 0, 8) . '...' : 'Not set'; ?></p>
            <p>Client Secret: <?php echo !empty(GOOGLE_CLIENT_SECRET) ? substr(GOOGLE_CLIENT_SECRET, 0, 8) . '...' : 'Not set'; ?></p>
            <p>Redirect URI: <?php echo h(GOOGLE_REDIRECT_URI); ?></p>
        </div>
        
        <h3>Test Google Sign-In</h3>
        <?php if ($is_configured): ?>
            <p>Click the button below to test the Google Sign-In functionality:</p>
            <a href="<?php echo h($google_login_url); ?>" class="btn btn-primary">
                <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="20" height="20" class="me-2">
                <span>Sign in with Google</span>
            </a>
        <?php else: ?>
            <p>Please configure Google OAuth credentials first:</p>
            <a href="<?php echo url('google_setup.php'); ?>" class="btn btn-primary">
                <i class="bi bi-gear-fill me-2"></i>
                <span>Configure Google OAuth</span>
            </a>
        <?php endif; ?>
        
        <div class="mt-4">
            <h3>Debug Information</h3>
            <div class="code-block">
                <p>BASE_URL: <?php echo h(BASE_URL); ?></p>
                <p>Google Login URL: <?php echo h($google_login_url); ?></p>
                <p>PHP Version: <?php echo h(phpversion()); ?></p>
                <p>cURL Enabled: <?php echo function_exists('curl_init') ? 'Yes' : 'No'; ?></p>
                <p>Session Status: <?php echo session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive'; ?></p>
            </div>
        </div>
        
        <div class="mt-4 text-center">
            <a href="index.php" class="btn btn-outline-secondary">Back to Home</a>
            <a href="login.php" class="btn btn-outline-primary ms-2">Go to Login Page</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
