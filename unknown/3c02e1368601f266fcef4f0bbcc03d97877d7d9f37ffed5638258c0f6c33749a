<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Check Book Covers</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h2>Book Cover Status Check & Fix</h2>";

// Get all books with their cover images
$query = "SELECT id, title, author, cover_image FROM books ORDER BY id";
$stmt = $db->prepare($query);
$stmt->execute();
$books = $stmt->fetchAll();

echo "<h3>Books and their cover images:</h3>";
echo "<div class='table-responsive'>";
echo "<table class='table table-striped'>";
echo "<thead><tr><th>ID</th><th>Title</th><th>Author</th><th>Cover Image</th><th>File Exists</th><th>Preview</th></tr></thead>";
echo "<tbody>";

$books_without_covers = [];
foreach ($books as $book) {
    $cover_exists = false;
    $cover_path = '';

    if (!empty($book['cover_image'])) {
        $cover_path = 'uploads/covers/' . $book['cover_image'];
        $cover_exists = file_exists($cover_path);
    }

    if (!$cover_exists) {
        $books_without_covers[] = $book;
    }

    echo "<tr>";
    echo "<td>" . $book['id'] . "</td>";
    echo "<td>" . htmlspecialchars($book['title']) . "</td>";
    echo "<td>" . htmlspecialchars($book['author']) . "</td>";
    echo "<td>" . ($book['cover_image'] ? htmlspecialchars($book['cover_image']) : 'NULL') . "</td>";
    echo "<td>" . ($cover_exists ? '✅ Yes' : '❌ No') . "</td>";
    echo "<td>";

    if ($cover_exists) {
        echo "<img src='" . $cover_path . "' alt='" . htmlspecialchars($book['title']) . "' style='width: 50px; height: auto;'>";
    } else {
        echo "No image";
    }

    echo "</td>";
    echo "</tr>";
}

echo "</tbody></table>";
echo "</div>";

// Check covers directory
echo "<h3>Available cover files:</h3>";
$covers_dir = 'uploads/covers/';
$available_covers = [];
if (is_dir($covers_dir)) {
    $files = scandir($covers_dir);
    echo "<ul>";
    foreach ($files as $file) {
        if ($file != '.' && $file != '..' && in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif'])) {
            $available_covers[] = $file;
            echo "<li>" . htmlspecialchars($file) . " - <img src='" . $covers_dir . $file . "' style='width: 30px; height: auto;'></li>";
        }
    }
    echo "</ul>";
} else {
    echo "<p>Covers directory not found!</p>";
}

echo "<h3>Fix Book Covers</h3>";
echo "<p>Books without covers: " . count($books_without_covers) . "</p>";
echo "<p>Available cover files: " . count($available_covers) . "</p>";

if (isset($_POST['fix_covers'])) {
    // Specific book-to-cover mappings
    $book_cover_mapping = [
        '1984' => '1984_cover.jpg',
        'A Game of Thrones' => '1747724281_AGameOfThrones.jpg',
        'Good to Great' => '1747724303_Good to Great.jpg',
        'Pride and Prejudice' => '1747724335_Pride and Prejudice.jpg',
        'Steve Jobs' => '1747725415_steve-jobs-9781451648553_hr.jpg',
        'The Alchemist' => '1747725431_the-alchemist-a-graphic-novel.jpg',
        'The Catcher in the Rye' => '1747725459_Book-Review-The-Catcher-in-the-Rye-by-J-D-Salinger.jpg',
        'The Great Gatsby' => '1747725503_the-great-gatsby-and-other-works-9781645173519_hr.jpg',
        'The Martian' => '1747725673_The_Martian_(Weir_novel).jpg'
    ];

    $updated_count = 0;

    foreach ($books_without_covers as $book) {
        $cover_file = null;

        // Check specific mappings first
        foreach ($book_cover_mapping as $title_pattern => $cover) {
            if (stripos($book['title'], $title_pattern) !== false && in_array($cover, $available_covers)) {
                $cover_file = $cover;
                break;
            }
        }

        // If no specific mapping, try to match by title similarity
        if (!$cover_file) {
            foreach ($available_covers as $cover) {
                $cover_name = pathinfo($cover, PATHINFO_FILENAME);
                $cover_clean = preg_replace('/[^a-z0-9]/', '', strtolower($cover_name));
                $title_clean = preg_replace('/[^a-z0-9]/', '', strtolower($book['title']));

                if (strpos($cover_clean, $title_clean) !== false || strpos($title_clean, $cover_clean) !== false) {
                    $cover_file = $cover;
                    break;
                }
            }
        }

        // Update the book if we found a cover
        if ($cover_file) {
            $update_query = "UPDATE books SET cover_image = :cover_image WHERE id = :id";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->bindParam(':cover_image', $cover_file);
            $update_stmt->bindParam(':id', $book['id']);

            if ($update_stmt->execute()) {
                echo "<div class='alert alert-success'>✅ Updated '{$book['title']}' with cover: {$cover_file}</div>";
                $updated_count++;
            }
        }
    }

    echo "<div class='alert alert-info'>Updated {$updated_count} books with cover images.</div>";
    echo "<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>";
}

echo "<form method='post'>";
echo "<button type='submit' name='fix_covers' class='btn btn-primary'>Fix Book Covers</button>";
echo "</form>";

echo "</div></body></html>";
?>
