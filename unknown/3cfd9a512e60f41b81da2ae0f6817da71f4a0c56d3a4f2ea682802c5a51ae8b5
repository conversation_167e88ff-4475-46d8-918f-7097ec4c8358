<?php
/**
 * Dashboard Functions
 * Contains functions for the enhanced librarian dashboard
 */

/**
 * Get tasks for the current librarian
 *
 * @param PDO $db The database connection
 * @param int $librarian_id The ID of the librarian
 * @param string $status The status of tasks to retrieve (pending, completed, all)
 * @param int $limit The maximum number of tasks to retrieve
 * @return array The tasks
 */
function getLibrarianTasks($db, $librarian_id, $status = 'pending', $limit = 10) {
    // This is a placeholder function since the actual table might not exist
    // In a real implementation, this would query the librarian_tasks table

    // Return sample tasks for demonstration
    $tasks = [];

    if ($status === 'pending' || $status === 'all') {
        $tasks[] = [
            'id' => 1,
            'title' => 'Process new book shipment',
            'description' => 'Catalog and shelve the new books that arrived yesterday',
            'due_date' => date('Y-m-d', strtotime('-1 day')),
            'priority' => 'high',
            'completed' => 0
        ];

        $tasks[] = [
            'id' => 2,
            'title' => 'Contact members with overdue books',
            'description' => 'Send reminders to members with books past due date',
            'due_date' => date('Y-m-d'),
            'priority' => 'medium',
            'completed' => 0
        ];

        $tasks[] = [
            'id' => 3,
            'title' => 'Update reading recommendations list',
            'description' => 'Add new titles to the monthly reading recommendations',
            'due_date' => date('Y-m-d', strtotime('+7 days')),
            'priority' => 'low',
            'completed' => 0
        ];
    }

    if ($status === 'completed' || $status === 'all') {
        $tasks[] = [
            'id' => 4,
            'title' => 'Organize summer reading program',
            'description' => 'Finalize the schedule and book list for the summer reading program',
            'due_date' => date('Y-m-d', strtotime('-3 days')),
            'priority' => 'medium',
            'completed' => 1,
            'completed_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
        ];
    }

    return $tasks;
}

/**
 * Add a new task for a librarian
 *
 * @param PDO $db The database connection
 * @param int $librarian_id The ID of the librarian
 * @param string $title The title of the task
 * @param string $description The description of the task
 * @param string $due_date The due date of the task
 * @param string $priority The priority of the task (low, medium, high)
 * @param string $category The category of the task
 * @return int The ID of the newly created task
 */
function addLibrarianTask($db, $librarian_id, $title, $description, $due_date, $priority, $category) {
    // This is a placeholder function since the actual table might not exist
    // In a real implementation, this would insert into the librarian_tasks table

    // Log the action for demonstration
    logActivity($db, 'create', 'Added new librarian task: ' . $title, 'task');

    // Return a dummy ID
    return 5;
}

/**
 * Update a task's status
 *
 * @param PDO $db The database connection
 * @param int $task_id The ID of the task
 * @param bool $completed Whether the task is completed
 * @return bool Whether the update was successful
 */
function updateTaskStatus($db, $task_id, $completed) {
    // This is a placeholder function since the actual table might not exist
    // In a real implementation, this would update the librarian_tasks table

    // Log the action for demonstration
    $status = $completed ? 'completed' : 'pending';
    logActivity($db, 'update', 'Updated task #' . $task_id . ' status to ' . $status, 'task', $task_id);

    return true;
}

/**
 * Get acquisition suggestions based on various metrics
 *
 * @param PDO $db The database connection
 * @param int $limit The maximum number of suggestions to retrieve
 * @return array The acquisition suggestions
 */
function getAcquisitionSuggestions($db, $limit = 5) {
    // This is a placeholder function since the actual tables might not exist
    // In a real implementation, this would query book requests, searches, etc.

    // Return sample suggestions for demonstration
    return [
        [
            'book_title' => 'The Midnight Library',
            'book_author' => 'Matt Haig',
            'request_count' => 12,
            'search_count' => 15,
            'avg_rating' => 4.5
        ],
        [
            'book_title' => 'Project Hail Mary',
            'book_author' => 'Andy Weir',
            'request_count' => 8,
            'search_count' => 10,
            'avg_rating' => 4.8
        ]
    ];
}

/**
 * Get members matching specific criteria for communication
 *
 * @param PDO $db The database connection
 * @param string $criteria The criteria to match (overdue, reservations, inactive, all)
 * @return array The matching members
 */
function getMembersForCommunication($db, $criteria) {
    try {
        $sql = "";

        switch ($criteria) {
            case 'overdue':
                $sql = "SELECT DISTINCT m.* FROM members m
                        JOIN book_loans bl ON m.id = bl.member_id
                        WHERE bl.due_date < CURDATE() AND bl.status = 'borrowed'";
                break;
            case 'reservations':
                $sql = "SELECT DISTINCT m.* FROM members m
                        JOIN book_reservations br ON m.id = br.member_id
                        WHERE br.status = 'ready'";
                break;
            case 'inactive':
                // This assumes there's a last_login field, which might not exist
                // Adjust as needed for your actual database schema
                $sql = "SELECT * FROM members m
                        WHERE m.last_activity < DATE_SUB(NOW(), INTERVAL 3 MONTH)";
                break;
            case 'all':
            default:
                $sql = "SELECT * FROM members";
                break;
        }

        $stmt = $db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll();
    } catch (Exception $e) {
        // If the query fails (e.g., missing tables or columns), return an empty array
        return [];
    }
}

/**
 * Register a new member quickly
 *
 * @param PDO $db The database connection
 * @param string $first_name The first name of the member
 * @param string $last_name The last name of the member
 * @param string $email The email of the member
 * @param string $phone The phone number of the member
 * @param string $membership_type The type of membership
 * @param bool $send_credentials Whether to send login credentials
 * @return int|bool The ID of the newly created member or false on failure
 */
function quickRegisterMember($db, $first_name, $last_name, $email, $phone, $membership_type, $send_credentials = true) {
    try {
        // Generate a random password
        $password = bin2hex(random_bytes(4));
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        // Create username from email
        $username = explode('@', $email)[0];

        // Check if username exists
        $query = "SELECT COUNT(*) as count FROM members WHERE email = :email";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        $result = $stmt->fetch();

        if ($result['count'] > 0) {
            // Email already exists
            return false;
        }

        // Insert the new member
        $query = "INSERT INTO members (first_name, last_name, email, phone, membership_status, created_at, updated_at)
                VALUES (:first_name, :last_name, :email, :phone, :membership_status, NOW(), NOW())";

        $stmt = $db->prepare($query);
        $status = 'active';
        $stmt->bindParam(':first_name', $first_name);
        $stmt->bindParam(':last_name', $last_name);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':phone', $phone);
        $stmt->bindParam(':membership_status', $status);
        $stmt->execute();

        $member_id = $db->lastInsertId();

        // Log the action
        logActivity($db, 'create', 'Quick registered new member: ' . $first_name . ' ' . $last_name, 'member', $member_id);

        return $member_id;
    } catch (Exception $e) {
        // If the query fails, return false
        return false;
    }
}
