# Google Authentication Setup for Library Management System

This document provides instructions on how to set up Google authentication for the Library Management System.

## Prerequisites

1. A Google account
2. Access to the [Google Cloud Console](https://console.cloud.google.com/)
3. PHP 7.4 or higher
4. Composer (for installing the Google API Client Library)

## Step 1: Set Up a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API and Google OAuth API for your project

## Step 2: Create OAuth Credentials

1. In the Google Cloud Console, navigate to "APIs & Services" > "Credentials"
2. Click "Create Credentials" and select "OAuth client ID"
3. Select "Web application" as the application type
4. Enter a name for your OAuth client (e.g., "Library Management System")
5. Add authorized JavaScript origins:
   - `http://localhost` (for local development)
   - Your production domain (e.g., `https://yourlibrary.com`)
6. Add authorized redirect URIs:
   - `http://localhost/Library/lms/lms/google_callback.php` (for local development)
   - Your production callback URL (e.g., `https://yourlibrary.com/google_callback.php`)
7. Click "Create"
8. Note your Client ID and Client Secret

## Step 3: Install Google API Client Library

1. Open a terminal or command prompt
2. Navigate to your project directory
3. Run the following command to install the Google API Client Library:

```bash
composer require google/apiclient:^2.0
```

## Step 4: Configure Google Authentication

1. Open the file `includes/google_auth.php`
2. Update the following constants with your Google OAuth credentials:

```php
define('GOOGLE_CLIENT_ID', 'YOUR_CLIENT_ID_HERE');
define('GOOGLE_CLIENT_SECRET', 'YOUR_CLIENT_SECRET_HERE');
```

3. Run the database update script to add the necessary columns:

```bash
php add_google_auth.php
```

## Step 5: Test Google Authentication

1. Open your browser and navigate to the login page
2. Click the "Login with Google" button
3. Follow the Google authentication flow
4. You should be redirected back to the library system and logged in

## Troubleshooting

### Common Issues

1. **Redirect URI Mismatch**: Ensure that the redirect URI in your Google Cloud Console matches exactly with your application's callback URL.

2. **API Not Enabled**: Make sure you've enabled the necessary APIs in your Google Cloud Console.

3. **Library Not Found**: If you get a "Class 'Google_Client' not found" error, make sure you've installed the Google API Client Library correctly.

4. **Database Columns Missing**: Run the `add_google_auth.php` script to add the necessary columns to your database.

### Debug Tips

1. Check the PHP error log for detailed error messages
2. Enable debug mode in the Google API Client:

```php
$client->setLogger(new Google\Logger\EchoLogger());
```

## Security Considerations

1. Keep your Client Secret secure and never expose it in client-side code
2. Use HTTPS in production to protect user data
3. Implement proper session management and CSRF protection
4. Regularly review and update your Google Cloud Console settings

## Additional Resources

- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Google API Client Library for PHP](https://github.com/googleapis/google-api-php-client)
