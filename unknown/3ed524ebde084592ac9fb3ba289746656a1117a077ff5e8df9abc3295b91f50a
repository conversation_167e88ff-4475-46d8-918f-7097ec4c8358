<?php
/**
 * Debug Email Settings - Simplified version to identify issues
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Email Settings</h1>";

// Test 1: Basic PHP
echo "<h3>1. Basic PHP Test</h3>";
echo "<p>✅ PHP is working</p>";
echo "<p>PHP Version: " . phpversion() . "</p>";

// Test 2: Session
echo "<h3>2. Session Test</h3>";
session_start();
echo "<p>✅ Session started</p>";
echo "<p>Session ID: " . session_id() . "</p>";

// Test 3: Database connection
echo "<h3>3. Database Connection Test</h3>";
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    echo "<p>✅ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}

// Test 4: Functions file
echo "<h3>4. Functions File Test</h3>";
try {
    require_once 'includes/functions.php';
    echo "<p>✅ Functions file loaded</p>";
    
    // Test specific functions
    if (function_exists('isLoggedIn')) {
        echo "<p>✅ isLoggedIn() function exists</p>";
        $logged_in = isLoggedIn();
        echo "<p>Login status: " . ($logged_in ? 'Logged in' : 'Not logged in') . "</p>";
    } else {
        echo "<p>❌ isLoggedIn() function not found</p>";
    }
    
    if (function_exists('isAdmin')) {
        echo "<p>✅ isAdmin() function exists</p>";
        if (isLoggedIn()) {
            $is_admin = isAdmin();
            echo "<p>Admin status: " . ($is_admin ? 'Is admin' : 'Not admin') . "</p>";
        }
    } else {
        echo "<p>❌ isAdmin() function not found</p>";
    }
    
    if (function_exists('url')) {
        echo "<p>✅ url() function exists</p>";
        echo "<p>Test URL: " . url('admin/email_settings.php') . "</p>";
    } else {
        echo "<p>❌ url() function not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Functions file error: " . $e->getMessage() . "</p>";
}

// Test 5: Settings table
echo "<h3>5. Settings Table Test</h3>";
try {
    $query = "SHOW TABLES LIKE 'settings'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "<p>✅ Settings table exists</p>";
        
        // Check for email settings
        $query = "SELECT * FROM settings WHERE setting_group = 'email'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $email_settings = $stmt->fetchAll();
        
        echo "<p>Email settings count: " . count($email_settings) . "</p>";
        if (count($email_settings) > 0) {
            echo "<ul>";
            foreach ($email_settings as $setting) {
                echo "<li>" . $setting['setting_key'] . " = " . $setting['setting_value'] . "</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p>❌ Settings table does not exist</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Settings table error: " . $e->getMessage() . "</p>";
}

// Test 6: File access
echo "<h3>6. File Access Test</h3>";
$email_settings_file = 'admin/email_settings.php';
if (file_exists($email_settings_file)) {
    echo "<p>✅ Email settings file exists</p>";
    echo "<p>File path: " . realpath($email_settings_file) . "</p>";
    echo "<p>File size: " . filesize($email_settings_file) . " bytes</p>";
    echo "<p>File permissions: " . substr(sprintf('%o', fileperms($email_settings_file)), -4) . "</p>";
} else {
    echo "<p>❌ Email settings file not found</p>";
}

// Test 7: Include files
echo "<h3>7. Include Files Test</h3>";
$include_files = ['includes/head.php', 'includes/header.php', 'includes/sidebar.php', 'includes/footer.php'];
foreach ($include_files as $file) {
    if (file_exists($file)) {
        echo "<p>✅ $file exists</p>";
    } else {
        echo "<p>❌ $file not found</p>";
    }
}

// Test 8: Direct access attempt
echo "<h3>8. Direct Access Test</h3>";
echo "<p><a href='admin/email_settings.php' target='_blank'>Click here to test Email Settings page</a></p>";

// Test 9: Session data
echo "<h3>9. Session Data</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<hr>";
echo "<p><strong>If the Email Settings page still doesn't work, the issue might be:</strong></p>";
echo "<ul>";
echo "<li>User not logged in as admin</li>";
echo "<li>Missing session data</li>";
echo "<li>PHP errors in the email_settings.php file</li>";
echo "<li>Missing database tables</li>";
echo "</ul>";

echo "<p><a href='fix_reminder_system.php'>Run Fix Script</a> | <a href='auth/login.php'>Login</a> | <a href='index.php'>Dashboard</a></p>";
?>
