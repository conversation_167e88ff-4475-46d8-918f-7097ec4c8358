<?php
/**
 * View Member Details
 */
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and has appropriate role
if (!isset($_SESSION['user_id']) && !isset($_SESSION['member_id'])) {
    redirect(url('login.php'));
}

// Check if user has permission (admin or librarian)
if (isset($_SESSION['role']) && !in_array($_SESSION['role'], ['admin', 'librarian'])) {
    redirect(url('index.php'));
}

// Get member ID from URL
$member_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($member_id <= 0) {
    redirect(url('members/index.php'));
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Get member data
$query = "SELECT * FROM members WHERE id = :id";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $member_id);
$stmt->execute();

if ($stmt->rowCount() === 0) {
    redirect(url('members/index.php'));
}

$member = $stmt->fetch();

// Get member's loan history
$query = "SELECT bl.*, b.title, b.author, b.isbn
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id
          ORDER BY bl.issue_date DESC
          LIMIT 10";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$loan_history = $stmt->fetchAll();

// Get current loans
$query = "SELECT bl.*, b.title, b.author, b.isbn
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id AND bl.return_date IS NULL
          ORDER BY bl.issue_date DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$current_loans = $stmt->fetchAll();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Member - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Member Details</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="index.php" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="bi bi-arrow-left"></i> Back to Members
                        </a>
                        <a href="edit.php?id=<?php echo $member_id; ?>" class="btn btn-sm btn-warning me-2">
                            <i class="bi bi-pencil"></i> Edit
                        </a>
                        <a href="activity_log.php?member_id=<?php echo $member_id; ?>" class="btn btn-sm btn-info me-2">
                            <i class="bi bi-clock-history"></i> Activity Log
                        </a>
                        <a href="profile_card.php?id=<?php echo $member_id; ?>" class="btn btn-sm btn-success me-2">
                            <i class="bi bi-card-text"></i> Library Card
                        </a>
                        <a href="delete.php?id=<?php echo $member_id; ?>" class="btn btn-sm btn-danger"
                           onclick="return confirm('Are you sure you want to delete this member?')">
                            <i class="bi bi-trash"></i> Delete
                        </a>
                    </div>
                </div>

                <div class="row">
                    <!-- Member Information -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-person me-2"></i>Member Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Member ID:</strong></td>
                                        <td><?php echo h($member['id']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Name:</strong></td>
                                        <td><?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td><?php echo h($member['email']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td><?php echo h($member['phone'] ?: 'Not provided'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Address:</strong></td>
                                        <td><?php echo h($member['address'] ?: 'Not provided'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Membership Date:</strong></td>
                                        <td><?php echo date('F j, Y', strtotime($member['membership_date'])); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <?php if ($member['membership_status'] === 'active'): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php elseif ($member['membership_status'] === 'inactive'): ?>
                                                <span class="badge bg-secondary">Inactive</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Suspended</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Created:</strong></td>
                                        <td><?php echo date('F j, Y g:i A', strtotime($member['created_at'])); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Last Updated:</strong></td>
                                        <td><?php echo date('F j, Y g:i A', strtotime($member['updated_at'])); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Current Loans -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-book me-2"></i>Current Loans (<?php echo count($current_loans); ?>)
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (count($current_loans) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Book</th>
                                                    <th>Loan Date</th>
                                                    <th>Due Date</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($current_loans as $loan): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo h($loan['title']); ?></strong><br>
                                                        <small class="text-muted">by <?php echo h($loan['author']); ?></small>
                                                    </td>
                                                    <td><?php echo date('M j, Y', strtotime($loan['issue_date'])); ?></td>
                                                    <td>
                                                        <?php
                                                        $due_date = strtotime($loan['due_date']);
                                                        $today = strtotime('today');
                                                        $class = $due_date < $today ? 'text-danger' : ($due_date == $today ? 'text-warning' : '');
                                                        ?>
                                                        <span class="<?php echo $class; ?>">
                                                            <?php echo date('M j, Y', $due_date); ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No current loans</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loan History -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-clock-history me-2"></i>Recent Loan History
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (count($loan_history) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Book</th>
                                                    <th>Author</th>
                                                    <th>ISBN</th>
                                                    <th>Loan Date</th>
                                                    <th>Due Date</th>
                                                    <th>Return Date</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($loan_history as $loan): ?>
                                                <tr>
                                                    <td><?php echo h($loan['title']); ?></td>
                                                    <td><?php echo h($loan['author']); ?></td>
                                                    <td><?php echo h($loan['isbn']); ?></td>
                                                    <td><?php echo date('M j, Y', strtotime($loan['issue_date'])); ?></td>
                                                    <td><?php echo date('M j, Y', strtotime($loan['due_date'])); ?></td>
                                                    <td>
                                                        <?php if ($loan['return_date']): ?>
                                                            <?php echo date('M j, Y', strtotime($loan['return_date'])); ?>
                                                        <?php else: ?>
                                                            <span class="text-muted">Not returned</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($loan['return_date']): ?>
                                                            <?php if (strtotime($loan['return_date']) <= strtotime($loan['due_date'])): ?>
                                                                <span class="badge bg-success">Returned on time</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-warning">Returned late</span>
                                                            <?php endif; ?>
                                                        <?php else: ?>
                                                            <?php if (strtotime($loan['due_date']) < strtotime('today')): ?>
                                                                <span class="badge bg-danger">Overdue</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-primary">On loan</span>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No loan history found</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
