<?php
/**
 * Test Member Storage Script
 * This script tests if members can be stored in the database
 */

require_once 'config/database.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

echo "<h2>Testing Member Storage in Database</h2>";

try {
    // Test 1: Check if members table exists and has correct structure
    echo "<h3>Test 1: Database Structure</h3>";
    
    $query = "DESCRIBE members";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<p>✅ Members table exists with the following columns:</p>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test 2: Count existing members
    echo "<h3>Test 2: Existing Members</h3>";
    
    $query = "SELECT COUNT(*) as count FROM members";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch();
    
    echo "<p>Current number of members in database: <strong>" . $result['count'] . "</strong></p>";
    
    // Test 3: Try to insert a test member
    echo "<h3>Test 3: Insert Test Member</h3>";
    
    $test_email = 'test_member_' . time() . '@example.com';
    $test_first_name = 'Test';
    $test_last_name = 'Member';
    $test_phone = '555-0123';
    $test_address = '123 Test Street';
    $membership_date = date('Y-m-d');
    $membership_status = 'active';
    $test_password = password_hash('testpassword123', PASSWORD_DEFAULT);
    
    // Check if password column exists
    $has_password_column = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'password') {
            $has_password_column = true;
            break;
        }
    }
    
    if ($has_password_column) {
        $query = "INSERT INTO members (first_name, last_name, email, phone, address, membership_date, membership_status, password) 
                  VALUES (:first_name, :last_name, :email, :phone, :address, :membership_date, :membership_status, :password)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':first_name', $test_first_name);
        $stmt->bindParam(':last_name', $test_last_name);
        $stmt->bindParam(':email', $test_email);
        $stmt->bindParam(':phone', $test_phone);
        $stmt->bindParam(':address', $test_address);
        $stmt->bindParam(':membership_date', $membership_date);
        $stmt->bindParam(':membership_status', $membership_status);
        $stmt->bindParam(':password', $test_password);
    } else {
        $query = "INSERT INTO members (first_name, last_name, email, phone, address, membership_date, membership_status) 
                  VALUES (:first_name, :last_name, :email, :phone, :address, :membership_date, :membership_status)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':first_name', $test_first_name);
        $stmt->bindParam(':last_name', $test_last_name);
        $stmt->bindParam(':email', $test_email);
        $stmt->bindParam(':phone', $test_phone);
        $stmt->bindParam(':address', $test_address);
        $stmt->bindParam(':membership_date', $membership_date);
        $stmt->bindParam(':membership_status', $membership_status);
    }
    
    if ($stmt->execute()) {
        $member_id = $db->lastInsertId();
        echo "<p>✅ Successfully inserted test member with ID: <strong>$member_id</strong></p>";
        echo "<p>Member details:</p>";
        echo "<ul>";
        echo "<li>Name: $test_first_name $test_last_name</li>";
        echo "<li>Email: $test_email</li>";
        echo "<li>Phone: $test_phone</li>";
        echo "<li>Address: $test_address</li>";
        echo "<li>Membership Date: $membership_date</li>";
        echo "<li>Status: $membership_status</li>";
        if ($has_password_column) {
            echo "<li>Password: Set (hashed)</li>";
        } else {
            echo "<li>Password: Not available (column doesn't exist)</li>";
        }
        echo "</ul>";
        
        // Test 4: Retrieve the inserted member
        echo "<h3>Test 4: Retrieve Test Member</h3>";
        
        $query = "SELECT * FROM members WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $member_id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $member = $stmt->fetch();
            echo "<p>✅ Successfully retrieved test member from database:</p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            foreach ($member as $key => $value) {
                if (!is_numeric($key)) {
                    echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
                }
            }
            echo "</table>";
        } else {
            echo "<p>❌ Failed to retrieve test member from database</p>";
        }
        
        // Test 5: Clean up - delete the test member
        echo "<h3>Test 5: Cleanup</h3>";
        
        $query = "DELETE FROM members WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $member_id);
        
        if ($stmt->execute()) {
            echo "<p>✅ Successfully deleted test member (cleanup completed)</p>";
        } else {
            echo "<p>⚠️ Warning: Could not delete test member. You may need to manually remove it.</p>";
        }
        
    } else {
        echo "<p>❌ Failed to insert test member</p>";
    }
    
    // Test 6: Final member count
    echo "<h3>Test 6: Final Member Count</h3>";
    
    $query = "SELECT COUNT(*) as count FROM members";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch();
    
    echo "<p>Final number of members in database: <strong>" . $result['count'] . "</strong></p>";
    
    echo "<h3>Summary</h3>";
    echo "<p>✅ <strong>Database is working correctly!</strong></p>";
    echo "<p>Members can be successfully stored in and retrieved from the database.</p>";
    
    if (!$has_password_column) {
        echo "<p>⚠️ <strong>Note:</strong> The password column is missing. Run <a href='update_members_table.php'>update_members_table.php</a> to add it.</p>";
    }
    
    echo "<p><a href='members/index.php'>Go to Members Management</a> | <a href='members/add.php'>Add New Member</a></p>";
    
} catch (PDOException $e) {
    echo "<h3>❌ Database Error</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and ensure the database is running.</p>";
}
?>
