<?php
/**
 * Comprehensive Member System Test
 */
require_once 'config/database.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

echo "<h1>Library Management System - Member System Test</h1>";
echo "<p>Testing all member management functionality...</p>";

$tests_passed = 0;
$tests_failed = 0;

function test_result($test_name, $passed, $message = '') {
    global $tests_passed, $tests_failed;
    
    if ($passed) {
        echo "<p>✅ <strong>$test_name:</strong> PASSED";
        if ($message) echo " - $message";
        echo "</p>";
        $tests_passed++;
    } else {
        echo "<p>❌ <strong>$test_name:</strong> FAILED";
        if ($message) echo " - $message";
        echo "</p>";
        $tests_failed++;
    }
}

try {
    // Test 1: Database Connection
    test_result("Database Connection", $db !== null, "Successfully connected to database");
    
    // Test 2: Members Table Structure
    $query = "DESCRIBE members";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    $required_columns = ['id', 'first_name', 'last_name', 'email', 'membership_status', 'membership_date'];
    $has_all_columns = true;
    $missing_columns = [];
    
    $existing_columns = array_column($columns, 'Field');
    foreach ($required_columns as $col) {
        if (!in_array($col, $existing_columns)) {
            $has_all_columns = false;
            $missing_columns[] = $col;
        }
    }
    
    test_result("Members Table Structure", $has_all_columns, 
                $has_all_columns ? "All required columns present" : "Missing: " . implode(', ', $missing_columns));
    
    // Test 3: Insert Test Member
    $test_email = 'test_system_' . time() . '@example.com';
    $query = "INSERT INTO members (first_name, last_name, email, phone, address, membership_date, membership_status) 
              VALUES ('Test', 'User', :email, '555-TEST', '123 Test St', CURDATE(), 'active')";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':email', $test_email);
    $insert_success = $stmt->execute();
    $test_member_id = $db->lastInsertId();
    
    test_result("Member Creation", $insert_success, "Created test member with ID: $test_member_id");
    
    // Test 4: Retrieve Member
    $query = "SELECT * FROM members WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $test_member_id);
    $stmt->execute();
    $retrieved_member = $stmt->fetch();
    
    test_result("Member Retrieval", $retrieved_member !== false, "Successfully retrieved member data");
    
    // Test 5: Update Member
    $query = "UPDATE members SET phone = '555-UPDATED' WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $test_member_id);
    $update_success = $stmt->execute();
    
    test_result("Member Update", $update_success, "Successfully updated member phone number");
    
    // Test 6: Search Functionality
    $query = "SELECT * FROM members WHERE first_name LIKE :search OR last_name LIKE :search OR email LIKE :search";
    $stmt = $db->prepare($query);
    $search_term = "%Test%";
    $stmt->bindParam(':search', $search_term);
    $stmt->execute();
    $search_results = $stmt->fetchAll();
    
    test_result("Member Search", count($search_results) > 0, "Found " . count($search_results) . " member(s) in search");
    
    // Test 7: Status Filter
    $query = "SELECT COUNT(*) as count FROM members WHERE membership_status = 'active'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $active_count = $stmt->fetch()['count'];
    
    test_result("Status Filtering", $active_count > 0, "Found $active_count active member(s)");
    
    // Test 8: Member Statistics
    $stats_queries = [
        'total' => "SELECT COUNT(*) as count FROM members",
        'active' => "SELECT COUNT(*) as count FROM members WHERE membership_status = 'active'",
        'with_loans' => "SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE return_date IS NULL"
    ];
    
    $stats_working = true;
    foreach ($stats_queries as $stat => $query) {
        try {
            $stmt = $db->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch();
        } catch (Exception $e) {
            $stats_working = false;
            break;
        }
    }
    
    test_result("Statistics Queries", $stats_working, "All statistics queries executed successfully");
    
    // Test 9: File Existence Tests
    $required_files = [
        'members/index.php' => 'Member List Page',
        'members/add.php' => 'Add Member Page',
        'members/edit.php' => 'Edit Member Page',
        'members/view.php' => 'View Member Page',
        'members/delete.php' => 'Delete Member Page',
        'members/statistics.php' => 'Statistics Page',
        'members/advanced_search.php' => 'Advanced Search Page',
        'members/bulk_operations.php' => 'Bulk Operations Page'
    ];
    
    foreach ($required_files as $file => $description) {
        $file_exists = file_exists($file);
        test_result("File: $description", $file_exists, $file_exists ? "File exists" : "File missing: $file");
    }
    
    // Test 10: Template File
    $template_exists = file_exists('templates/member_import_template.csv');
    test_result("Import Template", $template_exists, $template_exists ? "CSV template available" : "Template missing");
    
    // Test 11: Email Uniqueness Constraint
    $duplicate_email_query = "INSERT INTO members (first_name, last_name, email, membership_date, membership_status) 
                             VALUES ('Duplicate', 'Test', :email, CURDATE(), 'active')";
    $stmt = $db->prepare($duplicate_email_query);
    $stmt->bindParam(':email', $test_email);
    
    try {
        $stmt->execute();
        $duplicate_allowed = true;
    } catch (PDOException $e) {
        $duplicate_allowed = false; // This is what we want - duplicates should be prevented
    }
    
    test_result("Email Uniqueness", !$duplicate_allowed, "Duplicate emails properly prevented");
    
    // Test 12: Password Field (if exists)
    $has_password_field = in_array('password', $existing_columns);
    if ($has_password_field) {
        $password_hash = password_hash('testpassword', PASSWORD_DEFAULT);
        $query = "UPDATE members SET password = :password WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':password', $password_hash);
        $stmt->bindParam(':id', $test_member_id);
        $password_update = $stmt->execute();
        
        test_result("Password Functionality", $password_update, "Password hashing and storage working");
    } else {
        test_result("Password Field", false, "Password field not found - run update_members_table.php");
    }
    
    // Cleanup: Delete test member
    $query = "DELETE FROM members WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $test_member_id);
    $cleanup_success = $stmt->execute();
    
    test_result("Cleanup", $cleanup_success, "Test member deleted successfully");
    
} catch (Exception $e) {
    test_result("System Error", false, "Exception: " . $e->getMessage());
}

// Summary
echo "<hr>";
echo "<h2>Test Summary</h2>";
echo "<p><strong>Tests Passed:</strong> <span style='color: green;'>$tests_passed</span></p>";
echo "<p><strong>Tests Failed:</strong> <span style='color: red;'>$tests_failed</span></p>";

$total_tests = $tests_passed + $tests_failed;
$success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 1) : 0;

echo "<p><strong>Success Rate:</strong> $success_rate%</p>";

if ($tests_failed == 0) {
    echo "<div style='background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 All Tests Passed!</h3>";
    echo "<p>Your member management system is working perfectly. Users can be stored in the database successfully.</p>";
    echo "</div>";
} else {
    echo "<div style='background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>⚠️ Some Tests Failed</h3>";
    echo "<p>Please review the failed tests above and fix any issues.</p>";
    echo "</div>";
}

echo "<h3>Quick Links</h3>";
echo "<ul>";
echo "<li><a href='members/index.php'>Member Management</a></li>";
echo "<li><a href='members/add.php'>Add New Member</a></li>";
echo "<li><a href='members/statistics.php'>Member Statistics</a></li>";
echo "<li><a href='members/advanced_search.php'>Advanced Search</a></li>";
echo "<li><a href='members/bulk_operations.php'>Bulk Operations</a></li>";
echo "<li><a href='test_member_storage.php'>Basic Storage Test</a></li>";
echo "</ul>";

echo "<h3>Next Steps</h3>";
echo "<ul>";
echo "<li>Test adding a new member through the web interface</li>";
echo "<li>Try the advanced search with different criteria</li>";
echo "<li>View member statistics and charts</li>";
echo "<li>Test bulk operations (export/import)</li>";
echo "<li>Verify member login functionality (if passwords are set up)</li>";
echo "</ul>";
?>
