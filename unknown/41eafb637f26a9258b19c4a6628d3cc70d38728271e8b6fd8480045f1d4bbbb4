<?php
/**
 * Daily Status Update Script
 * Automatically updates loan statuses and calculates fines
 * This should be run daily via cron job or manually
 */

session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../../login.php');
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $updates_made = [];
    
    // Start transaction
    $db->beginTransaction();
    
    // 1. Update borrowed books that are now overdue
    $stmt = $db->prepare("
        UPDATE book_loans 
        SET status = 'overdue',
            fine = GREATEST(fine, DATEDIFF(CURDATE(), due_date) * 1.00)
        WHERE status = 'borrowed' 
        AND due_date < CURDATE()
    ");
    $stmt->execute();
    $overdue_updated = $stmt->rowCount();
    $updates_made[] = "Updated $overdue_updated borrowed books to overdue status";
    
    // 2. Update fines for existing overdue books
    $stmt = $db->prepare("
        UPDATE book_loans 
        SET fine = DATEDIFF(CURDATE(), due_date) * 1.00
        WHERE status = 'overdue'
        AND due_date < CURDATE()
        AND fine < DATEDIFF(CURDATE(), due_date) * 1.00
    ");
    $stmt->execute();
    $fines_updated = $stmt->rowCount();
    $updates_made[] = "Updated fines for $fines_updated overdue books";
    
    // 3. Log the maintenance activity
    logActivity($db, 'maintenance', 'Daily status update completed');
    
    // Commit transaction
    $db->commit();
    
    $page_title = "Daily Maintenance - Status Update";
    include_once '../../includes/head.php';
    ?>
    
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0"><i class="bi bi-gear-fill me-2"></i>Daily Status Update Complete</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h5><i class="bi bi-check-circle me-2"></i>Maintenance Completed Successfully</h5>
                            <p class="mb-0">The daily status update has been completed at <?php echo date('Y-m-d H:i:s'); ?></p>
                        </div>
                        
                        <h6>Updates Made:</h6>
                        <ul class="list-group list-group-flush">
                            <?php foreach($updates_made as $update): ?>
                                <li class="list-group-item">
                                    <i class="bi bi-check text-success me-2"></i><?php echo h($update); ?>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                        
                        <?php
                        // Show current statistics
                        $stmt = $db->prepare('SELECT status, COUNT(*) as count FROM book_loans GROUP BY status');
                        $stmt->execute();
                        $statuses = $stmt->fetchAll();
                        ?>
                        
                        <h6 class="mt-4">Current Loan Status Distribution:</h6>
                        <div class="row">
                            <?php foreach($statuses as $status): ?>
                                <div class="col-md-4 mb-2">
                                    <div class="card bg-light">
                                        <div class="card-body text-center py-2">
                                            <h6 class="card-title mb-1"><?php echo ucfirst(h($status['status'])); ?></h6>
                                            <h4 class="text-primary mb-0"><?php echo h($status['count']); ?></h4>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="mt-4">
                            <a href="../dashboard.php" class="btn btn-primary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                            </a>
                            <a href="daily_status_update.php" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise me-2"></i>Run Again
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Automation Recommendation</h5>
                    </div>
                    <div class="card-body">
                        <p>For automatic daily updates, you can set up a cron job to run this script daily:</p>
                        <code class="bg-light p-2 d-block">
                            0 2 * * * /usr/bin/php <?php echo $_SERVER['DOCUMENT_ROOT']; ?>/Library/lms/admin/maintenance/daily_status_update.php
                        </code>
                        <small class="text-muted">This will run the update every day at 2:00 AM</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php
    
} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollback();
    }
    
    $page_title = "Maintenance Error";
    include_once '../../includes/head.php';
    ?>
    
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h4 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i>Maintenance Error</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <h5><i class="bi bi-x-circle me-2"></i>Error During Status Update</h5>
                            <p class="mb-0"><?php echo h($e->getMessage()); ?></p>
                        </div>
                        
                        <div class="mt-4">
                            <a href="../dashboard.php" class="btn btn-primary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                            </a>
                            <a href="daily_status_update.php" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise me-2"></i>Try Again
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
