<?php
/**
 * Google OAuth Configuration
 *
 * This file contains the configuration for Google OAuth authentication.
 * You need to create a project in the Google Cloud Console and configure OAuth credentials.
 *
 * Steps to create Google OAuth credentials:
 * 1. Go to https://console.cloud.google.com/
 * 2. Create a new project or select an existing one
 * 3. Go to "APIs & Services" > "Credentials"
 * 4. Click "Create Credentials" > "OAuth client ID"
 * 5. Select "Web application" as the application type
 * 6. Add your domain to the "Authorized JavaScript origins"
 * 7. Add your redirect URI to the "Authorized redirect URIs" (e.g., http://localhost/Library/lms/google_callback.php)
 * 8. Click "Create" and copy the client ID and client secret
 */

// Google OAuth Client ID and Secret
// REPLACE THESE WITH YOUR REAL GOOGLE OAUTH CREDENTIALS
const GOOGLE_CLIENT_ID = '************-ib8i3timlaklope1g8qkpf075q3jb12e.apps.googleusercontent.com'; // Your Google Client ID
const GOOGLE_CLIENT_SECRET = '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'; // Your Google Client Secret

// Redirect URI for Google OAuth
// This should match the URI you configured in the Google Cloud Console
const GOOGLE_REDIRECT_URI = BASE_URL . 'google_callback.php';

// Google OAuth scopes
const GOOGLE_SCOPES = ['openid', 'email', 'profile'];

// Google OAuth URLs
const GOOGLE_AUTH_URL = 'https://accounts.google.com/o/oauth2/v2/auth';
const GOOGLE_TOKEN_URL = 'https://oauth2.googleapis.com/token';
const GOOGLE_USER_INFO_URL = 'https://www.googleapis.com/oauth2/v2/userinfo';

/**
 * Check if Google OAuth is properly configured
 */
function isGoogleOAuthConfigured() {
    return GOOGLE_CLIENT_ID !== 'YOUR_GOOGLE_CLIENT_ID_HERE' &&
           GOOGLE_CLIENT_SECRET !== 'YOUR_GOOGLE_CLIENT_SECRET_HERE' &&
           !empty(GOOGLE_CLIENT_ID) &&
           !empty(GOOGLE_CLIENT_SECRET);
}
