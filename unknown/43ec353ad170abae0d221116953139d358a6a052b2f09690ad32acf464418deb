<?php
session_start();

// Include required files with error handling
try {
    require_once '../config/database.php';
    require_once '../config/config.php';
    require_once '../includes/functions.php';
} catch (Exception $e) {
    die('Error loading required files: ' . $e->getMessage());
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Set up pagination
$records_per_page = 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $records_per_page;

// Get filter if any
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';

// Count total records for pagination
if (!empty($status_filter)) {
    $query = "SELECT COUNT(*) as total FROM book_loans WHERE status = :status";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':status', $status_filter);
} else {
    $query = "SELECT COUNT(*) as total FROM book_loans";
    $stmt = $db->prepare($query);
}
$stmt->execute();
$total_rows = $stmt->fetch()['total'];
$total_pages = ceil($total_rows / $records_per_page);

// Get loans with pagination
if (!empty($status_filter)) {
    $query = "SELECT bl.*, b.title as book_title, m.first_name, m.last_name
              FROM book_loans bl
              JOIN books b ON bl.book_id = b.id
              JOIN members m ON bl.member_id = m.id
              WHERE bl.status = :status
              ORDER BY bl.issue_date DESC LIMIT :offset, :records_per_page";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':status', $status_filter);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':records_per_page', $records_per_page, PDO::PARAM_INT);
} else {
    $query = "SELECT bl.*, b.title as book_title, m.first_name, m.last_name
              FROM book_loans bl
              JOIN books b ON bl.book_id = b.id
              JOIN members m ON bl.member_id = m.id
              ORDER BY bl.issue_date DESC LIMIT :offset, :records_per_page";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':records_per_page', $records_per_page, PDO::PARAM_INT);
}
$stmt->execute();
$loans = $stmt->fetchAll();

// Update overdue status
$today = date('Y-m-d');
foreach ($loans as &$loan) {
    if ($loan['status'] === 'borrowed' && $loan['due_date'] < $today) {
        $loan['status'] = 'overdue';

        // Update in database
        $update_query = "UPDATE book_loans SET status = 'overdue' WHERE id = :id";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':id', $loan['id']);
        $update_stmt->execute();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Loans - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Book Loans</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="issue.php" class="btn btn-sm btn-primary">
                            <i class="bi bi-plus-lg"></i> Issue New Book
                        </a>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- Filter Options -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="btn-group" role="group" aria-label="Filter loans">
                            <a href="index.php" class="btn btn-outline-primary <?php echo empty($status_filter) ? 'active' : ''; ?>">All</a>
                            <a href="?status=borrowed" class="btn btn-outline-primary <?php echo $status_filter === 'borrowed' ? 'active' : ''; ?>">Borrowed</a>
                            <a href="?status=returned" class="btn btn-outline-primary <?php echo $status_filter === 'returned' ? 'active' : ''; ?>">Returned</a>
                            <a href="?status=overdue" class="btn btn-outline-primary <?php echo $status_filter === 'overdue' ? 'active' : ''; ?>">Overdue</a>
                        </div>
                    </div>
                </div>

                <!-- Loans Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Book</th>
                                <th>Member</th>
                                <th>Issue Date</th>
                                <th>Due Date</th>
                                <th>Return Date</th>
                                <th>Fine</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (count($loans) > 0): ?>
                                <?php foreach ($loans as $loan): ?>
                                <tr>
                                    <td><?php echo $loan['id']; ?></td>
                                    <td><?php echo $loan['book_title']; ?></td>
                                    <td><?php echo $loan['first_name'] . ' ' . $loan['last_name']; ?></td>
                                    <td><?php echo formatDate($loan['issue_date']); ?></td>
                                    <td><?php echo formatDate($loan['due_date']); ?></td>
                                    <td><?php echo $loan['return_date'] ? formatDate($loan['return_date']) : '-'; ?></td>
                                    <td>
                                        <?php if ($loan['fine'] > 0): ?>
                                            <span class="text-danger">$<?php echo number_format($loan['fine'], 2); ?></span>
                                        <?php else: ?>
                                            $0.00
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($loan['status'] === 'borrowed'): ?>
                                            <span class="badge bg-primary">Borrowed</span>
                                        <?php elseif ($loan['status'] === 'returned'): ?>
                                            <span class="badge bg-success">Returned</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Overdue</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="view.php?id=<?php echo $loan['id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View Details">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <?php if ($loan['status'] !== 'returned'): ?>
                                            <a href="return.php?id=<?php echo $loan['id']; ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Return Book">
                                                <i class="bi bi-arrow-return-left"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="9" class="text-center">No loans found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo $page <= 1 ? '#' : '?page='.($page-1).(!empty($status_filter) ? '&status='.$status_filter : ''); ?>">Previous</a>
                        </li>

                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($status_filter) ? '&status='.$status_filter : ''; ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo $page >= $total_pages ? '#' : '?page='.($page+1).(!empty($status_filter) ? '&status='.$status_filter : ''); ?>">Next</a>
                        </li>
                    </ul>
                </nav>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
</body>
</html>
