<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    echo "Please log in first.";
    exit;
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Find the 1984 book
$query = "SELECT * FROM books WHERE title LIKE '%1984%' AND author LIKE '%<PERSON><PERSON>%'";
$stmt = $db->prepare($query);
$stmt->execute();

if ($stmt->rowCount() == 0) {
    echo "Book '1984' not found in the database.";
    exit;
}

$book = $stmt->fetch();
$book_id = $book['id'];

echo "<h2>Upload Cover Image for '1984' by <PERSON></h2>";
echo "<p>Book ID: " . $book_id . "</p>";

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if image was uploaded
    if (isset($_FILES['cover_image']) && $_FILES['cover_image']['error'] == 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 5 * 1024 * 1024; // 5MB
        
        if (in_array($_FILES['cover_image']['type'], $allowed_types) && $_FILES['cover_image']['size'] <= $max_size) {
            // Create uploads directory if it doesn't exist
            if (!file_exists(UPLOADS_PATH)) {
                mkdir(UPLOADS_PATH, 0777, true);
            }
            
            if (!file_exists(COVERS_PATH)) {
                mkdir(COVERS_PATH, 0777, true);
            }
            
            $filename = time() . '_1984_cover.jpg';
            $target_file = COVERS_PATH . $filename;
            
            if (move_uploaded_file($_FILES['cover_image']['tmp_name'], $target_file)) {
                // Delete old image if exists
                if (!empty($book['cover_image']) && file_exists(COVERS_PATH . $book['cover_image'])) {
                    unlink(COVERS_PATH . $book['cover_image']);
                }
                
                // Update book in database
                $query = "UPDATE books SET cover_image = :cover_image WHERE id = :id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':cover_image', $filename);
                $stmt->bindParam(':id', $book_id);
                
                if ($stmt->execute()) {
                    echo "<div style='color: green; margin: 20px 0;'>✅ Cover image uploaded and updated successfully!</div>";
                    echo "<p>You can now view the book details: <a href='books/view.php?id=" . $book_id . "'>View Book Details</a></p>";
                    echo "<p>Or go back to the books list: <a href='books/index.php'>Books List</a></p>";
                    
                    // Display the uploaded image
                    echo "<div style='margin: 20px 0;'>";
                    echo "<h3>Uploaded Image:</h3>";
                    echo "<img src='uploads/covers/" . $filename . "' alt='1984 Cover' style='max-width: 300px;'>";
                    echo "</div>";
                } else {
                    echo "<div style='color: red; margin: 20px 0;'>❌ Failed to update the database.</div>";
                }
            } else {
                echo "<div style='color: red; margin: 20px 0;'>❌ Failed to upload image.</div>";
            }
        } else {
            echo "<div style='color: red; margin: 20px 0;'>❌ Invalid image file. Only JPG, PNG, and GIF files under 5MB are allowed.</div>";
        }
    } else {
        echo "<div style='color: red; margin: 20px 0;'>❌ No image was uploaded or there was an error.</div>";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload 1984 Cover Image - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-6 offset-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Upload Cover Image for '1984'</h5>
                    </div>
                    <div class="card-body">
                        <form action="" method="post" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="cover_image" class="form-label">Select Image File</label>
                                <input type="file" class="form-control" id="cover_image" name="cover_image" required>
                                <div class="form-text">Select the 1984 cover image. Only JPG, PNG, and GIF files under 5MB are allowed.</div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">Upload Image</button>
                                <a href="books/index.php" class="btn btn-secondary">Back to Books</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
