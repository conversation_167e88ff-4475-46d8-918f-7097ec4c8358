<!DOCTYPE html>
<html>
<head>
    <title>Direct Image Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h2>Direct Image Access Test</h2>
    
    <h3>Testing Different Image Paths</h3>
    
    <div class="row">
        <div class="col-md-6">
            <h4>Path: uploads/covers/1984_cover.jpg</h4>
            <img src="uploads/covers/1984_cover.jpg" style="max-width: 200px; height: auto;" alt="Test 1" onerror="this.style.border='2px solid red'; this.alt='Failed to load';">
        </div>
        
        <div class="col-md-6">
            <h4>Path: ./uploads/covers/1984_cover.jpg</h4>
            <img src="./uploads/covers/1984_cover.jpg" style="max-width: 200px; height: auto;" alt="Test 2" onerror="this.style.border='2px solid red'; this.alt='Failed to load';">
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-6">
            <h4>Path: /Library/lms/uploads/covers/1984_cover.jpg</h4>
            <img src="/Library/lms/uploads/covers/1984_cover.jpg" style="max-width: 200px; height: auto;" alt="Test 3" onerror="this.style.border='2px solid red'; this.alt='Failed to load';">
        </div>
        
        <div class="col-md-6">
            <h4>Path: http://localhost/Library/lms/uploads/covers/1984_cover.jpg</h4>
            <img src="http://localhost/Library/lms/uploads/covers/1984_cover.jpg" style="max-width: 200px; height: auto;" alt="Test 4" onerror="this.style.border='2px solid red'; this.alt='Failed to load';">
        </div>
    </div>
    
    <h3>Current URL Information</h3>
    <p><strong>Current URL:</strong> <?php echo $_SERVER['REQUEST_URI']; ?></p>
    <p><strong>Script Name:</strong> <?php echo $_SERVER['SCRIPT_NAME']; ?></p>
    <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT']; ?></p>
    
    <h3>File System Check</h3>
    <?php
    $test_paths = [
        'uploads/covers/1984_cover.jpg',
        './uploads/covers/1984_cover.jpg',
        '../uploads/covers/1984_cover.jpg',
        __DIR__ . '/uploads/covers/1984_cover.jpg'
    ];
    
    foreach ($test_paths as $path) {
        $exists = file_exists($path);
        echo "<p><strong>{$path}:</strong> " . ($exists ? '✅ Found' : '❌ Not found') . "</p>";
    }
    ?>
</div>
</body>
</html>
