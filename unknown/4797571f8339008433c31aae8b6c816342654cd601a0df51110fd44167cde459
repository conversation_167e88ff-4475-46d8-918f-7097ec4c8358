<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Auto-login as admin for testing
try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get or create admin user
    $query = "SELECT * FROM users WHERE role = 'admin' LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if (!$admin) {
        // Create admin user if none exists
        $username = 'admin';
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        $email = '<EMAIL>';
        
        $create_query = "INSERT INTO users (username, password, email, role, created_at) VALUES (:username, :password, :email, 'admin', NOW())";
        $create_stmt = $db->prepare($create_query);
        $create_stmt->bindParam(':username', $username);
        $create_stmt->bindParam(':password', $password);
        $create_stmt->bindParam(':email', $email);
        $create_stmt->execute();
        
        // Get the newly created admin
        $stmt->execute();
        $admin = $stmt->fetch();
    }
    
    if ($admin) {
        // Set session variables for admin login
        $_SESSION['user_id'] = $admin['id'];
        $_SESSION['username'] = $admin['username'];
        $_SESSION['role'] = $admin['role'];
        $_SESSION['logged_in'] = true;
    }
    
} catch (Exception $e) {
    echo "Setup error: " . $e->getMessage();
}

// Test dashboard functionality
echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Dashboard Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head><body class='bg-light'>";

echo "<div class='container mt-4'>";
echo "<h2>Dashboard Functionality Test</h2>";

// Test authentication
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>Authentication Test</h5></div>";
echo "<div class='card-body'>";
echo "<p><strong>Logged In:</strong> " . (isLoggedIn() ? '<span class="text-success">✓ Yes</span>' : '<span class="text-danger">✗ No</span>') . "</p>";
echo "<p><strong>Is Admin:</strong> " . (isAdmin() ? '<span class="text-success">✓ Yes</span>' : '<span class="text-danger">✗ No</span>') . "</p>";
echo "<p><strong>User ID:</strong> " . ($_SESSION['user_id'] ?? 'Not set') . "</p>";
echo "<p><strong>Role:</strong> " . ($_SESSION['role'] ?? 'Not set') . "</p>";
echo "</div></div>";

// Test database connection and queries
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>Database Test</h5></div>";
echo "<div class='card-body'>";

try {
    $database = new Database();
    $db = $database->getConnection();
    echo "<p><strong>Database Connection:</strong> <span class='text-success'>✓ Connected</span></p>";
    
    // Test basic queries
    $tables = ['books', 'members', 'book_loans', 'users'];
    foreach ($tables as $table) {
        try {
            $query = "SELECT COUNT(*) as count FROM $table";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            echo "<p><strong>$table table:</strong> <span class='text-success'>✓ $count records</span></p>";
        } catch (Exception $e) {
            echo "<p><strong>$table table:</strong> <span class='text-danger'>✗ Error: " . $e->getMessage() . "</span></p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p><strong>Database Connection:</strong> <span class='text-danger'>✗ Failed: " . $e->getMessage() . "</span></p>";
}

echo "</div></div>";

// Test dashboard statistics
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>Dashboard Statistics Test</h5></div>";
echo "<div class='card-body'>";

try {
    // Test the same queries as dashboard
    $stats = [];
    
    // Total books
    $query = "SELECT COUNT(*) as total FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['total_books'] = $stmt->fetch()['total'] ?? 0;
    
    // Available books
    $query = "SELECT SUM(COALESCE(available_quantity, 0)) as available FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['available_books'] = $stmt->fetch()['available'] ?? 0;
    
    // Total members
    $query = "SELECT COUNT(*) as total FROM members";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['total_members'] = $stmt->fetch()['total'] ?? 0;
    
    // Active loans
    $query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'borrowed' AND due_date >= CURDATE()";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['active_loans'] = $stmt->fetch()['total'] ?? 0;
    
    // Overdue books
    $query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'overdue' OR (status = 'borrowed' AND due_date < CURDATE())";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['overdue_books'] = $stmt->fetch()['total'] ?? 0;
    
    echo "<div class='row'>";
    foreach ($stats as $key => $value) {
        $label = ucwords(str_replace('_', ' ', $key));
        echo "<div class='col-md-4 mb-2'>";
        echo "<div class='card bg-light'>";
        echo "<div class='card-body text-center'>";
        echo "<h5 class='text-primary'>$value</h5>";
        echo "<small>$label</small>";
        echo "</div></div></div>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p class='text-danger'>Statistics Error: " . $e->getMessage() . "</p>";
}

echo "</div></div>";

// Test functions
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>Functions Test</h5></div>";
echo "<div class='card-body'>";

$functions_to_test = ['url', 'formatDate', 'formatDateTime', 'timeAgo', 'formatFileSize', 'h'];
foreach ($functions_to_test as $func) {
    if (function_exists($func)) {
        echo "<p><strong>$func():</strong> <span class='text-success'>✓ Available</span></p>";
    } else {
        echo "<p><strong>$func():</strong> <span class='text-danger'>✗ Missing</span></p>";
    }
}

echo "</div></div>";

echo "<div class='text-center mt-4'>";
echo "<a href='admin/dashboard.php' class='btn btn-primary me-2'>Go to Dashboard</a>";
echo "<a href='admin_access_fix.php' class='btn btn-secondary me-2'>Admin Access Fix</a>";
echo "<a href='logout.php' class='btn btn-outline-danger'>Logout</a>";
echo "</div>";

echo "</div></body></html>";
?>
