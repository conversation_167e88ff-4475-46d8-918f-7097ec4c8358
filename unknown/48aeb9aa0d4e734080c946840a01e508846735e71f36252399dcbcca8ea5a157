<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h2>🔍 Admin Dashboard Error Diagnosis</h2>";

// Start session and check authentication
session_start();

echo "<div style='background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>📋 Step-by-Step Diagnosis</h3>";

// Step 1: Check session
echo "<h4>1. Session Check:</h4>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Step 2: Check file includes
echo "<h4>2. File Includes Check:</h4>";
try {
    echo "✅ Including config/database.php...<br>";
    require_once 'config/database.php';
    echo "✅ Including config/config.php...<br>";
    require_once 'config/config.php';
    echo "✅ Including includes/functions.php...<br>";
    require_once 'includes/functions.php';
    echo "✅ All includes successful!<br>";
} catch (Exception $e) {
    echo "❌ Include error: " . $e->getMessage() . "<br>";
}

// Step 3: Check authentication functions
echo "<h4>3. Authentication Check:</h4>";
try {
    echo "isLoggedIn(): " . (isLoggedIn() ? '✅ True' : '❌ False') . "<br>";
    echo "isAdmin(): " . (isAdmin() ? '✅ True' : '❌ False') . "<br>";
} catch (Exception $e) {
    echo "❌ Authentication error: " . $e->getMessage() . "<br>";
}

// Step 4: Database connection
echo "<h4>4. Database Connection:</h4>";
try {
    $database = new Database();
    $db = $database->getConnection();
    if ($db) {
        echo "✅ Database connected successfully<br>";
        
        // Test basic queries
        $tables = ['books', 'members', 'book_loans', 'users'];
        foreach ($tables as $table) {
            try {
                $query = "SELECT COUNT(*) as count FROM $table";
                $stmt = $db->prepare($query);
                $stmt->execute();
                $count = $stmt->fetch()['count'];
                echo "✅ $table table: $count records<br>";
            } catch (Exception $e) {
                echo "❌ $table table error: " . $e->getMessage() . "<br>";
            }
        }
    } else {
        echo "❌ Database connection failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Step 5: Test dashboard queries
echo "<h4>5. Dashboard Queries Test:</h4>";
try {
    if (isset($db) && $db) {
        // Test the problematic queries from dashboard
        
        // Total books
        $query = "SELECT COUNT(*) as total FROM books";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $total_books = $stmt->fetch()['total'] ?? 0;
        echo "✅ Total books: $total_books<br>";
        
        // Available books
        $query = "SELECT SUM(COALESCE(available_quantity, 0)) as available FROM books";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $available_books = $stmt->fetch()['available'] ?? 0;
        echo "✅ Available books: $available_books<br>";
        
        // Total members
        $query = "SELECT COUNT(*) as total FROM members";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $total_members = $stmt->fetch()['total'] ?? 0;
        echo "✅ Total members: $total_members<br>";
        
        // Test top borrowers query
        $top_borrowers_query = "SELECT m.first_name, m.last_name, m.email, 
                                       COUNT(bl.id) as total_loans,
                                       COUNT(CASE WHEN bl.status = 'borrowed' THEN 1 END) as active_loans,
                                       COUNT(CASE WHEN bl.status = 'overdue' THEN 1 END) as overdue_loans
                               FROM members m
                               LEFT JOIN book_loans bl ON m.id = bl.member_id
                               GROUP BY m.id, m.first_name, m.last_name, m.email
                               HAVING total_loans > 0
                               ORDER BY total_loans DESC
                               LIMIT 5";
        $stmt = $db->prepare($top_borrowers_query);
        $stmt->execute();
        $top_borrowers = $stmt->fetchAll();
        echo "✅ Top borrowers query: " . count($top_borrowers) . " results<br>";
        
    } else {
        echo "❌ No database connection for testing queries<br>";
    }
} catch (Exception $e) {
    echo "❌ Dashboard queries error: " . $e->getMessage() . "<br>";
    echo "❌ Error details: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

// Step 6: Check for specific dashboard file issues
echo "<h4>6. Dashboard File Check:</h4>";
$dashboard_file = 'admin/dashboard.php';
if (file_exists($dashboard_file)) {
    echo "✅ Dashboard file exists<br>";
    echo "✅ File size: " . filesize($dashboard_file) . " bytes<br>";
    echo "✅ File readable: " . (is_readable($dashboard_file) ? 'Yes' : 'No') . "<br>";
} else {
    echo "❌ Dashboard file not found<br>";
}

// Step 7: Check CSS file
echo "<h4>7. CSS File Check:</h4>";
$css_file = 'admin/css/dashboard-fixes.css';
if (file_exists($css_file)) {
    echo "✅ CSS file exists<br>";
    echo "✅ CSS file size: " . filesize($css_file) . " bytes<br>";
} else {
    echo "❌ CSS file not found<br>";
}

echo "</div>";

// Quick fix options
echo "<div style='background: #e7f3ff; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>🔧 Quick Fix Options:</h3>";
echo "<a href='quick_admin_login.php' style='display: inline-block; padding: 10px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>Quick Admin Login</a>";
echo "<a href='admin_access_fix.php' style='display: inline-block; padding: 10px 15px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>Admin Access Fix</a>";
echo "<a href='test_dashboard_complete.php' style='display: inline-block; padding: 10px 15px; background: #ffc107; color: black; text-decoration: none; border-radius: 5px; margin: 5px;'>Test Dashboard</a>";
echo "</div>";

// Try to load dashboard with error catching
echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>🎯 Attempting Dashboard Load:</h3>";
echo "<p>Trying to include dashboard.php with error catching...</p>";

ob_start();
try {
    // Auto-login for testing
    if (!isLoggedIn() || !isAdmin()) {
        if (isset($db)) {
            $query = "SELECT * FROM users WHERE role = 'admin' LIMIT 1";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $admin = $stmt->fetch();
            
            if ($admin) {
                $_SESSION['user_id'] = $admin['id'];
                $_SESSION['username'] = $admin['username'];
                $_SESSION['role'] = $admin['role'];
                $_SESSION['logged_in'] = true;
                echo "✅ Auto-logged in as admin<br>";
            }
        }
    }
    
    echo "<iframe src='admin/dashboard.php' width='100%' height='400' style='border: 1px solid #ccc; border-radius: 5px;'></iframe>";
    
} catch (Exception $e) {
    echo "❌ Dashboard load error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . "<br>";
    echo "❌ Line: " . $e->getLine() . "<br>";
}
$output = ob_get_clean();
echo $output;
echo "</div>";
?>
