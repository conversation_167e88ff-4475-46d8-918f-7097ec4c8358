<?php
/**
 * Quick Librarian Login Script
 * This script helps you quickly log in as a librarian
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Check if librarian user exists
$query = "SELECT * FROM users WHERE role = 'librarian' LIMIT 1";
$stmt = $db->prepare($query);
$stmt->execute();
$librarian = $stmt->fetch();

if (!$librarian) {
    // Create a librarian user if none exists
    $username = 'librarian';
    $password = password_hash('librarian123', PASSWORD_DEFAULT);
    $email = '<EMAIL>';
    $full_name = 'Library Staff';
    
    $create_query = "INSERT INTO users (username, password, email, full_name, role, status, created_at) VALUES (:username, :password, :email, :full_name, 'librarian', 'active', NOW())";
    $create_stmt = $db->prepare($create_query);
    $create_stmt->bindParam(':username', $username);
    $create_stmt->bindParam(':password', $password);
    $create_stmt->bindParam(':email', $email);
    $create_stmt->bindParam(':full_name', $full_name);
    $create_stmt->execute();
    
    // Get the newly created librarian
    $stmt->execute();
    $librarian = $stmt->fetch();
}

// Auto-login as librarian
if (isset($_GET['login']) && $_GET['login'] === 'auto') {
    // Set session variables
    $_SESSION['user_id'] = $librarian['id'];
    $_SESSION['username'] = $librarian['username'];
    $_SESSION['email'] = $librarian['email'];
    $_SESSION['role'] = $librarian['role'];
    $_SESSION['full_name'] = $librarian['full_name'] ?? $librarian['username'];
    
    // Redirect to librarian dashboard
    header('Location: librarian/dashboard.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Librarian Login - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="bi bi-person-badge me-2"></i>Quick Librarian Access</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($librarian): ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle me-2"></i>Librarian account found!
                            </div>
                            
                            <h5>Librarian Account Details:</h5>
                            <ul class="list-group list-group-flush mb-3">
                                <li class="list-group-item"><strong>Username:</strong> <?php echo htmlspecialchars($librarian['username']); ?></li>
                                <li class="list-group-item"><strong>Email:</strong> <?php echo htmlspecialchars($librarian['email']); ?></li>
                                <li class="list-group-item"><strong>Role:</strong> <?php echo htmlspecialchars($librarian['role']); ?></li>
                                <li class="list-group-item"><strong>Status:</strong> <?php echo htmlspecialchars($librarian['status']); ?></li>
                            </ul>
                            
                            <div class="d-grid gap-2">
                                <a href="?login=auto" class="btn btn-primary btn-lg">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>Auto-Login as Librarian
                                </a>
                                <a href="login.php" class="btn btn-outline-secondary">
                                    <i class="bi bi-door-open me-2"></i>Go to Regular Login
                                </a>
                            </div>
                            
                            <hr>
                            <div class="alert alert-info">
                                <strong>Manual Login Credentials:</strong><br>
                                Username: <code><?php echo htmlspecialchars($librarian['username']); ?></code><br>
                                Password: <code>librarian123</code>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle me-2"></i>No librarian account found and failed to create one.
                            </div>
                        <?php endif; ?>
                        
                        <div class="mt-3">
                            <h6>Other Options:</h6>
                            <div class="btn-group w-100" role="group">
                                <a href="check_users.php" class="btn btn-outline-info">Check All Users</a>
                                <a href="create_admin.php" class="btn btn-outline-warning">Create Admin</a>
                                <a href="index.php" class="btn btn-outline-secondary">Home</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
