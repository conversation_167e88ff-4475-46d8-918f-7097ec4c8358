/**
 * Enhanced Member Analytics Dashboard JavaScript
 * Provides interactive functionality for the member analytics dashboard
 */

// Global variables
let memberStatsChart = null;
let activityChart = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedAnalytics();
    setupEventListeners();
    loadInteractiveCharts();
});

/**
 * Initialize enhanced analytics functionality
 */
function initializeEnhancedAnalytics() {
    console.log('Enhanced Member Analytics Dashboard initialized');

    // Add loading animations
    addLoadingAnimations();

    // Initialize tooltips
    initializeTooltips();

    // Setup auto-refresh
    setupAutoRefresh();
}

/**
 * Setup event listeners for interactive elements
 */
function setupEventListeners() {
    // Refresh button functionality
    const refreshBtn = document.querySelector('[onclick="refreshMemberStats()"]');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function(e) {
            e.preventDefault();
            refreshMemberStats();
        });
    }

    // Collapsible sections
    setupCollapsibleSections();

    // Interactive stat cards
    setupInteractiveStatCards();

    // Activity level filters
    setupActivityFilters();
}

/**
 * Refresh member statistics
 */
function refreshMemberStats() {
    const refreshIcon = document.querySelector('[onclick="refreshMemberStats()"] i');
    if (refreshIcon) {
        refreshIcon.classList.add('spin');
    }

    // Show loading state
    showLoadingState();

    // Fetch updated data
    fetch('ajax/refresh_member_stats.php')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                updateStatCards(data.stats);
                updateAnalyticsData(data.analytics);
                showSuccessMessage('Statistics refreshed successfully!');
            } else {
                let errorMessage = 'Failed to refresh statistics: ' + data.message;
                if (data.debug_info) {
                    console.error('Debug info:', data.debug_info);
                    // Show a more user-friendly message but log the technical details
                    if (data.debug_info.includes('Connection refused') || data.debug_info.includes('Access denied')) {
                        errorMessage = 'Database connection failed. Please check if MySQL is running.';
                    } else if (data.debug_info.includes("doesn't exist")) {
                        errorMessage = 'Database tables are missing. Please contact administrator.';
                    }
                }
                showErrorMessage(errorMessage);
            }
        })
        .catch(error => {
            console.error('Error refreshing stats:', error);
            let errorMessage = 'Network error while refreshing statistics';
            if (error.message.includes('HTTP error')) {
                errorMessage = 'Server error occurred. Please try again later.';
            } else if (error.message.includes('Failed to fetch')) {
                errorMessage = 'Cannot connect to server. Please check your connection.';
            }
            showErrorMessage(errorMessage);
        })
        .finally(() => {
            hideLoadingState();
            if (refreshIcon) {
                refreshIcon.classList.remove('spin');
            }
        });
}

/**
 * Update stat cards with new data
 */
function updateStatCards(stats) {
    const statElements = {
        'members_with_active_loans': stats.members_with_active_loans,
        'members_who_returned': stats.members_who_returned,
        'members_with_overdue': stats.members_with_overdue,
        'total_fines': stats.total_fines,
        'members_never_borrowed': stats.members_never_borrowed
    };

    Object.keys(statElements).forEach(key => {
        const element = document.querySelector(`[data-stat="${key}"]`);
        if (element) {
            animateNumberChange(element, statElements[key]);
        }
    });

    // Update progress bars
    updateProgressBars(stats);
}

/**
 * Animate number changes in stat cards
 */
function animateNumberChange(element, newValue) {
    const currentValue = parseInt(element.textContent) || 0;
    const increment = (newValue - currentValue) / 20;
    let current = currentValue;

    const animation = setInterval(() => {
        current += increment;
        if ((increment > 0 && current >= newValue) || (increment < 0 && current <= newValue)) {
            current = newValue;
            clearInterval(animation);
        }
        element.textContent = Math.round(current);
    }, 50);
}

/**
 * Update progress bars
 */
function updateProgressBars(stats) {
    const totalMembers = stats.total_members || 1;

    const progressBars = [
        { selector: '.bg-primary .progress-bar', value: (stats.members_with_active_loans / totalMembers) * 100 },
        { selector: '.bg-success .progress-bar', value: (stats.members_who_returned / totalMembers) * 100 },
        { selector: '.bg-danger .progress-bar', value: (stats.members_with_overdue / totalMembers) * 100 },
        { selector: '.bg-secondary .progress-bar', value: (stats.members_never_borrowed / totalMembers) * 100 }
    ];

    progressBars.forEach(bar => {
        const element = document.querySelector(bar.selector);
        if (element) {
            element.style.width = Math.round(bar.value) + '%';
        }
    });
}

/**
 * Setup collapsible sections
 */
function setupCollapsibleSections() {
    const collapseButtons = document.querySelectorAll('[data-bs-toggle="collapse"]');
    collapseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const icon = this.querySelector('i');
            if (icon) {
                setTimeout(() => {
                    const target = document.querySelector(this.getAttribute('data-bs-target'));
                    if (target && target.classList.contains('show')) {
                        icon.classList.remove('bi-chevron-down');
                        icon.classList.add('bi-chevron-up');
                    } else {
                        icon.classList.remove('bi-chevron-up');
                        icon.classList.add('bi-chevron-down');
                    }
                }, 150);
            }
        });
    });
}

/**
 * Setup interactive stat cards
 */
function setupInteractiveStatCards() {
    const statCards = document.querySelectorAll('.stats-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

/**
 * Setup activity level filters
 */
function setupActivityFilters() {
    // Add click handlers for activity level bars
    const activityBars = document.querySelectorAll('.activity-levels .progress');
    activityBars.forEach(bar => {
        bar.style.cursor = 'pointer';
        bar.addEventListener('click', function() {
            const activityLevel = this.parentElement.querySelector('.fw-medium').textContent;
            showMembersInActivityLevel(activityLevel);
        });
    });
}

/**
 * Show members in specific activity level
 */
function showMembersInActivityLevel(activityLevel) {
    // This would open a modal or navigate to a filtered view
    console.log('Showing members in activity level:', activityLevel);
    // Implementation would depend on your specific requirements
}

/**
 * Load interactive charts
 */
function loadInteractiveCharts() {
    // Member activity distribution chart
    createMemberActivityChart();

    // Borrowing trends chart
    createBorrowingTrendsChart();
}

/**
 * Create member activity chart
 */
function createMemberActivityChart() {
    const canvas = document.getElementById('memberActivityChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    // Sample data - replace with actual data from your backend
    const activityData = {
        labels: ['Inactive', 'Light Reader', 'Regular Reader', 'Active Reader', 'Power Reader'],
        datasets: [{
            data: [150, 200, 300, 250, 100],
            backgroundColor: [
                '#6c757d',
                '#17a2b8',
                '#007bff',
                '#ffc107',
                '#28a745'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    };

    memberStatsChart = new Chart(ctx, {
        type: 'doughnut',
        data: activityData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return `${context.label}: ${context.parsed} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

/**
 * Create borrowing trends chart
 */
function createBorrowingTrendsChart() {
    const canvas = document.getElementById('borrowingTrendsChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    // Sample data - replace with actual data from your backend
    const trendsData = {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Books Borrowed',
            data: [120, 150, 180, 140, 200, 170],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'Books Returned',
            data: [100, 140, 160, 130, 180, 160],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: true
        }]
    };

    activityChart = new Chart(ctx, {
        type: 'line',
        data: trendsData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

/**
 * Add loading animations
 */
function addLoadingAnimations() {
    const statCards = document.querySelectorAll('.stats-card');
    statCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Setup auto-refresh
 */
function setupAutoRefresh() {
    // Auto-refresh every 5 minutes
    setInterval(() => {
        refreshMemberStats();
    }, 300000);
}

/**
 * Show loading state
 */
function showLoadingState() {
    const memberStatsSection = document.getElementById('memberStatsCollapse');
    if (memberStatsSection) {
        memberStatsSection.style.opacity = '0.6';
        memberStatsSection.style.pointerEvents = 'none';
    }
}

/**
 * Hide loading state
 */
function hideLoadingState() {
    const memberStatsSection = document.getElementById('memberStatsCollapse');
    if (memberStatsSection) {
        memberStatsSection.style.opacity = '1';
        memberStatsSection.style.pointerEvents = 'auto';
    }
}

/**
 * Show success message
 */
function showSuccessMessage(message) {
    showToast(message, 'success');
}

/**
 * Show error message
 */
function showErrorMessage(message) {
    showToast(message, 'error');
}

/**
 * Show toast notification
 */
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toastContainer') || createToastContainer();

    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

/**
 * Create toast container if it doesn't exist
 */
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toastContainer';
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

// Export functions for global access
window.refreshMemberStats = refreshMemberStats;
