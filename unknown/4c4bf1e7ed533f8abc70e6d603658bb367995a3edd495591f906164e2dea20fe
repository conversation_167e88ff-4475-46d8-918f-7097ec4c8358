<?php
/**
 * LMS System Access Page
 * This page provides easy access to all system components
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>LMS System Access</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css'>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .card { margin-bottom: 20px; }
        .status-good { color: green; }
        .status-bad { color: red; }
        .status-warning { color: orange; }
        .btn-custom { margin: 5px; }
    </style>
</head>
<body>";

echo "<div class='container'>";
echo "<h1 class='mb-4'><i class='bi bi-book'></i> Library Management System - Access Portal</h1>";

// System Status Check
echo "<div class='card'>";
echo "<div class='card-header'><h3>System Status</h3></div>";
echo "<div class='card-body'>";

// Check database connection
try {
    $database = new Database();
    $db = $database->getConnection();
    echo "<p class='status-good'><i class='bi bi-check-circle'></i> Database connection: OK</p>";
    $db_connected = true;
} catch (Exception $e) {
    echo "<p class='status-bad'><i class='bi bi-x-circle'></i> Database connection: FAILED - " . $e->getMessage() . "</p>";
    $db_connected = false;
}

// Check if admin users exist
if ($db_connected) {
    try {
        $query = "SELECT COUNT(*) as count FROM users WHERE role = 'admin'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        $admin_count = $result['count'];
        
        if ($admin_count > 0) {
            echo "<p class='status-good'><i class='bi bi-check-circle'></i> Admin users: $admin_count found</p>";
        } else {
            echo "<p class='status-bad'><i class='bi bi-x-circle'></i> Admin users: None found</p>";
        }
    } catch (Exception $e) {
        echo "<p class='status-warning'><i class='bi bi-exclamation-triangle'></i> Admin check: Error - " . $e->getMessage() . "</p>";
    }
    
    // Check sample data
    try {
        $query = "SELECT COUNT(*) as count FROM books";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        $book_count = $result['count'];
        
        $query = "SELECT COUNT(*) as count FROM members";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        $member_count = $result['count'];
        
        echo "<p class='status-good'><i class='bi bi-info-circle'></i> Books: $book_count | Members: $member_count</p>";
    } catch (Exception $e) {
        echo "<p class='status-warning'><i class='bi bi-exclamation-triangle'></i> Data check: Error - " . $e->getMessage() . "</p>";
    }
}

echo "</div></div>";

// Quick Access Section
echo "<div class='card'>";
echo "<div class='card-header'><h3>Quick Access</h3></div>";
echo "<div class='card-body'>";

echo "<div class='row'>";

// Main System Access
echo "<div class='col-md-6'>";
echo "<h5>Main System</h5>";
echo "<a href='index.php' class='btn btn-primary btn-custom'><i class='bi bi-house'></i> Home Page</a>";
echo "<a href='login.php' class='btn btn-success btn-custom'><i class='bi bi-box-arrow-in-right'></i> Login</a>";
echo "<a href='register.php' class='btn btn-info btn-custom'><i class='bi bi-person-plus'></i> Register</a>";
echo "<a href='catalog.php' class='btn btn-secondary btn-custom'><i class='bi bi-book'></i> Book Catalog</a>";
echo "</div>";

// Admin Access
echo "<div class='col-md-6'>";
echo "<h5>Admin Access</h5>";
echo "<a href='admin/dashboard.php' class='btn btn-warning btn-custom'><i class='bi bi-speedometer2'></i> Admin Dashboard</a>";
echo "<a href='admin/users.php' class='btn btn-outline-warning btn-custom'><i class='bi bi-people'></i> Manage Users</a>";
echo "<a href='books/index.php' class='btn btn-outline-primary btn-custom'><i class='bi bi-journal'></i> Manage Books</a>";
echo "<a href='members/index.php' class='btn btn-outline-success btn-custom'><i class='bi bi-person-badge'></i> Manage Members</a>";
echo "</div>";

echo "</div>";
echo "</div></div>";

// Setup & Maintenance
echo "<div class='card'>";
echo "<div class='card-header'><h3>Setup & Maintenance</h3></div>";
echo "<div class='card-body'>";

echo "<div class='row'>";

echo "<div class='col-md-6'>";
echo "<h5>Database Setup</h5>";
echo "<a href='setup.php' class='btn btn-danger btn-custom'><i class='bi bi-database'></i> Database Setup</a>";
echo "<a href='create_admin.php' class='btn btn-outline-danger btn-custom'><i class='bi bi-person-gear'></i> Create Admin</a>";
echo "<a href='setup_database.php' class='btn btn-outline-warning btn-custom'><i class='bi bi-plus-circle'></i> Add Sample Data</a>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h5>System Tools</h5>";
echo "<a href='diagnostic.php' class='btn btn-info btn-custom'><i class='bi bi-tools'></i> System Diagnostic</a>";
echo "<a href='system_fix.php' class='btn btn-success btn-custom'><i class='bi bi-wrench'></i> System Fix</a>";
echo "<a href='verify_database.php' class='btn btn-outline-info btn-custom'><i class='bi bi-check2-square'></i> Verify Database</a>";
echo "</div>";

echo "</div>";
echo "</div></div>";

// Current Session Info
echo "<div class='card'>";
echo "<div class='card-header'><h3>Session Information</h3></div>";
echo "<div class='card-body'>";

if (isLoggedIn()) {
    echo "<p class='status-good'><i class='bi bi-person-check'></i> Logged in as: " . ($_SESSION['username'] ?? 'Unknown') . "</p>";
    echo "<p><i class='bi bi-shield'></i> Role: " . ($_SESSION['role'] ?? 'Unknown') . "</p>";
    echo "<a href='logout.php' class='btn btn-outline-danger btn-custom'><i class='bi bi-box-arrow-right'></i> Logout</a>";
} elseif (isMemberLoggedIn()) {
    echo "<p class='status-good'><i class='bi bi-person-check'></i> Logged in as member: " . ($_SESSION['member_name'] ?? 'Unknown') . "</p>";
    echo "<a href='member_logout.php' class='btn btn-outline-danger btn-custom'><i class='bi bi-box-arrow-right'></i> Logout</a>";
} else {
    echo "<p class='status-warning'><i class='bi bi-person-x'></i> Not logged in</p>";
}

echo "<p><i class='bi bi-info-circle'></i> Session ID: " . session_id() . "</p>";
echo "<p><i class='bi bi-clock'></i> Current time: " . date('Y-m-d H:i:s') . "</p>";

echo "</div></div>";

// Quick Links
echo "<div class='card'>";
echo "<div class='card-header'><h3>Documentation & Help</h3></div>";
echo "<div class='card-body'>";
echo "<a href='README.md' class='btn btn-outline-secondary btn-custom'><i class='bi bi-file-text'></i> README</a>";
echo "<a href='about.php' class='btn btn-outline-secondary btn-custom'><i class='bi bi-info-circle'></i> About</a>";
echo "<a href='contact.php' class='btn btn-outline-secondary btn-custom'><i class='bi bi-envelope'></i> Contact</a>";
echo "</div></div>";

echo "</div>"; // container

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body></html>";
?>
