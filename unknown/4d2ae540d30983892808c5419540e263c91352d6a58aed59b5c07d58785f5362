<?php
/**
 * Global Configuration
 */

// Base URL configuration - Simplified for relative paths
// Use relative base URL - no absolute path needed
$base_url = '';

// Define constants
define('BASE_URL', $base_url);
define('UPLOADS_PATH', __DIR__ . '/../uploads/');
define('COVERS_PATH', UPLOADS_PATH . 'covers/');
define('MEMBERS_PATH', UPLOADS_PATH . 'members/');

// System settings
define('ITEMS_PER_PAGE', 10);
define('LOAN_PERIOD_DAYS', 14);
define('FINE_PER_DAY', 1.00); // $1 per day

// Function to get the base URL
function getBaseUrl() {
    return BASE_URL;
}

// Function to get the full URL for a path
function url($path = '') {
    // Clean the path
    $path = ltrim($path, '/');

    // Get current directory level
    $current_script = $_SERVER['SCRIPT_NAME'] ?? '';
    $current_dir = dirname($current_script);

    // If we're in a subdirectory (like admin/), we need to go back to root
    if (strpos($current_dir, '/admin') !== false) {
        // We're in admin directory, so go back to root
        return '../' . $path;
    } elseif (strpos($current_dir, '/members') !== false) {
        // We're in members directory, so go back to root
        return '../' . $path;
    } elseif (strpos($current_dir, '/books') !== false) {
        // We're in books directory, so go back to root
        return '../' . $path;
    } elseif (strpos($current_dir, '/loans') !== false) {
        // We're in loans directory, so go back to root
        return '../' . $path;
    } elseif (strpos($current_dir, '/reports') !== false) {
        // We're in reports directory, so go back to root
        return '../' . $path;
    } else {
        // We're in root directory, use path as-is
        return $path;
    }
}
?>
