<?php
session_start();
require_once 'config/config.php';

echo "<h2>Checking and Fixing Directory Permissions</h2>";

// Check if uploads directory exists
if (!file_exists(UPLOADS_PATH)) {
    echo "<p>Creating uploads directory...</p>";
    if (mkdir(UPLOADS_PATH, 0777, true)) {
        echo "<p style='color: green;'>✅ Uploads directory created successfully.</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create uploads directory.</p>";
    }
} else {
    echo "<p>Uploads directory exists.</p>";
    
    // Check permissions
    if (is_writable(UPLOADS_PATH)) {
        echo "<p style='color: green;'>✅ Uploads directory is writable.</p>";
    } else {
        echo "<p style='color: red;'>❌ Uploads directory is not writable. Attempting to fix...</p>";
        if (chmod(UPLOADS_PATH, 0777)) {
            echo "<p style='color: green;'>✅ Permissions fixed for uploads directory.</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to fix permissions for uploads directory.</p>";
        }
    }
}

// Check if covers directory exists
if (!file_exists(COVERS_PATH)) {
    echo "<p>Creating covers directory...</p>";
    if (mkdir(COVERS_PATH, 0777, true)) {
        echo "<p style='color: green;'>✅ Covers directory created successfully.</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create covers directory.</p>";
    }
} else {
    echo "<p>Covers directory exists.</p>";
    
    // Check permissions
    if (is_writable(COVERS_PATH)) {
        echo "<p style='color: green;'>✅ Covers directory is writable.</p>";
    } else {
        echo "<p style='color: red;'>❌ Covers directory is not writable. Attempting to fix...</p>";
        if (chmod(COVERS_PATH, 0777)) {
            echo "<p style='color: green;'>✅ Permissions fixed for covers directory.</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to fix permissions for covers directory.</p>";
        }
    }
}

echo "<p>Directory paths:</p>";
echo "<ul>";
echo "<li>Uploads path: " . UPLOADS_PATH . "</li>";
echo "<li>Covers path: " . COVERS_PATH . "</li>";
echo "</ul>";

echo "<p>You can now try to <a href='upload_1984_cover.php'>upload the 1984 cover image</a>.</p>";
echo "<p>Or go back to the <a href='books/index.php'>books list</a>.</p>";
?>
