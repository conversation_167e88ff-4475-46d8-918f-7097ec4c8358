<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Background Image Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        
        .bg-test-1 {
            width: 100%;
            height: 300px;
            background: url('uploads/images/library.jpg') center/cover no-repeat;
            border: 2px solid #333;
            margin: 10px 0;
            position: relative;
        }
        
        .bg-test-2 {
            width: 100%;
            height: 300px;
            background: url('./uploads/images/library.jpg') center/cover no-repeat;
            border: 2px solid #333;
            margin: 10px 0;
            position: relative;
        }
        
        .bg-test-3 {
            width: 100%;
            height: 300px;
            background: url('../../uploads/images/library.jpg') center/cover no-repeat;
            border: 2px solid #333;
            margin: 10px 0;
            position: relative;
        }
        
        .overlay-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 10px 20px;
            border-radius: 5px;
            text-align: center;
        }
        
        .hero-section-test {
            background: linear-gradient(135deg, rgba(5, 5, 20, 0.92) 0%, rgba(10, 20, 40, 0.85) 100%);
            position: relative;
            color: white;
            padding: 40px 0 20px;
            margin-bottom: 20px;
            overflow: hidden;
            min-height: 100vh;
            height: 100vh;
            max-height: 100vh;
            display: flex;
            align-items: center;
            box-shadow: inset 0 0 150px rgba(0, 0, 0, 0.8);
        }
        
        .hero-overlay-test {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('uploads/images/library.jpg') center/cover no-repeat;
            opacity: 1;
            z-index: 1;
        }
        
        .hero-overlay-test::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to right,
                rgba(0,0,0,0.7) 0%,
                rgba(0,0,0,0.6) 30%,
                rgba(0,0,0,0.5) 60%,
                rgba(0,0,0,0.4) 100%);
            z-index: 1;
            box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.5);
        }
        
        .hero-content-test {
            padding: 1.5rem;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4), 0 0 20px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.15);
            position: relative;
            z-index: 5;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .hero-title-test {
            font-size: 3.5rem;
            font-weight: 900;
            line-height: 1;
            margin-bottom: 0.5rem;
            letter-spacing: -0.5px;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8), 0 0 1px rgba(0, 0, 0, 1);
            color: #ffffff;
        }
        
        .hero-subtitle-test {
            font-size: 1.2rem;
            font-weight: 500;
            text-shadow: 1px 1px 5px rgba(0, 0, 0, 0.9), 0 0 2px rgba(0, 0, 0, 1);
            line-height: 1.4;
            max-width: 90%;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <h1>Background Image Test</h1>
    
    <div class="test-container">
        <h2>Test 1: Direct path (uploads/images/library.jpg)</h2>
        <div class="bg-test-1">
            <div class="overlay-text">Test 1 - Direct Path</div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>Test 2: Relative path (./uploads/images/library.jpg)</h2>
        <div class="bg-test-2">
            <div class="overlay-text">Test 2 - Relative Path</div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>Test 3: CSS path (../../uploads/images/library.jpg)</h2>
        <div class="bg-test-3">
            <div class="overlay-text">Test 3 - CSS Path</div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>Test 4: Full Hero Section Recreation</h2>
        <div class="hero-section-test">
            <div class="hero-overlay-test"></div>
            <div class="hero-content-test">
                <h1 class="hero-title-test">Welcome to Our Library</h1>
                <p class="hero-subtitle-test">A sanctuary of knowledge where books inspire minds and stories transform lives</p>
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>Direct Image Test</h2>
        <p>Direct image link test:</p>
        <img src="uploads/images/library.jpg" alt="Library" style="max-width: 100%; height: 200px; object-fit: cover;">
    </div>
    
    <script>
        // Test if images are loading
        const img = new Image();
        img.onload = function() {
            console.log('✅ Image loaded successfully: uploads/images/library.jpg');
            document.body.insertAdjacentHTML('beforeend', '<div style="background: green; color: white; padding: 10px; margin: 10px 0;">✅ Image loaded successfully!</div>');
        };
        img.onerror = function() {
            console.log('❌ Image failed to load: uploads/images/library.jpg');
            document.body.insertAdjacentHTML('beforeend', '<div style="background: red; color: white; padding: 10px; margin: 10px 0;">❌ Image failed to load!</div>');
        };
        img.src = 'uploads/images/library.jpg';
    </script>
</body>
</html>
