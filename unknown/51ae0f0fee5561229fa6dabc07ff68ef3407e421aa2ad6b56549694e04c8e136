<?php
/**
 * Test Login Functionality
 * This script tests the login system and creates test accounts if needed
 */

session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

$test_results = [];
$message = '';
$message_type = 'info';

// Function to create test admin if none exists
function createTestAdmin($db) {
    try {
        // Check if admin exists
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
        $stmt->execute();
        $admin_count = $stmt->fetch()['count'];
        
        if ($admin_count == 0) {
            // Create test admin
            $username = 'admin';
            $password = password_hash('admin123', PASSWORD_DEFAULT);
            $email = '<EMAIL>';
            $full_name = 'System Administrator';
            
            $stmt = $db->prepare("INSERT INTO users (username, password, email, full_name, role, status, created_at) VALUES (?, ?, ?, ?, 'admin', 'active', NOW())");
            $result = $stmt->execute([$username, $password, $email, $full_name]);
            
            if ($result) {
                return ['success' => true, 'message' => 'Test admin created successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to create test admin'];
            }
        } else {
            return ['success' => true, 'message' => 'Admin user already exists'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error creating admin: ' . $e->getMessage()];
    }
}

// Function to create test librarian if none exists
function createTestLibrarian($db) {
    try {
        // Check if librarian exists
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'librarian'");
        $stmt->execute();
        $librarian_count = $stmt->fetch()['count'];
        
        if ($librarian_count == 0) {
            // Create test librarian
            $username = 'librarian';
            $password = password_hash('librarian123', PASSWORD_DEFAULT);
            $email = '<EMAIL>';
            $full_name = 'Library Staff';
            
            $stmt = $db->prepare("INSERT INTO users (username, password, email, full_name, role, status, created_at) VALUES (?, ?, ?, ?, 'librarian', 'active', NOW())");
            $result = $stmt->execute([$username, $password, $email, $full_name]);
            
            if ($result) {
                return ['success' => true, 'message' => 'Test librarian created successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to create test librarian'];
            }
        } else {
            return ['success' => true, 'message' => 'Librarian user already exists'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error creating librarian: ' . $e->getMessage()];
    }
}

// Function to create test member if none exists
function createTestMember($db) {
    try {
        // Check if test member exists
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM members WHERE email = '<EMAIL>'");
        $stmt->execute();
        $member_count = $stmt->fetch()['count'];
        
        if ($member_count == 0) {
            // Create test member
            $first_name = 'Test';
            $last_name = 'Member';
            $email = '<EMAIL>';
            $password = password_hash('member123', PASSWORD_DEFAULT);
            $phone = '************';
            $address = '123 Test Street, Test City';
            
            $stmt = $db->prepare("INSERT INTO members (first_name, last_name, email, password, phone, address, membership_date, membership_status, created_at) VALUES (?, ?, ?, ?, ?, ?, CURDATE(), 'active', NOW())");
            $result = $stmt->execute([$first_name, $last_name, $email, $password, $phone, $address]);
            
            if ($result) {
                return ['success' => true, 'message' => 'Test member created successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to create test member'];
            }
        } else {
            return ['success' => true, 'message' => 'Test member already exists'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error creating member: ' . $e->getMessage()];
    }
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_accounts'])) {
        $admin_result = createTestAdmin($db);
        $librarian_result = createTestLibrarian($db);
        $member_result = createTestMember($db);
        
        $test_results = [
            'Admin' => $admin_result,
            'Librarian' => $librarian_result,
            'Member' => $member_result
        ];
        
        $message = 'Test accounts creation completed. Check results below.';
        $message_type = 'success';
    }
}

// Get current user counts
try {
    $stmt = $db->prepare("SELECT role, COUNT(*) as count FROM users GROUP BY role");
    $stmt->execute();
    $user_counts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM members");
    $stmt->execute();
    $member_count = $stmt->fetch()['count'];
} catch (Exception $e) {
    $user_counts = [];
    $member_count = 0;
}

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login Functionality - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
</head>
<body>
    <div class="container mt-4">
        <h1><i class="bi bi-shield-check me-2"></i>Test Login Functionality</h1>
        <p class="text-muted">Create and test user accounts for the Library Management System</p>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                <?php echo h($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <!-- Current User Statistics -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="bi bi-people me-2"></i>Current User Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary"><?php echo $user_counts['admin'] ?? 0; ?></h4>
                            <p class="mb-0">Admin Users</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success"><?php echo $user_counts['librarian'] ?? 0; ?></h4>
                            <p class="mb-0">Librarians</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info"><?php echo $member_count; ?></h4>
                            <p class="mb-0">Members</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning"><?php echo ($user_counts['admin'] ?? 0) + ($user_counts['librarian'] ?? 0) + $member_count; ?></h4>
                            <p class="mb-0">Total Users</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Create Test Accounts -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="bi bi-plus-circle me-2"></i>Create Test Accounts</h5>
            </div>
            <div class="card-body">
                <p>Create test accounts to verify login functionality:</p>
                <form method="post">
                    <button type="submit" name="create_accounts" class="btn btn-primary">
                        <i class="bi bi-person-plus me-2"></i>Create Test Accounts
                    </button>
                </form>
                
                <?php if (!empty($test_results)): ?>
                    <div class="mt-3">
                        <h6>Creation Results:</h6>
                        <?php foreach ($test_results as $type => $result): ?>
                            <div class="alert alert-<?php echo $result['success'] ? 'success' : 'danger'; ?> py-2">
                                <strong><?php echo h($type); ?>:</strong> <?php echo h($result['message']); ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Test Credentials -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="bi bi-key me-2"></i>Test Credentials</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">Admin Login</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Username:</strong> admin</p>
                                <p><strong>Password:</strong> admin123</p>
                                <a href="login.php" class="btn btn-primary btn-sm">Test Admin Login</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">Librarian Login</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Username:</strong> librarian</p>
                                <p><strong>Password:</strong> librarian123</p>
                                <a href="login.php" class="btn btn-success btn-sm">Test Librarian Login</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">Member Login</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Email:</strong> <EMAIL></p>
                                <p><strong>Password:</strong> member123</p>
                                <a href="login.php" class="btn btn-info btn-sm">Test Member Login</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Access Links -->
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-link me-2"></i>Quick Access Links</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="system_test.php" class="btn btn-outline-primary w-100 mb-2">
                            <i class="bi bi-gear me-2"></i>System Test
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="index.php" class="btn btn-outline-secondary w-100 mb-2">
                            <i class="bi bi-house me-2"></i>Home Page
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="catalog.php" class="btn btn-outline-info w-100 mb-2">
                            <i class="bi bi-book me-2"></i>Book Catalog
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="register.php" class="btn btn-outline-success w-100 mb-2">
                            <i class="bi bi-person-plus me-2"></i>Registration
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
