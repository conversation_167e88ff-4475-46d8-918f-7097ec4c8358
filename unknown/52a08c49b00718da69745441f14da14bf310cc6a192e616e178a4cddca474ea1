// Library Management System JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Confirm delete
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    });

    // Book search functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('table tbody tr');

            tableRows.forEach(function(row) {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }

    // Date picker initialization
    const datePickers = document.querySelectorAll('.datepicker');
    if (datePickers.length > 0) {
        datePickers.forEach(function(picker) {
            // This is a placeholder for a date picker library
            // You would typically use a library like Flatpickr or Bootstrap Datepicker
            console.log('Date picker initialized');
        });
    }

    // Calculate fine automatically when return date changes
    const returnDateInput = document.getElementById('return_date');
    const dueDateInput = document.getElementById('due_date');
    const fineInput = document.getElementById('fine');

    if (returnDateInput && dueDateInput && fineInput) {
        returnDateInput.addEventListener('change', function() {
            calculateFine(dueDateInput.value, this.value);
        });
    }

    // Function to calculate fine
    function calculateFine(dueDate, returnDate) {
        if (!dueDate || !returnDate) return;

        const due = new Date(dueDate);
        const returned = new Date(returnDate);
        const finePerDay = 1.00; // $1 per day

        if (returned > due) {
            const diffTime = Math.abs(returned - due);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            const fine = diffDays * finePerDay;
            fineInput.value = fine.toFixed(2);
        } else {
            fineInput.value = '0.00';
        }
    }

    // Print report button
    const printButton = document.getElementById('printReport');
    if (printButton) {
        printButton.addEventListener('click', function() {
            window.print();
        });
    }

    // Date range type handler for reports
    const dateRangeType = document.getElementById('date_range_type');

    // Quick date range buttons functionality
    function addQuickDateButtons() {
        const reportForm = document.getElementById('reportForm');
        if (!reportForm) return;

        // Create quick buttons container
        const quickButtonsContainer = document.createElement('div');
        quickButtonsContainer.className = 'col-12 mt-2';
        quickButtonsContainer.innerHTML = `
            <div class="d-flex flex-wrap gap-2">
                <small class="text-muted me-2 align-self-center">Quick Select:</small>
                <button type="button" class="btn btn-sm btn-outline-primary quick-date-btn" data-range="this_week">This Week</button>
                <button type="button" class="btn btn-sm btn-outline-primary quick-date-btn" data-range="last_week">Last Week</button>
                <button type="button" class="btn btn-sm btn-outline-primary quick-date-btn" data-range="this_month">This Month</button>
                <button type="button" class="btn btn-sm btn-outline-primary quick-date-btn" data-range="last_month">Last Month</button>
                <button type="button" class="btn btn-sm btn-outline-primary quick-date-btn" data-range="this_year">This Year</button>
                <button type="button" class="btn btn-sm btn-outline-primary quick-date-btn" data-range="last_30_days">Last 30 Days</button>
            </div>
        `;

        // Insert before the submit button
        const submitButtonContainer = reportForm.querySelector('.col-md-2.d-flex.align-items-end');
        if (submitButtonContainer) {
            reportForm.insertBefore(quickButtonsContainer, submitButtonContainer);
        }

        // Add event listeners to quick buttons
        const quickButtons = reportForm.querySelectorAll('.quick-date-btn');
        quickButtons.forEach(button => {
            button.addEventListener('click', function() {
                const range = this.getAttribute('data-range');
                dateRangeType.value = range;

                // Trigger change event
                dateRangeType.dispatchEvent(new Event('change'));

                // Update button states
                quickButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Set active state based on current selection
        const currentRange = dateRangeType.value;
        quickButtons.forEach(button => {
            if (button.getAttribute('data-range') === currentRange) {
                button.classList.add('active');
            }
        });
    }

    // Initialize quick date buttons
    addQuickDateButtons();
});
