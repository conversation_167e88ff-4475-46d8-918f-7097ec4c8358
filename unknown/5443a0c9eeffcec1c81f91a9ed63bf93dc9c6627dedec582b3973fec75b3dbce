<?php
/**
 * Test Members Navigation
 * This page specifically tests the Members navigation functionality
 */

session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Test the Members page specifically
$members_url = url('members/index.php');
$members_file_path = 'members/index.php';
$members_exists = file_exists($members_file_path);
$members_readable = is_readable($members_file_path);

// Test database connection for members
$db_connected = false;
$members_count = 0;
try {
    $database = new Database();
    $db = $database->getConnection();
    if ($db) {
        $db_connected = true;
        // Try to count members
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM members");
        $stmt->execute();
        $result = $stmt->fetch();
        $members_count = $result['count'];
    }
} catch (Exception $e) {
    $db_error = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Members Navigation Test - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .test-card {
            margin-bottom: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .status-good { color: #198754; }
        .status-bad { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4"><i class="bi bi-people me-2"></i>Members Navigation Test</h1>
        
        <!-- Test Results -->
        <div class="row">
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-check-circle me-2"></i>File System Tests</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <strong>Members File Exists:</strong> 
                                <span class="<?php echo $members_exists ? 'status-good' : 'status-bad'; ?>">
                                    <?php echo $members_exists ? '✅ Yes' : '❌ No'; ?>
                                </span>
                            </li>
                            <li class="mb-2">
                                <strong>Members File Readable:</strong> 
                                <span class="<?php echo $members_readable ? 'status-good' : 'status-bad'; ?>">
                                    <?php echo $members_readable ? '✅ Yes' : '❌ No'; ?>
                                </span>
                            </li>
                            <li class="mb-2">
                                <strong>Generated URL:</strong> 
                                <code><?php echo htmlspecialchars($members_url); ?></code>
                            </li>
                            <li class="mb-2">
                                <strong>File Path:</strong> 
                                <code><?php echo htmlspecialchars($members_file_path); ?></code>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="bi bi-database me-2"></i>Database Tests</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <strong>Database Connected:</strong> 
                                <span class="<?php echo $db_connected ? 'status-good' : 'status-bad'; ?>">
                                    <?php echo $db_connected ? '✅ Yes' : '❌ No'; ?>
                                </span>
                            </li>
                            <li class="mb-2">
                                <strong>Members Count:</strong> 
                                <span class="badge bg-info"><?php echo $members_count; ?> members</span>
                            </li>
                            <?php if (isset($db_error)): ?>
                            <li class="mb-2">
                                <strong>Error:</strong> 
                                <span class="text-danger"><?php echo htmlspecialchars($db_error); ?></span>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Test Buttons -->
        <div class="card test-card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0"><i class="bi bi-link me-2"></i>Navigation Tests</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>Direct Members Page</h6>
                        <a href="<?php echo $members_url; ?>" class="btn btn-primary w-100 mb-2" target="_blank">
                            <i class="bi bi-people me-2"></i>Open Members Page
                        </a>
                    </div>
                    <div class="col-md-4">
                        <h6>Admin Dashboard</h6>
                        <a href="<?php echo url('admin/dashboard.php'); ?>" class="btn btn-success w-100 mb-2" target="_blank">
                            <i class="bi bi-speedometer2 me-2"></i>Open Admin Dashboard
                        </a>
                    </div>
                    <div class="col-md-4">
                        <h6>Add New Member</h6>
                        <a href="<?php echo url('members/add.php'); ?>" class="btn btn-info w-100 mb-2" target="_blank">
                            <i class="bi bi-person-plus me-2"></i>Add Member
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="card test-card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Instructions</h5>
            </div>
            <div class="card-body">
                <h6>✅ If all tests show green checkmarks, your Members navigation is working!</h6>
                <ol>
                    <li><strong>Click "Open Members Page"</strong> to test the direct link</li>
                    <li><strong>Click "Open Admin Dashboard"</strong> and then click the Members link in the sidebar</li>
                    <li><strong>Click the Members card</strong> in the dashboard stats section</li>
                </ol>
                
                <div class="alert alert-success mt-3">
                    <h6>✨ Expected Results:</h6>
                    <ul class="mb-0">
                        <li>Members page should load without errors</li>
                        <li>You should see a list of members (or "No members found" if empty)</li>
                        <li>Navigation sidebar should work properly</li>
                        <li>All buttons and links should be functional</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="card test-card">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0"><i class="bi bi-bookmark me-2"></i>Quick Links</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="<?php echo url('members/index.php'); ?>" class="btn btn-outline-primary w-100 mb-2">
                            <i class="bi bi-people me-2"></i>Members List
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?php echo url('members/add.php'); ?>" class="btn btn-outline-success w-100 mb-2">
                            <i class="bi bi-person-plus me-2"></i>Add Member
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?php echo url('members/statistics.php'); ?>" class="btn btn-outline-info w-100 mb-2">
                            <i class="bi bi-graph-up me-2"></i>Member Stats
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?php echo url('admin/dashboard.php'); ?>" class="btn btn-outline-warning w-100 mb-2">
                            <i class="bi bi-speedometer2 me-2"></i>Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back to Main Tests -->
        <div class="text-center">
            <a href="<?php echo url('test_navigation.php'); ?>" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>Back to Full Navigation Test
            </a>
            <a href="<?php echo url('fix_navigation.php'); ?>" class="btn btn-primary">
                <i class="bi bi-tools me-2"></i>Navigation Fix Tool
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
