<?php
/**
 * Install Profile System Script
 * 
 * This script updates the members table to add profile picture and notification preferences columns.
 */

// Include database connection
require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Initialize variables
$success = true;
$messages = [];

// Add profile_picture column
try {
    $query = "ALTER TABLE members ADD COLUMN IF NOT EXISTS profile_picture VARCHAR(255) DEFAULT NULL";
    $db->exec($query);
    $messages[] = "✅ Added profile_picture column to members table";
} catch (PDOException $e) {
    $success = false;
    $messages[] = "❌ Error adding profile_picture column: " . $e->getMessage();
    
    // Try alternative syntax for MySQL versions that don't support IF NOT EXISTS
    try {
        // Check if column exists
        $query = "SHOW COLUMNS FROM members LIKE 'profile_picture'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            // Column doesn't exist, add it
            $query = "ALTER TABLE members ADD COLUMN profile_picture VARCHAR(255) DEFAULT NULL";
            $db->exec($query);
            $success = true;
            $messages[] = "✅ Added profile_picture column to members table (alternative method)";
        } else {
            $success = true;
            $messages[] = "✅ profile_picture column already exists";
        }
    } catch (PDOException $e2) {
        $success = false;
        $messages[] = "❌ Error adding profile_picture column (alternative method): " . $e2->getMessage();
    }
}

// Add notification_preferences column
try {
    $query = "ALTER TABLE members ADD COLUMN IF NOT EXISTS notification_preferences JSON DEFAULT NULL";
    $db->exec($query);
    $messages[] = "✅ Added notification_preferences column to members table";
} catch (PDOException $e) {
    $success = false;
    $messages[] = "❌ Error adding notification_preferences column: " . $e->getMessage();
    
    // Try alternative syntax for MySQL versions that don't support JSON or IF NOT EXISTS
    try {
        // Check if column exists
        $query = "SHOW COLUMNS FROM members LIKE 'notification_preferences'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            // Column doesn't exist, add it as TEXT instead of JSON for compatibility
            $query = "ALTER TABLE members ADD COLUMN notification_preferences TEXT DEFAULT NULL";
            $db->exec($query);
            $success = true;
            $messages[] = "✅ Added notification_preferences column as TEXT (alternative method)";
        } else {
            $success = true;
            $messages[] = "✅ notification_preferences column already exists";
        }
    } catch (PDOException $e2) {
        $success = false;
        $messages[] = "❌ Error adding notification_preferences column (alternative method): " . $e2->getMessage();
    }
}

// Create uploads/profiles directory if it doesn't exist
try {
    $profiles_dir = __DIR__ . '/uploads/profiles';
    if (!file_exists($profiles_dir)) {
        mkdir($profiles_dir, 0755, true);
        $messages[] = "✅ Created profiles directory";
    } else {
        $messages[] = "✅ Profiles directory already exists";
    }
} catch (Exception $e) {
    $success = false;
    $messages[] = "❌ Error creating profiles directory: " . $e->getMessage();
}

// Add link to member sidebar
try {
    $sidebar_file = __DIR__ . '/includes/sidebar.php';
    if (file_exists($sidebar_file)) {
        $sidebar_content = file_get_contents($sidebar_file);
        
        // Check if profile link already exists
        if (strpos($sidebar_content, 'member/profile.php') === false) {
            // Find the member services section
            $member_section_pos = strpos($sidebar_content, '<?php if (isMemberLoggedIn()): ?>');
            
            if ($member_section_pos !== false) {
                // Find the end of the member services list
                $member_section_end = strpos($sidebar_content, '</ul>', $member_section_pos);
                
                if ($member_section_end !== false) {
                    // Add the profile link before the end of the list
                    $profile_link = '
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER[\'PHP_SELF\'], \'/member/profile.php\') !== false ? \'active\' : \'\'; ?>" href="<?php echo url(\'member/profile.php\'); ?>">
                    <i class="bi bi-person-circle me-2"></i>
                    My Profile
                </a>
            </li>';
                    
                    $new_sidebar_content = substr($sidebar_content, 0, $member_section_end) . $profile_link . substr($sidebar_content, $member_section_end);
                    
                    file_put_contents($sidebar_file, $new_sidebar_content);
                    $messages[] = "✅ Added Profile link to member sidebar";
                } else {
                    $messages[] = "⚠️ Could not find end of member services list in sidebar.php";
                }
            } else {
                $messages[] = "⚠️ Could not find member services section in sidebar.php";
            }
        } else {
            $messages[] = "✅ Profile link already exists in sidebar";
        }
    } else {
        $messages[] = "⚠️ Could not find sidebar.php file";
    }
} catch (Exception $e) {
    $messages[] = "❌ Error updating sidebar: " . $e->getMessage();
}

// Display installation results
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install Profile System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 40px 0;
            background-color: #f8f9fa;
        }
        .installation-card {
            max-width: 800px;
            margin: 0 auto;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .message-list {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card installation-card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="bi bi-person-circle me-2"></i>Install Profile System</h4>
            </div>
            <div class="card-body">
                <div class="alert <?php echo $success ? 'alert-success' : 'alert-danger'; ?>">
                    <h5 class="alert-heading">
                        <?php echo $success ? '✅ Installation Completed Successfully!' : '❌ Installation Completed with Errors'; ?>
                    </h5>
                    <p>
                        <?php echo $success 
                            ? 'The profile system has been installed successfully. You can now use the profile management features.'
                            : 'The installation completed with some errors. Please check the messages below for details.'; 
                        ?>
                    </p>
                </div>
                
                <h5>Installation Messages:</h5>
                <div class="message-list border rounded p-3 bg-light">
                    <?php foreach ($messages as $message): ?>
                        <div class="mb-2"><?php echo $message; ?></div>
                    <?php endforeach; ?>
                </div>
                
                <div class="mt-4">
                    <h5>Next Steps:</h5>
                    <ol>
                        <li>Members can now update their profiles with profile pictures and notification preferences</li>
                        <li>The profile management page is available at <a href="member/profile.php">My Profile</a></li>
                        <li>Make sure members are logged in to access their profile page</li>
                    </ol>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="index.php" class="btn btn-primary">Go to Dashboard</a>
                    <a href="member/profile.php" class="btn btn-success">View Profile Page</a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
