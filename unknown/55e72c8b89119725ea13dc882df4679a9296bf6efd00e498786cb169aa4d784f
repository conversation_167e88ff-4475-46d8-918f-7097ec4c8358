<?php
/**
 * Add Sample Members to Database
 * This script will add some sample members to test the system
 */

require_once 'config/database.php';

echo "<h1>📝 Adding Sample Members to Database</h1>";

try {
    // Initialize database connection
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed!");
    }
    
    echo "<p style='color: green;'>✅ Database connected successfully!</p>";
    
    // Check if members table exists
    $query = "SHOW TABLES LIKE 'members'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ Members table doesn't exist!</p>";
        echo "<p><a href='setup_database.php' style='background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Setup Database First</a></p>";
        exit;
    }
    
    // Check if password field exists
    $query = "DESCRIBE members";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    $has_password_field = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'password') {
            $has_password_field = true;
            break;
        }
    }
    
    if (!$has_password_field) {
        echo "<p style='color: orange;'>⚠️ Password field missing. Adding it now...</p>";
        
        $query = "ALTER TABLE members ADD COLUMN password VARCHAR(255) NULL AFTER membership_status";
        $stmt = $db->prepare($query);
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✅ Password field added successfully!</p>";
            $has_password_field = true;
        } else {
            echo "<p style='color: red;'>❌ Failed to add password field.</p>";
        }
    }
    
    // Sample members data
    $sample_members = [
        [
            'first_name' => 'Juan',
            'last_name' => 'Dela Cruz',
            'email' => '<EMAIL>',
            'phone' => '09171234567',
            'address' => '123 Rizal Street, Manila',
            'status' => 'active'
        ],
        [
            'first_name' => 'Maria',
            'last_name' => 'Santos',
            'email' => '<EMAIL>',
            'phone' => '09181234567',
            'address' => '456 Bonifacio Avenue, Quezon City',
            'status' => 'active'
        ],
        [
            'first_name' => 'Jose',
            'last_name' => 'Rizal',
            'email' => '<EMAIL>',
            'phone' => '09191234567',
            'address' => '789 Mabini Street, Calamba',
            'status' => 'active'
        ],
        [
            'first_name' => 'Ana',
            'last_name' => 'Garcia',
            'email' => '<EMAIL>',
            'phone' => '09201234567',
            'address' => '321 Luna Street, Makati',
            'status' => 'inactive'
        ],
        [
            'first_name' => 'Pedro',
            'last_name' => 'Reyes',
            'email' => '<EMAIL>',
            'phone' => '09211234567',
            'address' => '654 Aguinaldo Road, Cavite',
            'status' => 'active'
        ]
    ];
    
    echo "<h2>Adding Sample Members...</h2>";
    
    $added_count = 0;
    $skipped_count = 0;
    
    foreach ($sample_members as $member) {
        // Check if email already exists
        $check_query = "SELECT COUNT(*) as count FROM members WHERE email = :email";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->bindParam(':email', $member['email']);
        $check_stmt->execute();
        
        if ($check_stmt->fetch()['count'] > 0) {
            echo "<p style='color: orange;'>⚠️ Skipped {$member['first_name']} {$member['last_name']} - email already exists</p>";
            $skipped_count++;
            continue;
        }
        
        // Insert member
        if ($has_password_field) {
            $insert_query = "INSERT INTO members (first_name, last_name, email, phone, address, membership_date, membership_status, password) 
                           VALUES (:first_name, :last_name, :email, :phone, :address, CURDATE(), :status, :password)";
        } else {
            $insert_query = "INSERT INTO members (first_name, last_name, email, phone, address, membership_date, membership_status) 
                           VALUES (:first_name, :last_name, :email, :phone, :address, CURDATE(), :status)";
        }
        
        $insert_stmt = $db->prepare($insert_query);
        $insert_stmt->bindParam(':first_name', $member['first_name']);
        $insert_stmt->bindParam(':last_name', $member['last_name']);
        $insert_stmt->bindParam(':email', $member['email']);
        $insert_stmt->bindParam(':phone', $member['phone']);
        $insert_stmt->bindParam(':address', $member['address']);
        $insert_stmt->bindParam(':status', $member['status']);
        
        if ($has_password_field) {
            $default_password = password_hash('password123', PASSWORD_DEFAULT);
            $insert_stmt->bindParam(':password', $default_password);
        }
        
        if ($insert_stmt->execute()) {
            $member_id = $db->lastInsertId();
            echo "<p style='color: green;'>✅ Added {$member['first_name']} {$member['last_name']} (ID: $member_id)</p>";
            $added_count++;
        } else {
            echo "<p style='color: red;'>❌ Failed to add {$member['first_name']} {$member['last_name']}</p>";
        }
    }
    
    echo "<h2>📊 Summary</h2>";
    echo "<p><strong>Members added:</strong> $added_count</p>";
    echo "<p><strong>Members skipped:</strong> $skipped_count</p>";
    
    // Show current member count
    $count_query = "SELECT COUNT(*) as total FROM members";
    $count_stmt = $db->prepare($count_query);
    $count_stmt->execute();
    $total_members = $count_stmt->fetch()['total'];
    
    echo "<p><strong>Total members in database:</strong> $total_members</p>";
    
    if ($total_members > 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>🎉 SUCCESS! Users are now stored in the database!</h3>";
        echo "<p>You now have $total_members member(s) in your database.</p>";
        echo "<p><strong>What you can do now:</strong></p>";
        echo "<ul>";
        echo "<li><a href='members/index.php'>View all members</a></li>";
        echo "<li><a href='members/add.php'>Add more members</a></li>";
        echo "<li><a href='members/statistics.php'>View member statistics</a></li>";
        echo "<li><a href='members/bulk_operations.php'>Import/Export members</a></li>";
        echo "</ul>";
        echo "</div>";
        
        // Show sample of stored members
        echo "<h3>Sample of Stored Members:</h3>";
        $sample_query = "SELECT id, first_name, last_name, email, membership_status, membership_date FROM members ORDER BY id DESC LIMIT 5";
        $sample_stmt = $db->prepare($sample_query);
        $sample_stmt->execute();
        $stored_members = $sample_stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Name</th><th>Email</th><th>Status</th><th>Date</th></tr>";
        
        foreach ($stored_members as $stored_member) {
            echo "<tr>";
            echo "<td>" . $stored_member['id'] . "</td>";
            echo "<td>" . htmlspecialchars($stored_member['first_name'] . ' ' . $stored_member['last_name']) . "</td>";
            echo "<td>" . htmlspecialchars($stored_member['email']) . "</td>";
            echo "<td><span style='background: " . ($stored_member['membership_status'] === 'active' ? '#28a745' : '#6c757d') . "; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px;'>" . ucfirst($stored_member['membership_status']) . "</span></td>";
            echo "<td>" . $stored_member['membership_date'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if ($has_password_field) {
            echo "<p style='background: #e7f3ff; border: 1px solid #b3d9ff; color: #004085; padding: 10px; border-radius: 5px;'>";
            echo "<strong>📝 Note:</strong> All sample members have been created with the default password: <code>password123</code>";
            echo "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ Error occurred:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p><strong>Possible solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Make sure XAMPP/WAMP is running</li>";
    echo "<li>Check database credentials in config/database.php</li>";
    echo "<li>Run <a href='setup_database.php'>setup_database.php</a> first</li>";
    echo "</ul>";
    echo "</div>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h1, h2, h3 { color: #333; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    a { color: #007bff; text-decoration: none; }
    a:hover { text-decoration: underline; }
    code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
</style>
