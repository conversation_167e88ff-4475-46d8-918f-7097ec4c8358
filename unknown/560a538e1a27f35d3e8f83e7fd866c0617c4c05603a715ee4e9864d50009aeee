<?php
/**
 * Navigation Test Page
 * This page tests all navigation links to identify any issues
 */

// Start session and include required files
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Function to test if a file exists and is accessible
function testNavLink($name, $path, $full_url) {
    $file_exists = file_exists($path);
    $status = $file_exists ? '✅' : '❌';
    
    echo "<tr>";
    echo "<td>{$name}</td>";
    echo "<td>{$path}</td>";
    echo "<td>{$status}</td>";
    echo "<td><a href='{$full_url}' target='_blank' class='btn btn-sm btn-primary'>Test Link</a></td>";
    echo "</tr>";
    
    return $file_exists;
}

// Test database connection
$db_status = '❌';
try {
    $database = new Database();
    $db = $database->getConnection();
    if ($db) {
        $db_status = '✅';
    }
} catch (Exception $e) {
    $db_error = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            background-color: #f8f9fa;
        }
        .status-good { color: #198754; }
        .status-bad { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4"><i class="bi bi-bug me-2"></i>Navigation Test & Diagnostics</h1>
        
        <!-- System Status -->
        <div class="test-section">
            <h2>System Status</h2>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Database Connection:</strong> <span class="<?php echo $db_status === '✅' ? 'status-good' : 'status-bad'; ?>"><?php echo $db_status; ?></span></p>
                    <?php if (isset($db_error)): ?>
                        <p class="text-danger">Error: <?php echo htmlspecialchars($db_error); ?></p>
                    <?php endif; ?>
                </div>
                <div class="col-md-6">
                    <p><strong>Base URL:</strong> <?php echo BASE_URL; ?></p>
                    <p><strong>Current URL:</strong> <?php echo getCurrentUrl(); ?></p>
                </div>
            </div>
        </div>

        <!-- Function Tests -->
        <div class="test-section">
            <h2>Function Tests</h2>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>isLoggedIn():</strong> <?php echo isLoggedIn() ? '✅ True' : '❌ False'; ?></p>
                    <p><strong>isAdmin():</strong> <?php echo isAdmin() ? '✅ True' : '❌ False'; ?></p>
                </div>
                <div class="col-md-4">
                    <p><strong>isMemberLoggedIn():</strong> <?php echo isMemberLoggedIn() ? '✅ True' : '❌ False'; ?></p>
                    <p><strong>url() function:</strong> <?php echo function_exists('url') ? '✅ Available' : '❌ Missing'; ?></p>
                </div>
                <div class="col-md-4">
                    <p><strong>Test URL:</strong> <?php echo url('test/path'); ?></p>
                </div>
            </div>
        </div>

        <!-- Navigation Links Test -->
        <div class="test-section">
            <h2>Navigation Links Test</h2>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Page Name</th>
                            <th>File Path</th>
                            <th>File Exists</th>
                            <th>Test Link</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Test main navigation links
                        $nav_links = [
                            'Members' => ['members/index.php', url('members/index.php')],
                            'Book Loans' => ['loans/index.php', url('loans/index.php')],
                            'Reports' => ['reports/index.php', url('reports/index.php')],
                            'Books' => ['books/index.php', url('books/index.php')],
                            'Manage Users' => ['admin/users.php', url('admin/users.php')],
                            'Settings' => ['admin/settings.php', url('admin/settings.php')],
                            'Email Settings' => ['admin/email_settings.php', url('admin/email_settings.php')],
                            'Admin Dashboard' => ['admin/dashboard.php', url('admin/dashboard.php')]
                        ];

                        $total_links = count($nav_links);
                        $working_links = 0;

                        foreach ($nav_links as $name => $link_data) {
                            if (testNavLink($name, $link_data[0], $link_data[1])) {
                                $working_links++;
                            }
                        }
                        ?>
                    </tbody>
                </table>
                
                <div class="alert alert-info">
                    <strong>Summary:</strong> <?php echo $working_links; ?> out of <?php echo $total_links; ?> navigation links are working.
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="test-section">
            <h2>Quick Actions</h2>
            <div class="row">
                <div class="col-md-3">
                    <a href="<?php echo url('admin/dashboard.php'); ?>" class="btn btn-primary w-100 mb-2">
                        <i class="bi bi-speedometer2 me-2"></i>Admin Dashboard
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="<?php echo url('members/index.php'); ?>" class="btn btn-success w-100 mb-2">
                        <i class="bi bi-people me-2"></i>Members
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="<?php echo url('books/index.php'); ?>" class="btn btn-info w-100 mb-2">
                        <i class="bi bi-book me-2"></i>Books
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="<?php echo url('reports/index.php'); ?>" class="btn btn-warning w-100 mb-2">
                        <i class="bi bi-file-earmark-bar-graph me-2"></i>Reports
                    </a>
                </div>
            </div>
        </div>

        <!-- Error Log -->
        <div class="test-section">
            <h2>Error Checking</h2>
            <div class="alert alert-warning">
                <strong>Note:</strong> If any navigation links are not working, check the following:
                <ul class="mb-0 mt-2">
                    <li>Ensure you're accessing the correct base URL: <code><?php echo BASE_URL; ?></code></li>
                    <li>Check that all required files exist in their respective directories</li>
                    <li>Verify database connection is working</li>
                    <li>Make sure PHP session is properly started</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
