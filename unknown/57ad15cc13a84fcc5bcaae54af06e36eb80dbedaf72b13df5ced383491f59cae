<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if member is logged in
if (!isMemberLoggedIn()) {
    redirect('../member_login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get member ID from session
$member_id = $_SESSION['member_id'];

// Initialize variables
$success_message = '';
$error_message = '';
$member = [];

// Get member details
$query = "SELECT * FROM members WHERE id = :member_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$member = $stmt->fetch();

// Process profile update form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    // Get form data
    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $address = trim($_POST['address']);
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validate input
    $errors = [];
    
    if (empty($first_name)) {
        $errors[] = 'First name is required';
    }
    
    if (empty($last_name)) {
        $errors[] = 'Last name is required';
    }
    
    if (empty($email)) {
        $errors[] = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format';
    }
    
    // Check if email already exists (if changed)
    if ($email !== $member['email']) {
        $query = "SELECT id FROM members WHERE email = :email AND id != :member_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $errors[] = 'Email already exists. Please use a different email address.';
        }
    }
    
    // Check password if changing
    if (!empty($new_password)) {
        // Verify current password
        if (empty($current_password)) {
            $errors[] = 'Current password is required to set a new password';
        } elseif (!password_verify($current_password, $member['password'])) {
            $errors[] = 'Current password is incorrect';
        }
        
        // Validate new password
        if (strlen($new_password) < 6) {
            $errors[] = 'New password must be at least 6 characters long';
        }
        
        if ($new_password !== $confirm_password) {
            $errors[] = 'New passwords do not match';
        }
    }
    
    // Process profile picture upload
    $profile_picture = $member['profile_picture']; // Keep existing by default
    
    if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2MB
        
        if (!in_array($_FILES['profile_picture']['type'], $allowed_types)) {
            $errors[] = 'Invalid image format. Only JPG, PNG, and GIF are allowed.';
        } elseif ($_FILES['profile_picture']['size'] > $max_size) {
            $errors[] = 'Image size exceeds the maximum allowed (2MB).';
        } else {
            // Create uploads directory if it doesn't exist
            $upload_dir = '../uploads/profiles/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            // Generate unique filename
            $filename = time() . '_' . basename($_FILES['profile_picture']['name']);
            $target_file = $upload_dir . $filename;
            
            // Move uploaded file
            if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $target_file)) {
                // Delete old profile picture if exists
                if (!empty($member['profile_picture']) && file_exists($upload_dir . $member['profile_picture'])) {
                    unlink($upload_dir . $member['profile_picture']);
                }
                
                $profile_picture = $filename;
            } else {
                $errors[] = 'Failed to upload profile picture.';
            }
        }
    }
    
    // Process notification preferences
    $notification_preferences = [
        'due_date_reminder' => isset($_POST['due_date_reminder']) ? 1 : 0,
        'overdue_notification' => isset($_POST['overdue_notification']) ? 1 : 0,
        'reservation_notification' => isset($_POST['reservation_notification']) ? 1 : 0,
        'newsletter' => isset($_POST['newsletter']) ? 1 : 0
    ];
    
    // Convert to JSON
    $notification_preferences_json = json_encode($notification_preferences);
    
    // If no errors, update profile
    if (empty($errors)) {
        try {
            // Start transaction
            $db->beginTransaction();
            
            // Prepare query
            $query = "UPDATE members SET 
                      first_name = :first_name,
                      last_name = :last_name,
                      email = :email,
                      phone = :phone,
                      address = :address,
                      profile_picture = :profile_picture,
                      notification_preferences = :notification_preferences";
            
            // Add password update if changing
            if (!empty($new_password)) {
                $query .= ", password = :password";
            }
            
            $query .= " WHERE id = :member_id";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(':first_name', $first_name);
            $stmt->bindParam(':last_name', $last_name);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':phone', $phone);
            $stmt->bindParam(':address', $address);
            $stmt->bindParam(':profile_picture', $profile_picture);
            $stmt->bindParam(':notification_preferences', $notification_preferences_json);
            $stmt->bindParam(':member_id', $member_id);
            
            // Bind password if changing
            if (!empty($new_password)) {
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt->bindParam(':password', $hashed_password);
            }
            
            if ($stmt->execute()) {
                // Update session variables
                $_SESSION['member_name'] = $first_name . ' ' . $last_name;
                $_SESSION['member_email'] = $email;
                
                // Commit transaction
                $db->commit();
                
                $success_message = 'Profile updated successfully';
                
                // Refresh member data
                $query = "SELECT * FROM members WHERE id = :member_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':member_id', $member_id);
                $stmt->execute();
                $member = $stmt->fetch();
            } else {
                $db->rollBack();
                $error_message = 'Failed to update profile';
            }
        } catch (Exception $e) {
            $db->rollBack();
            $error_message = 'Error: ' . $e->getMessage();
        }
    } else {
        $error_message = implode('<br>', $errors);
    }
}

// Parse notification preferences
$notification_preferences = [];
if (!empty($member['notification_preferences'])) {
    $notification_preferences = json_decode($member['notification_preferences'], true);
}

// Set defaults if not set
if (empty($notification_preferences)) {
    $notification_preferences = [
        'due_date_reminder' => 1,
        'overdue_notification' => 1,
        'reservation_notification' => 1,
        'newsletter' => 0
    ];
}

// Page title
$page_title = 'My Profile';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include '../includes/head.php'; ?>
    <style>
        .profile-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .profile-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        .profile-picture {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
            border: 5px solid #fff;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin: 0 auto 20px;
            display: block;
        }
        .profile-picture-preview {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
            border: 5px solid #fff;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin: 10px auto;
            display: block;
        }
        .nav-pills .nav-link.active {
            background-color: #007bff;
        }
        .nav-pills .nav-link {
            color: #495057;
        }
        .nav-pills .nav-link:hover:not(.active) {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-person-circle me-2 text-primary"></i>My Profile</h1>
                </div>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card profile-card">
                            <div class="card-body text-center">
                                <?php if (!empty($member['profile_picture']) && file_exists('../uploads/profiles/' . $member['profile_picture'])): ?>
                                    <img src="<?php echo url('../uploads/profiles/' . $member['profile_picture']); ?>" alt="Profile Picture" class="profile-picture">
                                <?php else: ?>
                                    <img src="<?php echo url('../assets/img/default-profile.jpg'); ?>" alt="Default Profile" class="profile-picture">
                                <?php endif; ?>
                                
                                <h4><?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?></h4>
                                <p class="text-muted"><?php echo htmlspecialchars($member['email']); ?></p>
                                
                                <div class="d-grid gap-2 mt-3">
                                    <a href="<?php echo url('member/my_loans.php'); ?>" class="btn btn-outline-primary">
                                        <i class="bi bi-journal-bookmark me-1"></i> My Loans
                                    </a>
                                    <a href="<?php echo url('member/self_checkout.php'); ?>" class="btn btn-outline-success">
                                        <i class="bi bi-upc-scan me-1"></i> Self-Checkout
                                    </a>
                                </div>
                            </div>
                            <div class="card-footer bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">Member since: <?php echo date('F j, Y', strtotime($member['membership_date'])); ?></small>
                                    <span class="badge bg-success">Active</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-8">
                        <div class="card profile-card">
                            <div class="card-header bg-primary text-white">
                                <ul class="nav nav-pills card-header-pills" id="profileTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active text-white" id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" type="button" role="tab" aria-controls="personal" aria-selected="true">
                                            <i class="bi bi-person me-1"></i> Personal Info
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link text-white" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab" aria-controls="security" aria-selected="false">
                                            <i class="bi bi-shield-lock me-1"></i> Security
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link text-white" id="preferences-tab" data-bs-toggle="tab" data-bs-target="#preferences" type="button" role="tab" aria-controls="preferences" aria-selected="false">
                                            <i class="bi bi-gear me-1"></i> Preferences
                                        </button>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <form action="" method="post" enctype="multipart/form-data">
                                    <div class="tab-content" id="profileTabsContent">
                                        <!-- Personal Info Tab -->
                                        <div class="tab-pane fade show active" id="personal" role="tabpanel" aria-labelledby="personal-tab">
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="first_name" class="form-label">First Name</label>
                                                    <input type="text" class="form-control" id="first_name" name="first_name" value="<?php echo htmlspecialchars($member['first_name']); ?>" required>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="last_name" class="form-label">Last Name</label>
                                                    <input type="text" class="form-control" id="last_name" name="last_name" value="<?php echo htmlspecialchars($member['last_name']); ?>" required>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="email" class="form-label">Email Address</label>
                                                <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($member['email']); ?>" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="phone" class="form-label">Phone Number</label>
                                                <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($member['phone']); ?>">
                                            </div>
                                            <div class="mb-3">
                                                <label for="address" class="form-label">Address</label>
                                                <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($member['address']); ?></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label for="profile_picture" class="form-label">Profile Picture</label>
                                                <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                                                <div class="form-text">Maximum file size: 2MB. Supported formats: JPG, PNG, GIF</div>
                                                
                                                <div id="profile-picture-preview-container" class="mt-3 text-center" style="display: none;">
                                                    <h6>Preview:</h6>
                                                    <img id="profile-picture-preview" src="#" alt="Profile Picture Preview" class="profile-picture-preview">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Security Tab -->
                                        <div class="tab-pane fade" id="security" role="tabpanel" aria-labelledby="security-tab">
                                            <div class="mb-3">
                                                <label for="current_password" class="form-label">Current Password</label>
                                                <input type="password" class="form-control" id="current_password" name="current_password">
                                                <div class="form-text">Enter your current password to change to a new password</div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="new_password" class="form-label">New Password</label>
                                                <input type="password" class="form-control" id="new_password" name="new_password">
                                                <div class="form-text">Leave blank if you don't want to change your password</div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="confirm_password" class="form-label">Confirm New Password</label>
                                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                            </div>
                                            <div class="alert alert-info">
                                                <i class="bi bi-info-circle me-2"></i>
                                                Password must be at least 6 characters long.
                                            </div>
                                        </div>
                                        
                                        <!-- Preferences Tab -->
                                        <div class="tab-pane fade" id="preferences" role="tabpanel" aria-labelledby="preferences-tab">
                                            <h5 class="mb-3">Notification Preferences</h5>
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="due_date_reminder" name="due_date_reminder" <?php echo ($notification_preferences['due_date_reminder'] ?? 1) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="due_date_reminder">Due Date Reminders</label>
                                                <div class="form-text">Receive email reminders before books are due</div>
                                            </div>
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="overdue_notification" name="overdue_notification" <?php echo ($notification_preferences['overdue_notification'] ?? 1) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="overdue_notification">Overdue Notifications</label>
                                                <div class="form-text">Receive notifications when books are overdue</div>
                                            </div>
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="reservation_notification" name="reservation_notification" <?php echo ($notification_preferences['reservation_notification'] ?? 1) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="reservation_notification">Reservation Notifications</label>
                                                <div class="form-text">Receive notifications when reserved books are available</div>
                                            </div>
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="newsletter" name="newsletter" <?php echo ($notification_preferences['newsletter'] ?? 0) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="newsletter">Library Newsletter</label>
                                                <div class="form-text">Receive monthly newsletter with library updates and new books</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid gap-2 mt-4">
                                        <button type="submit" name="update_profile" class="btn btn-primary">
                                            <i class="bi bi-save me-1"></i> Save Changes
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Profile picture preview
            const profilePictureInput = document.getElementById('profile_picture');
            const profilePicturePreview = document.getElementById('profile-picture-preview');
            const previewContainer = document.getElementById('profile-picture-preview-container');
            
            profilePictureInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        profilePicturePreview.src = e.target.result;
                        previewContainer.style.display = 'block';
                    }
                    
                    reader.readAsDataURL(this.files[0]);
                } else {
                    previewContainer.style.display = 'none';
                }
            });
            
            // Handle tab navigation from URL hash
            const hash = window.location.hash;
            if (hash) {
                const tab = document.querySelector(`button[data-bs-target="${hash}"]`);
                if (tab) {
                    const tabInstance = new bootstrap.Tab(tab);
                    tabInstance.show();
                }
            }
            
            // Update URL hash when tab changes
            const tabs = document.querySelectorAll('button[data-bs-toggle="tab"]');
            tabs.forEach(tab => {
                tab.addEventListener('shown.bs.tab', function(event) {
                    const target = event.target.getAttribute('data-bs-target');
                    window.location.hash = target;
                });
            });
        });
    </script>
</body>
</html>
