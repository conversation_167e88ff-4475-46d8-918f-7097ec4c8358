<?php
// Simple test script to verify UI fixes
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Set test session data if not exists
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
    $_SESSION['role'] = 'admin';
}

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>UI Fixes Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css'>";
echo "<link rel='stylesheet' href='css/dashboard-fixes.css'>";
echo "</head>";
echo "<body>";

echo "<div class='container mt-5'>";
echo "<h1>UI Fixes Test</h1>";

echo "<div class='alert alert-info'>";
echo "<h4>Testing UI Components:</h4>";
echo "<ul>";
echo "<li>Sign out button functionality</li>";
echo "<li>Notification system</li>";
echo "<li>CSS z-index fixes</li>";
echo "<li>Button clickability</li>";
echo "</ul>";
echo "</div>";

// Test sign out button
echo "<div class='card mb-3'>";
echo "<div class='card-header'>";
echo "<h5>Sign Out Button Test</h5>";
echo "</div>";
echo "<div class='card-body'>";
echo "<p>This button should be clickable and redirect to logout:</p>";
echo "<a class='btn btn-danger btn-sm mx-2' href='../logout.php' style='z-index: 1060; position: relative; pointer-events: auto;'>";
echo "<i class='bi bi-box-arrow-right me-1'></i>Test Sign out";
echo "</a>";
echo "</div>";
echo "</div>";

// Test notification bell
echo "<div class='card mb-3'>";
echo "<div class='card-header'>";
echo "<h5>Notification Bell Test</h5>";
echo "</div>";
echo "<div class='card-body'>";
echo "<p>This bell should be clickable:</p>";
echo "<div class='nav-item me-2'>";
echo "<a class='nav-link position-relative bell-icon' href='#'>";
echo "<span class='bell-icon'><i class='bi bi-bell fs-5 text-dark'></i></span>";
echo "<span class='position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge'>";
echo "3";
echo "</span>";
echo "</a>";
echo "</div>";
echo "</div>";
echo "</div>";

// Test CSS fixes
echo "<div class='card mb-3'>";
echo "<div class='card-header'>";
echo "<h5>CSS Fixes Applied</h5>";
echo "</div>";
echo "<div class='card-body'>";
echo "<ul>";
echo "<li>✅ Dashboard fixes CSS loaded</li>";
echo "<li>✅ Z-index fixes applied</li>";
echo "<li>✅ Button pointer events enabled</li>";
echo "<li>✅ Notification container positioning fixed</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "<div class='alert alert-success'>";
echo "<strong>Test Complete!</strong> If you can see this page and the buttons are clickable, the fixes are working.";
echo "</div>";

echo "<a href='dashboard.php' class='btn btn-primary'>Back to Dashboard</a>";

echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    console.log('UI Fixes Test loaded successfully');";
echo "    ";
echo "    // Test sign out button";
echo "    const testSignOutBtn = document.querySelector('a[href=\"../logout.php\"]');";
echo "    if (testSignOutBtn) {";
echo "        testSignOutBtn.addEventListener('click', function(e) {";
echo "            e.preventDefault();";
echo "            alert('Sign out button is working! (Prevented actual logout for testing)');";
echo "        });";
echo "    }";
echo "    ";
echo "    // Test notification bell";
echo "    const testBell = document.querySelector('.bell-icon');";
echo "    if (testBell) {";
echo "        testBell.addEventListener('click', function(e) {";
echo "            e.preventDefault();";
echo "            alert('Notification bell is working!');";
echo "        });";
echo "    }";
echo "});";
echo "</script>";

echo "</body>";
echo "</html>";
?>
