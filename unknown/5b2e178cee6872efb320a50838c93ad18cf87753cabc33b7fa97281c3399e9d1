<?php
/**
 * Dashboard Modals Templates
 * Contains HTML templates for the modals used in the enhanced dashboard
 */

// Widget settings modal removed

/**
 * Renders the add task modal
 */
function renderAddTaskModal() {
    ?>
    <div class="modal fade" id="addTaskModal" tabindex="-1" aria-labelledby="addTaskModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addTaskModalLabel">Add New Task</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addTaskForm">
                        <div class="mb-3">
                            <label for="taskTitle" class="form-label">Task Title</label>
                            <input type="text" class="form-control" id="taskTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="taskDescription" class="form-label">Description (Optional)</label>
                            <textarea class="form-control" id="taskDescription" rows="3"></textarea>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="taskDueDate" class="form-label">Due Date</label>
                                <input type="date" class="form-control" id="taskDueDate">
                            </div>
                            <div class="col-md-6">
                                <label for="taskPriority" class="form-label">Priority</label>
                                <select class="form-select" id="taskPriority">
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="taskCategory" class="form-label">Category</label>
                            <select class="form-select" id="taskCategory">
                                <option value="general" selected>General</option>
                                <option value="books">Books</option>
                                <option value="members">Members</option>
                                <option value="events">Events</option>
                                <option value="maintenance">Maintenance</option>
                            </select>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="taskReminder">
                            <label class="form-check-label" for="taskReminder">
                                Set reminder
                            </label>
                        </div>
                        <div class="mb-3 reminder-options" style="display: none;">
                            <label for="reminderTime" class="form-label">Remind me</label>
                            <select class="form-select" id="reminderTime">
                                <option value="15">15 minutes before</option>
                                <option value="30">30 minutes before</option>
                                <option value="60">1 hour before</option>
                                <option value="120">2 hours before</option>
                                <option value="1440">1 day before</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveTaskBtn">Save Task</button>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * Renders the save filter modal
 */
function renderSaveFilterModal() {
    ?>
    <div class="modal fade" id="saveFilterModal" tabindex="-1" aria-labelledby="saveFilterModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="saveFilterModalLabel">Save Current Filter</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="saveFilterForm">
                        <div class="mb-3">
                            <label for="filterName" class="form-label">Filter Name</label>
                            <input type="text" class="form-control" id="filterName" required>
                        </div>
                        <div class="mb-3">
                            <label for="filterDescription" class="form-label">Description (Optional)</label>
                            <textarea class="form-control" id="filterDescription" rows="2"></textarea>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="filterDefault">
                            <label class="form-check-label" for="filterDefault">
                                Set as default filter
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveFilterBtn">Save Filter</button>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * Renders the book condition modal
 */
function renderBookConditionModal() {
    ?>
    <div class="modal fade" id="bookConditionModal" tabindex="-1" aria-labelledby="bookConditionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bookConditionModalLabel">Book Condition Assessment</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="bookConditionForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="bookTitle" class="form-label">Book Title</label>
                                <input type="text" class="form-control" id="bookTitle" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="bookISBN" class="form-label">ISBN</label>
                                <input type="text" class="form-control" id="bookISBN" readonly>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Overall Condition</label>
                            <div class="condition-rating">
                                <div class="rating-stars">
                                    <i class="bi bi-star-fill" data-rating="1"></i>
                                    <i class="bi bi-star-fill" data-rating="2"></i>
                                    <i class="bi bi-star-fill" data-rating="3"></i>
                                    <i class="bi bi-star" data-rating="4"></i>
                                    <i class="bi bi-star" data-rating="5"></i>
                                </div>
                                <span class="ms-2 rating-text">Good</span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="coverCondition" class="form-label">Cover Condition</label>
                                <select class="form-select" id="coverCondition">
                                    <option value="new">Like New</option>
                                    <option value="good" selected>Good</option>
                                    <option value="fair">Fair</option>
                                    <option value="poor">Poor</option>
                                    <option value="damaged">Damaged</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="bindingCondition" class="form-label">Binding Condition</label>
                                <select class="form-select" id="bindingCondition">
                                    <option value="new">Like New</option>
                                    <option value="good" selected>Good</option>
                                    <option value="fair">Fair</option>
                                    <option value="poor">Poor</option>
                                    <option value="damaged">Damaged</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="pagesCondition" class="form-label">Pages Condition</label>
                                <select class="form-select" id="pagesCondition">
                                    <option value="new">Like New</option>
                                    <option value="good" selected>Good</option>
                                    <option value="fair">Fair</option>
                                    <option value="poor">Poor</option>
                                    <option value="damaged">Damaged</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="conditionNotes" class="form-label">Condition Notes</label>
                            <textarea class="form-control" id="conditionNotes" rows="3" placeholder="Describe any specific damage or issues..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Condition Photos</label>
                            <div class="condition-images">
                                <img src="../assets/images/placeholder.jpg" class="condition-image" alt="Condition Photo">
                                <div class="d-flex align-items-center justify-content-center" style="width: 80px; height: 80px; border: 1px dashed #dee2e6; border-radius: 0.25rem; cursor: pointer;">
                                    <i class="bi bi-plus-lg"></i>
                                </div>
                            </div>
                            <div class="mt-2">
                                <input type="file" class="form-control" id="conditionPhotos" multiple accept="image/*">
                            </div>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="needsRepair">
                            <label class="form-check-label" for="needsRepair">
                                Book needs repair
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveConditionBtn">Save Assessment</button>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * Renders the quick member registration modal
 */
function renderQuickMemberRegistrationModal() {
    ?>
    <div class="modal fade" id="quickMemberRegistrationModal" tabindex="-1" aria-labelledby="quickMemberRegistrationModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="quickMemberRegistrationModalLabel">Quick Member Registration</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="quickMemberRegistrationForm" class="quick-registration-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="memberFirstName" class="required-field">First Name</label>
                                    <input type="text" class="form-control" id="memberFirstName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="memberLastName" class="required-field">Last Name</label>
                                    <input type="text" class="form-control" id="memberLastName" required>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="memberEmail" class="required-field">Email</label>
                            <input type="email" class="form-control" id="memberEmail" required>
                        </div>
                        <div class="form-group">
                            <label for="memberPhone">Phone</label>
                            <input type="tel" class="form-control" id="memberPhone">
                        </div>
                        <div class="form-group">
                            <label for="membershipType" class="required-field">Membership Type</label>
                            <select class="form-select" id="membershipType" required>
                                <option value="standard" selected>Standard</option>
                                <option value="student">Student</option>
                                <option value="senior">Senior</option>
                                <option value="family">Family</option>
                            </select>
                        </div>
                        <div class="form-check mt-3">
                            <input class="form-check-input" type="checkbox" id="sendCredentials" checked>
                            <label class="form-check-label" for="sendCredentials">
                                Send login credentials via email
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="registerMemberBtn">Register Member</button>
                </div>
            </div>
        </div>
    </div>
    <?php
}
