<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get a book with cover image
$query = "SELECT * FROM books WHERE cover_image IS NOT NULL AND cover_image != '' LIMIT 1";
$stmt = $db->prepare($query);
$stmt->execute();
$book = $stmt->fetch();

if (!$book) {
    echo "No books with cover images found. Please run the fix first.";
    exit;
}

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Book Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .book-cover {
            max-width: 100%;
            height: auto;
            max-height: 400px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            cursor: pointer;
        }
        .book-cover-placeholder {
            width: 300px;
            height: 400px;
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>Test Book Details Display</h2>
        
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-4 mb-md-0">
                        <h5>Book Cover Test</h5>
                        
                        <?php if (!empty($book['cover_image'])): ?>
                            <?php
                            // Check if the cover_image is a URL or a local file
                            if (strpos($book['cover_image'], 'http') === 0) {
                                $image_src = $book['cover_image'];
                            } else {
                                // Try different paths to find the image
                                $possible_paths = [
                                    'uploads/covers/' . $book['cover_image'],
                                    './uploads/covers/' . $book['cover_image']
                                ];
                                
                                $image_src = 'uploads/covers/' . $book['cover_image']; // Default
                                foreach ($possible_paths as $path) {
                                    if (file_exists($path)) {
                                        $image_src = $path;
                                        break;
                                    }
                                }
                            }
                            ?>
                            
                            <div class="mb-3">
                                <small class="text-muted">Image source: <?php echo h($image_src); ?></small><br>
                                <small class="text-muted">File exists: <?php echo file_exists($image_src) ? 'Yes' : 'No'; ?></small>
                            </div>
                            
                            <img src="<?php echo h($image_src); ?>" class="book-cover" alt="<?php echo h($book['title']); ?>" 
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                            <div class="book-cover-placeholder" style="display: none;">
                                <i class="bi bi-book fs-1 text-secondary"></i>
                            </div>
                        <?php else: ?>
                            <div class="book-cover-placeholder">
                                <i class="bi bi-book fs-1 text-secondary"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-md-8">
                        <h2 class="mb-2"><?php echo h($book['title']); ?></h2>
                        <h5 class="text-muted mb-3">by <?php echo h($book['author']); ?></h5>

                        <div class="mb-3">
                            <?php if (!empty($book['category'])): ?>
                                <span class="badge bg-secondary"><?php echo h($book['category']); ?></span>
                            <?php endif; ?>
                            <?php if (!empty($book['publication_year'])): ?>
                                <span class="badge bg-light text-dark"><?php echo h($book['publication_year']); ?></span>
                            <?php endif; ?>
                        </div>

                        <div class="mb-4">
                            <p><strong>ISBN:</strong> <?php echo h($book['isbn'] ?: 'N/A'); ?></p>
                            <p><strong>Publisher:</strong> <?php echo h($book['publisher'] ?: 'N/A'); ?></p>
                            <p><strong>Shelf Location:</strong> <?php echo h($book['shelf_location'] ?: 'N/A'); ?></p>
                            <p><strong>Cover Image:</strong> <?php echo h($book['cover_image']); ?></p>
                            <p>
                                <strong>Availability:</strong>
                                <?php if ($book['available_quantity'] > 0): ?>
                                    <span class="text-success">
                                        <?php echo h($book['available_quantity']); ?> of <?php echo h($book['quantity']); ?> copies available
                                    </span>
                                <?php else: ?>
                                    <span class="text-danger">Currently unavailable</span>
                                <?php endif; ?>
                            </p>
                        </div>

                        <?php if (!empty($book['description'])): ?>
                            <div class="mb-4">
                                <h5>Description</h5>
                                <p><?php echo nl2br(h($book['description'])); ?></p>
                            </div>
                        <?php endif; ?>

                        <div class="d-grid gap-2 d-md-block">
                            <a href="book_details.php?id=<?php echo $book['id']; ?>" class="btn btn-primary">
                                <i class="bi bi-eye me-2"></i>View Full Details
                            </a>
                            <a href="catalog.php" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Catalog
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Debug Information</h3>
            <div class="alert alert-info">
                <p><strong>Book ID:</strong> <?php echo $book['id']; ?></p>
                <p><strong>Cover Image Field:</strong> <?php echo $book['cover_image'] ? h($book['cover_image']) : 'NULL'; ?></p>
                <p><strong>Current Directory:</strong> <?php echo getcwd(); ?></p>
                <p><strong>Script Name:</strong> <?php echo $_SERVER['SCRIPT_NAME']; ?></p>
            </div>
        </div>
    </div>
</body>
</html>
