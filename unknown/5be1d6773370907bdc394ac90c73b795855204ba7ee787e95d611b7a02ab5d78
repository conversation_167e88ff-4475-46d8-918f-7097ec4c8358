<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Redirect based on user type
if (isLoggedIn()) {
    // Staff is logged in, redirect based on role
    if (isAdmin()) {
        redirect('admin/dashboard.php');
    } elseif (isLibrarian()) {
        redirect('librarian/dashboard.php');
    }
    // If role is neither admin nor librarian, continue to this page
} elseif (isMemberLoggedIn()) {
    // Member is logged in, redirect to member dashboard
    redirect('member_dashboard.php');
}
// If no one is logged in, continue to show the public home page

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get library statistics
$stats = [];

// Total books
$query = "SELECT COUNT(*) as total FROM books";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['total_books'] = $stmt->fetch()['total'];

// Available books
$query = "SELECT SUM(available_quantity) as available FROM books";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['available_books'] = $stmt->fetch()['available'];

// Total members
$query = "SELECT COUNT(*) as total FROM members";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['total_members'] = $stmt->fetch()['total'];

// Active loans
$query = "SELECT COUNT(*) as active FROM book_loans WHERE status = 'borrowed'";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['active_loans'] = $stmt->fetch()['active'];

// Get featured books (newest additions)
$query = "SELECT * FROM books ORDER BY created_at DESC LIMIT 6";
$stmt = $db->prepare($query);
$stmt->execute();
$featured_books = $stmt->fetchAll();

// Get popular categories
$query = "SELECT category, COUNT(*) as count FROM books WHERE category IS NOT NULL GROUP BY category ORDER BY count DESC LIMIT 6";
$stmt = $db->prepare($query);
$stmt->execute();
$popular_categories = $stmt->fetchAll();

// Get recent activities (for demo purposes)
$recent_activities = [
    ['action' => 'New book added', 'item' => 'The Great Gatsby', 'time' => '2 hours ago'],
    ['action' => 'Member joined', 'item' => 'John Doe', 'time' => '5 hours ago'],
    ['action' => 'Book returned', 'item' => '1984', 'time' => '1 day ago'],
    ['action' => 'New book added', 'item' => 'Pride and Prejudice', 'time' => '2 days ago']
];

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .hero-section {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%),
                        url('uploads/images/library.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            color: white;
            padding: 100px 0;
            min-height: 80vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5);
            color: white;
        }

        .hero-subtitle {
            font-size: 1.4rem;
            margin-bottom: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            opacity: 0.95;
        }

        .stats-card {
            background: linear-gradient(145deg, #ffffff, #f0f0f0);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.6);
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stats-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .stats-label {
            color: #6c757d;
            font-size: 1rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stats-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .book-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            height: 100%;
        }

        .book-card:hover {
            transform: translateY(-15px) rotateY(5deg);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }

        .book-cover {
            height: 280px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .book-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .book-card:hover .book-cover img {
            transform: scale(1.1);
        }

        .category-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            text-decoration: none;
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }

        .activity-item {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .activity-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .search-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 4rem 0;
            color: white;
        }

        .btn-gradient {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .floating-elements::before,
        .floating-elements::after {
            content: '';
            position: absolute;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 15s ease-in-out infinite;
        }

        .floating-elements::before {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-elements::after {
            bottom: 10%;
            right: 10%;
            animation-delay: 7s;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .stats-card {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="home.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="home.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="catalog.php">Book Catalog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">About Us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">Contact</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <?php if (isLoggedIn()): ?>
                        <a href="admin/dashboard.php" class="btn btn-outline-light me-2">Admin Dashboard</a>
                        <a href="logout.php" class="btn btn-outline-danger">Logout</a>
                    <?php elseif (isMemberLoggedIn()): ?>
                        <a href="member_dashboard.php" class="btn btn-outline-light me-2">My Dashboard</a>
                        <a href="logout.php" class="btn btn-outline-danger">Logout</a>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-outline-light me-2">
                            <i class="bi bi-person me-1"></i>Login
                        </a>
                        <a href="register.php" class="btn btn-primary">Register</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="floating-elements"></div>
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-10 mx-auto text-center">
                    <h1 class="hero-title">Welcome to Our Digital Library</h1>
                    <p class="hero-subtitle">Discover endless worlds of knowledge, adventure, and imagination at your fingertips</p>

                    <!-- Enhanced Search Bar -->
                    <div class="row justify-content-center mb-5">
                        <div class="col-md-10">
                            <form action="catalog.php" method="get" class="position-relative">
                                <div class="input-group input-group-lg shadow-lg">
                                    <input type="text" name="search" class="form-control border-0"
                                           placeholder="Search for books, authors, ISBN, or topics..."
                                           style="border-radius: 25px 0 0 25px; padding: 20px;">
                                    <button class="btn btn-gradient" type="submit" style="border-radius: 0 25px 25px 0; padding: 20px 30px;">
                                        <i class="bi bi-search me-2"></i>Explore
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="d-flex justify-content-center gap-4 flex-wrap">
                        <a href="catalog.php" class="btn btn-gradient btn-lg">
                            <i class="bi bi-book me-2"></i>Browse Collection
                        </a>
                        <?php if (!isMemberLoggedIn()): ?>
                            <a href="register.php" class="btn btn-outline-light btn-lg">
                                <i class="bi bi-person-plus me-2"></i>Join Our Community
                            </a>
                        <?php else: ?>
                            <a href="member_dashboard.php" class="btn btn-outline-light btn-lg">
                                <i class="bi bi-speedometer2 me-2"></i>My Dashboard
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Library Statistics -->
    <section class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Library at a Glance</h2>
                <p class="text-muted fs-5">Discover our growing collection and vibrant community</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon">
                            <i class="bi bi-book-fill"></i>
                        </div>
                        <div class="stats-number"><?php echo number_format($stats['total_books']); ?></div>
                        <div class="stats-label">Total Books</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon">
                            <i class="bi bi-check-circle-fill"></i>
                        </div>
                        <div class="stats-number"><?php echo number_format($stats['available_books']); ?></div>
                        <div class="stats-label">Available Now</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon">
                            <i class="bi bi-people-fill"></i>
                        </div>
                        <div class="stats-number"><?php echo number_format($stats['total_members']); ?></div>
                        <div class="stats-label">Active Members</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon">
                            <i class="bi bi-bookmark-heart-fill"></i>
                        </div>
                        <div class="stats-number"><?php echo number_format($stats['active_loans']); ?></div>
                        <div class="stats-label">Books on Loan</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Popular Categories Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Explore by Category</h2>
                <p class="text-muted fs-5">Find your next great read in these popular genres</p>
            </div>
            <div class="row g-4">
                <?php foreach ($popular_categories as $category): ?>
                    <div class="col-lg-4 col-md-6">
                        <a href="catalog.php?category=<?php echo urlencode($category['category']); ?>" class="category-card d-block">
                            <h5 class="mb-2">
                                <i class="bi bi-tag-fill me-2"></i>
                                <?php echo h($category['category']); ?>
                            </h5>
                            <p class="mb-0"><?php echo number_format($category['count']); ?> books available</p>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Recent Activity Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <!-- Featured Books will go here -->
                </div>
                <div class="col-lg-4">
                    <div class="card border-0 shadow-lg">
                        <div class="card-header bg-gradient text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-activity me-2"></i>Recent Library Activity
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($recent_activities as $activity): ?>
                                <div class="activity-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <strong><?php echo h($activity['action']); ?></strong><br>
                                            <span class="text-muted"><?php echo h($activity['item']); ?></span>
                                        </div>
                                        <small class="text-muted"><?php echo h($activity['time']); ?></small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                            <div class="text-center mt-3">
                                <a href="catalog.php" class="btn btn-sm btn-outline-primary">View All Books</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Featured Books Section -->
    <section class="search-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <div class="text-center mb-5">
                        <h2 class="text-white">Featured Books</h2>
                        <p class="text-white-50 fs-5">Discover our newest and most popular additions</p>
                    </div>

                    <div class="row g-4">
                        <?php foreach ($featured_books as $book): ?>
                            <div class="col-lg-6 col-md-6 mb-4">
                                <div class="book-card">
                                    <div class="book-cover">
                                        <?php if (!empty($book['cover_image'])): ?>
                                            <?php
                                            // Check if the cover_image is a URL or a local file
                                            if (strpos($book['cover_image'], 'http') === 0) {
                                                $image_src = $book['cover_image'];
                                            } else {
                                                // Try different paths to find the image
                                                $possible_paths = [
                                                    'uploads/covers/' . $book['cover_image'],
                                                    './uploads/covers/' . $book['cover_image']
                                                ];

                                                $image_src = 'uploads/covers/' . $book['cover_image']; // Default
                                                foreach ($possible_paths as $path) {
                                                    if (file_exists($path)) {
                                                        $image_src = $path;
                                                        break;
                                                    }
                                                }
                                            }
                                            ?>
                                            <img src="<?php echo h($image_src); ?>" alt="<?php echo h($book['title']); ?>"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                            <div style="display: none; height: 100%; align-items: center; justify-content: center;">
                                                <i class="bi bi-book fs-1 text-white"></i>
                                            </div>
                                        <?php else: ?>
                                            <div style="height: 100%; display: flex; align-items: center; justify-content: center;">
                                                <i class="bi bi-book fs-1 text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="p-4">
                                        <h5 class="card-title mb-2"><?php echo h($book['title']); ?></h5>
                                        <p class="card-text text-muted mb-2">by <?php echo h($book['author']); ?></p>

                                        <div class="d-flex flex-wrap gap-2 mb-3">
                                            <?php if (!empty($book['category'])): ?>
                                                <span class="badge bg-primary"><?php echo h($book['category']); ?></span>
                                            <?php endif; ?>
                                            <?php if (!empty($book['publication_year'])): ?>
                                                <span class="badge bg-secondary"><?php echo h($book['publication_year']); ?></span>
                                            <?php endif; ?>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="availability-status">
                                                <small class="text-<?php echo ($book['available_quantity'] > 0) ? 'success' : 'danger'; ?>">
                                                    <i class="bi <?php echo ($book['available_quantity'] > 0) ? 'bi-check-circle-fill' : 'bi-x-circle-fill'; ?> me-1"></i>
                                                    <?php if ($book['available_quantity'] > 0): ?>
                                                        <?php echo $book['available_quantity']; ?> available
                                                    <?php else: ?>
                                                        Currently unavailable
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                            <a href="book_details.php?id=<?php echo h($book['id']); ?>" class="btn btn-gradient btn-sm">
                                                <i class="bi bi-eye me-1"></i>View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="text-center mt-5">
                        <a href="catalog.php" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-collection me-2"></i>Explore Our Full Collection
                        </a>
                    </div>
                </div>

                <!-- Activity sidebar is already included above -->
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h2 class="section-title mb-3">Ready to Start Your Reading Journey?</h2>
                    <p class="fs-5 text-muted mb-4">Join thousands of book lovers in our community. Get access to our vast collection, personalized recommendations, and exclusive member benefits.</p>
                    <div class="d-flex gap-3 flex-wrap">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                            <span>Free membership</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                            <span>Extended borrowing periods</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                            <span>Digital resources access</span>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <?php if (!isMemberLoggedIn()): ?>
                        <a href="register.php" class="btn btn-gradient btn-lg mb-3 d-block">
                            <i class="bi bi-person-plus me-2"></i>Join Our Library Today
                        </a>
                        <p class="text-muted">Already a member? <a href="login.php" class="text-decoration-none">Sign in here</a></p>
                    <?php else: ?>
                        <a href="member_dashboard.php" class="btn btn-gradient btn-lg mb-3 d-block">
                            <i class="bi bi-speedometer2 me-2"></i>Go to My Dashboard
                        </a>
                        <p class="text-muted">Welcome back! Ready to discover new books?</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Footer -->
    <footer class="bg-dark text-light py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <h5 class="mb-3">
                        <i class="bi bi-book-fill me-2 text-primary"></i>
                        Library Management System
                    </h5>
                    <p class="text-light opacity-75 mb-3">
                        Your digital gateway to endless knowledge, stories, and discoveries.
                        Connecting readers with books since day one.
                    </p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-light opacity-75 fs-5"><i class="bi bi-facebook"></i></a>
                        <a href="#" class="text-light opacity-75 fs-5"><i class="bi bi-twitter"></i></a>
                        <a href="#" class="text-light opacity-75 fs-5"><i class="bi bi-instagram"></i></a>
                        <a href="#" class="text-light opacity-75 fs-5"><i class="bi bi-linkedin"></i></a>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6">
                    <h6 class="mb-3 text-primary">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="catalog.php" class="text-light opacity-75 text-decoration-none">Browse Books</a></li>
                        <li class="mb-2"><a href="search.php" class="text-light opacity-75 text-decoration-none">Advanced Search</a></li>
                        <li class="mb-2"><a href="about.php" class="text-light opacity-75 text-decoration-none">About Us</a></li>
                        <li class="mb-2"><a href="contact.php" class="text-light opacity-75 text-decoration-none">Contact</a></li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6">
                    <h6 class="mb-3 text-primary">Library Hours</h6>
                    <div class="text-light opacity-75">
                        <p class="mb-2">
                            <i class="bi bi-clock me-2"></i>
                            <strong>Monday - Friday:</strong><br>
                            <span class="ms-4">8:00 AM - 8:00 PM</span>
                        </p>
                        <p class="mb-2">
                            <i class="bi bi-clock me-2"></i>
                            <strong>Saturday:</strong><br>
                            <span class="ms-4">9:00 AM - 5:00 PM</span>
                        </p>
                        <p class="mb-0">
                            <i class="bi bi-clock me-2"></i>
                            <strong>Sunday:</strong><br>
                            <span class="ms-4">Closed</span>
                        </p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <h6 class="mb-3 text-primary">Contact Info</h6>
                    <div class="text-light opacity-75">
                        <p class="mb-2">
                            <i class="bi bi-geo-alt-fill me-2"></i>
                            123 Library Street<br>
                            <span class="ms-4">Cabadbaran City, Philippines</span>
                        </p>
                        <p class="mb-2">
                            <i class="bi bi-telephone-fill me-2"></i>
                            (*************
                        </p>
                        <p class="mb-0">
                            <i class="bi bi-envelope-fill me-2"></i>
                            <EMAIL>
                        </p>
                    </div>
                </div>
            </div>

            <hr class="my-4 opacity-25">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <small class="text-light opacity-75">
                        &copy; <?php echo date('Y'); ?> Library Management System. All rights reserved.
                    </small>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-light opacity-75">
                        <a href="terms.php" class="text-light opacity-75 text-decoration-none me-3">Terms of Service</a>
                        <a href="privacy.php" class="text-light opacity-75 text-decoration-none">Privacy Policy</a>
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript for Enhanced Interactions -->
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add loading animation to search form
        document.querySelector('form[action="catalog.php"]').addEventListener('submit', function(e) {
            const button = this.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Searching...';
            button.disabled = true;

            // Re-enable after a short delay (in case of quick redirect)
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 3000);
        });

        // Animate statistics on scroll
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const statsNumbers = entry.target.querySelectorAll('.stats-number');
                    statsNumbers.forEach(stat => {
                        const finalValue = parseInt(stat.textContent.replace(/,/g, ''));
                        animateNumber(stat, 0, finalValue, 2000);
                    });
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe stats section
        const statsSection = document.querySelector('.stats-card').closest('section');
        if (statsSection) {
            observer.observe(statsSection);
        }

        // Number animation function
        function animateNumber(element, start, end, duration) {
            const startTime = performance.now();
            const range = end - start;

            function updateNumber(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Easing function for smooth animation
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const current = Math.floor(start + (range * easeOutQuart));

                element.textContent = current.toLocaleString();

                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            }

            requestAnimationFrame(updateNumber);
        }

        // Add hover effects to book cards
        document.querySelectorAll('.book-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-15px) rotateY(5deg)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) rotateY(0)';
            });
        });

        // Add parallax effect to hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const heroSection = document.querySelector('.hero-section');
            if (heroSection) {
                heroSection.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });

        // Add fade-in animation for sections
        const fadeElements = document.querySelectorAll('.stats-card, .book-card, .category-card');
        const fadeObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });

        fadeElements.forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            fadeObserver.observe(el);
        });

        // Search suggestions (basic implementation)
        const searchInput = document.querySelector('input[name="search"]');
        if (searchInput) {
            const suggestions = ['Fiction', 'Science', 'History', 'Biography', 'Romance', 'Mystery', 'Fantasy'];

            searchInput.addEventListener('focus', function() {
                this.placeholder = suggestions[Math.floor(Math.random() * suggestions.length)] + '...';
            });

            searchInput.addEventListener('blur', function() {
                this.placeholder = 'Search for books, authors, ISBN, or topics...';
            });
        }

        // Hero title is now static - no typing effect needed
    </script>
</body>
</html>
