<?php
// Test notification bell functionality
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Set test session data
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
    $_SESSION['role'] = 'admin';
}

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Notification Bell Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css'>";
echo "<link rel='stylesheet' href='css/dashboard-fixes.css'>";
echo "<style>";
echo "body { background-color: #f8f9fa; }";
echo ".test-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 10px; margin-bottom: 2rem; }";
echo ".test-section { background: white; padding: 1.5rem; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 1rem; }";
echo ".notification-demo { background: #343a40; padding: 1rem; border-radius: 8px; position: relative; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container mt-4'>";

echo "<div class='test-header text-center'>";
echo "<h1><i class='bi bi-bell-fill me-2'></i>Notification Bell Test</h1>";
echo "<p class='mb-0'>Testing the clickable notification bell functionality</p>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h3>🔔 Interactive Notification Bell Demo</h3>";
echo "<p>Click the bell below to test the notification toggle functionality:</p>";

echo "<div class='notification-demo'>";
echo "<div class='d-flex justify-content-between align-items-center'>";
echo "<span class='text-white'>Admin Dashboard Header Simulation</span>";
echo "<div class='d-flex align-items-center'>";

// Notification bell
echo "<div class='nav-item me-3'>";
echo "<a class='nav-link position-relative bell-icon' href='#' onclick='toggleNotifications(event)' style='cursor: pointer; z-index: 1060;'>";
echo "<span><i class='bi bi-bell fs-5 text-white'></i></span>";
echo "<span class='position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge'>3</span>";
echo "</a>";
echo "</div>";

echo "<span class='text-white me-3'>Welcome, admin</span>";
echo "<button class='btn btn-danger btn-sm'>Sign out</button>";
echo "</div>";
echo "</div>";

// Notification container
echo "<div class='static-notifications-container' id='staticNotifications' style='position: relative; top: 10px; right: 0; width: 100%; max-width: 400px; margin-top: 1rem;'>";
echo "<div class='notifications-header' style='background: linear-gradient(135deg, #4361ee, #3a0ca3); color: white; padding: 15px; border-radius: 8px 8px 0 0;'>";
echo "<div class='d-flex justify-content-between align-items-center'>";
echo "<h6 class='mb-0'>Notifications</h6>";
echo "<button type='button' class='btn-close btn-close-white btn-sm' onclick='closeNotifications()' aria-label='Close'></button>";
echo "</div>";
echo "</div>";
echo "<div class='notifications-body' style='background: white; padding: 15px; border-radius: 0 0 8px 8px; border: 1px solid #ddd;'>";
echo "<div class='notification-item' style='padding: 10px; border-bottom: 1px solid #eee;'>";
echo "<div class='d-flex'>";
echo "<div class='notification-icon bg-info' style='width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; border-radius: 8px; margin-right: 10px;'>";
echo "<i class='bi bi-info-circle' style='color: #4361ee;'></i>";
echo "</div>";
echo "<div class='notification-content'>";
echo "<p class='mb-1'>New book added to library</p>";
echo "<small class='text-muted'>2 hours ago</small>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "<div class='notification-item' style='padding: 10px; border-bottom: 1px solid #eee;'>";
echo "<div class='d-flex'>";
echo "<div class='notification-icon bg-warning' style='width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; border-radius: 8px; margin-right: 10px;'>";
echo "<i class='bi bi-exclamation-triangle' style='color: #fb8500;'></i>";
echo "</div>";
echo "<div class='notification-content'>";
echo "<p class='mb-1'>Book return overdue</p>";
echo "<small class='text-muted'>5 hours ago</small>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "<div class='notification-item' style='padding: 10px;'>";
echo "<div class='d-flex'>";
echo "<div class='notification-icon bg-success' style='width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; border-radius: 8px; margin-right: 10px;'>";
echo "<i class='bi bi-check-circle' style='color: #2a9d8f;'></i>";
echo "</div>";
echo "<div class='notification-content'>";
echo "<p class='mb-1'>Member registration approved</p>";
echo "<small class='text-muted'>1 day ago</small>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h3>✅ Test Results</h3>";
echo "<div id='testResults'>";
echo "<p class='text-muted'>Click the notification bell above to see the results...</p>";
echo "</div>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h3>🔧 Changes Made</h3>";
echo "<ul>";
echo "<li>✅ Removed dark mode toggle button</li>";
echo "<li>✅ Fixed notification bell click functionality</li>";
echo "<li>✅ Added proper onclick handler</li>";
echo "<li>✅ Improved CSS styling for better interaction</li>";
echo "<li>✅ Added hover effects for better user feedback</li>";
echo "</ul>";
echo "</div>";

echo "<div class='alert alert-success'>";
echo "<h4>🎯 Ready to Test</h4>";
echo "<p>The notification bell should now be fully clickable. <a href='dashboard.php' class='btn btn-primary btn-sm'>Test on Real Dashboard</a></p>";
echo "</div>";

echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
echo "<script>";
echo "let clickCount = 0;";
echo "";
echo "function toggleNotifications(event) {";
echo "    event.preventDefault();";
echo "    event.stopPropagation();";
echo "    clickCount++;";
echo "    ";
echo "    const notificationsContainer = document.getElementById('staticNotifications');";
echo "    const testResults = document.getElementById('testResults');";
echo "    ";
echo "    if (notificationsContainer) {";
echo "        if (notificationsContainer.style.display === 'none' || notificationsContainer.style.display === '') {";
echo "            notificationsContainer.style.display = 'block';";
echo "            testResults.innerHTML = '<p class=\"text-success\">✅ Click #' + clickCount + ': Notifications opened successfully!</p>';";
echo "        } else {";
echo "            notificationsContainer.style.display = 'none';";
echo "            testResults.innerHTML = '<p class=\"text-info\">ℹ️ Click #' + clickCount + ': Notifications closed successfully!</p>';";
echo "        }";
echo "    } else {";
echo "        testResults.innerHTML = '<p class=\"text-danger\">❌ Error: Notifications container not found!</p>';";
echo "    }";
echo "}";
echo "";
echo "function closeNotifications() {";
echo "    const notificationsContainer = document.getElementById('staticNotifications');";
echo "    const testResults = document.getElementById('testResults');";
echo "    ";
echo "    if (notificationsContainer) {";
echo "        notificationsContainer.style.display = 'none';";
echo "        testResults.innerHTML = '<p class=\"text-info\">ℹ️ Notifications closed via close button!</p>';";
echo "    }";
echo "}";
echo "";
echo "// Initialize - hide notifications by default";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    const notificationsContainer = document.getElementById('staticNotifications');";
echo "    if (notificationsContainer) {";
echo "        notificationsContainer.style.display = 'none';";
echo "    }";
echo "});";
echo "</script>";

echo "</body>";
echo "</html>";
?>
