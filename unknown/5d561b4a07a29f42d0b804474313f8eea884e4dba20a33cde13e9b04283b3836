<?php
/**
 * Comprehensive System Dashboard
 * Central hub for system management and monitoring
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get system statistics
$stats = [];

try {
    // Basic stats
    $queries = [
        'total_books' => "SELECT COUNT(*) as count FROM books",
        'total_members' => "SELECT COUNT(*) as count FROM members",
        'active_loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed'",
        'overdue_loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'overdue'",
        'total_users' => "SELECT COUNT(*) as count FROM users",
        'admin_users' => "SELECT COUNT(*) as count FROM users WHERE role = 'admin'",
        'librarian_users' => "SELECT COUNT(*) as count FROM users WHERE role = 'librarian'"
    ];
    
    foreach ($queries as $key => $query) {
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats[$key] = $stmt->fetch()['count'] ?? 0;
    }
    
    // System health indicators
    $health_checks = [
        'database_responsive' => true,
        'files_writable' => is_writable('.'),
        'uploads_dir_exists' => is_dir('uploads'),
        'logs_dir_exists' => is_dir('logs'),
        'config_accessible' => file_exists('config/config.php'),
        'htaccess_exists' => file_exists('.htaccess')
    ];
    
} catch (Exception $e) {
    $stats = array_fill_keys(array_keys($queries), 0);
    $health_checks = array_fill_keys(['database_responsive', 'files_writable', 'uploads_dir_exists', 'logs_dir_exists', 'config_accessible', 'htaccess_exists'], false);
}

// Calculate health score
$health_score = (array_sum($health_checks) / count($health_checks)) * 100;

// Recent activity (if activity log exists)
$recent_activities = [];
try {
    $activity_query = "SELECT * FROM activity_log ORDER BY timestamp DESC LIMIT 5";
    $stmt = $db->prepare($activity_query);
    $stmt->execute();
    $recent_activities = $stmt->fetchAll();
} catch (Exception $e) {
    // Activity log might not exist
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Dashboard - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .dashboard-card {
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .health-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .health-good { background-color: #28a745; }
        .health-warning { background-color: #ffc107; }
        .health-critical { background-color: #dc3545; }
        .stat-icon {
            font-size: 2rem;
            opacity: 0.7;
        }
        .enhancement-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="bi bi-gear-fill me-2"></i>System Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="bi bi-house me-1"></i>Home
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- System Health Overview -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-heart-pulse me-2"></i>System Health Overview
                            <span class="badge bg-light text-dark ms-2"><?php echo round($health_score); ?>%</span>
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($health_checks as $check => $status): ?>
                                <div class="col-md-4 mb-2">
                                    <span class="health-indicator <?php echo $status ? 'health-good' : 'health-critical'; ?>"></span>
                                    <?php echo ucwords(str_replace('_', ' ', $check)); ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Statistics -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-white bg-primary">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Total Books</h6>
                                <h3 class="mb-0"><?php echo number_format($stats['total_books']); ?></h3>
                            </div>
                            <i class="bi bi-book stat-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-white bg-success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Total Members</h6>
                                <h3 class="mb-0"><?php echo number_format($stats['total_members']); ?></h3>
                            </div>
                            <i class="bi bi-people stat-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-white bg-warning">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Active Loans</h6>
                                <h3 class="mb-0"><?php echo number_format($stats['active_loans']); ?></h3>
                            </div>
                            <i class="bi bi-journal-arrow-up stat-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-white bg-danger">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Overdue Loans</h6>
                                <h3 class="mb-0"><?php echo number_format($stats['overdue_loans']); ?></h3>
                            </div>
                            <i class="bi bi-exclamation-triangle stat-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Enhancements -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0"><i class="bi bi-tools me-2"></i>System Enhancements</h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">Apply comprehensive improvements to your LMS system:</p>
                        <div class="enhancement-grid">
                            <a href="system_enhancement.php" class="btn btn-outline-primary btn-lg">
                                <i class="bi bi-gear-fill me-2"></i>
                                <div>Database Enhancements</div>
                                <small class="text-muted">Indexes, tables, performance</small>
                            </a>
                            <a href="accessibility_improvements.php" class="btn btn-outline-success btn-lg">
                                <i class="bi bi-universal-access me-2"></i>
                                <div>Accessibility</div>
                                <small class="text-muted">WCAG compliance, screen readers</small>
                            </a>
                            <a href="ui_enhancements.php" class="btn btn-outline-info btn-lg">
                                <i class="bi bi-palette me-2"></i>
                                <div>UI/UX Enhancements</div>
                                <small class="text-muted">Modern design, animations</small>
                            </a>
                            <a href="security_hardening.php" class="btn btn-outline-warning btn-lg">
                                <i class="bi bi-shield-check me-2"></i>
                                <div>Security Hardening</div>
                                <small class="text-muted">CSRF, rate limiting, headers</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Access -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="bi bi-lightning me-2"></i>Quick Access</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="quick_librarian_login.php" class="btn btn-primary">
                                <i class="bi bi-person-badge me-2"></i>Librarian Dashboard
                            </a>
                            <a href="admin/dashboard.php" class="btn btn-success">
                                <i class="bi bi-shield-lock me-2"></i>Admin Dashboard
                            </a>
                            <a href="member_dashboard.php" class="btn btn-info">
                                <i class="bi bi-person me-2"></i>Member Portal
                            </a>
                            <a href="system_health_check.php" class="btn btn-warning">
                                <i class="bi bi-heart-pulse me-2"></i>Health Check
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0"><i class="bi bi-activity me-2"></i>System Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="text-muted">Users</h6>
                                    <h4 class="text-primary"><?php echo $stats['total_users']; ?></h4>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="text-muted">Admins</h6>
                                    <h4 class="text-success"><?php echo $stats['admin_users']; ?></h4>
                                </div>
                            </div>
                            <div class="col-4">
                                <h6 class="text-muted">Librarians</h6>
                                <h4 class="text-info"><?php echo $stats['librarian_users']; ?></h4>
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <small class="text-muted">
                                Last updated: <?php echo date('Y-m-d H:i:s'); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <?php if (!empty($recent_activities)): ?>
        <div class="row">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>Recent Activity</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <?php foreach ($recent_activities as $activity): ?>
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($activity['action']); ?></h6>
                                        <small><?php echo date('M j, Y H:i', strtotime($activity['timestamp'])); ?></small>
                                    </div>
                                    <?php if (!empty($activity['details'])): ?>
                                        <p class="mb-1"><?php echo htmlspecialchars($activity['details']); ?></p>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
