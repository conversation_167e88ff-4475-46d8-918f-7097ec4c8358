<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session to prevent header issues
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>LMS System Diagnostic</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

// Test 1: PHP Version
echo "<div class='section'>";
echo "<h2>PHP Environment</h2>";
echo "<p class='info'>PHP Version: " . phpversion() . "</p>";
echo "<p class='info'>Server: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p class='info'>Current Time: " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";

// Test 2: File Permissions
echo "<div class='section'>";
echo "<h2>File Permissions</h2>";
$files_to_check = [
    'config/database.php',
    'config/config.php',
    'includes/functions.php',
    'home.php',
    'index.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p class='success'>✓ $file exists and is readable</p>";
    } else {
        echo "<p class='error'>✗ $file not found</p>";
    }
}
echo "</div>";

// Test 3: Database Connection
echo "<div class='section'>";
echo "<h2>Database Connection</h2>";
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    echo "<p class='success'>✓ Database connection successful</p>";

    // Test basic queries
    $tables = ['books', 'members', 'users', 'book_loans'];
    foreach ($tables as $table) {
        try {
            $query = "SELECT COUNT(*) as count FROM $table";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch();
            echo "<p class='success'>✓ Table '$table' accessible - {$result['count']} records</p>";
        } catch (Exception $e) {
            echo "<p class='warning'>⚠ Table '$table' issue: " . $e->getMessage() . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 4: Session
echo "<div class='section'>";
echo "<h2>Session Test</h2>";
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
echo "<p class='success'>✓ Session started successfully</p>";
echo "<p class='info'>Session ID: " . session_id() . "</p>";
echo "<p class='info'>Session save path: " . session_save_path() . "</p>";
echo "<p class='info'>Session status: " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</p>";
echo "</div>";

// Test 5: Configuration
echo "<div class='section'>";
echo "<h2>Configuration</h2>";
try {
    require_once 'config/config.php';
    echo "<p class='success'>✓ Config loaded successfully</p>";
    echo "<p class='info'>Base URL: " . BASE_URL . "</p>";
    echo "<p class='info'>Uploads path exists: " . (is_dir(UPLOADS_PATH) ? 'Yes' : 'No') . "</p>";
} catch (Exception $e) {
    echo "<p class='error'>✗ Config error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 6: Functions
echo "<div class='section'>";
echo "<h2>Functions Test</h2>";
try {
    require_once 'includes/functions.php';
    echo "<p class='success'>✓ Functions loaded successfully</p>";
    echo "<p class='info'>isLoggedIn(): " . (isLoggedIn() ? 'true' : 'false') . "</p>";
    echo "<p class='info'>isMemberLoggedIn(): " . (isMemberLoggedIn() ? 'true' : 'false') . "</p>";
} catch (Exception $e) {
    echo "<p class='error'>✗ Functions error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 7: URL Access
echo "<div class='section'>";
echo "<h2>URL Test</h2>";
echo "<p class='info'>Current URL: " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>";
echo "<p class='info'>Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
echo "<p class='info'>Script Name: " . ($_SERVER['SCRIPT_NAME'] ?? 'diagnostic.php') . "</p>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>Quick Links</h2>";
echo "<p><a href='home.php'>Go to Home Page</a></p>";
echo "<p><a href='index.php'>Go to Index Page</a></p>";
echo "<p><a href='login.php'>Go to Login Page</a></p>";
echo "</div>";
