<?php
/**
 * Test Member Dashboard Access
 */
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Member Dashboard Test</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body class='bg-light'>
    <div class='container mt-3'>
        <div class='card'>
            <div class='card-header'>
                <h5>Member Dashboard Access Test</h5>
            </div>
            <div class='card-body'>";

// Test 1: Check if member_dashboard.php exists
echo "<h6>1. File Existence Check:</h6>";
$dashboard_exists = file_exists('member_dashboard.php');
echo "<p>member_dashboard.php exists: " . ($dashboard_exists ? '✅ Yes' : '❌ No') . "</p>";

// Test 2: Check session
echo "<h6>2. Session Check:</h6>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>Session Status: " . (session_status() === PHP_SESSION_ACTIVE ? '✅ Active' : '❌ Inactive') . "</p>";
echo "<p>Member Logged In: " . (isMemberLoggedIn() ? '✅ Yes' : '❌ No') . "</p>";

if (isset($_SESSION['member_id'])) {
    echo "<p>Member ID: " . $_SESSION['member_id'] . "</p>";
    echo "<p>Member Name: " . $_SESSION['member_name'] . "</p>";
    echo "<p>Member Email: " . $_SESSION['member_email'] . "</p>";
}

// Test 3: Database connection
echo "<h6>3. Database Check:</h6>";
try {
    $database = new Database();
    $db = $database->getConnection();
    echo "<p>Database Connection: ✅ Success</p>";
    
    // Count members
    $query = "SELECT COUNT(*) as total FROM members";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch();
    echo "<p>Total Members: " . $result['total'] . "</p>";
    
} catch (Exception $e) {
    echo "<p>Database Connection: ❌ Failed - " . $e->getMessage() . "</p>";
}

// Test 4: Try to access dashboard content
echo "<h6>4. Dashboard Content Test:</h6>";
if ($dashboard_exists) {
    try {
        // Set a test session
        $_SESSION['member_id'] = 1;
        $_SESSION['member_name'] = 'Test User';
        $_SESSION['member_email'] = '<EMAIL>';
        
        echo "<p>Test session set. Trying to include dashboard...</p>";
        
        // Capture any output from the dashboard
        ob_start();
        $error = null;
        try {
            include 'member_dashboard.php';
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
        $dashboard_output = ob_get_clean();
        
        if ($error) {
            echo "<p>❌ Dashboard Error: " . htmlspecialchars($error) . "</p>";
        } else {
            echo "<p>✅ Dashboard loaded successfully</p>";
            echo "<p>Output length: " . strlen($dashboard_output) . " characters</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Dashboard Test Failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

// Test 5: URL and path info
echo "<h6>5. URL and Path Info:</h6>";
echo "<p>Current Directory: " . __DIR__ . "</p>";
echo "<p>Base URL: " . BASE_URL . "</p>";
echo "<p>Request URI: " . ($_SERVER['REQUEST_URI'] ?? 'Not available') . "</p>";
echo "<p>Script Name: " . ($_SERVER['SCRIPT_NAME'] ?? 'Not available') . "</p>";

echo "</div>
        </div>
        
        <div class='card mt-3'>
            <div class='card-header'>
                <h5>Quick Actions</h5>
            </div>
            <div class='card-body'>
                <div class='d-grid gap-2'>
                    <a href='direct_dashboard_access.php' class='btn btn-primary'>Force Dashboard Access</a>
                    <a href='member_dashboard.php' class='btn btn-success'>Try Dashboard Direct</a>
                    <a href='member_login.php' class='btn btn-outline-primary'>Member Login</a>
                    <a href='home.php' class='btn btn-outline-secondary'>Home Page</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
