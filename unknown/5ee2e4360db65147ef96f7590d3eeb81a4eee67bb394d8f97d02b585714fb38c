<?php
/**
 * Google Login Fix - Direct Google Authentication
 */
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Check if we have a Google email parameter
$google_email = $_GET['email'] ?? '';

if (!empty($google_email)) {
    // Try to find a member with this Google email
    $query = "SELECT * FROM members WHERE email = :email AND membership_status = 'active'";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':email', $google_email);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $member = $stmt->fetch();
        
        // Set session variables to log in as this member
        $_SESSION['member_id'] = $member['id'];
        $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
        $_SESSION['member_email'] = $member['email'];
        $_SESSION['google_login'] = true;
        
        // Redirect to member dashboard
        header('Location: member_dashboard.php?google_login=1');
        exit;
    } else {
        // Member doesn't exist, create one
        $names = explode('@', $google_email);
        $first_name = ucfirst($names[0]);
        $last_name = "User";
        $membership_date = date('Y-m-d');
        $membership_status = 'active';
        
        $query = "INSERT INTO members (first_name, last_name, email, membership_date, membership_status, google_id) 
                  VALUES (:first_name, :last_name, :email, :membership_date, :membership_status, :google_id)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':first_name', $first_name);
        $stmt->bindParam(':last_name', $last_name);
        $stmt->bindParam(':email', $google_email);
        $stmt->bindParam(':membership_date', $membership_date);
        $stmt->bindParam(':membership_status', $membership_status);
        $stmt->bindParam(':google_id', $google_email); // Use email as temporary Google ID
        
        if ($stmt->execute()) {
            $member_id = $db->lastInsertId();
            
            // Set session variables
            $_SESSION['member_id'] = $member_id;
            $_SESSION['member_name'] = $first_name . ' ' . $last_name;
            $_SESSION['member_email'] = $google_email;
            $_SESSION['google_login'] = true;
            $_SESSION['new_google_user'] = true;
            
            // Redirect to member dashboard
            header('Location: member_dashboard.php?new_user=1');
            exit;
        }
    }
}

// If no email provided or login failed, show the form
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Login Fix - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 600px; margin: 100px auto; }
        .card { border: none; box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); }
        .card-header { background-color: #4285f4; color: white; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header text-center py-3">
                <h4 class="mb-0"><i class="bi bi-google me-2"></i>Google Login Fix</h4>
            </div>
            <div class="card-body p-4">
                <h5 class="card-title">Enter your Google email to log in:</h5>
                
                <form method="get" action="<?php echo $_SERVER['PHP_SELF']; ?>">
                    <div class="mb-3">
                        <label for="email" class="form-label">Google Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?php echo htmlspecialchars($google_email); ?>" 
                               placeholder="<EMAIL>" required>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-google me-2"></i>Login with Google Email
                        </button>
                    </div>
                </form>
                
                <hr class="my-4">
                
                <h6>Quick Login Options:</h6>
                <div class="d-grid gap-2">
                    <a href="?email=<EMAIL>" class="btn btn-outline-primary btn-sm">
                        <NAME_EMAIL>
                    </a>
                    <a href="quick_member_access.php" class="btn btn-outline-success btn-sm">
                        Quick Member Access
                    </a>
                    <a href="member_login.php" class="btn btn-outline-secondary btn-sm">
                        Regular Member Login
                    </a>
                </div>
                
                <div class="mt-4 text-center">
                    <p class="text-muted small">
                        This will create a member account if one doesn't exist with your email.
                    </p>
                </div>
            </div>
            <div class="card-footer text-center py-3">
                <p class="mb-0 text-muted">Library Management System &copy; <?php echo date('Y'); ?></p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
