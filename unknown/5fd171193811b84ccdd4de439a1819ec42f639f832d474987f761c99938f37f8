<?php
/**
 * Google Auto Login
 *
 * This script simulates automatic login with Google.
 * It checks if the user is already logged into Google in their browser
 * and uses that information to log them into the library system.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if already logged in as a member
if (isMemberLoggedIn()) {
    redirect(url('member_dashboard.php'));
    exit;
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// Get browser information to simulate Google account detection
$user_agent = $_SERVER['HTTP_USER_AGENT'];
$browser_name = "Unknown Browser";
$browser_logged_in = false;
$google_email = "";
$google_name = "";

// Check if a specific email was provided in the URL (for direct login)
$specific_email = $_GET['email'] ?? '';
$specific_name = $_GET['name'] ?? '';

// If a specific email was provided, use it
if (!empty($specific_email)) {
    $browser_logged_in = true;
    $google_email = $specific_email;

    // If a specific name was provided, use it
    if (!empty($specific_name)) {
        $google_name = $specific_name;
    } else {
        // Extract a name from the email
        $name_parts = explode('@', $google_email);
        $email_username = $name_parts[0];
        $google_name = str_replace('.', ' ', ucwords($email_username));
    }
}
// Otherwise, simulate detecting if user is logged into Google based on browser
else if (strpos($user_agent, 'Chrome') !== false) {
    $browser_name = "Google Chrome";
    $browser_logged_in = true;

    // Use a specific email for testing - this will be the default for Chrome users
    $google_email = "<EMAIL>";
    $google_name = "Elgen Plisco";
} elseif (strpos($user_agent, 'Firefox') !== false) {
    $browser_name = "Mozilla Firefox";
    $browser_logged_in = true;

    // Get user's IP address to create a unique but consistent email
    $ip = $_SERVER['REMOTE_ADDR'];
    $hashed_ip = md5($ip);
    $google_email = "user_" . substr($hashed_ip, 0, 8) . "@gmail.com";

    // Create a name based on the browser and a random element
    $google_name = "Firefox User " . substr($hashed_ip, 0, 4);
} elseif (strpos($user_agent, 'Safari') !== false && strpos($user_agent, 'Chrome') === false) {
    $browser_name = "Safari";
    $browser_logged_in = true;

    // Get user's IP address to create a unique but consistent email
    $ip = $_SERVER['REMOTE_ADDR'];
    $hashed_ip = md5($ip);
    $google_email = "user_" . substr($hashed_ip, 0, 8) . "@gmail.com";

    // Create a name based on the browser and a random element
    $google_name = "Safari User " . substr($hashed_ip, 0, 4);
} elseif (strpos($user_agent, 'Edge') !== false) {
    $browser_name = "Microsoft Edge";
    $browser_logged_in = true;

    // Get user's IP address to create a unique but consistent email
    $ip = $_SERVER['REMOTE_ADDR'];
    $hashed_ip = md5($ip);
    $google_email = "user_" . substr($hashed_ip, 0, 8) . "@gmail.com";

    // Create a name based on the browser and a random element
    $google_name = "Edge User " . substr($hashed_ip, 0, 4);
}

// If we detected a "logged in" browser, proceed with auto-login
if ($browser_logged_in && !empty($google_email) && !empty($google_name)) {
    // Connect to database
    $database = new Database();
    $db = $database->getConnection();

    // Parse name into first and last name
    $name_parts = explode(' ', $google_name, 2);
    $first_name = $name_parts[0];
    $last_name = $name_parts[1] ?? '';

    // Try to find a member with this email
    $query = "SELECT * FROM members WHERE email = :email AND membership_status = 'active'";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':email', $google_email);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        // Found a member with this email, log in as this member
        $member = $stmt->fetch();

        // Set session variables
        $_SESSION['member_id'] = $member['id'];
        $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
        $_SESSION['member_email'] = $member['email'];

        // Set flags for Google login
        $_SESSION['google_login'] = true;
        $_SESSION['direct_google_signin'] = true;
        $_SESSION['streamlined_google_signin'] = true;

        // Log the successful login
        error_log("Google Auto Login: Successfully logged in member ID: " . $member['id'] . ", Email: " . $google_email);

        // Force redirect to member dashboard with a clear URL parameter to avoid caching issues
        redirect(url('member_dashboard.php?login=' . time()));
        exit;
    } else {
        // Member with this email not found, create a new member
        $password = password_hash(bin2hex(random_bytes(8)), PASSWORD_DEFAULT);
        $membership_date = date('Y-m-d');

        // Insert new member
        $query = "INSERT INTO members (first_name, last_name, email, membership_date, membership_status, password)
                  VALUES (:first_name, :last_name, :email, :membership_date, 'active', :password)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':first_name', $first_name);
        $stmt->bindParam(':last_name', $last_name);
        $stmt->bindParam(':email', $google_email);
        $stmt->bindParam(':membership_date', $membership_date);
        $stmt->bindParam(':password', $password);

        if ($stmt->execute()) {
            $member_id = $db->lastInsertId();

            // Set session variables
            $_SESSION['member_id'] = $member_id;
            $_SESSION['member_name'] = "$first_name $last_name";
            $_SESSION['member_email'] = $google_email;

            // Set flags for Google login and new user
            $_SESSION['google_login'] = true;
            $_SESSION['new_google_user'] = true;
            $_SESSION['direct_google_signin'] = true;
            $_SESSION['streamlined_google_signin'] = true;

            // Log the successful registration and login
            error_log("Google Auto Login: Successfully created and logged in new member ID: $member_id, Email: $google_email");

            // Force redirect to member dashboard with a clear URL parameter to avoid caching issues
            redirect(url('member_dashboard.php?login=' . time() . '&new=1'));
            exit;
        } else {
            // Failed to create member, redirect to Google account form with error
            $_SESSION['error'] = 'Failed to create member account. Please enter your information manually.';
            redirect(url('google_account_form.php'));
            exit;
        }
    }
} else {
    // No browser login detected, redirect to Google account form
    redirect(url('google_account_form.php'));
    exit;
}
