<?php
/**
 * Google OAuth Setup Page
 */

session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'config/google_oauth.php';
require_once 'includes/functions.php';

$message = '';
$message_type = '';

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// Handle form submission
if ($_POST) {
    $client_id = trim($_POST['client_id'] ?? '');
    $client_secret = trim($_POST['client_secret'] ?? '');
    
    if (empty($client_id) || empty($client_secret)) {
        $message = 'Both Client ID and Client Secret are required.';
        $message_type = 'error';
    } else {
        // Update the configuration file
        $config_file = __DIR__ . '/config/google_oauth.php';
        $config_content = file_get_contents($config_file);
        
        // Replace the client ID and client secret
        $config_content = preg_replace(
            "/define\('GOOGLE_CLIENT_ID', '.*?'\);/",
            "define('GOOGLE_CLIENT_ID', '$client_id');",
            $config_content
        );
        
        $config_content = preg_replace(
            "/define\('GOOGLE_CLIENT_SECRET', '.*?'\);/",
            "define('GOOGLE_CLIENT_SECRET', '$client_secret');",
            $config_content
        );
        
        if (file_put_contents($config_file, $config_content)) {
            $message = 'Google OAuth credentials have been saved successfully!';
            $message_type = 'success';
        } else {
            $message = 'Failed to save credentials. Please check file permissions.';
            $message_type = 'error';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth Setup - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .setup-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }
        .setup-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .card-body {
            padding: 2rem;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .instructions {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .step {
            margin-bottom: 1rem;
        }
        .step-number {
            background: #667eea;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <div class="card-header">
                <h2 class="mb-0">
                    <i class="bi bi-google me-2"></i>
                    Google OAuth Setup
                </h2>
                <p class="mb-0 mt-2 opacity-75">Configure Google Sign-In for your Library Management System</p>
            </div>
            <div class="card-body">
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> mb-4">
                        <i class="bi bi-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo h($message); ?>
                    </div>
                <?php endif; ?>

                <div class="instructions">
                    <h5 class="mb-3"><i class="bi bi-info-circle me-2"></i>Setup Instructions</h5>
                    <div class="step">
                        <span class="step-number">1</span>
                        Go to <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a>
                    </div>
                    <div class="step">
                        <span class="step-number">2</span>
                        Create a new project or select an existing one
                    </div>
                    <div class="step">
                        <span class="step-number">3</span>
                        Go to "APIs & Services" → "Credentials"
                    </div>
                    <div class="step">
                        <span class="step-number">4</span>
                        Click "Create Credentials" → "OAuth client ID"
                    </div>
                    <div class="step">
                        <span class="step-number">5</span>
                        Select "Web application" as the application type
                    </div>
                    <div class="step">
                        <span class="step-number">6</span>
                        Add these URLs:
                        <ul class="mt-2 mb-0">
                            <li><strong>Authorized JavaScript origins:</strong> <code><?php echo rtrim(BASE_URL, '/'); ?></code></li>
                            <li><strong>Authorized redirect URIs:</strong> <code><?php echo GOOGLE_REDIRECT_URI; ?></code></li>
                        </ul>
                    </div>
                    <div class="step">
                        <span class="step-number">7</span>
                        Copy the Client ID and Client Secret and paste them below
                    </div>
                </div>

                <form method="POST">
                    <div class="mb-3">
                        <label for="client_id" class="form-label">
                            <i class="bi bi-key me-2"></i>Google Client ID
                        </label>
                        <input type="text" class="form-control" id="client_id" name="client_id" 
                               placeholder="Enter your Google Client ID" required>
                        <div class="form-text">This should end with .apps.googleusercontent.com</div>
                    </div>

                    <div class="mb-4">
                        <label for="client_secret" class="form-label">
                            <i class="bi bi-shield-lock me-2"></i>Google Client Secret
                        </label>
                        <input type="text" class="form-control" id="client_secret" name="client_secret" 
                               placeholder="Enter your Google Client Secret" required>
                        <div class="form-text">This should start with GOCSPX-</div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>Save Configuration
                        </button>
                    </div>
                </form>

                <div class="text-center mt-4">
                    <a href="login.php" class="text-decoration-none">
                        <i class="bi bi-arrow-left me-1"></i>Back to Login
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
