<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    echo "Please log in first.";
    exit;
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Find the 1984 book
$query = "SELECT * FROM books WHERE title LIKE '%1984%' AND author LIKE '%Orwell%'";
$stmt = $db->prepare($query);
$stmt->execute();

if ($stmt->rowCount() == 0) {
    echo "Book '1984' not found in the database.";
    exit;
}

$book = $stmt->fetch();
$book_id = $book['id'];

echo "<h2>Update Cover Image for '1984' by <PERSON></h2>";
echo "<p>Book ID: " . $book_id . "</p>";

// Check if the image exists in the covers directory
$image_filename = "1984_cover.jpg";
$image_path = COVERS_PATH . $image_filename;

if (file_exists($image_path)) {
    echo "<p style='color: green;'>✅ Found the image file: " . $image_path . "</p>";
    
    // Update the book in the database with the image filename
    $query = "UPDATE books SET cover_image = :cover_image WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':cover_image', $image_filename);
    $stmt->bindParam(':id', $book_id);
    
    if ($stmt->execute()) {
        echo "<div style='color: green; margin: 20px 0;'>✅ Cover image updated successfully in the database!</div>";
        echo "<p>You can now view the book details: <a href='books/view.php?id=" . $book_id . "'>View Book Details</a></p>";
        echo "<p>Or go back to the books list: <a href='books/index.php'>Books List</a></p>";
        
        // Display the image
        echo "<div style='margin: 20px 0;'>";
        echo "<h3>Cover Image:</h3>";
        echo "<img src='uploads/covers/" . $image_filename . "' alt='1984 Cover' style='max-width: 300px;'>";
        echo "</div>";
    } else {
        echo "<div style='color: red; margin: 20px 0;'>❌ Failed to update the database.</div>";
    }
} else {
    echo "<div style='color: red; margin: 20px 0;'>❌ Image file not found at: " . $image_path . "</div>";
    echo "<p>Please follow these steps:</p>";
    echo "<ol>";
    echo "<li>Save the 1984 image to your computer</li>";
    echo "<li>Rename it to <strong>1984_cover.jpg</strong></li>";
    echo "<li>Copy it to this folder: <strong>C:\\xampp\\htdocs\\Library\\lms\\lms\\uploads\\covers</strong></li>";
    echo "<li>Refresh this page after copying the file</li>";
    echo "</ol>";
    
    // Create the directory if it doesn't exist
    if (!file_exists(COVERS_PATH)) {
        echo "<p>Attempting to create the covers directory...</p>";
        if (mkdir(COVERS_PATH, 0777, true)) {
            echo "<p style='color: green;'>✅ Covers directory created successfully.</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create covers directory. Please create it manually.</p>";
        }
    }
}

// Display the current book information
echo "<h3>Current Book Information:</h3>";
echo "<ul>";
echo "<li><strong>ID:</strong> " . $book['id'] . "</li>";
echo "<li><strong>Title:</strong> " . $book['title'] . "</li>";
echo "<li><strong>Author:</strong> " . $book['author'] . "</li>";
echo "<li><strong>Current Cover Image:</strong> " . ($book['cover_image'] ? $book['cover_image'] : 'None') . "</li>";
echo "</ul>";

// Display directory information
echo "<h3>Directory Information:</h3>";
echo "<ul>";
echo "<li><strong>Uploads Path:</strong> " . UPLOADS_PATH . "</li>";
echo "<li><strong>Covers Path:</strong> " . COVERS_PATH . "</li>";
echo "</ul>";
?>
