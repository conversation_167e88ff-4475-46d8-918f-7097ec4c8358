/**
 * Google Authentication JavaScript
 * Enhances the Google login experience with loading states and animations
 */

/**
 * Connection quality detector
 * Detects the user's connection quality and adjusts the authentication method accordingly
 */
function detectConnectionQuality() {
    return new Promise((resolve) => {
        // Default to medium quality
        let quality = 'medium';

        // Check if the browser supports the Network Information API
        if ('connection' in navigator) {
            const connection = navigator.connection;

            // Check connection type if available
            if (connection.effectiveType) {
                switch (connection.effectiveType) {
                    case 'slow-2g':
                    case '2g':
                        quality = 'low';
                        break;
                    case '3g':
                        quality = 'medium';
                        break;
                    case '4g':
                        quality = 'high';
                        break;
                }
            }

            // Also consider downlink speed if available
            if (connection.downlink) {
                if (connection.downlink < 1) {
                    quality = 'low';
                } else if (connection.downlink > 5) {
                    quality = 'high';
                }
            }
        } else {
            // Fallback method: measure image loading time
            const startTime = Date.now();
            const img = new Image();

            img.onload = function() {
                const loadTime = Date.now() - startTime;

                if (loadTime < 100) {
                    quality = 'high';
                } else if (loadTime > 300) {
                    quality = 'low';
                }

                resolve(quality);
            };

            img.onerror = function() {
                // If image fails to load, assume low quality
                quality = 'low';
                resolve(quality);
            };

            // Set a small timeout to prevent waiting too long
            setTimeout(() => resolve(quality), 500);

            // Load a small image to test connection speed
            img.src = 'https://www.google.com/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png?' + Date.now();
            return;
        }

        resolve(quality);
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // Get all Google auth buttons
    const googleButtons = document.querySelectorAll('.btn-google');
    const directLinks = document.querySelectorAll('.direct-link');

    // Detect connection quality and adjust UI accordingly
    detectConnectionQuality().then(quality => {
        // Store connection quality in localStorage for future use
        localStorage.setItem('connectionQuality', quality);

        // If connection quality is low, highlight the direct link
        if (quality === 'low') {
            directLinks.forEach(link => {
                link.classList.remove('text-muted');
                link.classList.add('text-primary', 'fw-bold');
                link.innerHTML = '<i class="bi bi-lightning-charge-fill me-1"></i>Use Direct Link (Recommended for your connection)';
            });

            // Add a small notification about connection quality
            const firstButton = document.querySelector('.btn-google');
            if (firstButton) {
                const connectionNote = document.createElement('div');
                connectionNote.className = 'connection-note small text-muted mt-1';
                connectionNote.innerHTML = '<i class="bi bi-info-circle-fill me-1"></i>Slow connection detected. Direct link recommended.';
                firstButton.parentNode.appendChild(connectionNote);
            }
        }
    });

    // Add click event listeners to all Google buttons
    googleButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Prevent default action
            e.preventDefault();

            // Get the href attribute (Google auth URL)
            const authUrl = this.getAttribute('href');

            // For direct_google_login_fix.php, just redirect immediately
            if (authUrl.includes('direct_google_login_fix.php')) {
                window.location.href = authUrl;
                return;
            }

            // Get connection quality from localStorage
            const connectionQuality = localStorage.getItem('connectionQuality') || 'medium';

            // For low quality connections, redirect immediately without animation
            if (connectionQuality === 'low') {
                // Add direct=1 parameter to skip processing page
                const directUrl = authUrl + (authUrl.includes('?') ? '&' : '?') + 'direct=1';
                window.location.href = directUrl;
                return;
            }

            // Show loading state
            this.classList.add('btn-loading');
            this.setAttribute('disabled', 'disabled');

            // Change button text to "Connecting..." (shorter text for faster perception)
            const buttonSpan = this.querySelector('span');
            const originalText = buttonSpan.textContent;

            // Use a more optimized spinner animation
            buttonSpan.innerHTML = '<i class="spinner-grow spinner-grow-sm me-2" style="animation-duration: 0.6s;"></i> Connecting...';

            // Store original button text in localStorage for the callback page
            localStorage.setItem('googleAuthButtonText', originalText);

            // Check if persistent Google login is enabled
            const persistentGoogleCheckbox = document.getElementById('persistentGoogle');
            if (persistentGoogleCheckbox && persistentGoogleCheckbox.checked) {
                // Store persistent login preference
                localStorage.setItem('persistentGoogleLogin', '1');

                // Add persistent parameter to auth URL
                const persistentParam = 'persistent=1';
                authUrl += (authUrl.includes('?') ? '&' : '?') + persistentParam;
            }

            // Adjust delay based on connection quality
            const delay = connectionQuality === 'high' ? 200 : 300;

            // Redirect to Google auth URL after a very short delay (just enough for animation)
            setTimeout(() => {
                window.location.href = authUrl;
            }, delay);

            // Add a timeout to prevent indefinite loading state
            setTimeout(() => {
                // If we're still on this page after 5 seconds, something went wrong
                if (document.getElementById('googleLoginBtn')) {
                    // Reset button state
                    this.classList.remove('btn-loading');
                    this.removeAttribute('disabled');
                    buttonSpan.textContent = originalText;

                    // Show error message with retry button
                    const errorMsg = document.createElement('div');
                    errorMsg.className = 'alert alert-danger mt-2 small';
                    errorMsg.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div><i class="bi bi-exclamation-triangle-fill me-2"></i>Connection to Google timed out.</div>
                            <button class="btn btn-sm btn-danger retry-btn">Retry</button>
                        </div>
                        <div class="mt-2">
                            <a href="${authUrl + (authUrl.includes('?') ? '&' : '?') + 'direct=1'}" class="btn btn-sm btn-outline-secondary w-100">
                                <i class="bi bi-lightning-charge-fill me-1"></i>Try Direct Link
                            </a>
                        </div>
                    `;
                    this.parentNode.appendChild(errorMsg);

                    // Add click event to retry button
                    const retryBtn = errorMsg.querySelector('.retry-btn');
                    if (retryBtn) {
                        retryBtn.addEventListener('click', () => {
                            // Remove error message
                            errorMsg.remove();
                            // Trigger click event on the button again
                            this.click();
                        });
                    }
                }
            }, 5000);
        });
    });

    // Check if we're on the callback page and show success message
    if (window.location.pathname.includes('google_callback.php')) {
        showCallbackStatus('Processing your Google sign-in...');
    }

    // Check for returning Google user
    checkReturningGoogleUser();
});

/**
 * Show callback status message
 * @param {string} message - Status message to display
 */
function showCallbackStatus(message) {
    // Create status element if it doesn't exist
    if (!document.getElementById('google-auth-status')) {
        const statusDiv = document.createElement('div');
        statusDiv.id = 'google-auth-status';
        statusDiv.className = 'google-auth-status';

        const spinner = document.createElement('div');
        spinner.className = 'spinner-border text-primary';
        spinner.setAttribute('role', 'status');

        const spinnerText = document.createElement('span');
        spinnerText.className = 'visually-hidden';
        spinnerText.textContent = 'Loading...';

        const statusMessage = document.createElement('p');
        statusMessage.className = 'mt-3';
        statusMessage.textContent = message;

        spinner.appendChild(spinnerText);
        statusDiv.appendChild(spinner);
        statusDiv.appendChild(statusMessage);

        // Add to body
        document.body.appendChild(statusDiv);

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .google-auth-status {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(255, 255, 255, 0.9);
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                font-family: 'Roboto', sans-serif;
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * Check for returning Google user
 * Shows a welcome back message if the user has logged in with Google before
 */
function checkReturningGoogleUser() {
    // Check if we have a stored Google user
    const googleUser = localStorage.getItem('googleUser');

    if (googleUser) {
        try {
            const user = JSON.parse(googleUser);
            const loginForm = document.querySelector('form[action*="login.php"]');

            // Only show on login page
            if (loginForm && user.name && user.picture) {
                // Create returning user element
                const returningUserDiv = document.createElement('div');
                returningUserDiv.className = 'returning-google-user mb-4 text-center';

                returningUserDiv.innerHTML = `
                    <div class="card border-0 shadow-sm">
                        <div class="card-body py-3">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <img src="${user.picture}" alt="${user.name}" class="rounded-circle me-3" width="40" height="40">
                                    <div class="text-start">
                                        <small class="text-muted d-block">Continue as</small>
                                        <span class="fw-medium">${user.name}</span>
                                    </div>
                                </div>
                                <button type="button" class="btn-close btn-sm" aria-label="Close" id="close-returning-user"></button>
                            </div>
                        </div>
                    </div>
                `;

                // Insert before the form
                loginForm.parentNode.insertBefore(returningUserDiv, loginForm);

                // Add click event to the returning user div
                returningUserDiv.addEventListener('click', function(e) {
                    if (!e.target.closest('#close-returning-user')) {
                        // Get the Google button and trigger its click event
                        const googleButton = document.querySelector('.btn-google');
                        if (googleButton) {
                            googleButton.click();
                        }
                    }
                });

                // Add click event to the close button
                const closeButton = document.getElementById('close-returning-user');
                if (closeButton) {
                    closeButton.addEventListener('click', function(e) {
                        e.stopPropagation();
                        returningUserDiv.remove();
                        localStorage.removeItem('googleUser');
                    });
                }
            }
        } catch (e) {
            console.error('Error parsing Google user data', e);
            localStorage.removeItem('googleUser');
        }
    }
}

// Add loading state styles
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        .btn-google.btn-loading {
            background-color: #f1f3f4;
            pointer-events: none;
        }
        .spinner-border, .spinner-grow {
            width: 1rem;
            height: 1rem;
            border-width: 0.15em;
        }
        /* Optimize spinner animation for better performance */
        @keyframes spinner-grow {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }
        .returning-google-user {
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        .returning-google-user:hover {
            transform: translateY(-2px);
        }
    `;
    document.head.appendChild(style);
});
