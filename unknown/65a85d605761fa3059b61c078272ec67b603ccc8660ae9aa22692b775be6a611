<?php
// Test admin path access
echo "<h2>🔍 Admin Path Test</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px;'>";

// Check current directory
echo "<h3>📁 Current Directory Information:</h3>";
echo "<p><strong>Current working directory:</strong> " . getcwd() . "</p>";
echo "<p><strong>Script filename:</strong> " . __FILE__ . "</p>";
echo "<p><strong>Document root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Request URI:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p><strong>Server name:</strong> " . $_SERVER['SERVER_NAME'] . "</p>";

// Check if admin directory exists
echo "<h3>📂 Directory Structure Check:</h3>";
$admin_dir = __DIR__ . '/admin';
echo "<p><strong>Admin directory path:</strong> $admin_dir</p>";
echo "<p><strong>Admin directory exists:</strong> " . (is_dir($admin_dir) ? '✅ Yes' : '❌ No') . "</p>";

if (is_dir($admin_dir)) {
    $dashboard_file = $admin_dir . '/dashboard.php';
    echo "<p><strong>Dashboard file path:</strong> $dashboard_file</p>";
    echo "<p><strong>Dashboard file exists:</strong> " . (file_exists($dashboard_file) ? '✅ Yes' : '❌ No') . "</p>";
    echo "<p><strong>Dashboard file readable:</strong> " . (is_readable($dashboard_file) ? '✅ Yes' : '❌ No') . "</p>";
    
    if (file_exists($dashboard_file)) {
        echo "<p><strong>Dashboard file size:</strong> " . filesize($dashboard_file) . " bytes</p>";
    }
}

// List admin directory contents
echo "<h3>📋 Admin Directory Contents:</h3>";
if (is_dir($admin_dir)) {
    $files = scandir($admin_dir);
    echo "<ul>";
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            $full_path = $admin_dir . '/' . $file;
            $type = is_dir($full_path) ? '📁 Directory' : '📄 File';
            echo "<li>$type: $file</li>";
        }
    }
    echo "</ul>";
} else {
    echo "<p>❌ Admin directory not found</p>";
}

echo "</div>";

// Test different URL patterns
echo "<div style='background: #e7f3ff; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>🔗 URL Test Links:</h3>";

$base_url = 'http://' . $_SERVER['SERVER_NAME'];
$current_path = dirname($_SERVER['REQUEST_URI']);

echo "<p><strong>Base URL:</strong> $base_url</p>";
echo "<p><strong>Current path:</strong> $current_path</p>";

// Different URL variations to try
$urls = [
    $base_url . '/Library/lms/admin/dashboard.php',
    $base_url . $current_path . '/admin/dashboard.php',
    $base_url . '/lms/admin/dashboard.php',
    $base_url . '/Library/admin/dashboard.php'
];

echo "<h4>Try these URLs:</h4>";
foreach ($urls as $url) {
    echo "<p><a href='$url' target='_blank' style='color: #007bff; text-decoration: none;'>$url</a></p>";
}

echo "</div>";

// Quick admin login options
echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>🚀 Quick Access Options:</h3>";

$quick_urls = [
    'Admin Dashboard (Method 1)' => $base_url . $current_path . '/admin/dashboard.php',
    'Admin Dashboard (Method 2)' => $base_url . '/Library/lms/admin/dashboard.php',
    'Quick Admin Login' => $base_url . $current_path . '/quick_admin_login.php',
    'Simple Dashboard' => $base_url . $current_path . '/admin/dashboard_simple_working.php',
    'Admin Access Fix' => $base_url . $current_path . '/admin_access_fix.php'
];

foreach ($quick_urls as $label => $url) {
    echo "<p><a href='$url' style='display: inline-block; padding: 8px 15px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 2px;'>$label</a></p>";
}

echo "</div>";

// Server configuration check
echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>⚙️ Server Configuration:</h3>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server Software:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>Apache Modules:</strong> " . (function_exists('apache_get_modules') ? implode(', ', apache_get_modules()) : 'Not available') . "</p>";

// Check .htaccess
$htaccess_file = __DIR__ . '/.htaccess';
echo "<p><strong>.htaccess file exists:</strong> " . (file_exists($htaccess_file) ? '✅ Yes' : '❌ No') . "</p>";

if (file_exists($htaccess_file)) {
    echo "<p><strong>.htaccess content:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;'>";
    echo htmlspecialchars(file_get_contents($htaccess_file));
    echo "</pre>";
}

echo "</div>";
?>
