<?php
/**
 * Email Notification System Installation Script
 * 
 * This script creates the necessary database tables and default settings
 * for the email notification system.
 */

// Include database connection
require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Initialize variables
$success = true;
$messages = [];

// Create email_logs table
try {
    $query = "CREATE TABLE IF NOT EXISTS email_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        recipient_email VARCHAR(100) NOT NULL,
        subject VARCHAR(255) NOT NULL,
        status ENUM('pending', 'sent', 'failed') NOT NULL DEFAULT 'pending',
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $db->exec($query);
    $messages[] = "✅ Created email_logs table";
} catch (PDOException $e) {
    $success = false;
    $messages[] = "❌ Error creating email_logs table: " . $e->getMessage();
}

// Create reminder_logs table
try {
    $query = "CREATE TABLE IF NOT EXISTS reminder_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        loan_id INT NOT NULL,
        reminder_type ENUM('due_date', 'overdue') NOT NULL,
        days_before INT,
        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (loan_id) REFERENCES book_loans(id) ON DELETE CASCADE
    )";
    $db->exec($query);
    $messages[] = "✅ Created reminder_logs table";
} catch (PDOException $e) {
    $success = false;
    $messages[] = "❌ Error creating reminder_logs table: " . $e->getMessage();
}

// Create settings table
try {
    $query = "CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_group VARCHAR(50) NOT NULL,
        setting_key VARCHAR(50) NOT NULL,
        setting_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY group_key (setting_group, setting_key)
    )";
    $db->exec($query);
    $messages[] = "✅ Created settings table";
} catch (PDOException $e) {
    $success = false;
    $messages[] = "❌ Error creating settings table: " . $e->getMessage();
}

// Insert default email settings
try {
    $settings = [
        ['email', 'from_email', '<EMAIL>'],
        ['email', 'from_name', 'Library Management System'],
        ['email', 'reply_to', '<EMAIL>'],
        ['email', 'smtp_enabled', 'false'],
        ['email', 'smtp_host', 'smtp.example.com'],
        ['email', 'smtp_port', '587'],
        ['email', 'smtp_username', ''],
        ['email', 'smtp_password', ''],
        ['email', 'smtp_secure', 'tls']
    ];
    
    $query = "INSERT INTO settings (setting_group, setting_key, setting_value) 
              VALUES (?, ?, ?)
              ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
    $stmt = $db->prepare($query);
    
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
    
    $messages[] = "✅ Inserted default email settings";
} catch (PDOException $e) {
    $success = false;
    $messages[] = "❌ Error inserting email settings: " . $e->getMessage();
}

// Insert default notification settings
try {
    $settings = [
        ['notifications', 'due_date_reminder_days', '3'],
        ['notifications', 'send_overdue_notifications', 'true'],
        ['notifications', 'overdue_notification_frequency', '7']
    ];
    
    $query = "INSERT INTO settings (setting_group, setting_key, setting_value) 
              VALUES (?, ?, ?)
              ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
    $stmt = $db->prepare($query);
    
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
    
    $messages[] = "✅ Inserted default notification settings";
} catch (PDOException $e) {
    $success = false;
    $messages[] = "❌ Error inserting notification settings: " . $e->getMessage();
}

// Insert default fine settings
try {
    $settings = [
        ['fines', 'fine_rate_per_day', '0.25'],
        ['fines', 'grace_period_days', '3'],
        ['fines', 'max_fine_per_book', '25.00']
    ];
    
    $query = "INSERT INTO settings (setting_group, setting_key, setting_value) 
              VALUES (?, ?, ?)
              ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
    $stmt = $db->prepare($query);
    
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
    
    $messages[] = "✅ Inserted default fine settings";
} catch (PDOException $e) {
    $success = false;
    $messages[] = "❌ Error inserting fine settings: " . $e->getMessage();
}

// Create logs directory
try {
    $logs_dir = __DIR__ . '/logs';
    if (!file_exists($logs_dir)) {
        mkdir($logs_dir, 0755, true);
        $messages[] = "✅ Created logs directory";
    } else {
        $messages[] = "✅ Logs directory already exists";
    }
} catch (Exception $e) {
    $success = false;
    $messages[] = "❌ Error creating logs directory: " . $e->getMessage();
}

// Add link to admin sidebar
try {
    $sidebar_file = __DIR__ . '/includes/sidebar.php';
    if (file_exists($sidebar_file)) {
        $sidebar_content = file_get_contents($sidebar_file);
        
        // Check if email settings link already exists
        if (strpos($sidebar_content, 'email_settings.php') === false) {
            // Find the admin section
            $admin_section_pos = strpos($sidebar_content, '<?php if (isAdmin()): ?>');
            
            if ($admin_section_pos !== false) {
                // Find the end of the admin section
                $admin_section_end = strpos($sidebar_content, '<?php endif; ?>', $admin_section_pos);
                
                if ($admin_section_end !== false) {
                    // Add the email settings link before the end of the admin section
                    $email_settings_link = '
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo url(\'admin/email_settings.php\'); ?>">
                            <i class="bi bi-envelope me-2"></i>
                            Email Settings
                        </a>
                    </li>';
                    
                    $new_sidebar_content = substr($sidebar_content, 0, $admin_section_end) . $email_settings_link . substr($sidebar_content, $admin_section_end);
                    
                    file_put_contents($sidebar_file, $new_sidebar_content);
                    $messages[] = "✅ Added Email Settings link to admin sidebar";
                } else {
                    $messages[] = "⚠️ Could not find end of admin section in sidebar.php";
                }
            } else {
                $messages[] = "⚠️ Could not find admin section in sidebar.php";
            }
        } else {
            $messages[] = "✅ Email Settings link already exists in sidebar";
        }
    } else {
        $messages[] = "⚠️ Could not find sidebar.php file";
    }
} catch (Exception $e) {
    $messages[] = "❌ Error updating sidebar: " . $e->getMessage();
}

// Display installation results
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Notification System Installation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 40px 0;
            background-color: #f8f9fa;
        }
        .installation-card {
            max-width: 800px;
            margin: 0 auto;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .message-list {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card installation-card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="bi bi-envelope me-2"></i>Email Notification System Installation</h4>
            </div>
            <div class="card-body">
                <div class="alert <?php echo $success ? 'alert-success' : 'alert-danger'; ?>">
                    <h5 class="alert-heading">
                        <?php echo $success ? '✅ Installation Completed Successfully!' : '❌ Installation Completed with Errors'; ?>
                    </h5>
                    <p>
                        <?php echo $success 
                            ? 'The email notification system has been installed successfully. You can now configure email settings and start sending notifications.'
                            : 'The installation completed with some errors. Please check the messages below for details.'; 
                        ?>
                    </p>
                </div>
                
                <h5>Installation Messages:</h5>
                <div class="message-list border rounded p-3 bg-light">
                    <?php foreach ($messages as $message): ?>
                        <div class="mb-2"><?php echo $message; ?></div>
                    <?php endforeach; ?>
                </div>
                
                <div class="mt-4">
                    <h5>Next Steps:</h5>
                    <ol>
                        <li>Configure email settings in the <a href="admin/email_settings.php">Email Settings</a> page</li>
                        <li>Set up a cron job to run the notification script daily:
                            <pre class="bg-dark text-light p-2 mt-2 rounded">0 0 * * * php <?php echo realpath(__DIR__ . '/cron/send_notifications.php'); ?></pre>
                        </li>
                        <li>Test the email system by sending a test email</li>
                    </ol>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="index.php" class="btn btn-primary">Go to Dashboard</a>
                    <a href="admin/email_settings.php" class="btn btn-success">Configure Email Settings</a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
