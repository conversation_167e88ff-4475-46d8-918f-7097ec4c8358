<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Check if loan ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    setMessage('Invalid loan ID', 'danger');
    redirect('index.php');
}

$loan_id = (int)$_GET['id'];

// Get loan details
$query = "SELECT bl.*, b.title as book_title, b.id as book_id, m.first_name, m.last_name 
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          JOIN members m ON bl.member_id = m.id
          WHERE bl.id = :loan_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':loan_id', $loan_id);
$stmt->execute();

if ($stmt->rowCount() === 0) {
    setMessage('Loan not found', 'danger');
    redirect('index.php');
}

$loan = $stmt->fetch();

// Check if book is already returned
if ($loan['status'] === 'returned') {
    setMessage('This book has already been returned', 'warning');
    redirect('index.php');
}

// Initialize variables
$return_date = date('Y-m-d');
$fine = 0;

// Calculate fine if book is overdue
if (strtotime($loan['due_date']) < strtotime($return_date)) {
    $fine = calculateFine($loan['due_date'], $return_date);
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $return_date = sanitize($_POST['return_date']);
    $fine = (float)sanitize($_POST['fine']);
    
    // Begin transaction
    $db->beginTransaction();
    
    try {
        // Update loan record
        $query = "UPDATE book_loans 
                  SET return_date = :return_date, fine = :fine, status = 'returned' 
                  WHERE id = :loan_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':return_date', $return_date);
        $stmt->bindParam(':fine', $fine);
        $stmt->bindParam(':loan_id', $loan_id);
        $stmt->execute();
        
        // Update book available quantity
        $query = "UPDATE books 
                  SET available_quantity = available_quantity + 1 
                  WHERE id = :book_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':book_id', $loan['book_id']);
        $stmt->execute();
        
        // Commit transaction
        $db->commit();
        
        setMessage('Book returned successfully', 'success');
        redirect('index.php');
    } catch (Exception $e) {
        // Rollback transaction on error
        $db->rollBack();
        $error = 'Failed to return book: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Return Book - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Return Book</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="index.php" class="btn btn-sm btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Loans
                        </a>
                    </div>
                </div>
                
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Loan Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Book:</strong> <?php echo $loan['book_title']; ?></p>
                                <p><strong>Member:</strong> <?php echo $loan['first_name'] . ' ' . $loan['last_name']; ?></p>
                                <p><strong>Issue Date:</strong> <?php echo formatDate($loan['issue_date']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Due Date:</strong> <?php echo formatDate($loan['due_date']); ?></p>
                                <p><strong>Status:</strong> 
                                    <?php if ($loan['status'] === 'borrowed'): ?>
                                        <span class="badge bg-primary">Borrowed</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Overdue</span>
                                    <?php endif; ?>
                                </p>
                                <?php if (strtotime($loan['due_date']) < strtotime($return_date)): ?>
                                    <p class="text-danger">
                                        <strong>Overdue by:</strong> 
                                        <?php 
                                            $due = new DateTime($loan['due_date']);
                                            $return = new DateTime($return_date);
                                            echo $due->diff($return)->days . ' days';
                                        ?>
                                    </p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-body">
                        <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF'] . '?id=' . $loan_id); ?>" method="post">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="return_date" class="form-label">Return Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="return_date" name="return_date" value="<?php echo $return_date; ?>" required>
                                    <input type="hidden" id="due_date" value="<?php echo $loan['due_date']; ?>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="fine" class="form-label">Fine Amount ($)</label>
                                    <input type="number" class="form-control" id="fine" name="fine" value="<?php echo $fine; ?>" step="0.01" min="0">
                                    <small class="text-muted">Fine is calculated automatically based on overdue days ($1 per day)</small>
                                </div>
                                
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">Return Book</button>
                                    <a href="index.php" class="btn btn-secondary">Cancel</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
</body>
</html>
