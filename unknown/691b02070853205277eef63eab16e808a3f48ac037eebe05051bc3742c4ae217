<?php
session_start();

// Include functions if available
if (file_exists('includes/functions.php')) {
    require_once 'includes/functions.php';
}

// Check if admin/staff is logged in
if (isset($_SESSION['user_id'])) {
    // Unset all admin session variables
    unset($_SESSION['user_id']);
    unset($_SESSION['username']);
    unset($_SESSION['email']);
    unset($_SESSION['role']);
    unset($_SESSION['full_name']);
}

// Check if member is logged in
if (isset($_SESSION['member_id'])) {
    // Unset all member session variables
    unset($_SESSION['member_id']);
    unset($_SESSION['member_name']);
    unset($_SESSION['member_email']);
}

// Clear Google session data if exists
if (isset($_SESSION['google_user'])) {
    unset($_SESSION['google_user']);
    unset($_SESSION['google_email']);
    unset($_SESSION['google_name']);
}

// Clear remember me cookies if set
if (isset($_COOKIE['member_email'])) {
    setcookie('member_email', '', time() - 3600, '/');
}

if (isset($_COOKIE['remember_user'])) {
    setcookie('remember_user', '', time() - 3600, '/');
}

// Clear all session variables
$_SESSION = [];

// Destroy the session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy the session
session_destroy();

// Redirect to logout success page
header('Location: logout_success.php');
exit;
