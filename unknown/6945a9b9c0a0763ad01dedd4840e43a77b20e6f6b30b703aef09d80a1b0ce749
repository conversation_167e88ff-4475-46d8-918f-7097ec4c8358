# Library Management System

A comprehensive web-based Library Management System built with PHP and MySQL.

## Features

- **User Authentication**: Secure login system with role-based access control
- **Book Management**: Add, edit, delete, and search books
- **Member Management**: Register and manage library members
- **Loan Management**: Issue and return books with fine calculation
- **Reporting**: Generate various reports including overdue books, popular books, etc.
- **Dashboard**: Overview of library statistics and recent activities
- **Responsive Design**: Works on desktop and mobile devices

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache, Nginx, etc.)
- XAMPP, WAMP, MAMP, or similar local development environment

## Installation

1. **Clone or download** the repository to your web server's document root (e.g., `htdocs` for XAMPP)

2. **Create a MySQL database** named `lms_db`

3. **Import the database schema**:
   - Open phpMyAdmin or any MySQL client
   - Select the `lms_db` database
   - Import the SQL file from `database/lms_db.sql`

4. **Configure the database connection**:
   - Open `config/database.php`
   - Update the database credentials if needed (default is username: `root`, password: ``)

5. **Access the application**:
   - Open your web browser and navigate to `http://localhost/lms/`
   - Login with the default admin credentials:
     - Username: `admin`
     - Password: `admin123`

## Directory Structure

```
lms/
├── admin/             # Admin-specific functionality
├── assets/            # CSS, JavaScript, and images
│   ├── css/
│   ├── js/
│   └── img/
├── auth/              # Authentication files
├── books/             # Book management
├── config/            # Configuration files
├── database/          # Database schema and migrations
├── includes/          # Reusable PHP components
├── loans/             # Loan management
├── members/           # Member management
├── reports/           # Reporting functionality
├── uploads/           # Uploaded files (book covers, etc.)
├── index.php          # Main entry point
└── README.md          # This file
```

## Usage

### Books Management
- Add new books with details like ISBN, title, author, etc.
- Update book information and availability
- Search books by title, author, or ISBN
- View book details and borrowing history

### Members Management
- Register new library members
- Update member information
- View member borrowing history
- Manage membership status

### Loans Management
- Issue books to members
- Set due dates for returns
- Process book returns
- Calculate and collect fines for overdue books

### Reports
- Generate reports on overdue books
- Track popular books
- Monitor active members
- View fine collection statistics
- Check inventory status

## Default Credentials

- **Admin User**:
  - Username: `admin`
  - Password: `admin123`

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Bootstrap for the responsive UI components
- Font Awesome for icons
- PHP and MySQL communities for documentation and support
