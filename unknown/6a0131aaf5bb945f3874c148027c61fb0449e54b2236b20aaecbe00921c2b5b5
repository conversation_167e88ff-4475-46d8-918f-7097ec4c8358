<?php
/**
 * Google Auto Login
 *
 * This file handles automatic login with Google credentials.
 */

// Check if auto-login is enabled
if (!isset($_SESSION['user_id']) && !isset($_SESSION['member_id'])) {
    // Check for persistent Google login
    if (isset($_CO<PERSON>IE['persistent_google']) && $_COOKIE['persistent_google'] == '1' &&
        isset($_COOKIE['google_remember_token']) && !empty($_COOKIE['google_remember_token'])) {

        $remember_token = $_COOKIE['google_remember_token'];

        // Connect to database
        $database = new Database();
        $db = $database->getConnection();

        // Try to find user with this remember token
        $query = "SELECT * FROM users WHERE remember_token = :remember_token";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':remember_token', $remember_token);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            // Found a staff user
            $user = $stmt->fetch();

            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['role'] = $user['role'];

            // Refresh the token and cookie
            $new_token = bin2hex(random_bytes(32));

            // Update the token in the database
            $update_query = "UPDATE users SET remember_token = :new_token WHERE id = :id";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->bindParam(':new_token', $new_token);
            $update_stmt->bindParam(':id', $user['id']);
            $update_stmt->execute();

            // Update the cookie
            setcookie('google_remember_token', $new_token, time() + 86400 * 30, '/');

            // Set auto-login flag for this session
            $_SESSION['auto_login'] = true;

            // If this is an AJAX request, return success
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'role' => $user['role']]);
                exit;
            }

            // If not already on the dashboard, redirect
            if (!strpos($_SERVER['PHP_SELF'], 'dashboard.php')) {
                // Redirect based on role
                if ($user['role'] === 'admin') {
                    redirect(url('admin/dashboard.php'));
                } elseif ($user['role'] === 'librarian') {
                    // Store full name in session for librarian
                    $_SESSION['full_name'] = !empty($user['full_name']) ? $user['full_name'] : $user['username'];
                    redirect(url('librarian/dashboard.php'));
                } else {
                    redirect(url('index.php'));
                }
            }
        } else {
            // Try to find member with this remember token
            $query = "SELECT * FROM members WHERE remember_token = :remember_token";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':remember_token', $remember_token);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                // Found a member
                $member = $stmt->fetch();

                // Set session variables
                $_SESSION['member_id'] = $member['id'];
                $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
                $_SESSION['member_email'] = $member['email'];

                // Refresh the token and cookie
                $new_token = bin2hex(random_bytes(32));

                // Update the token in the database
                $update_query = "UPDATE members SET remember_token = :new_token WHERE id = :id";
                $update_stmt = $db->prepare($update_query);
                $update_stmt->bindParam(':new_token', $new_token);
                $update_stmt->bindParam(':id', $member['id']);
                $update_stmt->execute();

                // Update the cookie
                setcookie('google_remember_token', $new_token, time() + 86400 * 30, '/');

                // Set auto-login flag for this session
                $_SESSION['auto_login'] = true;

                // If this is an AJAX request, return success
                if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => true, 'role' => 'member']);
                    exit;
                }

                // If not already on the dashboard, redirect
                if (!strpos($_SERVER['PHP_SELF'], 'member_dashboard.php')) {
                    redirect(url('member_dashboard.php'));
                }
            }
        }
    }
}
