<?php
/**
 * Direct Access Test for Diagnostic Files
 * This bypasses any .htaccess issues
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Direct Access Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .test-link { display: inline-block; margin: 5px; padding: 8px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
    iframe { width: 100%; height: 400px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

echo "<div class='section'>";
echo "<h2>🌐 Current URL Information</h2>";
echo "<div class='info'>";
echo "<p><strong>Current URL:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>";
echo "<p><strong>Server Name:</strong> " . ($_SERVER['SERVER_NAME'] ?? 'Unknown') . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
echo "<p><strong>Script Name:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Unknown') . "</p>";
echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";
echo "</div>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>📁 File Existence Check</h2>";

$diagnostic_files = [
    'diagnostic.php' => 'Main Diagnostic Page',
    'database_status.php' => 'Database Status Check',
    'troubleshoot.php' => 'Troubleshooting Guide',
    'admin/ajax/test_database_clean.php' => 'Clean AJAX Test'
];

foreach ($diagnostic_files as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ $description ($file) - File exists and readable</div>";
    } else {
        echo "<div class='error'>❌ $description ($file) - File missing</div>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🔗 Direct Access Links</h2>";
echo "<p>These links should work regardless of .htaccess configuration:</p>";

$base_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);

foreach ($diagnostic_files as $file => $description) {
    if (file_exists($file)) {
        $direct_url = $base_url . '/' . $file;
        echo "<p><a href='$direct_url' target='_blank' class='test-link'>🔗 $description</a></p>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🧪 Inline Test: Database Status</h2>";
echo "<p>Testing database_status.php directly:</p>";

if (file_exists('database_status.php')) {
    echo "<div class='info'>Loading database_status.php content:</div>";
    echo "<iframe src='database_status.php'></iframe>";
} else {
    echo "<div class='error'>❌ database_status.php not found</div>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🔧 Quick Database Test</h2>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<div class='success'>✅ Database connection successful</div>";
        
        // Test basic query
        $stmt = $db->query("SELECT COUNT(*) as count FROM members");
        $result = $stmt->fetch();
        echo "<div class='success'>✅ Members count: " . $result['count'] . "</div>";
        
        $stmt = $db->query("SELECT COUNT(*) as count FROM books");
        $result = $stmt->fetch();
        echo "<div class='success'>✅ Books count: " . $result['count'] . "</div>";
        
        $stmt = $db->query("SELECT COUNT(*) as count FROM book_loans");
        $result = $stmt->fetch();
        echo "<div class='success'>✅ Loans count: " . $result['count'] . "</div>";
        
    } else {
        echo "<div class='error'>❌ Database connection failed</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🎯 Alternative Access Methods</h2>";
echo "<div class='info'>";
echo "<p><strong>If the above links don't work, try these direct URLs:</strong></p>";
echo "<ul>";
echo "<li><code>http://localhost/LMS_SYSTEM/diagnostic.php</code></li>";
echo "<li><code>http://localhost/LMS_SYSTEM/database_status.php</code></li>";
echo "<li><code>http://localhost/LMS_SYSTEM/troubleshoot.php</code></li>";
echo "<li><code>http://localhost/LMS_SYSTEM/admin/ajax/test_database_clean.php</code></li>";
echo "</ul>";
echo "</div>";
echo "</div>";
