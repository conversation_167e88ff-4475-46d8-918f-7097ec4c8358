<?php
/**
 * Member Profile Card Generator
 */
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and has appropriate role
if (!isset($_SESSION['user_id']) && !isset($_SESSION['member_id'])) {
    redirect(url('login.php'));
}

// Get member ID from URL
$member_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($member_id <= 0) {
    redirect(url('members/index.php'));
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Get member data
$query = "SELECT * FROM members WHERE id = :id";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $member_id);
$stmt->execute();

if ($stmt->rowCount() === 0) {
    redirect(url('members/index.php'));
}

$member = $stmt->fetch();

// Generate QR code data (member ID and basic info)
$qr_data = json_encode([
    'member_id' => $member['id'],
    'name' => $member['first_name'] . ' ' . $member['last_name'],
    'email' => $member['email'],
    'status' => $member['membership_status']
]);

// QR Code API URL (using qr-server.com)
$qr_code_url = 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=' . urlencode($qr_data);

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Profile Card - <?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        @media print {
            .no-print { display: none !important; }
            body { margin: 0; }
            .card { border: none !important; box-shadow: none !important; }
        }
        
        .member-card {
            max-width: 400px;
            margin: 0 auto;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .member-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100px;
            background: rgba(255,255,255,0.1);
            border-radius: 50% 50% 0 0;
            transform: scale(2) translateY(-50px);
        }
        
        .card-header-custom {
            background: rgba(255,255,255,0.1);
            border-bottom: 1px solid rgba(255,255,255,0.2);
            padding: 20px;
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .member-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 2rem;
        }
        
        .card-body-custom {
            padding: 25px;
            position: relative;
            z-index: 2;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .qr-section {
            background: white;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            margin-top: 20px;
        }
        
        .library-logo {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1.5rem;
            opacity: 0.7;
        }
        
        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-active { background: #28a745; }
        .status-inactive { background: #6c757d; }
        .status-suspended { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid no-print">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Member Profile Card</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="view.php?id=<?php echo $member_id; ?>" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="bi bi-arrow-left"></i> Back to Member
                        </a>
                        <button onclick="window.print()" class="btn btn-sm btn-primary">
                            <i class="bi bi-printer"></i> Print Card
                        </button>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <!-- Member Profile Card -->
                <div class="member-card">
                    <div class="library-logo">
                        <i class="bi bi-book"></i>
                    </div>
                    
                    <div class="card-header-custom">
                        <div class="member-avatar">
                            <i class="bi bi-person"></i>
                        </div>
                        <h4 class="mb-1"><?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></h4>
                        <p class="mb-0 opacity-75">Library Member</p>
                    </div>
                    
                    <div class="card-body-custom">
                        <div class="info-row">
                            <span><i class="bi bi-hash me-2"></i>Member ID</span>
                            <strong><?php echo str_pad($member['id'], 6, '0', STR_PAD_LEFT); ?></strong>
                        </div>
                        
                        <div class="info-row">
                            <span><i class="bi bi-envelope me-2"></i>Email</span>
                            <span class="text-end" style="font-size: 0.9rem;"><?php echo h($member['email']); ?></span>
                        </div>
                        
                        <?php if ($member['phone']): ?>
                        <div class="info-row">
                            <span><i class="bi bi-telephone me-2"></i>Phone</span>
                            <span><?php echo h($member['phone']); ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <div class="info-row">
                            <span><i class="bi bi-calendar me-2"></i>Member Since</span>
                            <span><?php echo date('M j, Y', strtotime($member['membership_date'])); ?></span>
                        </div>
                        
                        <div class="info-row">
                            <span><i class="bi bi-shield-check me-2"></i>Status</span>
                            <span class="status-badge status-<?php echo $member['membership_status']; ?>">
                                <?php echo ucfirst($member['membership_status']); ?>
                            </span>
                        </div>
                        
                        <div class="qr-section">
                            <img src="<?php echo $qr_code_url; ?>" alt="Member QR Code" style="max-width: 120px;">
                            <p class="text-dark mb-0 mt-2" style="font-size: 0.8rem;">
                                Scan for quick access
                            </p>
                        </div>
                        
                        <div class="text-center mt-3" style="font-size: 0.8rem; opacity: 0.8;">
                            <p class="mb-0">Library Management System</p>
                            <p class="mb-0">Valid until: <?php echo date('M j, Y', strtotime('+1 year', strtotime($member['membership_date']))); ?></p>
                        </div>
                    </div>
                </div>
                
                <!-- Card Actions -->
                <div class="text-center mt-4 no-print">
                    <div class="btn-group" role="group">
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="bi bi-printer me-2"></i>Print Card
                        </button>
                        <a href="view.php?id=<?php echo $member_id; ?>" class="btn btn-outline-secondary">
                            <i class="bi bi-eye me-2"></i>View Profile
                        </a>
                        <a href="edit.php?id=<?php echo $member_id; ?>" class="btn btn-outline-warning">
                            <i class="bi bi-pencil me-2"></i>Edit Member
                        </a>
                    </div>
                </div>
                
                <!-- Card Information -->
                <div class="card mt-4 no-print">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-info-circle me-2"></i>Card Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><i class="bi bi-check-circle text-success me-2"></i>This card contains a QR code for quick member identification</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>The card is valid for one year from the membership date</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Print on cardstock for best results</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>QR code contains encrypted member information</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
