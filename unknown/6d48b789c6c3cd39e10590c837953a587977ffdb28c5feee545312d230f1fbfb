<?php
/**
 * Google Lite Authentication
 * 
 * This is a lightweight version of the Google authentication page for slow connections.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
} else {
    session_regenerate_id(true);
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/google_auth.php';

// Check if user is already logged in
if (isLoggedIn()) {
    // Redirect based on role
    if (isAdmin()) {
        redirect(url('admin/dashboard.php'));
    } elseif (isLibrarian()) {
        redirect(url('librarian/dashboard.php'));
    } else {
        redirect(url('index.php'));
    }
} elseif (isMemberLoggedIn()) {
    redirect(url('member_dashboard.php'));
}

// Get Google login URL with direct=1 parameter
$google_login_url = getGoogleLoginUrl('', true);

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Login - Library Management System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
            text-align: center;
        }
        .container {
            max-width: 400px;
            margin: 40px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
        }
        .google-btn {
            display: inline-block;
            background-color: #fff;
            color: #757575;
            border: 1px solid #dadce0;
            border-radius: 4px;
            padding: 10px 20px;
            font-size: 16px;
            text-decoration: none;
            margin: 20px 0;
            transition: background-color 0.3s;
        }
        .google-btn:hover {
            background-color: #f8f9fa;
        }
        .google-logo {
            vertical-align: middle;
            margin-right: 10px;
            width: 18px;
            height: 18px;
        }
        .back-link {
            display: block;
            margin-top: 20px;
            color: #1a73e8;
            text-decoration: none;
            font-size: 14px;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .loading {
            display: none;
            margin: 20px 0;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Google Authentication</h1>
        <p>Sign in to the Library Management System using your Google account.</p>
        
        <a href="<?php echo h($google_login_url); ?>" class="google-btn" id="googleBtn">
            <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" class="google-logo">
            Sign in with Google
        </a>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <span>Connecting to Google...</span>
        </div>
        
        <a href="login.php" class="back-link">Return to standard login</a>
    </div>
    
    <script>
        document.getElementById('googleBtn').addEventListener('click', function(e) {
            document.getElementById('loading').style.display = 'block';
            this.style.display = 'none';
        });
    </script>
</body>
</html>
