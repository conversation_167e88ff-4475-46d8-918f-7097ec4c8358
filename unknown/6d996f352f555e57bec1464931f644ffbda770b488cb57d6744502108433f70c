<?php
// Include config file if not already included
if (!defined('BASE_URL')) {
    require_once __DIR__ . '/../config/config.php';
}
?>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>Library Management System</title>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

<!-- PWA Support -->
<link rel="manifest" href="<?php echo url('manifest.json'); ?>">
<meta name="theme-color" content="#007bff">
<link rel="apple-touch-icon" href="<?php echo url('assets/img/icon-192.png'); ?>">

<!-- Custom CSS -->
<link rel="stylesheet" href="<?php echo url('assets/css/style.css'); ?>">
<link rel="stylesheet" href="<?php echo url('assets/css/notifications.css'); ?>">

<!-- Bootstrap JS (loaded early for dropdown functionality) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

<!-- Custom JavaScript -->
<script src="<?php echo url('assets/js/notifications-dropdown.js'); ?>"></script>

<!-- PWA Registration Script -->
<script>
    // Register Service Worker for PWA support
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('<?php echo url('service-worker.js'); ?>')
                .then(registration => {
                    console.log('Service Worker registered with scope:', registration.scope);
                })
                .catch(error => {
                    console.error('Service Worker registration failed:', error);
                });
        });
    }
</script>
