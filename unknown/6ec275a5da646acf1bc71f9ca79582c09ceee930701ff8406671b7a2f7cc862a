<?php
// Set the content type to PNG image
header('Content-Type: image/png');

// Get the book number from the query string
$book_number = isset($_GET['book']) ? (int)$_GET['book'] : 1;

// Set colors based on book number
switch ($book_number) {
    case 1:
        $bg_color = [65, 105, 225]; // Royal Blue
        $text_color = [255, 255, 255]; // White
        break;
    case 2:
        $bg_color = [220, 20, 60]; // Crimson
        $text_color = [255, 255, 255]; // White
        break;
    case 3:
        $bg_color = [46, 139, 87]; // Sea Green
        $text_color = [255, 255, 255]; // White
        break;
    default:
        $bg_color = [65, 105, 225]; // Royal Blue
        $text_color = [255, 255, 255]; // White
}

// Create a 200x300 image (book dimensions)
$img = imagecreatetruecolor(200, 300);

// Enable alpha blending
imagealphablending($img, true);
imagesavealpha($img, true);

// Fill with transparent background
$transparent = imagecolorallocatealpha($img, 0, 0, 0, 127);
imagefill($img, 0, 0, $transparent);

// Allocate colors
$bg = imagecolorallocate($img, $bg_color[0], $bg_color[1], $bg_color[2]);
$text = imagecolorallocate($img, $text_color[0], $text_color[1], $text_color[2]);
$spine = imagecolorallocate($img,
    max(0, $bg_color[0] - 30),
    max(0, $bg_color[1] - 30),
    max(0, $bg_color[2] - 30)
);

// Draw book cover (rectangle with slight perspective)
$points = [
    20, 30,   // top-left
    180, 30,  // top-right
    190, 270, // bottom-right
    10, 270   // bottom-left
];
imagefilledpolygon($img, $points, 4, $bg);

// Draw book spine
$spine_points = [
    10, 30,   // top-left
    20, 30,   // top-right
    10, 270,  // bottom-right
    0, 270    // bottom-left
];
imagefilledpolygon($img, $spine_points, 4, $spine);

// Add some decoration lines
imageline($img, 30, 60, 170, 60, $text);
imageline($img, 30, 240, 170, 240, $text);

// No text label as requested

// Output the image
imagepng($img);

// Free memory
imagedestroy($img);
?>
