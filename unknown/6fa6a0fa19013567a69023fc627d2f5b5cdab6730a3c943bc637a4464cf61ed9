<?php
/**
 * Direct Google Login
 *
 * This script provides a direct login using a specific Google account.
 * It's useful for quickly logging in with a known Google account.
 */

// Get the email from the URL parameter or use a default
$email = isset($_GET['email']) ? $_GET['email'] : '<EMAIL>';
$name = isset($_GET['name']) ? $_GET['name'] : '';

// Redirect to Google auto login with the email parameter
header('Location: google_auto_login.php?email=' . urlencode($email) . '&name=' . urlencode($name));
exit;
?>
