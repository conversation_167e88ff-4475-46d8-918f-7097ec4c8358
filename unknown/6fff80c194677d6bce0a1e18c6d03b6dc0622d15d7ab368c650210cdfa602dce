<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if member is logged in
if (!isMemberLoggedIn()) {
    redirect('../member_login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get member ID from session
$member_id = $_SESSION['member_id'];

// Initialize variables
$success_message = '';
$error_message = '';
$book = null;

// Process checkout form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['checkout'])) {
    $isbn = trim($_POST['isbn']);
    
    if (empty($isbn)) {
        $error_message = "Please enter a book ISBN";
    } else {
        try {
            // Start transaction
            $db->beginTransaction();
            
            // Get book details
            $query = "SELECT * FROM books WHERE isbn = :isbn";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':isbn', $isbn);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $book = $stmt->fetch();
                
                // Check if book is available
                if ($book['available_quantity'] > 0) {
                    // Check if member has already borrowed this book
                    $query = "SELECT * FROM book_loans 
                              WHERE book_id = :book_id AND member_id = :member_id AND status = 'borrowed'";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':book_id', $book['id']);
                    $stmt->bindParam(':member_id', $member_id);
                    $stmt->execute();
                    
                    if ($stmt->rowCount() > 0) {
                        $error_message = "You have already borrowed this book";
                    } else {
                        // Check if member has reached the maximum allowed loans
                        $query = "SELECT COUNT(*) as loan_count FROM book_loans 
                                  WHERE member_id = :member_id AND status = 'borrowed'";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':member_id', $member_id);
                        $stmt->execute();
                        $loan_count = $stmt->fetch()['loan_count'];
                        
                        // Get max loans setting (default to 5)
                        $query = "SELECT setting_value FROM settings WHERE setting_key = 'max_loans_per_member'";
                        $stmt = $db->prepare($query);
                        $stmt->execute();
                        $max_loans = $stmt->rowCount() > 0 ? (int)$stmt->fetch()['setting_value'] : 5;
                        
                        if ($loan_count >= $max_loans) {
                            $error_message = "You have reached the maximum number of allowed loans ($max_loans)";
                        } else {
                            // Check if member has any overdue books
                            $query = "SELECT COUNT(*) as overdue_count FROM book_loans 
                                      WHERE member_id = :member_id AND status = 'borrowed' AND due_date < CURDATE()";
                            $stmt = $db->prepare($query);
                            $stmt->bindParam(':member_id', $member_id);
                            $stmt->execute();
                            $overdue_count = $stmt->fetch()['overdue_count'];
                            
                            // Get setting for allowing checkout with overdue books (default to false)
                            $query = "SELECT setting_value FROM settings WHERE setting_key = 'allow_checkout_with_overdue'";
                            $stmt = $db->prepare($query);
                            $stmt->execute();
                            $allow_checkout_with_overdue = $stmt->rowCount() > 0 ? 
                                ($stmt->fetch()['setting_value'] === 'true') : false;
                            
                            if ($overdue_count > 0 && !$allow_checkout_with_overdue) {
                                $error_message = "You have overdue books. Please return them before borrowing more books.";
                            } else {
                                // Check if member has any unpaid fines
                                $query = "SELECT SUM(fine) as total_fine FROM book_loans 
                                          WHERE member_id = :member_id AND fine > 0 AND fine_paid = 0";
                                $stmt = $db->prepare($query);
                                $stmt->bindParam(':member_id', $member_id);
                                $stmt->execute();
                                $total_fine = $stmt->fetch()['total_fine'] ?? 0;
                                
                                // Get setting for allowing checkout with unpaid fines (default to false)
                                $query = "SELECT setting_value FROM settings WHERE setting_key = 'allow_checkout_with_fines'";
                                $stmt = $db->prepare($query);
                                $stmt->execute();
                                $allow_checkout_with_fines = $stmt->rowCount() > 0 ? 
                                    ($stmt->fetch()['setting_value'] === 'true') : false;
                                
                                if ($total_fine > 0 && !$allow_checkout_with_fines) {
                                    $error_message = "You have unpaid fines. Please pay your fines before borrowing more books.";
                                } else {
                                    // All checks passed, proceed with checkout
                                    
                                    // Get loan duration setting (default to 14 days)
                                    $query = "SELECT setting_value FROM settings WHERE setting_key = 'loan_duration_days'";
                                    $stmt = $db->prepare($query);
                                    $stmt->execute();
                                    $loan_duration = $stmt->rowCount() > 0 ? (int)$stmt->fetch()['setting_value'] : 14;
                                    
                                    // Set issue date and due date
                                    $issue_date = date('Y-m-d');
                                    $due_date = date('Y-m-d', strtotime("+$loan_duration days"));
                                    
                                    // Insert loan record
                                    $query = "INSERT INTO book_loans (book_id, member_id, issue_date, due_date, status) 
                                              VALUES (:book_id, :member_id, :issue_date, :due_date, 'borrowed')";
                                    $stmt = $db->prepare($query);
                                    $stmt->bindParam(':book_id', $book['id']);
                                    $stmt->bindParam(':member_id', $member_id);
                                    $stmt->bindParam(':issue_date', $issue_date);
                                    $stmt->bindParam(':due_date', $due_date);
                                    
                                    if ($stmt->execute()) {
                                        // Update book available quantity
                                        $query = "UPDATE books SET available_quantity = available_quantity - 1 
                                                  WHERE id = :book_id";
                                        $stmt = $db->prepare($query);
                                        $stmt->bindParam(':book_id', $book['id']);
                                        
                                        if ($stmt->execute()) {
                                            // Log activity
                                            logActivity($db, 'checkout', "Member self-checkout: {$book['title']}", 'book', $book['id']);
                                            
                                            // Commit transaction
                                            $db->commit();
                                            
                                            $success_message = "Book '{$book['title']}' has been checked out successfully. Due date: " . date('F j, Y', strtotime($due_date));
                                            
                                            // Clear form
                                            $isbn = '';
                                        } else {
                                            $db->rollBack();
                                            $error_message = "Failed to update book quantity";
                                        }
                                    } else {
                                        $db->rollBack();
                                        $error_message = "Failed to create loan record";
                                    }
                                }
                            }
                        }
                    }
                } else {
                    $error_message = "This book is not available for checkout";
                }
            } else {
                $error_message = "Book with ISBN '$isbn' not found";
            }
        } catch (Exception $e) {
            // Rollback transaction on error
            $db->rollBack();
            $error_message = "Error: " . $e->getMessage();
        }
    }
}

// Process barcode scan
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['isbn'])) {
    $isbn = trim($_GET['isbn']);
    
    // Get book details
    $query = "SELECT * FROM books WHERE isbn = :isbn";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':isbn', $isbn);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $book = $stmt->fetch();
    }
}

// Page title
$page_title = 'Self-Checkout';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include '../includes/head.php'; ?>
    <style>
        .checkout-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .checkout-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        .book-preview {
            display: flex;
            align-items: center;
            padding: 15px;
            border-radius: 10px;
            background-color: #f8f9fa;
            margin-top: 20px;
        }
        .book-cover {
            width: 100px;
            height: 150px;
            object-fit: cover;
            margin-right: 20px;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .book-info {
            flex: 1;
        }
        .scanner-container {
            position: relative;
            width: 100%;
            height: 300px;
            overflow: hidden;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        #scanner-placeholder {
            width: 100%;
            height: 100%;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-upc-scan me-2 text-primary"></i>Self-Checkout</h1>
                </div>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card checkout-card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-upc me-2"></i>Enter Book ISBN</h5>
                            </div>
                            <div class="card-body">
                                <form action="" method="post">
                                    <div class="mb-3">
                                        <label for="isbn" class="form-label">ISBN</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="isbn" name="isbn" placeholder="Enter book ISBN" value="<?php echo htmlspecialchars($isbn ?? ''); ?>" required>
                                            <button type="submit" name="checkout" class="btn btn-primary">
                                                <i class="bi bi-check-circle me-1"></i> Checkout
                                            </button>
                                        </div>
                                        <div class="form-text">Enter the ISBN number printed on the back of the book</div>
                                    </div>
                                </form>
                                
                                <?php if ($book): ?>
                                    <div class="book-preview">
                                        <?php if (!empty($book['cover_image']) && file_exists('../uploads/covers/' . $book['cover_image'])): ?>
                                            <img src="<?php echo url('../uploads/covers/' . $book['cover_image']); ?>" class="book-cover" alt="<?php echo htmlspecialchars($book['title']); ?>">
                                        <?php else: ?>
                                            <img src="<?php echo url('../assets/img/book-placeholder.jpg'); ?>" class="book-cover" alt="No Cover Available">
                                        <?php endif; ?>
                                        
                                        <div class="book-info">
                                            <h5><?php echo htmlspecialchars($book['title']); ?></h5>
                                            <p class="text-muted"><?php echo htmlspecialchars($book['author']); ?></p>
                                            <p><strong>ISBN:</strong> <?php echo htmlspecialchars($book['isbn']); ?></p>
                                            <p>
                                                <strong>Status:</strong>
                                                <?php if ($book['available_quantity'] > 0): ?>
                                                    <span class="badge bg-success">Available</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Not Available</span>
                                                <?php endif; ?>
                                            </p>
                                            
                                            <?php if ($book['available_quantity'] > 0): ?>
                                                <form action="" method="post">
                                                    <input type="hidden" name="isbn" value="<?php echo htmlspecialchars($book['isbn']); ?>">
                                                    <button type="submit" name="checkout" class="btn btn-primary">
                                                        <i class="bi bi-check-circle me-1"></i> Checkout This Book
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="card checkout-card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Self-Checkout Instructions</h5>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li>Enter the ISBN number of the book you want to borrow</li>
                                    <li>Verify that the book information is correct</li>
                                    <li>Click the "Checkout" button to borrow the book</li>
                                    <li>Take your book and enjoy reading!</li>
                                </ol>
                                <div class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    <strong>Important:</strong> Please make sure to return the book by the due date to avoid fines.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card checkout-card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-camera me-2"></i>Scan Barcode</h5>
                            </div>
                            <div class="card-body">
                                <div class="scanner-container">
                                    <div id="scanner-placeholder">
                                        <i class="bi bi-camera text-muted" style="font-size: 48px;"></i>
                                        <p class="mt-3 text-muted">Barcode scanner not available</p>
                                        <button id="enable-scanner" class="btn btn-outline-primary mt-2">
                                            <i class="bi bi-camera me-1"></i> Enable Camera
                                        </button>
                                    </div>
                                    <video id="scanner" style="width: 100%; height: 100%; object-fit: cover; display: none;"></video>
                                </div>
                                <div class="d-grid">
                                    <button id="toggle-scanner" class="btn btn-outline-primary" disabled>
                                        <i class="bi bi-camera-video me-1"></i> <span id="scanner-btn-text">Start Scanner</span>
                                    </button>
                                </div>
                                <div class="form-text text-center mt-2">
                                    Point your camera at the book's barcode to scan it automatically
                                </div>
                            </div>
                        </div>
                        
                        <div class="card checkout-card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="bi bi-book me-2"></i>My Current Loans</h5>
                            </div>
                            <div class="card-body">
                                <?php
                                // Get active loans
                                $query = "SELECT bl.*, b.title, b.author 
                                          FROM book_loans bl
                                          JOIN books b ON bl.book_id = b.id
                                          WHERE bl.member_id = :member_id AND bl.status = 'borrowed'
                                          ORDER BY bl.due_date ASC
                                          LIMIT 5";
                                $stmt = $db->prepare($query);
                                $stmt->bindParam(':member_id', $member_id);
                                $stmt->execute();
                                $active_loans = $stmt->fetchAll();
                                ?>
                                
                                <?php if (count($active_loans) > 0): ?>
                                    <div class="list-group">
                                        <?php foreach ($active_loans as $loan): ?>
                                            <?php
                                            $is_overdue = strtotime($loan['due_date']) < time();
                                            $due_soon = !$is_overdue && strtotime($loan['due_date']) < strtotime('+3 days');
                                            $list_class = $is_overdue ? 'list-group-item-danger' : ($due_soon ? 'list-group-item-warning' : '');
                                            ?>
                                            <div class="list-group-item <?php echo $list_class; ?>">
                                                <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($loan['title']); ?></h6>
                                                    <small>
                                                        <?php if ($is_overdue): ?>
                                                            <span class="text-danger">Overdue</span>
                                                        <?php elseif ($due_soon): ?>
                                                            <span class="text-warning">Due Soon</span>
                                                        <?php else: ?>
                                                            <span class="text-success">On Time</span>
                                                        <?php endif; ?>
                                                    </small>
                                                </div>
                                                <p class="mb-1 small"><?php echo htmlspecialchars($loan['author']); ?></p>
                                                <small>Due: <?php echo date('M j, Y', strtotime($loan['due_date'])); ?></small>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="d-grid mt-3">
                                        <a href="<?php echo url('member/my_loans.php'); ?>" class="btn btn-outline-success">
                                            <i class="bi bi-list-ul me-1"></i> View All Loans
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle me-2"></i>You don't have any active loans.
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/@zxing/library@latest"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const enableScannerBtn = document.getElementById('enable-scanner');
            const toggleScannerBtn = document.getElementById('toggle-scanner');
            const scannerBtnText = document.getElementById('scanner-btn-text');
            const scannerPlaceholder = document.getElementById('scanner-placeholder');
            const scanner = document.getElementById('scanner');
            const isbnInput = document.getElementById('isbn');
            
            let codeReader = null;
            let selectedDeviceId = null;
            let scanning = false;
            
            // Enable scanner button click
            enableScannerBtn.addEventListener('click', function() {
                initScanner();
            });
            
            // Toggle scanner button click
            toggleScannerBtn.addEventListener('click', function() {
                if (scanning) {
                    stopScanner();
                } else {
                    startScanner();
                }
            });
            
            // Initialize scanner
            function initScanner() {
                codeReader = new ZXing.BrowserMultiFormatReader();
                
                codeReader.listVideoInputDevices()
                    .then((videoInputDevices) => {
                        if (videoInputDevices.length > 0) {
                            selectedDeviceId = videoInputDevices[0].deviceId;
                            toggleScannerBtn.disabled = false;
                            startScanner();
                        } else {
                            console.error('No video input devices found');
                            alert('No camera detected. Please make sure your camera is connected and you have given permission to use it.');
                        }
                    })
                    .catch((err) => {
                        console.error(err);
                        alert('Error accessing camera: ' + err);
                    });
            }
            
            // Start scanner
            function startScanner() {
                scanning = true;
                scannerPlaceholder.style.display = 'none';
                scanner.style.display = 'block';
                scannerBtnText.textContent = 'Stop Scanner';
                toggleScannerBtn.classList.replace('btn-outline-primary', 'btn-outline-danger');
                
                codeReader.decodeFromVideoDevice(selectedDeviceId, 'scanner', (result, err) => {
                    if (result) {
                        console.log('Found ISBN:', result.text);
                        isbnInput.value = result.text;
                        
                        // Redirect to the same page with ISBN parameter
                        window.location.href = '?isbn=' + result.text;
                    }
                    
                    if (err && !(err instanceof ZXing.NotFoundException)) {
                        console.error(err);
                    }
                });
            }
            
            // Stop scanner
            function stopScanner() {
                scanning = false;
                codeReader.reset();
                scanner.style.display = 'none';
                scannerPlaceholder.style.display = 'flex';
                scannerBtnText.textContent = 'Start Scanner';
                toggleScannerBtn.classList.replace('btn-outline-danger', 'btn-outline-primary');
            }
        });
    </script>
</body>
</html>
