-- Email related tables for Library Management System

-- Create email_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS email_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    recipient_email VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    status ENUM('pending', 'sent', 'failed') NOT NULL DEFAULT 'pending',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create reminder_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS reminder_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    loan_id INT NOT NULL,
    reminder_type ENUM('due_date', 'overdue') NOT NULL,
    days_before INT,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (loan_id) REFERENCES book_loans(id) ON DELETE CASCADE
);

-- Create settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_group VARCHAR(50) NOT NULL,
    setting_key VARCHAR(50) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY group_key (setting_group, setting_key)
);

-- Insert default email settings
INSERT INTO settings (setting_group, setting_key, setting_value) VALUES
('email', 'from_email', '<EMAIL>'),
('email', 'from_name', 'Library Management System'),
('email', 'reply_to', '<EMAIL>'),
('email', 'smtp_enabled', 'false'),
('email', 'smtp_host', 'smtp.example.com'),
('email', 'smtp_port', '587'),
('email', 'smtp_username', ''),
('email', 'smtp_password', ''),
('email', 'smtp_secure', 'tls')
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

-- Insert default fine settings
INSERT INTO settings (setting_group, setting_key, setting_value) VALUES
('fines', 'fine_rate_per_day', '0.25'),
('fines', 'grace_period_days', '3'),
('fines', 'max_fine_per_book', '25.00')
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

-- Insert default notification settings
INSERT INTO settings (setting_group, setting_key, setting_value) VALUES
('notifications', 'due_date_reminder_days', '3'),
('notifications', 'send_overdue_notifications', 'true'),
('notifications', 'overdue_notification_frequency', '7')
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;
