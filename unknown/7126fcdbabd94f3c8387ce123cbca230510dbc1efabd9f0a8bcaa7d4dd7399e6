<?php
header('Content-Type: application/json');
session_start();

require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['query']) || empty(trim($input['query']))) {
    echo json_encode(['success' => false, 'message' => 'Query is required']);
    exit;
}

$query = trim($input['query']);

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Search books by title, author, ISBN, or category
    $sql = "SELECT id, title, author, isbn, category, quantity, available_quantity 
            FROM books 
            WHERE title LIKE :query 
               OR author LIKE :query 
               OR isbn LIKE :query 
               OR category LIKE :query
            ORDER BY title ASC 
            LIMIT 10";
    
    $stmt = $db->prepare($sql);
    $searchTerm = '%' . $query . '%';
    $stmt->bindParam(':query', $searchTerm);
    $stmt->execute();
    
    $books = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'books' => $books,
        'count' => count($books)
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
