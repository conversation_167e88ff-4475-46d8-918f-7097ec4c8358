<?php
/**
 * Member Self-Service Dashboard
 */
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if member is logged in
if (!isset($_SESSION['member_id'])) {
    redirect(url('member_login.php'));
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Get member information
$member_id = $_SESSION['member_id'];
$query = "SELECT * FROM members WHERE id = :id";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $member_id);
$stmt->execute();
$member = $stmt->fetch();

if (!$member) {
    session_destroy();
    redirect(url('member_login.php'));
}

// Get member's current loans
$query = "SELECT bl.*, b.title, b.author, b.isbn, b.cover_image 
          FROM book_loans bl 
          JOIN books b ON bl.book_id = b.id 
          WHERE bl.member_id = :member_id AND bl.return_date IS NULL 
          ORDER BY bl.due_date ASC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$current_loans = $stmt->fetchAll();

// Get member's loan history
$query = "SELECT bl.*, b.title, b.author, b.isbn 
          FROM book_loans bl 
          JOIN books b ON bl.book_id = b.id 
          WHERE bl.member_id = :member_id AND bl.return_date IS NOT NULL 
          ORDER BY bl.return_date DESC 
          LIMIT 10";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$loan_history = $stmt->fetchAll();

// Get member's reservations
$query = "SELECT br.*, b.title, b.author, b.isbn 
          FROM book_reservations br 
          JOIN books b ON br.book_id = b.id 
          WHERE br.member_id = :member_id 
          ORDER BY br.created_at DESC 
          LIMIT 5";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$reservations = $stmt->fetchAll();

// Calculate statistics
$overdue_count = 0;
$total_fines = 0;
foreach ($current_loans as $loan) {
    if (strtotime($loan['due_date']) < strtotime('today')) {
        $overdue_count++;
        // Calculate fine (example: $0.50 per day)
        $days_overdue = (strtotime('today') - strtotime($loan['due_date'])) / (60 * 60 * 24);
        $total_fines += $days_overdue * 0.50;
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Library Account - <?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- Member Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-book me-2"></i>My Library Account
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="member_self_service.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="catalog.php">Browse Books</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="member/profile.php">My Profile</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?php echo h($member['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="member/profile.php"><i class="bi bi-person me-2"></i>My Profile</a></li>
                            <li><a class="dropdown-item" href="members/profile_card.php?id=<?php echo $member_id; ?>"><i class="bi bi-card-text me-2"></i>My Library Card</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="member_logout.php"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="mb-1">Welcome back, <?php echo h($member['first_name']); ?>!</h2>
                                <p class="mb-0">Member since <?php echo date('F j, Y', strtotime($member['membership_date'])); ?></p>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <div class="d-flex justify-content-md-end gap-2">
                                    <span class="badge bg-light text-dark fs-6">
                                        ID: <?php echo str_pad($member['id'], 6, '0', STR_PAD_LEFT); ?>
                                    </span>
                                    <span class="badge bg-<?php echo $member['membership_status'] === 'active' ? 'success' : 'warning'; ?> fs-6">
                                        <?php echo ucfirst($member['membership_status']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-book-fill text-primary fs-1"></i>
                        <h4 class="mt-2"><?php echo count($current_loans); ?></h4>
                        <p class="text-muted mb-0">Current Loans</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-clock-fill text-warning fs-1"></i>
                        <h4 class="mt-2"><?php echo $overdue_count; ?></h4>
                        <p class="text-muted mb-0">Overdue Books</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-bookmark-fill text-info fs-1"></i>
                        <h4 class="mt-2"><?php echo count($reservations); ?></h4>
                        <p class="text-muted mb-0">Reservations</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-currency-dollar text-danger fs-1"></i>
                        <h4 class="mt-2">$<?php echo number_format($total_fines, 2); ?></h4>
                        <p class="text-muted mb-0">Outstanding Fines</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Current Loans -->
            <div class="col-md-8 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-book me-2"></i>Current Loans</h5>
                        <a href="catalog.php" class="btn btn-sm btn-primary">
                            <i class="bi bi-search me-1"></i>Browse Books
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if (count($current_loans) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Book</th>
                                            <th>Due Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($current_loans as $loan): ?>
                                            <?php
                                            $is_overdue = strtotime($loan['due_date']) < strtotime('today');
                                            $due_soon = strtotime($loan['due_date']) <= strtotime('+3 days');
                                            ?>
                                            <tr class="<?php echo $is_overdue ? 'table-danger' : ($due_soon ? 'table-warning' : ''); ?>">
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <?php if ($loan['cover_image']): ?>
                                                            <img src="<?php echo url('uploads/covers/' . $loan['cover_image']); ?>" 
                                                                 alt="Book Cover" class="me-3" style="width: 40px; height: 60px; object-fit: cover;">
                                                        <?php else: ?>
                                                            <div class="bg-light me-3 d-flex align-items-center justify-content-center" 
                                                                 style="width: 40px; height: 60px;">
                                                                <i class="bi bi-book text-muted"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                        <div>
                                                            <h6 class="mb-0"><?php echo h($loan['title']); ?></h6>
                                                            <small class="text-muted">by <?php echo h($loan['author']); ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php echo date('M j, Y', strtotime($loan['due_date'])); ?>
                                                    <?php if ($is_overdue): ?>
                                                        <br><small class="text-danger">Overdue</small>
                                                    <?php elseif ($due_soon): ?>
                                                        <br><small class="text-warning">Due Soon</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($is_overdue): ?>
                                                        <span class="badge bg-danger">Overdue</span>
                                                    <?php elseif ($due_soon): ?>
                                                        <span class="badge bg-warning">Due Soon</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">On Time</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="requestRenewal(<?php echo $loan['id']; ?>)">
                                                        <i class="bi bi-arrow-clockwise"></i> Renew
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="bi bi-book display-1 text-muted"></i>
                                <h5 class="mt-3">No current loans</h5>
                                <p class="text-muted">Browse our catalog to find books to borrow.</p>
                                <a href="catalog.php" class="btn btn-primary">
                                    <i class="bi bi-search me-2"></i>Browse Books
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Quick Actions & Info -->
            <div class="col-md-4">
                <!-- Quick Actions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-lightning me-2"></i>Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="catalog.php" class="btn btn-primary">
                                <i class="bi bi-search me-2"></i>Browse Books
                            </a>
                            <a href="member/profile.php" class="btn btn-outline-secondary">
                                <i class="bi bi-person me-2"></i>Update Profile
                            </a>
                            <a href="members/profile_card.php?id=<?php echo $member_id; ?>" class="btn btn-outline-info">
                                <i class="bi bi-card-text me-2"></i>View Library Card
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Account Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>Account Status</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="d-flex justify-content-between">
                                <span>Status:</span>
                                <span class="badge bg-<?php echo $member['membership_status'] === 'active' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst($member['membership_status']); ?>
                                </span>
                            </li>
                            <li class="d-flex justify-content-between mt-2">
                                <span>Books Borrowed:</span>
                                <strong><?php echo count($current_loans); ?></strong>
                            </li>
                            <li class="d-flex justify-content-between mt-2">
                                <span>Overdue Books:</span>
                                <strong class="<?php echo $overdue_count > 0 ? 'text-danger' : 'text-success'; ?>">
                                    <?php echo $overdue_count; ?>
                                </strong>
                            </li>
                            <li class="d-flex justify-content-between mt-2">
                                <span>Outstanding Fines:</span>
                                <strong class="<?php echo $total_fines > 0 ? 'text-danger' : 'text-success'; ?>">
                                    $<?php echo number_format($total_fines, 2); ?>
                                </strong>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Recent Activity -->
                <?php if (count($loan_history) > 0): ?>
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-clock-history me-2"></i>Recent Returns</h6>
                    </div>
                    <div class="card-body">
                        <?php foreach (array_slice($loan_history, 0, 3) as $loan): ?>
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-check-circle text-success me-2"></i>
                                <div class="flex-grow-1">
                                    <small class="fw-bold"><?php echo h($loan['title']); ?></small><br>
                                    <small class="text-muted">Returned <?php echo date('M j', strtotime($loan['return_date'])); ?></small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function requestRenewal(loanId) {
            if (confirm('Request renewal for this book?')) {
                // In a real system, this would make an AJAX call to request renewal
                alert('Renewal request submitted! You will be notified of the status.');
            }
        }
    </script>
</body>
</html>
