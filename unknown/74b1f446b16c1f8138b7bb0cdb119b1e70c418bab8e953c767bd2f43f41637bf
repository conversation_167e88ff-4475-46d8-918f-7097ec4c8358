<?php
session_start();

// Include required files with error handling
try {
    require_once '../config/database.php';
    require_once '../config/config.php';
    require_once '../includes/functions.php';
} catch (Exception $e) {
    die('Error loading required files: ' . $e->getMessage());
}

// Check if user is logged in and has admin/librarian privileges
if (!isLoggedIn()) {
    redirect('../login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get overdue books (both explicitly marked as overdue AND borrowed books past due)
$query = "SELECT bl.*, b.title as book_title, b.isbn, b.author, m.first_name, m.last_name, m.email, m.phone
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          JOIN members m ON bl.member_id = m.id
          WHERE (bl.status = 'overdue' OR (bl.due_date < CURDATE() AND bl.status = 'borrowed'))
          ORDER BY bl.due_date ASC";

$stmt = $db->prepare($query);
$stmt->execute();
$overdue_books = $stmt->fetchAll();

// Calculate total fine
$total_fine = 0;
foreach ($overdue_books as $book) {
    $total_fine += calculateFine($book['due_date']);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Overdue Books Report - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .stats-card {
            border-left: 4px solid #dc3545;
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .print-hide {
            display: block;
        }
        @media print {
            .print-hide {
                display: none !important;
            }
            .container-fluid {
                margin: 0;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>Overdue Books Report</h1>
                    <div class="btn-toolbar mb-2 mb-md-0 print-hide">
                        <button onclick="window.print()" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="bi bi-printer"></i> Print Report
                        </button>
                        <a href="index.php" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> Back to Reports
                        </a>
                    </div>
                </div>

                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-6 mb-3">
                        <div class="card stats-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-exclamation-triangle-fill fs-1 text-danger mb-2"></i>
                                <h5 class="card-title">Total Overdue Books</h5>
                                <h3 class="text-danger"><?php echo count($overdue_books); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card stats-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-currency-dollar fs-1 text-warning mb-2"></i>
                                <h5 class="card-title">Total Estimated Fines</h5>
                                <h3 class="text-warning">$<?php echo number_format($total_fine, 2); ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Overdue Books Table -->
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>Overdue Books
                            <small>(As of <?php echo date('F j, Y'); ?>)</small>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($overdue_books)): ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                <strong>Great news!</strong> No overdue books found. All books are returned on time!
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning mb-3">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong><?php echo count($overdue_books); ?> book(s)</strong> are currently overdue. 
                                Please contact the members to arrange returns.
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Book Details</th>
                                            <th>Member Information</th>
                                            <th>Loan Details</th>
                                            <th>Days Overdue</th>
                                            <th>Estimated Fine</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($overdue_books as $book): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($book['book_title']); ?></strong>
                                                    <br><small class="text-muted">by <?php echo htmlspecialchars($book['author']); ?></small>
                                                    <?php if (!empty($book['isbn'])): ?>
                                                        <br><small class="text-muted">ISBN: <?php echo htmlspecialchars($book['isbn']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($book['first_name'] . ' ' . $book['last_name']); ?></strong>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($book['email']); ?></small>
                                                    <?php if (!empty($book['phone'])): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($book['phone']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong>Issue:</strong> <?php echo formatDate($book['issue_date']); ?>
                                                    <br><strong>Due:</strong> <?php echo formatDate($book['due_date']); ?>
                                                    <br><span class="badge bg-<?php echo $book['status'] === 'overdue' ? 'danger' : 'warning'; ?>">
                                                        <?php echo ucfirst($book['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php
                                                        $due = new DateTime($book['due_date']);
                                                        $today = new DateTime();
                                                        $days_overdue = $due->diff($today)->days;
                                                    ?>
                                                    <span class="badge bg-danger fs-6">
                                                        <?php echo $days_overdue; ?> day<?php echo $days_overdue != 1 ? 's' : ''; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong class="text-danger">
                                                        $<?php echo number_format(calculateFine($book['due_date']), 2); ?>
                                                    </strong>
                                                </td>
                                                <td>
                                                    <div class="btn-group-vertical btn-group-sm">
                                                        <a href="mailto:<?php echo htmlspecialchars($book['email']); ?>?subject=Overdue Book Reminder&body=Dear <?php echo htmlspecialchars($book['first_name']); ?>,%0A%0AThis is a reminder that the book '<?php echo htmlspecialchars($book['book_title']); ?>' is overdue. Please return it as soon as possible.%0A%0AThank you." 
                                                           class="btn btn-outline-primary btn-sm">
                                                            <i class="bi bi-envelope"></i> Email
                                                        </a>
                                                        <?php if (!empty($book['phone'])): ?>
                                                        <a href="tel:<?php echo htmlspecialchars($book['phone']); ?>" 
                                                           class="btn btn-outline-success btn-sm">
                                                            <i class="bi bi-telephone"></i> Call
                                                        </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Summary -->
                <?php if (!empty($overdue_books)): ?>
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title">Summary & Recommendations</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><strong>Total Overdue Books:</strong> <?php echo count($overdue_books); ?></li>
                                    <li><strong>Unique Members Affected:</strong> 
                                        <?php 
                                        $unique_members = array_unique(array_column($overdue_books, 'member_id'));
                                        echo count($unique_members); 
                                        ?>
                                    </li>
                                    <li><strong>Average Days Overdue:</strong> 
                                        <?php 
                                        $total_days = 0;
                                        foreach ($overdue_books as $book) {
                                            $due = new DateTime($book['due_date']);
                                            $today = new DateTime();
                                            $total_days += $due->diff($today)->days;
                                        }
                                        echo count($overdue_books) > 0 ? round($total_days / count($overdue_books), 1) : 0;
                                        ?> days
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><strong>Total Estimated Fines:</strong> $<?php echo number_format($total_fine, 2); ?></li>
                                    <li><strong>Recommended Actions:</strong></li>
                                    <li class="ms-3">• Send email reminders</li>
                                    <li class="ms-3">• Make phone calls for urgent cases</li>
                                    <li class="ms-3">• Consider suspension for repeat offenders</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Report Footer -->
                <div class="mt-4 text-center text-muted">
                    <small>
                        Report generated on <?php echo date('F j, Y \a\t g:i A'); ?> | 
                        Total overdue records: <?php echo count($overdue_books); ?>
                    </small>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
