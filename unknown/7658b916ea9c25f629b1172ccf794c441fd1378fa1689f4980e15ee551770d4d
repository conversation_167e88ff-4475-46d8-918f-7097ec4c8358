<?php
/**
 * Database diagnostic script for troubleshooting statistics refresh issues
 */

// Clean output buffer to prevent issues
if (ob_get_level()) {
    ob_clean();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

// Set content type to JSON
header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in JSON response

try {
    // Test 1: Database connection
    $database = new Database();
    $connection_test = $database->testConnection();

    if (!$connection_test['success']) {
        echo json_encode([
            'success' => false,
            'test' => 'connection',
            'message' => $connection_test['message']
        ]);
        exit;
    }

    $db = $database->getConnection();

    // Test 2: Check if required tables exist
    $required_tables = ['members', 'books', 'book_loans'];
    $missing_tables = [];

    foreach ($required_tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() == 0) {
            $missing_tables[] = $table;
        }
    }

    if (!empty($missing_tables)) {
        echo json_encode([
            'success' => false,
            'test' => 'tables',
            'message' => 'Missing tables: ' . implode(', ', $missing_tables)
        ]);
        exit;
    }

    // Test 3: Check table structure for required columns
    $table_checks = [
        'members' => ['id', 'first_name', 'last_name', 'email', 'created_at'],
        'books' => ['id', 'title'],
        'book_loans' => ['id', 'member_id', 'book_id', 'status', 'issue_date', 'due_date', 'fine']
    ];

    $missing_columns = [];

    foreach ($table_checks as $table => $columns) {
        $stmt = $db->query("DESCRIBE $table");
        $existing_columns = array_column($stmt->fetchAll(), 'Field');

        foreach ($columns as $column) {
            if (!in_array($column, $existing_columns)) {
                $missing_columns[] = "$table.$column";
            }
        }
    }

    if (!empty($missing_columns)) {
        echo json_encode([
            'success' => false,
            'test' => 'columns',
            'message' => 'Missing columns: ' . implode(', ', $missing_columns)
        ]);
        exit;
    }

    // Test 4: Try basic queries
    $test_queries = [
        'members_count' => "SELECT COUNT(*) as count FROM members",
        'books_count' => "SELECT COUNT(*) as count FROM books",
        'loans_count' => "SELECT COUNT(*) as count FROM book_loans"
    ];

    $query_results = [];

    foreach ($test_queries as $name => $query) {
        try {
            $stmt = $db->query($query);
            $result = $stmt->fetch();
            $query_results[$name] = $result['count'];
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'test' => 'query',
                'query' => $name,
                'message' => 'Query failed: ' . $e->getMessage()
            ]);
            exit;
        }
    }

    // Test 5: Try complex query (similar to what's failing)
    try {
        $stmt = $db->query("
            SELECT COUNT(DISTINCT member_id) as count
            FROM book_loans
            WHERE status = 'borrowed'
        ");
        $active_loans = $stmt->fetch()['count'];
        $query_results['active_loans'] = $active_loans;
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'test' => 'complex_query',
            'message' => 'Complex query failed: ' . $e->getMessage()
        ]);
        exit;
    }

    // All tests passed
    echo json_encode([
        'success' => true,
        'message' => 'All database tests passed',
        'results' => $query_results,
        'timestamp' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'test' => 'general',
        'message' => 'Unexpected error: ' . $e->getMessage()
    ]);
}
