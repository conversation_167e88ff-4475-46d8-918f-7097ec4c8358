<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Mark all notifications as read
$query = "UPDATE notifications 
          SET is_read = 1 
          WHERE (user_id = :user_id OR user_id IS NULL) AND is_read = 0";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $_SESSION['user_id']);
$stmt->execute();

// Log activity
logActivity($db, 'mark_read', 'Marked all notifications as read');

// Set success message
setMessage('All notifications marked as read', 'success');

// Redirect back to referring page or dashboard
$redirect_to = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '../admin/dashboard.php';
redirect($redirect_to);
?>
