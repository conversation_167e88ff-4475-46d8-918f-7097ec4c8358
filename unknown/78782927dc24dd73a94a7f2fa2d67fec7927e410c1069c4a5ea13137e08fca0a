<?php
echo "<h1>✅ Admin Directory Access Test</h1>";
echo "<p>If you can see this, the admin directory is accessible!</p>";
echo "<p><strong>Current time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>File location:</strong> " . __FILE__ . "</p>";

echo "<h2>🔗 Dashboard Links to Try:</h2>";
echo "<ul>";
echo "<li><a href='dashboard.php'>dashboard.php</a></li>";
echo "<li><a href='dashboard_simple_working.php'>dashboard_simple_working.php</a></li>";
echo "<li><a href='../quick_admin_login.php'>../quick_admin_login.php</a></li>";
echo "</ul>";

// Check if dashboard file exists
$dashboard_file = __DIR__ . '/dashboard.php';
echo "<h2>📁 File Check:</h2>";
echo "<p><strong>Dashboard file exists:</strong> " . (file_exists($dashboard_file) ? '✅ Yes' : '❌ No') . "</p>";
echo "<p><strong>Dashboard file size:</strong> " . (file_exists($dashboard_file) ? filesize($dashboard_file) . ' bytes' : 'N/A') . "</p>";
echo "<p><strong>Dashboard file readable:</strong> " . (is_readable($dashboard_file) ? '✅ Yes' : '❌ No') . "</p>";

// Try to include the dashboard file to check for syntax errors
echo "<h2>🔍 Syntax Check:</h2>";
try {
    $syntax_check = php_check_syntax($dashboard_file, $error_message);
    echo "<p><strong>Syntax check:</strong> " . ($syntax_check ? '✅ Valid' : '❌ Invalid') . "</p>";
    if (!$syntax_check && isset($error_message)) {
        echo "<p><strong>Error:</strong> $error_message</p>";
    }
} catch (Exception $e) {
    echo "<p><strong>Syntax check error:</strong> " . $e->getMessage() . "</p>";
}
?>
