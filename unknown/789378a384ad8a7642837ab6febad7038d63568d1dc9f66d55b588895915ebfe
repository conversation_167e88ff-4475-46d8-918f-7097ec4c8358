<?php
/**
 * Google Authentication Configuration
 *
 * This file contains the configuration and functions for Google OAuth authentication.
 */

// Load the Google API PHP Client Library
// Note: You need to install this library using Composer
// composer require google/apiclient:^2.0

// Include configuration files
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/google_oauth.php';

/**
 * Get Google OAuth Client
 *
 * @return object|null A simple object with Google OAuth configuration
 */
function getGoogleClient() {
    // Create a simple configuration object instead of using the Google API Client
    $client = new stdClass();
    $client->client_id = GOOGLE_CLIENT_ID;
    $client->client_secret = GOOGLE_CLIENT_SECRET;
    $client->redirect_uri = GOOGLE_REDIRECT_URI;
    $client->scopes = GOOGLE_SCOPES;

    return $client;
}

/**
 * Get Google Login URL
 *
 * @param string $redirect_after Optional URL to redirect to after authentication
 * @param bool $direct Whether to use direct authentication (skip processing page)
 * @return string The Google login URL or setup URL if not configured
 */
function getGoogleLoginUrl($redirect_after = '', $direct = false) {
    // Check if Google OAuth is configured
    if (!isGoogleOAuthConfigured()) {
        // If not configured, return the setup URL
        return url('google_oauth_setup.php');
    }

    try {
        // Generate a random state parameter for CSRF protection
        $state = bin2hex(random_bytes(16));

        // Store state in session for verification
        $_SESSION['google_auth_state'] = $state;

        // Store redirect URL if provided
        if (!empty($redirect_after)) {
            $_SESSION['google_auth_redirect'] = $redirect_after;
        }

        // Build the Google OAuth URL manually
        $scopes = implode(' ', GOOGLE_SCOPES);
        $auth_url = GOOGLE_AUTH_URL . '?' . http_build_query([
            'client_id' => GOOGLE_CLIENT_ID,
            'redirect_uri' => GOOGLE_REDIRECT_URI,
            'response_type' => 'code',
            'scope' => $scopes,
            'state' => $state,
            'access_type' => 'online',
            'prompt' => 'select_account'
        ]);

        // Add direct parameter if requested
        if ($direct) {
            $auth_url .= (strpos($auth_url, '?') !== false ? '&' : '?') . 'direct=1';
        }

        return $auth_url;
    } catch (Exception $e) {
        error_log('Google Auth Error: ' . $e->getMessage());
        return url('google_oauth_setup.php');
    }
}

/**
 * Get Google User Profile from Auth Code
 *
 * @param string $auth_code The authorization code from Google
 * @return array|false User profile data or false on failure
 */
function getGoogleUserProfile($auth_code) {
    try {
        // Exchange authorization code for access token
        $token_data = [
            'code' => $auth_code,
            'client_id' => GOOGLE_CLIENT_ID,
            'client_secret' => GOOGLE_CLIENT_SECRET,
            'redirect_uri' => GOOGLE_REDIRECT_URI,
            'grant_type' => 'authorization_code'
        ];

        // Initialize cURL session
        $ch = curl_init(GOOGLE_TOKEN_URL);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($token_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);

        // Execute cURL request
        $token_response = curl_exec($ch);
        $token_info = json_decode($token_response, true);
        curl_close($ch);

        if (!isset($token_info['access_token'])) {
            error_log('Google Token Error: ' . ($token_info['error'] ?? 'Unknown error'));
            return false;
        }

        // Get user profile with access token
        $ch = curl_init(GOOGLE_USER_INFO_URL);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Authorization: Bearer ' . $token_info['access_token']]);

        // Execute cURL request
        $userinfo_response = curl_exec($ch);
        $userinfo = json_decode($userinfo_response, true);
        curl_close($ch);

        if (!isset($userinfo['sub'])) {
            error_log('Google User Info Error: Unable to get user profile');
            return false;
        }

        // Create user data array
        $user_data = [
            'google_id' => $userinfo['sub'],
            'email' => $userinfo['email'] ?? '',
            'full_name' => $userinfo['name'] ?? '',
            'first_name' => $userinfo['given_name'] ?? '',
            'last_name' => $userinfo['family_name'] ?? '',
            'picture' => $userinfo['picture'] ?? '',
            'token' => $token_response
        ];

        return $user_data;
    } catch (Exception $e) {
        error_log('Google Auth Error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Find user by Google ID
 *
 * @param PDO $db Database connection
 * @param string $google_id Google ID
 * @return array|false User data or false if not found
 */
function findUserByGoogleId($db, $google_id) {
    // Check in users table (admin/librarian)
    $query = "SELECT * FROM users WHERE google_id = :google_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':google_id', $google_id);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $user = $stmt->fetch();
        return [
            'type' => 'staff',
            'data' => $user
        ];
    }

    // Check in members table
    $query = "SELECT * FROM members WHERE google_id = :google_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':google_id', $google_id);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $member = $stmt->fetch();
        return [
            'type' => 'member',
            'data' => $member
        ];
    }

    return false;
}

/**
 * Find user by email
 *
 * @param PDO $db Database connection
 * @param string $email Email address
 * @return array|false User data or false if not found
 */
function findUserByEmail($db, $email) {
    // Check in users table (admin/librarian)
    $query = "SELECT * FROM users WHERE email = :email";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':email', $email);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $user = $stmt->fetch();
        return [
            'type' => 'staff',
            'data' => $user
        ];
    }

    // Check in members table
    $query = "SELECT * FROM members WHERE email = :email AND membership_status = 'active'";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':email', $email);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $member = $stmt->fetch();
        return [
            'type' => 'member',
            'data' => $member
        ];
    }

    return false;
}
