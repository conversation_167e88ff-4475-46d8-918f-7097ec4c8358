<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../login.php');
}

// Check if loan ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['message'] = 'Invalid loan ID';
    $_SESSION['message_type'] = 'danger';
    redirect('overdue.php');
}

$loan_id = $_GET['id'];

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get loan details
$query = "SELECT bl.*, b.title as book_title, m.first_name, m.last_name, m.email
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          JOIN members m ON bl.member_id = m.id
          WHERE bl.id = :id";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $loan_id);
$stmt->execute();

if ($stmt->rowCount() == 0) {
    $_SESSION['message'] = 'Loan not found';
    $_SESSION['message_type'] = 'danger';
    redirect('overdue.php');
}

$loan = $stmt->fetch();

// Calculate days overdue and fine
function calculateOverdue($due_date) {
    $due = new DateTime($due_date);
    $today = new DateTime();
    $interval = $today->diff($due);
    return $interval->days;
}

function calculateReminderFine($days_overdue) {
    $fine_per_day = FINE_PER_DAY;
    return $days_overdue * $fine_per_day;
}

$days_overdue = calculateOverdue($loan['due_date']);
$fine = calculateReminderFine($days_overdue);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // In a real application, this would send an email
    // For now, we'll just simulate it

    // Log the reminder in the reminder_logs table
    $query = "INSERT INTO reminder_logs (loan_id, reminder_type, days_before, sent_at) VALUES (:loan_id, 'overdue', :days_overdue, NOW())";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':loan_id', $loan_id);
    $stmt->bindParam(':days_overdue', $days_overdue);

    if ($stmt->execute()) {
        $_SESSION['message'] = 'Reminder sent successfully to ' . $loan['first_name'] . ' ' . $loan['last_name'];
        $_SESSION['message_type'] = 'success';
    } else {
        $_SESSION['message'] = 'Failed to send reminder';
        $_SESSION['message_type'] = 'danger';
    }

    redirect('overdue.php');
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Send Reminder - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Send Overdue Reminder</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button id="darkModeToggle" class="btn btn-outline-secondary me-2" title="Toggle Dark Mode">
                            <i id="darkModeIcon" class="bi bi-moon"></i>
                        </button>
                        <a href="overdue.php" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i> Back to Overdue Books
                        </a>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-envelope me-2"></i> Send Reminder Email</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>Book Details</h5>
                                <p><strong>Title:</strong> <?php echo h($loan['book_title']); ?></p>
                                <p><strong>Due Date:</strong> <?php echo formatDate($loan['due_date']); ?></p>
                                <p><strong>Days Overdue:</strong> <span class="badge bg-danger"><?php echo $days_overdue; ?> days</span></p>
                                <p><strong>Fine Amount:</strong> $<?php echo number_format($fine, 2); ?></p>
                            </div>
                            <div class="col-md-6">
                                <h5>Member Details</h5>
                                <p><strong>Name:</strong> <?php echo h($loan['first_name'] . ' ' . $loan['last_name']); ?></p>
                                <p><strong>Email:</strong> <?php echo h($loan['email']); ?></p>
                            </div>
                        </div>

                        <form method="post" action="">
                            <div class="mb-3">
                                <label for="subject" class="form-label">Email Subject</label>
                                <input type="text" class="form-control" id="subject" name="subject" value="REMINDER: Overdue Book - <?php echo h($loan['book_title']); ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="message" class="form-label">Email Message</label>
                                <textarea class="form-control" id="message" name="message" rows="10" required>Dear <?php echo h($loan['first_name'] . ' ' . $loan['last_name']); ?>,

This is a reminder that the following book is overdue:

Book: <?php echo h($loan['book_title']); ?>
Due Date: <?php echo formatDate($loan['due_date']); ?>
Days Overdue: <?php echo $days_overdue; ?> days
Current Fine: $<?php echo number_format($fine, 2); ?>

Please return the book as soon as possible to avoid additional fines.

Thank you,
Library Management System</textarea>
                            </div>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="overdue.php" class="btn btn-secondary me-md-2">Cancel</a>
                                <button type="submit" class="btn btn-primary">Send Reminder</button>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo url('../assets/js/dark-mode.js'); ?>"></script>
</body>
</html>
