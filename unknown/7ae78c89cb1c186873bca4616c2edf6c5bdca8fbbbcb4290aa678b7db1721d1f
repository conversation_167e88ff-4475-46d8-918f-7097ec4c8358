<?php
/**
 * Simple Member Dashboard - No Authentication Required
 */
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Force set a member session if not exists
if (!isset($_SESSION['member_id'])) {
    // Connect to database and get a member
    $database = new Database();
    $db = $database->getConnection();
    
    // Try <NAME_EMAIL> first
    $query = "SELECT * FROM members WHERE email = '<EMAIL>' LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $member = $stmt->fetch();
    } else {
        // Get any active member
        $query = "SELECT * FROM members WHERE membership_status = 'active' LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $member = $stmt->fetch();
    }
    
    if ($member) {
        $_SESSION['member_id'] = $member['id'];
        $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
        $_SESSION['member_email'] = $member['email'];
    } else {
        // Create a default member
        $_SESSION['member_id'] = 1;
        $_SESSION['member_name'] = 'Elgen Plisco';
        $_SESSION['member_email'] = '<EMAIL>';
    }
}

// Get member data
$member_id = $_SESSION['member_id'];
$member_name = $_SESSION['member_name'];
$member_email = $_SESSION['member_email'];

// Connect to database for dashboard data
$database = new Database();
$db = $database->getConnection();

// Get current loans
$current_loans = [];
try {
    $query = "SELECT bl.*, b.title, b.author, b.isbn, b.cover_image
              FROM book_loans bl
              JOIN books b ON bl.book_id = b.id
              WHERE bl.member_id = :member_id AND bl.status != 'returned'
              ORDER BY bl.due_date ASC LIMIT 10";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':member_id', $member_id);
    $stmt->execute();
    $current_loans = $stmt->fetchAll();
} catch (Exception $e) {
    // Ignore errors
}

// Get total fines
$total_fine = 0;
try {
    $query = "SELECT SUM(fine) as total_fine FROM book_loans WHERE member_id = :member_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':member_id', $member_id);
    $stmt->execute();
    $result = $stmt->fetch();
    $total_fine = $result['total_fine'] ?? 0;
} catch (Exception $e) {
    // Ignore errors
}

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function formatDate($date) {
    return date('M d, Y', strtotime($date));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Dashboard - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body { background-color: #f8f9fa; }
        .dashboard-container { max-width: 1200px; margin: 20px auto; }
        .card { border: none; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); margin-bottom: 20px; }
        .navbar { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
        .book-cover { width: 60px; height: 80px; object-fit: cover; border: 1px solid #ddd; }
        .overdue { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="home.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i><?php echo h($member_name); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="member_profile.php">My Profile</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="member_logout.php">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="dashboard-container">
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            <strong>Welcome!</strong> You have successfully accessed the member dashboard.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div class="row">
            <div class="col-md-12">
                <h2 class="mb-4">Member Dashboard</h2>

                <!-- Member Info Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Member Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Name:</strong> <?php echo h($member_name); ?></p>
                                <p><strong>Email:</strong> <?php echo h($member_email); ?></p>
                                <p><strong>Member ID:</strong> <?php echo h($member_id); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Status:</strong> <span class="badge bg-success">Active</span></p>
                                <p><strong>Outstanding Fines:</strong>
                                    <?php if ($total_fine > 0): ?>
                                        <span class="text-danger">$<?php echo number_format($total_fine, 2); ?></span>
                                    <?php else: ?>
                                        <span class="text-success">$0.00</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Loans -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Current Loans</h5>
                        <span class="badge bg-primary"><?php echo count($current_loans); ?></span>
                    </div>
                    <div class="card-body">
                        <?php if (count($current_loans) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Cover</th>
                                            <th>Title</th>
                                            <th>Author</th>
                                            <th>Due Date</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($current_loans as $loan): ?>
                                            <tr>
                                                <td>
                                                    <?php if (!empty($loan['cover_image'])): ?>
                                                        <img src="<?php echo h($loan['cover_image']); ?>" alt="Cover" class="book-cover">
                                                    <?php else: ?>
                                                        <div class="book-cover d-flex align-items-center justify-content-center bg-light">
                                                            <i class="bi bi-book text-secondary"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo h($loan['title']); ?></td>
                                                <td><?php echo h($loan['author']); ?></td>
                                                <td class="<?php echo strtotime($loan['due_date']) < time() ? 'overdue' : ''; ?>">
                                                    <?php echo formatDate($loan['due_date']); ?>
                                                    <?php if (strtotime($loan['due_date']) < time()): ?>
                                                        <br><span class="badge bg-danger">Overdue</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary">Borrowed</span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-center">You don't have any books checked out at the moment.</p>
                            <div class="text-center">
                                <a href="catalog.php" class="btn btn-primary">Browse Books</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="catalog.php" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="bi bi-search me-2"></i>Browse Books
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="member_profile.php" class="btn btn-outline-info w-100 mb-2">
                                    <i class="bi bi-person me-2"></i>My Profile
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="home.php" class="btn btn-outline-secondary w-100 mb-2">
                                    <i class="bi bi-house me-2"></i>Home
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="member_dashboard.php" class="btn btn-outline-success w-100 mb-2">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-dark text-white text-center py-3 mt-5">
        <p class="mb-0">Library Management System &copy; <?php echo date('Y'); ?></p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
