<?php
/**
 * Library Management System Database Setup Script
 */

// Database connection parameters
$host = 'localhost';
$username = 'root';
$password = '';

try {
    // Connect to MySQL server (without selecting a database)
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Library Management System - Database Setup</h2>";
    
    // Create database if not exists
    $pdo->exec("CREATE DATABASE IF NOT EXISTS lms_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✅ Database 'lms_db' created successfully.</p>";
    
    // Select the database
    $pdo->exec("USE lms_db");
    
    // Drop existing tables if they exist
    $pdo->exec("DROP TABLE IF EXISTS book_loans");
    $pdo->exec("DROP TABLE IF EXISTS book_reservations");
    $pdo->exec("DROP TABLE IF EXISTS books");
    $pdo->exec("DROP TABLE IF EXISTS members");
    $pdo->exec("DROP TABLE IF EXISTS users");
    $pdo->exec("DROP TABLE IF EXISTS email_logs");
    echo "<p>✅ Existing tables dropped successfully.</p>";
    
    // Create users table
    $pdo->exec("CREATE TABLE users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100) NOT NULL UNIQUE,
        role ENUM('admin', 'librarian') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    echo "<p>✅ Table 'users' created successfully.</p>";
    
    // Create members table
    $pdo->exec("CREATE TABLE members (
        id INT AUTO_INCREMENT PRIMARY KEY,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        email VARCHAR(100) NOT NULL UNIQUE,
        phone VARCHAR(20),
        address TEXT,
        membership_date DATE NOT NULL,
        membership_status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    echo "<p>✅ Table 'members' created successfully.</p>";
    
    // Create books table
    $pdo->exec("CREATE TABLE books (
        id INT AUTO_INCREMENT PRIMARY KEY,
        isbn VARCHAR(20) UNIQUE,
        title VARCHAR(255) NOT NULL,
        author VARCHAR(100) NOT NULL,
        category VARCHAR(50),
        publication_year INT,
        publisher VARCHAR(100),
        quantity INT NOT NULL DEFAULT 1,
        available_quantity INT NOT NULL DEFAULT 1,
        shelf_location VARCHAR(50),
        description TEXT,
        cover_image VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    echo "<p>✅ Table 'books' created successfully.</p>";
    
    // Create book_loans table
    $pdo->exec("CREATE TABLE book_loans (
        id INT AUTO_INCREMENT PRIMARY KEY,
        book_id INT NOT NULL,
        member_id INT NOT NULL,
        issue_date DATE NOT NULL,
        due_date DATE NOT NULL,
        return_date DATE,
        fine DECIMAL(10, 2) DEFAULT 0.00,
        status ENUM('borrowed', 'returned', 'overdue') DEFAULT 'borrowed',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
    )");
    echo "<p>✅ Table 'book_loans' created successfully.</p>";
    
    // Create book_reservations table
    $pdo->exec("CREATE TABLE book_reservations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        book_id INT NOT NULL,
        member_id INT NOT NULL,
        reservation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expiry_date DATE NOT NULL,
        status ENUM('pending', 'ready', 'completed', 'cancelled') DEFAULT 'pending',
        notification_sent BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
        UNIQUE KEY (book_id, member_id, status)
    )");
    echo "<p>✅ Table 'book_reservations' created successfully.</p>";
    
    // Create email_logs table
    $pdo->exec("CREATE TABLE email_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        member_id INT NOT NULL,
        email_type VARCHAR(50) NOT NULL,
        related_id INT,
        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('success', 'failed') DEFAULT 'success',
        error_message TEXT,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
    )");
    echo "<p>✅ Table 'email_logs' created successfully.</p>";
    
    // Insert default admin user (password: admin123)
    $pdo->exec("INSERT INTO users (username, password, email, role) VALUES 
    ('admin', '\$2y\$10\$8WxmVVVJGKZ6NvnUt2qgJeP.ZWdj8WuLLaY6tXRRzXRZYXtB5EKlq', '<EMAIL>', 'admin'),
    ('librarian', '\$2y\$10\$8WxmVVVJGKZ6NvnUt2qgJeP.ZWdj8WuLLaY6tXRRzXRZYXtB5EKlq', '<EMAIL>', 'librarian')");
    echo "<p>✅ Default users created successfully.</p>";
    
    // Insert sample members
    $pdo->exec("INSERT INTO members (first_name, last_name, email, phone, address, membership_date) VALUES
    ('John', 'Doe', '<EMAIL>', '************', '123 Main St, City', '2023-01-15'),
    ('Jane', 'Smith', '<EMAIL>', '************', '456 Oak Ave, Town', '2023-02-20'),
    ('Michael', 'Johnson', '<EMAIL>', '************', '789 Pine Rd, Village', '2023-03-10'),
    ('Emily', 'Williams', '<EMAIL>', '************', '101 Elm St, Borough', '2023-04-05'),
    ('David', 'Brown', '<EMAIL>', '************', '202 Maple Dr, District', '2023-05-12')");
    echo "<p>✅ Sample members created successfully.</p>";
    
    // Insert sample books
    $pdo->exec("INSERT INTO books (isbn, title, author, category, publication_year, publisher, quantity, available_quantity, shelf_location, description) VALUES
    ('9780132350884', 'Clean Code', 'Robert C. Martin', 'Programming', 2008, 'Prentice Hall', 3, 3, 'A1', 'A handbook of agile software craftsmanship'),
    ('9780262033848', 'Introduction to Algorithms', 'Thomas H. Cormen', 'Computer Science', 2009, 'MIT Press', 2, 2, 'B2', 'A comprehensive introduction to algorithms'),
    ('9780134685991', 'Effective Java', 'Joshua Bloch', 'Programming', 2018, 'Addison-Wesley', 4, 4, 'A3', 'Best practices for the Java platform'),
    ('9780596007126', 'Head First Design Patterns', 'Eric Freeman', 'Programming', 2004, 'O\\'Reilly Media', 2, 2, 'C1', 'A brain-friendly guide to design patterns'),
    ('9781449331818', 'Learning JavaScript Design Patterns', 'Addy Osmani', 'Web Development', 2012, 'O\\'Reilly Media', 3, 3, 'D2', 'A guide to building better applications with design patterns'),
    ('9780451524935', '1984', 'George Orwell', 'Fiction', 1949, 'Signet Classics', 5, 5, 'F1', 'A dystopian novel set in a totalitarian society'),
    ('9780061120084', 'To Kill a Mockingbird', 'Harper Lee', 'Fiction', 1960, 'HarperCollins', 4, 4, 'F2', 'A novel about racial inequality in the American South'),
    ('9780307474278', 'The Da Vinci Code', 'Dan Brown', 'Mystery', 2003, 'Anchor', 3, 3, 'M1', 'A mystery thriller novel'),
    ('9780553296983', 'The Diary of a Young Girl', 'Anne Frank', 'Biography', 1947, 'Bantam', 2, 2, 'B1', 'The writings from the Dutch-language diary kept by Anne Frank'),
    ('9780743273565', 'The Great Gatsby', 'F. Scott Fitzgerald', 'Fiction', 1925, 'Scribner', 3, 3, 'F3', 'A novel about the American Dream')");
    echo "<p>✅ Sample books created successfully.</p>";
    
    // Insert sample book loans
    $pdo->exec("INSERT INTO book_loans (book_id, member_id, issue_date, due_date, return_date, status) VALUES
    (1, 1, '2023-06-01', '2023-06-15', '2023-06-14', 'returned'),
    (2, 2, '2023-06-05', '2023-06-19', NULL, 'borrowed'),
    (3, 3, '2023-06-10', '2023-06-24', NULL, 'borrowed'),
    (4, 1, '2023-06-15', '2023-06-29', NULL, 'borrowed'),
    (5, 2, '2023-05-20', '2023-06-03', NULL, 'overdue')");
    echo "<p>✅ Sample book loans created successfully.</p>";
    
    // Insert sample book reservations
    $pdo->exec("INSERT INTO book_reservations (book_id, member_id, expiry_date, status) VALUES
    (1, 3, DATE_ADD(CURRENT_DATE, INTERVAL 3 DAY), 'pending'),
    (5, 1, DATE_ADD(CURRENT_DATE, INTERVAL 3 DAY), 'pending')");
    echo "<p>✅ Sample book reservations created successfully.</p>";
    
    echo "<h3>Database setup completed successfully! 🎉</h3>";
    echo "<p>You can now access your Library Management System.</p>";
    echo "<p><strong>Default login credentials:</strong></p>";
    echo "<ul>";
    echo "<li>Admin: username = 'admin', password = 'admin123'</li>";
    echo "<li>Librarian: username = 'librarian', password = 'admin123'</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<h2>Error!</h2>";
    echo "<p>An error occurred during database setup:</p>";
    echo "<pre>" . $e->getMessage() . "</pre>";
}
?>
