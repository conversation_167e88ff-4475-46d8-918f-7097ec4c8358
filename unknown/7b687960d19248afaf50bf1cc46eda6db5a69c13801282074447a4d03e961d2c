<?php
/**
 * UI/UX Enhancements Script
 * Improves the user interface and user experience
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

$enhancements_applied = [];
$errors = [];

// Function to create enhanced UI CSS
function createEnhancedUICSS() {
    $css_content = '
/* Enhanced UI/UX Styles */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Modern card design */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-bottom: none;
    padding: 1.25rem;
}

/* Enhanced buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #1e7e34);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #c82333);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #e0a800);
    border: none;
    color: #212529;
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #138496);
    border: none;
}

/* Enhanced forms */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Enhanced tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: scale(1.01);
}

/* Enhanced navigation */
.navbar {
    box-shadow: var(--box-shadow);
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    transition: var(--transition);
    position: relative;
}

.nav-link::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
    transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Enhanced sidebar */
.sidebar {
    background: linear-gradient(180deg, #f8f9fa, #e9ecef);
    box-shadow: var(--box-shadow);
}

.sidebar .nav-link {
    border-radius: var(--border-radius);
    margin: 0.25rem 0.5rem;
    transition: var(--transition);
}

.sidebar .nav-link:hover {
    background: rgba(0, 123, 255, 0.1);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background: var(--primary-color);
    color: white;
}

/* Enhanced stats cards */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border: none;
    border-radius: var(--border-radius);
    color: white;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: "";
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
}

.stats-card:hover {
    transform: translateY(-5px) scale(1.02);
}

/* Enhanced alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    border-left: 4px solid;
    box-shadow: var(--box-shadow);
}

.alert-success {
    border-left-color: var(--success-color);
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
}

.alert-danger {
    border-left-color: var(--danger-color);
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
}

.alert-warning {
    border-left-color: var(--warning-color);
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
}

.alert-info {
    border-left-color: var(--info-color);
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
}

/* Enhanced modals */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-bottom: none;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    background: var(--light-color);
}

/* Enhanced badges */
.badge {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.5rem 0.75rem;
}

/* Loading animations */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Pulse animation for notifications */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Enhanced search */
.search-container {
    position: relative;
}

.search-container .form-control {
    padding-left: 3rem;
}

.search-container .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
}

/* Enhanced progress bars */
.progress {
    border-radius: var(--border-radius);
    height: 1rem;
    background: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), #0056b3);
    transition: width 0.6s ease;
}

/* Enhanced tooltips */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background: var(--dark-color);
    border-radius: var(--border-radius);
    padding: 0.5rem 0.75rem;
}

/* Enhanced dropdowns */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 0.5rem 0;
}

.dropdown-item {
    transition: var(--transition);
    padding: 0.5rem 1rem;
}

.dropdown-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

/* Enhanced pagination */
.pagination .page-link {
    border: none;
    border-radius: var(--border-radius);
    margin: 0 0.25rem;
    transition: var(--transition);
}

.pagination .page-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Responsive enhancements */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
}

/* Dark theme */
[data-theme="dark"] {
    --light-color: #343a40;
    --dark-color: #f8f9fa;
}

[data-theme="dark"] body {
    background-color: #1a1a1a;
    color: #f8f9fa;
}

[data-theme="dark"] .card {
    background-color: #2d3748;
    color: #f8f9fa;
}

[data-theme="dark"] .table {
    background-color: #2d3748;
    color: #f8f9fa;
}

/* Print styles */
@media print {
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .btn {
        display: none;
    }
    
    .sidebar {
        display: none;
    }
    
    .navbar {
        display: none;
    }
}
';

    $css_file = 'assets/css/ui-enhancements.css';
    
    // Create directory if it doesn't exist
    if (!is_dir('assets/css')) {
        mkdir('assets/css', 0755, true);
    }
    
    return file_put_contents($css_file, $css_content) !== false;
}

// Function to create enhanced UI JavaScript
function createEnhancedUIJS() {
    $js_content = '
// Enhanced UI/UX JavaScript

document.addEventListener("DOMContentLoaded", function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll("[data-bs-toggle=\"tooltip\"]"));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll("[data-bs-toggle=\"popover\"]"));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Dark theme toggle
    const themeToggle = document.getElementById("themeToggle");
    if (themeToggle) {
        themeToggle.addEventListener("click", function() {
            const currentTheme = document.documentElement.getAttribute("data-theme");
            const newTheme = currentTheme === "dark" ? "light" : "dark";
            
            document.documentElement.setAttribute("data-theme", newTheme);
            localStorage.setItem("theme", newTheme);
            
            // Update icon
            const icon = themeToggle.querySelector("i");
            if (newTheme === "dark") {
                icon.className = "bi bi-sun";
            } else {
                icon.className = "bi bi-moon";
            }
        });
        
        // Load saved theme
        const savedTheme = localStorage.getItem("theme") || "light";
        document.documentElement.setAttribute("data-theme", savedTheme);
        
        const icon = themeToggle.querySelector("i");
        if (savedTheme === "dark") {
            icon.className = "bi bi-sun";
        } else {
            icon.className = "bi bi-moon";
        }
    }
    
    // Enhanced search functionality
    const searchInputs = document.querySelectorAll("input[type=\"search\"], .search-input");
    searchInputs.forEach(input => {
        const container = input.closest(".search-container") || input.parentElement;
        
        // Add search icon if not present
        if (!container.querySelector(".search-icon")) {
            const icon = document.createElement("i");
            icon.className = "bi bi-search search-icon";
            container.style.position = "relative";
            container.appendChild(icon);
        }
        
        // Add clear button
        const clearBtn = document.createElement("button");
        clearBtn.type = "button";
        clearBtn.className = "btn btn-sm btn-outline-secondary position-absolute";
        clearBtn.style.right = "0.5rem";
        clearBtn.style.top = "50%";
        clearBtn.style.transform = "translateY(-50%)";
        clearBtn.style.display = "none";
        clearBtn.innerHTML = "<i class=\"bi bi-x\"></i>";
        
        clearBtn.addEventListener("click", function() {
            input.value = "";
            clearBtn.style.display = "none";
            input.focus();
        });
        
        container.appendChild(clearBtn);
        
        input.addEventListener("input", function() {
            clearBtn.style.display = this.value ? "block" : "none";
        });
    });
    
    // Auto-hide alerts
    const alerts = document.querySelectorAll(".alert:not(.alert-permanent)");
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = "0";
            alert.style.transform = "translateY(-20px)";
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
    
    // Enhanced form validation
    const forms = document.querySelectorAll("form");
    forms.forEach(form => {
        form.addEventListener("submit", function(e) {
            const requiredFields = form.querySelectorAll("[required]");
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add("is-invalid");
                    isValid = false;
                } else {
                    field.classList.remove("is-invalid");
                    field.classList.add("is-valid");
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                // Focus first invalid field
                const firstInvalid = form.querySelector(".is-invalid");
                if (firstInvalid) {
                    firstInvalid.focus();
                }
            }
        });
    });
    
    // Loading states for buttons
    const submitButtons = document.querySelectorAll("button[type=\"submit\"], .btn-submit");
    submitButtons.forEach(button => {
        button.addEventListener("click", function() {
            if (this.form && this.form.checkValidity()) {
                this.disabled = true;
                this.innerHTML = "<span class=\"loading-spinner me-2\"></span>Loading...";
            }
        });
    });
    
    // Smooth scrolling for anchor links
    const anchorLinks = document.querySelectorAll("a[href^=\"#\"]");
    anchorLinks.forEach(link => {
        link.addEventListener("click", function(e) {
            const target = document.querySelector(this.getAttribute("href"));
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: "smooth",
                    block: "start"
                });
            }
        });
    });
    
    // Auto-refresh for dashboard
    if (window.location.pathname.includes("dashboard")) {
        setInterval(() => {
            // Refresh stats without page reload
            const statsCards = document.querySelectorAll(".stats-card [data-stat]");
            statsCards.forEach(card => {
                // Add subtle pulse animation to indicate refresh
                card.classList.add("pulse");
                setTimeout(() => {
                    card.classList.remove("pulse");
                }, 2000);
            });
        }, 30000); // Refresh every 30 seconds
    }
});
';

    $js_file = 'assets/js/ui-enhancements.js';
    
    // Create directory if it doesn't exist
    if (!is_dir('assets/js')) {
        mkdir('assets/js', 0755, true);
    }
    
    return file_put_contents($js_file, $js_content) !== false;
}

// Apply enhancements
try {
    if (createEnhancedUICSS()) {
        $enhancements_applied[] = "Created enhanced UI CSS with modern design elements";
    } else {
        $errors[] = "Failed to create enhanced UI CSS file";
    }
    
    if (createEnhancedUIJS()) {
        $enhancements_applied[] = "Created enhanced UI JavaScript with interactive features";
    } else {
        $errors[] = "Failed to create enhanced UI JavaScript file";
    }
    
} catch (Exception $e) {
    $errors[] = "Error applying UI enhancements: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI/UX Enhancements - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="assets/css/ui-enhancements.css">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header">
                        <h1 class="h4 mb-0"><i class="bi bi-palette me-2"></i>UI/UX Enhancements Applied</h1>
                        <button id="themeToggle" class="btn btn-outline-light btn-sm position-absolute" style="top: 1rem; right: 1rem;">
                            <i class="bi bi-moon"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($enhancements_applied)): ?>
                            <div class="alert alert-success">
                                <h2 class="h5"><i class="bi bi-check-circle me-2"></i>Enhancements Applied Successfully!</h2>
                                <ul class="mb-0">
                                    <?php foreach ($enhancements_applied as $enhancement): ?>
                                        <li><?php echo htmlspecialchars($enhancement); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <h2 class="h5"><i class="bi bi-exclamation-triangle me-2"></i>Errors Encountered</h2>
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <div class="mt-4">
                            <h2 class="h5">UI/UX Features Added:</h2>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Modern gradient designs</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Enhanced animations</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Improved hover effects</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Better form styling</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Enhanced navigation</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Dark theme support</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Loading animations</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Auto-hide alerts</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Enhanced search</li>
                                        <li class="list-group-item"><i class="bi bi-check text-success me-2"></i>Smooth scrolling</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h2 class="h5">Next Steps:</h2>
                            <div class="d-grid gap-2 d-md-flex">
                                <a href="security_hardening.php" class="btn btn-warning">
                                    <i class="bi bi-shield-check me-2"></i>Security Hardening
                                </a>
                                <a href="performance_optimization.php" class="btn btn-info">
                                    <i class="bi bi-speedometer2 me-2"></i>Performance Optimization
                                </a>
                                <a href="system_health_check.php" class="btn btn-success">
                                    <i class="bi bi-heart-pulse me-2"></i>Health Check
                                </a>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="bi bi-house me-2"></i>Back to Home
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/ui-enhancements.js"></script>
</body>
</html>
