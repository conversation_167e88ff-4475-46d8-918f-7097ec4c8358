<?php
/**
 * Direct Dashboard Access - Force Login and Access
 */
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Force clear any existing sessions
$_SESSION = array();

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get or create a member
$member = null;

// First, try <NAME_EMAIL>
$query = "SELECT * FROM members WHERE email = '<EMAIL>' LIMIT 1";
$stmt = $db->prepare($query);
$stmt->execute();

if ($stmt->rowCount() > 0) {
    $member = $stmt->fetch();
} else {
    // Try to find any active member
    $query = "SELECT * FROM members WHERE membership_status = 'active' LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $member = $stmt->fetch();
    } else {
        // Create a new member
        $query = "INSERT INTO members (first_name, last_name, email, phone, address, membership_date, membership_status) 
                  VALUES ('Elgen', 'Plisco', '<EMAIL>', '09123456789', 'CSUCC', :date, 'active')";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':date', date('Y-m-d'));
        
        if ($stmt->execute()) {
            $member_id = $db->lastInsertId();
            
            // Get the newly created member
            $query = "SELECT * FROM members WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $member_id);
            $stmt->execute();
            $member = $stmt->fetch();
        }
    }
}

if ($member) {
    // Force set session variables
    $_SESSION['member_id'] = $member['id'];
    $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
    $_SESSION['member_email'] = $member['email'];
    $_SESSION['direct_login'] = true;
    
    // Verify session is set
    if (isset($_SESSION['member_id'])) {
        echo "<!DOCTYPE html>
<html>
<head>
    <title>Redirecting...</title>
    <meta http-equiv='refresh' content='2;url=member_dashboard.php'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body class='bg-light'>
    <div class='container mt-5'>
        <div class='row justify-content-center'>
            <div class='col-md-6'>
                <div class='card'>
                    <div class='card-body text-center'>
                        <div class='spinner-border text-primary mb-3' role='status'>
                            <span class='visually-hidden'>Loading...</span>
                        </div>
                        <h5>Login Successful!</h5>
                        <p>Redirecting to member dashboard...</p>
                        <p><strong>Member:</strong> " . htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) . "</p>
                        <p><strong>Email:</strong> " . htmlspecialchars($member['email']) . "</p>
                        <a href='member_dashboard.php' class='btn btn-primary'>Go to Dashboard Now</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
        exit;
    }
}

// If we get here, something went wrong
echo "<!DOCTYPE html>
<html>
<head>
    <title>Access Failed</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body class='bg-light'>
    <div class='container mt-5'>
        <div class='row justify-content-center'>
            <div class='col-md-8'>
                <div class='card'>
                    <div class='card-header bg-danger text-white'>
                        <h5 class='mb-0'>Access Failed</h5>
                    </div>
                    <div class='card-body'>
                        <p>Unable to create or access member account. Let's try some alternatives:</p>
                        
                        <div class='d-grid gap-2'>
                            <a href='member_dashboard.php' class='btn btn-primary'>Try Dashboard Direct</a>
                            <a href='member_login.php' class='btn btn-outline-primary'>Member Login Page</a>
                            <a href='home.php' class='btn btn-outline-secondary'>Home Page</a>
                        </div>
                        
                        <hr>
                        <h6>Debug Information:</h6>
                        <p><strong>Session Status:</strong> " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</p>
                        <p><strong>Database Connection:</strong> " . (isset($db) ? 'Connected' : 'Failed') . "</p>
                        <p><strong>Member Found:</strong> " . (isset($member) && $member ? 'Yes' : 'No') . "</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
