<?php
/**
 * Simple database status checker
 */

require_once 'config/database.php';

echo "<h2>🔍 Database Status Check</h2>";
echo "<style>
    .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
</style>";

try {
    echo "<div class='info'>Testing database connection...</div>";

    // Test database connection
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        echo "<div class='error'>❌ Database connection failed</div>";
        exit;
    }

    echo "<div class='success'>✅ Database connection successful</div>";

    // Check tables
    $required_tables = ['members', 'books', 'book_loans'];
    echo "<div class='info'>Checking required tables...</div>";

    foreach ($required_tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            // Get row count
            $count_stmt = $db->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_stmt->fetch()['count'];
            echo "<div class='success'>✅ Table '$table' exists with $count records</div>";
        } else {
            echo "<div class='error'>❌ Table '$table' is missing</div>";
        }
    }

    // Test specific queries that might be failing
    echo "<div class='info'>Testing specific queries...</div>";

    $test_queries = [
        'Total members' => "SELECT COUNT(*) as count FROM members",
        'Active loans' => "SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status = 'borrowed'",
        'Members with fines' => "SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE fine > 0"
    ];

    foreach ($test_queries as $description => $query) {
        try {
            $stmt = $db->query($query);
            $result = $stmt->fetch();
            echo "<div class='success'>✅ $description: " . $result['count'] . "</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ $description failed: " . $e->getMessage() . "</div>";
        }
    }

    // Check for missing columns
    echo "<div class='info'>Checking table structure...</div>";

    $required_columns = [
        'members' => ['id', 'first_name', 'last_name', 'email', 'created_at'],
        'book_loans' => ['id', 'member_id', 'book_id', 'status', 'issue_date', 'due_date', 'fine']
    ];

    foreach ($required_columns as $table => $columns) {
        try {
            $stmt = $db->query("DESCRIBE $table");
            $existing_columns = array_column($stmt->fetchAll(), 'Field');

            foreach ($columns as $column) {
                if (in_array($column, $existing_columns)) {
                    echo "<div class='success'>✅ $table.$column exists</div>";
                } else {
                    echo "<div class='error'>❌ $table.$column is missing</div>";
                }
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Cannot check $table structure: " . $e->getMessage() . "</div>";
        }
    }

    echo "<div class='success'>🎉 Database status check completed!</div>";

} catch (Exception $e) {
    echo "<div class='error'>❌ Critical error: " . $e->getMessage() . "</div>";
    echo "<div class='info'>Possible solutions:</div>";
    echo "<ul>";
    echo "<li>Make sure XAMPP/WAMP is running</li>";
    echo "<li>Check if MySQL service is started</li>";
    echo "<li>Verify database credentials in config/database.php</li>";
    echo "<li>Run setup.php to create the database and tables</li>";
    echo "</ul>";
}
