<?php
/**
 * Delete Member
 */
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and has appropriate role
if (!isset($_SESSION['user_id']) && !isset($_SESSION['member_id'])) {
    redirect(url('login.php'));
}

// Check if user has permission (admin or librarian)
if (isset($_SESSION['role']) && !in_array($_SESSION['role'], ['admin', 'librarian'])) {
    redirect(url('index.php'));
}

// Get member ID from URL
$member_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($member_id <= 0) {
    redirect(url('members/index.php'));
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Get member data
$query = "SELECT * FROM members WHERE id = :id";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $member_id);
$stmt->execute();

if ($stmt->rowCount() === 0) {
    redirect(url('members/index.php'));
}

$member = $stmt->fetch();

// Check if member has active loans
$query = "SELECT COUNT(*) as count FROM book_loans WHERE member_id = :member_id AND return_date IS NULL";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$active_loans = $stmt->fetch()['count'];

// Initialize variables
$errors = [];
$success = false;

// Process deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
    if ($active_loans > 0) {
        $errors[] = 'Cannot delete member with active loans. Please return all books first.';
    } else {
        try {
            // Start transaction
            $db->beginTransaction();
            
            // Delete member's loan history (optional - you might want to keep this for records)
            // $query = "DELETE FROM book_loans WHERE member_id = :member_id";
            // $stmt = $db->prepare($query);
            // $stmt->bindParam(':member_id', $member_id);
            // $stmt->execute();
            
            // Delete the member
            $query = "DELETE FROM members WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $member_id);
            
            if ($stmt->execute()) {
                // Log the activity if function exists
                if (function_exists('logActivity')) {
                    logActivity($db, 'delete', 'Deleted member: ' . $member['first_name'] . ' ' . $member['last_name'], 'member', $member_id);
                }
                
                // Commit transaction
                $db->commit();
                
                // Redirect to members list with success message
                $_SESSION['success_message'] = 'Member deleted successfully';
                redirect(url('members/index.php'));
            } else {
                $db->rollBack();
                $errors[] = 'Failed to delete member. Please try again.';
            }
        } catch (PDOException $e) {
            $db->rollBack();
            $errors[] = 'Database error: ' . $e->getMessage();
        }
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Member - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Delete Member</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="index.php" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="bi bi-arrow-left"></i> Back to Members
                        </a>
                        <a href="view.php?id=<?php echo $member_id; ?>" class="btn btn-sm btn-info">
                            <i class="bi bi-eye"></i> View Details
                        </a>
                    </div>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo h($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-exclamation-triangle me-2"></i>Confirm Member Deletion
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>Warning:</strong> This action cannot be undone. The member's account will be permanently deleted.
                                </div>

                                <h6>Member to be deleted:</h6>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Name:</strong> <?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></p>
                                                <p><strong>Email:</strong> <?php echo h($member['email']); ?></p>
                                                <p><strong>Phone:</strong> <?php echo h($member['phone'] ?: 'Not provided'); ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Member ID:</strong> <?php echo h($member['id']); ?></p>
                                                <p><strong>Membership Date:</strong> <?php echo date('F j, Y', strtotime($member['membership_date'])); ?></p>
                                                <p><strong>Status:</strong> 
                                                    <?php if ($member['membership_status'] === 'active'): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php elseif ($member['membership_status'] === 'inactive'): ?>
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Suspended</span>
                                                    <?php endif; ?>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php if ($active_loans > 0): ?>
                                    <div class="alert alert-danger mt-3">
                                        <i class="bi bi-exclamation-circle me-2"></i>
                                        <strong>Cannot delete this member!</strong> 
                                        This member has <?php echo $active_loans; ?> active loan(s). 
                                        Please ensure all books are returned before deleting the member.
                                    </div>
                                    
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a href="index.php" class="btn btn-secondary">
                                            <i class="bi bi-arrow-left me-2"></i>Back to Members
                                        </a>
                                        <a href="view.php?id=<?php echo $member_id; ?>" class="btn btn-primary">
                                            <i class="bi bi-eye me-2"></i>View Member Details
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <form method="POST" action="" class="mt-3">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="confirm_checkbox" required>
                                            <label class="form-check-label" for="confirm_checkbox">
                                                I understand that this action cannot be undone and want to permanently delete this member.
                                            </label>
                                        </div>

                                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                            <a href="index.php" class="btn btn-secondary me-md-2">
                                                <i class="bi bi-x-lg me-2"></i>Cancel
                                            </a>
                                            <button type="submit" name="confirm_delete" class="btn btn-danger" id="delete_button" disabled>
                                                <i class="bi bi-trash me-2"></i>Delete Member
                                            </button>
                                        </div>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Enable delete button only when checkbox is checked
        document.getElementById('confirm_checkbox').addEventListener('change', function() {
            document.getElementById('delete_button').disabled = !this.checked;
        });
    </script>
</body>
</html>
