<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if member is logged in
if (!isMemberLoggedIn()) {
    redirect('../member_login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get member ID from session
$member_id = $_SESSION['member_id'];

// Initialize variables
$category_recommendations = [];
$author_recommendations = [];
$popular_books = [];
$new_arrivals = [];

// Get member's borrowing history
$query = "SELECT b.category, b.author
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id
          ORDER BY bl.issue_date DESC
          LIMIT 20";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$borrowing_history = $stmt->fetchAll();

// Extract categories and authors from borrowing history
$borrowed_categories = [];
$borrowed_authors = [];

foreach ($borrowing_history as $item) {
    if (!empty($item['category']) && !in_array($item['category'], $borrowed_categories)) {
        $borrowed_categories[] = $item['category'];
    }
    
    if (!empty($item['author']) && !in_array($item['author'], $borrowed_authors)) {
        $borrowed_authors[] = $item['author'];
    }
}

// Limit to top 3 categories and authors
$borrowed_categories = array_slice($borrowed_categories, 0, 3);
$borrowed_authors = array_slice($borrowed_authors, 0, 3);

// Get books already borrowed by the member
$query = "SELECT book_id FROM book_loans WHERE member_id = :member_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$borrowed_books = array_column($stmt->fetchAll(), 'book_id');

// Get recommendations based on categories
if (!empty($borrowed_categories)) {
    $placeholders = implode(',', array_fill(0, count($borrowed_categories), '?'));
    $exclude_placeholders = !empty($borrowed_books) ? implode(',', array_fill(0, count($borrowed_books), '?')) : '';
    
    $query = "SELECT * FROM books 
              WHERE category IN ($placeholders)";
    
    if (!empty($borrowed_books)) {
        $query .= " AND id NOT IN ($exclude_placeholders)";
    }
    
    $query .= " ORDER BY RAND() LIMIT 6";
    
    $stmt = $db->prepare($query);
    
    // Bind category parameters
    $param_index = 1;
    foreach ($borrowed_categories as $category) {
        $stmt->bindValue($param_index++, $category);
    }
    
    // Bind borrowed books parameters
    if (!empty($borrowed_books)) {
        foreach ($borrowed_books as $book_id) {
            $stmt->bindValue($param_index++, $book_id);
        }
    }
    
    $stmt->execute();
    $category_recommendations = $stmt->fetchAll();
}

// Get recommendations based on authors
if (!empty($borrowed_authors)) {
    $placeholders = implode(',', array_fill(0, count($borrowed_authors), '?'));
    $exclude_placeholders = !empty($borrowed_books) ? implode(',', array_fill(0, count($borrowed_books), '?')) : '';
    
    $query = "SELECT * FROM books 
              WHERE author IN ($placeholders)";
    
    if (!empty($borrowed_books)) {
        $query .= " AND id NOT IN ($exclude_placeholders)";
    }
    
    $query .= " ORDER BY RAND() LIMIT 6";
    
    $stmt = $db->prepare($query);
    
    // Bind author parameters
    $param_index = 1;
    foreach ($borrowed_authors as $author) {
        $stmt->bindValue($param_index++, $author);
    }
    
    // Bind borrowed books parameters
    if (!empty($borrowed_books)) {
        foreach ($borrowed_books as $book_id) {
            $stmt->bindValue($param_index++, $book_id);
        }
    }
    
    $stmt->execute();
    $author_recommendations = $stmt->fetchAll();
}

// Get popular books (most borrowed)
$query = "SELECT b.*, COUNT(bl.id) as borrow_count
          FROM books b
          JOIN book_loans bl ON b.id = bl.book_id
          WHERE b.available_quantity > 0";

if (!empty($borrowed_books)) {
    $exclude_placeholders = implode(',', array_fill(0, count($borrowed_books), '?'));
    $query .= " AND b.id NOT IN ($exclude_placeholders)";
}

$query .= " GROUP BY b.id
          ORDER BY borrow_count DESC
          LIMIT 6";

$stmt = $db->prepare($query);

// Bind borrowed books parameters
if (!empty($borrowed_books)) {
    $param_index = 1;
    foreach ($borrowed_books as $book_id) {
        $stmt->bindValue($param_index++, $book_id);
    }
}

$stmt->execute();
$popular_books = $stmt->fetchAll();

// Get new arrivals
$query = "SELECT * FROM books 
          WHERE available_quantity > 0";

if (!empty($borrowed_books)) {
    $exclude_placeholders = implode(',', array_fill(0, count($borrowed_books), '?'));
    $query .= " AND id NOT IN ($exclude_placeholders)";
}

$query .= " ORDER BY created_at DESC
          LIMIT 6";

$stmt = $db->prepare($query);

// Bind borrowed books parameters
if (!empty($borrowed_books)) {
    $param_index = 1;
    foreach ($borrowed_books as $book_id) {
        $stmt->bindValue($param_index++, $book_id);
    }
}

$stmt->execute();
$new_arrivals = $stmt->fetchAll();

// Page title
$page_title = 'Book Recommendations';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include '../includes/head.php'; ?>
    <style>
        .recommendation-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .recommendation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        .book-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            height: 100%;
        }
        .book-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        .book-cover {
            height: 200px;
            object-fit: cover;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }
        .book-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .book-category {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 3px 8px;
            border-radius: 20px;
            font-size: 0.8rem;
        }
        .section-header {
            border-left: 5px solid #007bff;
            padding-left: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-lightbulb me-2 text-primary"></i>Book Recommendations</h1>
                </div>

                <?php if (empty($borrowing_history)): ?>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        We don't have enough data to provide personalized recommendations yet. Borrow some books to get started!
                    </div>
                <?php endif; ?>

                <!-- Category-based Recommendations -->
                <?php if (!empty($category_recommendations)): ?>
                    <h3 class="section-header">Based on Categories You've Read</h3>
                    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-6 g-4 mb-5">
                        <?php foreach ($category_recommendations as $book): ?>
                            <div class="col">
                                <div class="card book-card h-100">
                                    <div class="position-relative">
                                        <?php if (!empty($book['cover_image']) && file_exists('../uploads/covers/' . $book['cover_image'])): ?>
                                            <img src="<?php echo url('../uploads/covers/' . $book['cover_image']); ?>" class="book-cover w-100" alt="<?php echo htmlspecialchars($book['title']); ?>">
                                        <?php else: ?>
                                            <img src="<?php echo url('../assets/img/book-placeholder.jpg'); ?>" class="book-cover w-100" alt="No Cover Available">
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($book['category'])): ?>
                                            <span class="book-category"><?php echo htmlspecialchars($book['category']); ?></span>
                                        <?php endif; ?>
                                        
                                        <?php if ($book['available_quantity'] == 0): ?>
                                            <span class="badge bg-danger book-badge">Not Available</span>
                                        <?php elseif ($book['available_quantity'] < $book['quantity']): ?>
                                            <span class="badge bg-warning text-dark book-badge"><?php echo $book['available_quantity']; ?> Available</span>
                                        <?php else: ?>
                                            <span class="badge bg-success book-badge">Available</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo htmlspecialchars($book['title']); ?></h5>
                                        <h6 class="card-subtitle mb-2 text-muted"><?php echo htmlspecialchars($book['author']); ?></h6>
                                    </div>
                                    <div class="card-footer bg-transparent border-top-0">
                                        <div class="d-grid gap-2">
                                            <a href="<?php echo url('../books/view.php?id=' . $book['id']); ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="bi bi-eye me-1"></i>View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <!-- Author-based Recommendations -->
                <?php if (!empty($author_recommendations)): ?>
                    <h3 class="section-header">More from Authors You've Read</h3>
                    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-6 g-4 mb-5">
                        <?php foreach ($author_recommendations as $book): ?>
                            <div class="col">
                                <div class="card book-card h-100">
                                    <div class="position-relative">
                                        <?php if (!empty($book['cover_image']) && file_exists('../uploads/covers/' . $book['cover_image'])): ?>
                                            <img src="<?php echo url('../uploads/covers/' . $book['cover_image']); ?>" class="book-cover w-100" alt="<?php echo htmlspecialchars($book['title']); ?>">
                                        <?php else: ?>
                                            <img src="<?php echo url('../assets/img/book-placeholder.jpg'); ?>" class="book-cover w-100" alt="No Cover Available">
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($book['category'])): ?>
                                            <span class="book-category"><?php echo htmlspecialchars($book['category']); ?></span>
                                        <?php endif; ?>
                                        
                                        <?php if ($book['available_quantity'] == 0): ?>
                                            <span class="badge bg-danger book-badge">Not Available</span>
                                        <?php elseif ($book['available_quantity'] < $book['quantity']): ?>
                                            <span class="badge bg-warning text-dark book-badge"><?php echo $book['available_quantity']; ?> Available</span>
                                        <?php else: ?>
                                            <span class="badge bg-success book-badge">Available</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo htmlspecialchars($book['title']); ?></h5>
                                        <h6 class="card-subtitle mb-2 text-muted"><?php echo htmlspecialchars($book['author']); ?></h6>
                                    </div>
                                    <div class="card-footer bg-transparent border-top-0">
                                        <div class="d-grid gap-2">
                                            <a href="<?php echo url('../books/view.php?id=' . $book['id']); ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="bi bi-eye me-1"></i>View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <!-- Popular Books -->
                <h3 class="section-header">Popular Books</h3>
                <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-6 g-4 mb-5">
                    <?php if (!empty($popular_books)): ?>
                        <?php foreach ($popular_books as $book): ?>
                            <div class="col">
                                <div class="card book-card h-100">
                                    <div class="position-relative">
                                        <?php if (!empty($book['cover_image']) && file_exists('../uploads/covers/' . $book['cover_image'])): ?>
                                            <img src="<?php echo url('../uploads/covers/' . $book['cover_image']); ?>" class="book-cover w-100" alt="<?php echo htmlspecialchars($book['title']); ?>">
                                        <?php else: ?>
                                            <img src="<?php echo url('../assets/img/book-placeholder.jpg'); ?>" class="book-cover w-100" alt="No Cover Available">
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($book['category'])): ?>
                                            <span class="book-category"><?php echo htmlspecialchars($book['category']); ?></span>
                                        <?php endif; ?>
                                        
                                        <?php if ($book['available_quantity'] == 0): ?>
                                            <span class="badge bg-danger book-badge">Not Available</span>
                                        <?php elseif ($book['available_quantity'] < $book['quantity']): ?>
                                            <span class="badge bg-warning text-dark book-badge"><?php echo $book['available_quantity']; ?> Available</span>
                                        <?php else: ?>
                                            <span class="badge bg-success book-badge">Available</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo htmlspecialchars($book['title']); ?></h5>
                                        <h6 class="card-subtitle mb-2 text-muted"><?php echo htmlspecialchars($book['author']); ?></h6>
                                    </div>
                                    <div class="card-footer bg-transparent border-top-0">
                                        <div class="d-grid gap-2">
                                            <a href="<?php echo url('../books/view.php?id=' . $book['id']); ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="bi bi-eye me-1"></i>View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>No popular books available at the moment.
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- New Arrivals -->
                <h3 class="section-header">New Arrivals</h3>
                <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-6 g-4">
                    <?php if (!empty($new_arrivals)): ?>
                        <?php foreach ($new_arrivals as $book): ?>
                            <div class="col">
                                <div class="card book-card h-100">
                                    <div class="position-relative">
                                        <?php if (!empty($book['cover_image']) && file_exists('../uploads/covers/' . $book['cover_image'])): ?>
                                            <img src="<?php echo url('../uploads/covers/' . $book['cover_image']); ?>" class="book-cover w-100" alt="<?php echo htmlspecialchars($book['title']); ?>">
                                        <?php else: ?>
                                            <img src="<?php echo url('../assets/img/book-placeholder.jpg'); ?>" class="book-cover w-100" alt="No Cover Available">
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($book['category'])): ?>
                                            <span class="book-category"><?php echo htmlspecialchars($book['category']); ?></span>
                                        <?php endif; ?>
                                        
                                        <span class="badge bg-info book-badge">New</span>
                                    </div>
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo htmlspecialchars($book['title']); ?></h5>
                                        <h6 class="card-subtitle mb-2 text-muted"><?php echo htmlspecialchars($book['author']); ?></h6>
                                    </div>
                                    <div class="card-footer bg-transparent border-top-0">
                                        <div class="d-grid gap-2">
                                            <a href="<?php echo url('../books/view.php?id=' . $book['id']); ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="bi bi-eye me-1"></i>View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>No new arrivals available at the moment.
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
