<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Fix Featured Book Covers</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h2>Fix Featured Book Covers</h2>";

// Specific mappings for the featured books
$featured_books_covers = [
    'To Kill a Mockingbird' => '1984_cover.jpg', // You can change this to a proper cover
    '1984' => '1984_cover.jpg',
    'The Great Gatsby' => '1747725503_the-great-gatsby-and-other-works-9781645173519_hr.jpg'
];

$updated_count = 0;

foreach ($featured_books_covers as $title => $cover_file) {
    // Check if the cover file exists
    if (file_exists('uploads/covers/' . $cover_file)) {
        // Update the book
        $query = "UPDATE books SET cover_image = :cover_image WHERE title = :title";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':cover_image', $cover_file);
        $stmt->bindParam(':title', $title);
        
        if ($stmt->execute()) {
            $affected_rows = $stmt->rowCount();
            if ($affected_rows > 0) {
                echo "<div class='alert alert-success'>✅ Updated '{$title}' with cover: {$cover_file}</div>";
                $updated_count++;
            } else {
                echo "<div class='alert alert-warning'>⚠️ Book '{$title}' not found in database</div>";
            }
        } else {
            echo "<div class='alert alert-danger'>❌ Failed to update '{$title}'</div>";
        }
    } else {
        echo "<div class='alert alert-danger'>❌ Cover file not found: {$cover_file}</div>";
    }
}

// Also update any other books with available covers
$other_mappings = [
    'Pride and Prejudice' => '1747724335_Pride and Prejudice.jpg',
    'A Game of Thrones' => '1747724281_AGameOfThrones.jpg',
    'Good to Great' => '1747724303_Good to Great.jpg',
    'The Alchemist' => '1747725431_the-alchemist-a-graphic-novel.jpg',
    'The Catcher in the Rye' => '1747725459_Book-Review-The-Catcher-in-the-Rye-by-J-D-Salinger.jpg',
    'The Martian' => '1747725673_The_Martian_(Weir_novel).jpg'
];

foreach ($other_mappings as $title => $cover_file) {
    if (file_exists('uploads/covers/' . $cover_file)) {
        $query = "UPDATE books SET cover_image = :cover_image WHERE title LIKE :title";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':cover_image', $cover_file);
        $like_title = '%' . $title . '%';
        $stmt->bindParam(':title', $like_title);
        
        if ($stmt->execute()) {
            $affected_rows = $stmt->rowCount();
            if ($affected_rows > 0) {
                echo "<div class='alert alert-success'>✅ Updated '{$title}' with cover: {$cover_file}</div>";
                $updated_count++;
            }
        }
    }
}

echo "<div class='alert alert-info mt-4'>
    <h4>Summary:</h4>
    <p>Updated {$updated_count} books with cover images.</p>
    <p><a href='home.php' class='btn btn-primary'>View Home Page</a></p>
    <p><a href='catalog.php' class='btn btn-secondary'>View Book Catalog</a></p>
</div>";

// Show current featured books
echo "<h3 class='mt-4'>Current Featured Books:</h3>";
$query = "SELECT * FROM books ORDER BY created_at DESC LIMIT 3";
$stmt = $db->prepare($query);
$stmt->execute();
$books = $stmt->fetchAll();

foreach ($books as $book) {
    echo "<div class='card mb-2'>
        <div class='card-body'>
            <h5>{$book['title']}</h5>
            <p>Author: {$book['author']}</p>
            <p>Cover Image: " . ($book['cover_image'] ? $book['cover_image'] : 'None') . "</p>";
    
    if ($book['cover_image'] && file_exists('uploads/covers/' . $book['cover_image'])) {
        echo "<img src='uploads/covers/{$book['cover_image']}' alt='{$book['title']}' style='max-width: 100px; height: auto;'>";
    }
    
    echo "</div></div>";
}

echo "</div></body></html>";
?>
