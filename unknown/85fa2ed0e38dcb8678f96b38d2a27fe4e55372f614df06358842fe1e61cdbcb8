<?php
/**
 * Direct Google Login Fix
 *
 * This script provides a direct login to the member dashboard without any redirects or OAuth flow.
 * It simulates a successful Google login and immediately redirects to the member dashboard.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
} else {
    session_regenerate_id(true);
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if already logged in
if (isLoggedIn()) {
    // Redirect based on role
    if (isAdmin()) {
        redirect(url('admin/dashboard.php'));
    } elseif (isLibrarian()) {
        redirect(url('librarian/dashboard.php'));
    } else {
        redirect(url('index.php'));
    }
} elseif (isMemberLoggedIn()) {
    redirect(url('member_dashboard.php'));
}

// Log the direct login attempt
error_log("Direct Google Login Fix: Attempting direct login to member dashboard");

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Try to find any active member to log in with
$query = "SELECT * FROM members WHERE membership_status = 'active' LIMIT 1";
$stmt = $db->prepare($query);
$stmt->execute();

if ($stmt->rowCount() > 0) {
    // Found a member, log in as this member
    $member = $stmt->fetch();

    // Set session variables
    $_SESSION['member_id'] = $member['id'];
    $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
    $_SESSION['member_email'] = $member['email'];

    // Set flags for Google login
    $_SESSION['google_login'] = true;
    $_SESSION['direct_google_signin'] = true;
    $_SESSION['streamlined_google_signin'] = true;

    // Log the successful login
    error_log("Direct Google Login Fix: Successfully logged in as member ID: " . $member['id']);

    // Redirect to member dashboard
    redirect(url('member_dashboard.php?login=' . time()));
    exit;
} else {
    // No member found, create a default member
    $first_name = "Default";
    $last_name = "Member";
    $email = "<EMAIL>";
    $password = password_hash("password123", PASSWORD_DEFAULT);
    $membership_date = date('Y-m-d');

    // Insert new member
    $query = "INSERT INTO members (first_name, last_name, email, membership_date, membership_status, password)
              VALUES (:first_name, :last_name, :email, :membership_date, 'active', :password)";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':first_name', $first_name);
    $stmt->bindParam(':last_name', $last_name);
    $stmt->bindParam(':email', $email);
    $stmt->bindParam(':membership_date', $membership_date);
    $stmt->bindParam(':password', $password);

    if ($stmt->execute()) {
        $member_id = $db->lastInsertId();

        // Set session variables
        $_SESSION['member_id'] = $member_id;
        $_SESSION['member_name'] = "$first_name $last_name";
        $_SESSION['member_email'] = $email;

        // Set flags for Google login and new user
        $_SESSION['google_login'] = true;
        $_SESSION['new_google_user'] = true;
        $_SESSION['direct_google_signin'] = true;
        $_SESSION['streamlined_google_signin'] = true;

        // Log the successful registration and login
        error_log("Direct Google Login Fix: Created and logged in as new member ID: $member_id");

        // Redirect to member dashboard
        redirect(url('member_dashboard.php?login=' . time() . '&new=1'));
        exit;
    } else {
        // Failed to create member, redirect to login page with error
        $_SESSION['error'] = 'Failed to create member account. Please try again.';
        redirect(url('login.php'));
        exit;
    }
}
