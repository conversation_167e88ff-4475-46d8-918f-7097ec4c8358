<?php
header('Content-Type: application/json');
session_start();

require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['query']) || empty(trim($input['query']))) {
    echo json_encode(['success' => false, 'message' => 'Query is required']);
    exit;
}

$query = trim($input['query']);

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Search members by name, email, or phone
    $sql = "SELECT id, first_name, last_name, email, phone, membership_date 
            FROM members 
            WHERE first_name LIKE :query 
               OR last_name LIKE :query 
               OR email LIKE :query 
               OR phone LIKE :query
               OR CONCAT(first_name, ' ', last_name) LIKE :query
            ORDER BY first_name ASC, last_name ASC 
            LIMIT 10";
    
    $stmt = $db->prepare($sql);
    $searchTerm = '%' . $query . '%';
    $stmt->bindParam(':query', $searchTerm);
    $stmt->execute();
    
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'members' => $members,
        'count' => count($members)
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
