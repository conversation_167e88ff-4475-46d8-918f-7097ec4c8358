-- Add Google authentication columns to users and members tables

-- Add columns to users table if they don't exist
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS google_id VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS google_token TEXT NULL,
ADD COLUMN IF NOT EXISTS google_picture VARCHAR(255) NULL;

-- Add columns to members table if they don't exist
ALTER TABLE members 
ADD COLUMN IF NOT EXISTS google_id VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS google_token TEXT NULL,
ADD COLUMN IF NOT EXISTS google_picture VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS remember_token VARCHAR(255) NULL;

-- Add indexes for faster lookups
ALTER TABLE users ADD INDEX idx_google_id (google_id);
ALTER TABLE members ADD INDEX idx_google_id (google_id);
