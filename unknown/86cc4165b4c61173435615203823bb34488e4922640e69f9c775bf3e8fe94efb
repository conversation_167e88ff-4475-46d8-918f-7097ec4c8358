<?php
/**
 * Admin Home Page - Redirects to Dashboard
 */
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    // Redirect to login page
    header('Location: ' . url('login.php'));
    exit;
}

// Redirect to admin dashboard
header('Location: ' . url('admin/dashboard.php'));
exit;
?>
