<?php
/**
 * Fix Diagnostics Access Issues
 * This script ensures all diagnostic files are accessible
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Diagnostics Access Fix</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .test-link { display: inline-block; margin: 5px; padding: 8px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
</style>";

$fixes_applied = [];
$errors = [];

echo "<div class='section'>";
echo "<h2>🔍 Checking Current Status</h2>";

// Check if diagnostic files exist
$diagnostic_files = [
    'diagnostic.php' => 'Main Diagnostic Page',
    'database_status.php' => 'Database Status Check',
    'troubleshoot.php' => 'Troubleshooting Guide',
    'admin/ajax/test_database.php' => 'Advanced Database Test'
];

foreach ($diagnostic_files as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ $description exists</div>";
    } else {
        echo "<div class='error'>❌ $description missing</div>";
        $errors[] = "$description file missing";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🔧 Applying Fixes</h2>";

// Check and fix .htaccess files
$htaccess_main = '.htaccess';
if (file_exists($htaccess_main)) {
    $content = file_get_contents($htaccess_main);
    if (strpos($content, 'diagnostic\.php') !== false) {
        echo "<div class='success'>✅ Main .htaccess already allows diagnostic files</div>";
    } else {
        echo "<div class='warning'>⚠️ Main .htaccess may need updating</div>";
    }
} else {
    echo "<div class='error'>❌ Main .htaccess missing</div>";
}

// Check admin/ajax .htaccess
$htaccess_ajax = 'admin/ajax/.htaccess';
if (file_exists($htaccess_ajax)) {
    echo "<div class='success'>✅ Admin AJAX .htaccess exists</div>";
} else {
    echo "<div class='warning'>⚠️ Creating admin/ajax/.htaccess</div>";
    
    $ajax_htaccess_content = '# Admin AJAX Directory Access Configuration

# Allow access to all PHP files in this directory
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

# Disable URL rewriting for this directory
<IfModule mod_rewrite.c>
    RewriteEngine Off
</IfModule>

# Prevent directory listing
Options -Indexes';

    if (!is_dir('admin/ajax')) {
        mkdir('admin/ajax', 0755, true);
    }
    
    if (file_put_contents($htaccess_ajax, $ajax_htaccess_content)) {
        echo "<div class='success'>✅ Created admin/ajax/.htaccess</div>";
        $fixes_applied[] = "Created admin/ajax/.htaccess";
    } else {
        echo "<div class='error'>❌ Failed to create admin/ajax/.htaccess</div>";
        $errors[] = "Failed to create admin/ajax/.htaccess";
    }
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>🧪 Testing Access</h2>";

foreach ($diagnostic_files as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='info'>";
        echo "<h4>$description</h4>";
        echo "<p>File: $file</p>";
        echo "<p>Readable: " . (is_readable($file) ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><a href='$file' target='_blank' class='test-link'>🔗 Test Access</a></p>";
        echo "</div>";
    }
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>📊 Summary</h2>";

if (empty($errors)) {
    echo "<div class='success'>";
    echo "<h3>✅ All Checks Passed!</h3>";
    echo "<p>All diagnostic files should now be accessible.</p>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h3>❌ Issues Found:</h3>";
    foreach ($errors as $error) {
        echo "<p>• $error</p>";
    }
    echo "</div>";
}

if (!empty($fixes_applied)) {
    echo "<div class='info'>";
    echo "<h3>🔧 Fixes Applied:</h3>";
    foreach ($fixes_applied as $fix) {
        echo "<p>• $fix</p>";
    }
    echo "</div>";
}

echo "<div class='info'>";
echo "<h3>🎯 Next Steps:</h3>";
echo "<p>1. Test the diagnostic menu in the admin dashboard</p>";
echo "<p>2. Click on each diagnostic option to verify access</p>";
echo "<p>3. If you still see 'Forbidden' errors, check your Apache error logs</p>";
echo "</div>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>🔗 Quick Links</h2>";
echo "<p><a href='admin/dashboard.php' class='test-link'>📊 Admin Dashboard</a></p>";
echo "<p><a href='test_diagnostics.php' class='test-link'>🔧 Full Diagnostics Test</a></p>";
echo "<p><a href='diagnostic.php' class='test-link'>🔍 Main Diagnostic</a></p>";
echo "<p><a href='database_status.php' class='test-link'>💾 Database Status</a></p>";
echo "<p><a href='troubleshoot.php' class='test-link'>❓ Troubleshooting</a></p>";
echo "</div>";
