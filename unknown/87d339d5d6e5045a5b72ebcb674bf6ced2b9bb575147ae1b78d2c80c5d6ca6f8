<?php
/**
 * Quick Member Access - Emergency Access to Member Dashboard
 */
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Check if we have any active members
$query = "SELECT * FROM members WHERE membership_status = 'active' LIMIT 1";
$stmt = $db->prepare($query);
$stmt->execute();

if ($stmt->rowCount() > 0) {
    $member = $stmt->fetch();
    
    // Set session variables to log in as this member
    $_SESSION['member_id'] = $member['id'];
    $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
    $_SESSION['member_email'] = $member['email'];
    
    // Redirect to member dashboard
    header('Location: member_dashboard.php');
    exit;
} else {
    // No members found, create a quick test member
    $first_name = "Test";
    $last_name = "Member";
    $email = "<EMAIL>";
    $phone = "555-0123";
    $address = "123 Library St";
    $membership_date = date('Y-m-d');
    $membership_status = 'active';
    
    $query = "INSERT INTO members (first_name, last_name, email, phone, address, membership_date, membership_status) 
              VALUES (:first_name, :last_name, :email, :phone, :address, :membership_date, :membership_status)";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':first_name', $first_name);
    $stmt->bindParam(':last_name', $last_name);
    $stmt->bindParam(':email', $email);
    $stmt->bindParam(':phone', $phone);
    $stmt->bindParam(':address', $address);
    $stmt->bindParam(':membership_date', $membership_date);
    $stmt->bindParam(':membership_status', $membership_status);
    
    if ($stmt->execute()) {
        $member_id = $db->lastInsertId();
        
        // Set session variables
        $_SESSION['member_id'] = $member_id;
        $_SESSION['member_name'] = $first_name . ' ' . $last_name;
        $_SESSION['member_email'] = $email;
        
        // Redirect to member dashboard
        header('Location: member_dashboard.php');
        exit;
    } else {
        echo "Error creating test member. Please check your database connection.";
    }
}
?>
