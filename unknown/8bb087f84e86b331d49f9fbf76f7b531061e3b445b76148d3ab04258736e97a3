<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Database Structure Check</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<p class='success'>✓ Database connection successful!</p>";
    
    // Check if required tables exist
    $required_tables = ['books', 'members', 'users', 'book_loans'];
    
    foreach ($required_tables as $table) {
        echo "<h2>Table: $table</h2>";
        
        // Check if table exists
        $query = "SHOW TABLES LIKE '$table'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result) {
            echo "<p class='success'>✓ Table '$table' exists</p>";
            
            // Show table structure
            $query = "DESCRIBE $table";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $columns = $stmt->fetchAll();
            
            echo "<table>";
            echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>" . $column['Field'] . "</td>";
                echo "<td>" . $column['Type'] . "</td>";
                echo "<td>" . $column['Null'] . "</td>";
                echo "<td>" . $column['Key'] . "</td>";
                echo "<td>" . $column['Default'] . "</td>";
                echo "<td>" . $column['Extra'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Show record count
            $query = "SELECT COUNT(*) as count FROM $table";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $count = $stmt->fetch();
            echo "<p class='info'>Records in table: " . $count['count'] . "</p>";
            
            // Show sample data (first 5 records)
            if ($count['count'] > 0) {
                $query = "SELECT * FROM $table LIMIT 5";
                $stmt = $db->prepare($query);
                $stmt->execute();
                $samples = $stmt->fetchAll();
                
                if (!empty($samples)) {
                    echo "<h3>Sample Data (first 5 records):</h3>";
                    echo "<table>";
                    
                    // Header
                    echo "<tr>";
                    foreach (array_keys($samples[0]) as $column) {
                        echo "<th>$column</th>";
                    }
                    echo "</tr>";
                    
                    // Data
                    foreach ($samples as $row) {
                        echo "<tr>";
                        foreach ($row as $value) {
                            echo "<td>" . htmlspecialchars(substr($value, 0, 50)) . (strlen($value) > 50 ? '...' : '') . "</td>";
                        }
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            }
            
        } else {
            echo "<p class='error'>✗ Table '$table' does not exist</p>";
        }
        
        echo "<hr>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ Database error: " . $e->getMessage() . "</p>";
}

echo "<h2>Quick Actions</h2>";
echo "<p><a href='home.php'>Go to Home Page</a></p>";
echo "<p><a href='diagnostic.php'>Go to Diagnostic Page</a></p>";
echo "<p><a href='index.php'>Go to Index Page</a></p>";
?>
