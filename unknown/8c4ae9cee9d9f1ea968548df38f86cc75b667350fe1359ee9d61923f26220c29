<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if member is logged in
if (!isMemberLoggedIn()) {
    redirect('../member_login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get member ID from session
$member_id = $_SESSION['member_id'];

// Initialize variables
$active_loans = [];
$loan_history = [];
$reservations = [];
$success_message = '';
$error_message = '';

// Process reservation cancellation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['cancel_reservation'])) {
    $reservation_id = $_POST['reservation_id'];
    
    try {
        // Start transaction
        $db->beginTransaction();
        
        // Get reservation details
        $query = "SELECT book_id FROM book_reservations WHERE id = :reservation_id AND member_id = :member_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':reservation_id', $reservation_id);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $reservation = $stmt->fetch();
            $book_id = $reservation['book_id'];
            
            // Delete the reservation
            $query = "DELETE FROM book_reservations WHERE id = :reservation_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':reservation_id', $reservation_id);
            
            if ($stmt->execute()) {
                // Log activity
                logActivity($db, 'delete', "Member cancelled reservation", 'reservation', $reservation_id);
                
                $success_message = "Reservation cancelled successfully";
            } else {
                $error_message = "Failed to cancel reservation";
            }
        } else {
            $error_message = "Reservation not found or you don't have permission to cancel it";
        }
        
        // Commit transaction
        $db->commit();
    } catch (Exception $e) {
        // Rollback transaction on error
        $db->rollBack();
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get active loans
$query = "SELECT bl.*, b.title, b.author, b.isbn, b.cover_image
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id AND bl.status = 'borrowed'
          ORDER BY bl.due_date ASC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$active_loans = $stmt->fetchAll();

// Get loan history
$query = "SELECT bl.*, b.title, b.author, b.isbn, b.cover_image
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id AND bl.status = 'returned'
          ORDER BY bl.return_date DESC
          LIMIT 10";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$loan_history = $stmt->fetchAll();

// Get reservations
$query = "SELECT br.*, b.title, b.author, b.isbn, b.cover_image
          FROM book_reservations br
          JOIN books b ON br.book_id = b.id
          WHERE br.member_id = :member_id
          ORDER BY br.created_at DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$reservations = $stmt->fetchAll();

// Get member details
$query = "SELECT * FROM members WHERE id = :member_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$member = $stmt->fetch();

// Calculate total fines
$total_fines = 0;
foreach ($active_loans as $loan) {
    if (strtotime($loan['due_date']) < time()) {
        // Book is overdue
        $days_overdue = floor((time() - strtotime($loan['due_date'])) / (60 * 60 * 24));
        
        // Get fine rate from settings (default to $0.25 per day)
        $query = "SELECT setting_value FROM settings WHERE setting_key = 'fine_rate_per_day'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $fine_rate = $stmt->rowCount() > 0 ? (float)$stmt->fetch()['setting_value'] : 0.25;
        
        // Get grace period from settings (default to 3 days)
        $query = "SELECT setting_value FROM settings WHERE setting_key = 'grace_period_days'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $grace_period = $stmt->rowCount() > 0 ? (int)$stmt->fetch()['setting_value'] : 3;
        
        // Apply grace period
        $days_overdue = max(0, $days_overdue - $grace_period);
        
        // Calculate fine
        $fine = $days_overdue * $fine_rate;
        
        // Get max fine per book from settings (default to $25)
        $query = "SELECT setting_value FROM settings WHERE setting_key = 'max_fine_per_book'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $max_fine = $stmt->rowCount() > 0 ? (float)$stmt->fetch()['setting_value'] : 25.00;
        
        // Apply max fine cap
        $fine = min($fine, $max_fine);
        
        $total_fines += $fine;
    }
}

// Page title
$page_title = 'My Loans & Reservations';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include '../includes/head.php'; ?>
    <style>
        .loan-card, .reservation-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            height: 100%;
        }
        .loan-card:hover, .reservation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        .book-cover {
            height: 150px;
            object-fit: cover;
        }
        .overdue {
            border-left: 4px solid #dc3545;
        }
        .due-soon {
            border-left: 4px solid #ffc107;
        }
        .status-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .nav-pills .nav-link.active {
            background-color: #007bff;
        }
        .nav-pills .nav-link {
            color: #495057;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-book me-2 text-primary"></i>My Loans & Reservations</h1>
                </div>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Member Info Card -->
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-person-circle me-2"></i>Member Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Name:</strong> <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?></p>
                                <p><strong>Email:</strong> <?php echo htmlspecialchars($member['email']); ?></p>
                                <p><strong>Member Since:</strong> <?php echo date('F j, Y', strtotime($member['membership_date'])); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Active Loans:</strong> <span class="badge bg-primary"><?php echo count($active_loans); ?></span></p>
                                <p><strong>Active Reservations:</strong> <span class="badge bg-info"><?php echo count($reservations); ?></span></p>
                                <?php if ($total_fines > 0): ?>
                                    <p><strong>Current Fines:</strong> <span class="badge bg-danger">$<?php echo number_format($total_fines, 2); ?></span></p>
                                <?php else: ?>
                                    <p><strong>Current Fines:</strong> <span class="badge bg-success">$0.00</span></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tabs Navigation -->
                <ul class="nav nav-pills mb-3" id="loans-tab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="active-loans-tab" data-bs-toggle="pill" data-bs-target="#active-loans" type="button" role="tab" aria-controls="active-loans" aria-selected="true">
                            <i class="bi bi-book me-1"></i> Active Loans <span class="badge bg-primary"><?php echo count($active_loans); ?></span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="reservations-tab" data-bs-toggle="pill" data-bs-target="#reservations" type="button" role="tab" aria-controls="reservations" aria-selected="false">
                            <i class="bi bi-bookmark me-1"></i> Reservations <span class="badge bg-info"><?php echo count($reservations); ?></span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="history-tab" data-bs-toggle="pill" data-bs-target="#history" type="button" role="tab" aria-controls="history" aria-selected="false">
                            <i class="bi bi-clock-history me-1"></i> Loan History
                        </button>
                    </li>
                </ul>

                <!-- Tabs Content -->
                <div class="tab-content" id="loans-tabContent">
                    <!-- Active Loans Tab -->
                    <div class="tab-pane fade show active" id="active-loans" role="tabpanel" aria-labelledby="active-loans-tab">
                        <?php if (count($active_loans) > 0): ?>
                            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                                <?php foreach ($active_loans as $loan): ?>
                                    <?php
                                    $is_overdue = strtotime($loan['due_date']) < time();
                                    $due_soon = !$is_overdue && strtotime($loan['due_date']) < strtotime('+3 days');
                                    $card_class = $is_overdue ? 'overdue' : ($due_soon ? 'due-soon' : '');
                                    ?>
                                    <div class="col">
                                        <div class="card loan-card h-100 <?php echo $card_class; ?>">
                                            <div class="position-relative">
                                                <?php if (!empty($loan['cover_image']) && file_exists('../uploads/covers/' . $loan['cover_image'])): ?>
                                                    <img src="<?php echo url('../uploads/covers/' . $loan['cover_image']); ?>" class="book-cover w-100" alt="<?php echo htmlspecialchars($loan['title']); ?>">
                                                <?php else: ?>
                                                    <img src="<?php echo url('../assets/img/book-placeholder.jpg'); ?>" class="book-cover w-100" alt="No Cover Available">
                                                <?php endif; ?>
                                                
                                                <?php if ($is_overdue): ?>
                                                    <span class="badge bg-danger status-badge">Overdue</span>
                                                <?php elseif ($due_soon): ?>
                                                    <span class="badge bg-warning text-dark status-badge">Due Soon</span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="card-body">
                                                <h5 class="card-title"><?php echo htmlspecialchars($loan['title']); ?></h5>
                                                <h6 class="card-subtitle mb-2 text-muted"><?php echo htmlspecialchars($loan['author']); ?></h6>
                                                <p class="card-text">
                                                    <small class="text-muted">ISBN: <?php echo htmlspecialchars($loan['isbn']); ?></small>
                                                </p>
                                                <p class="card-text">
                                                    <strong>Borrowed:</strong> <?php echo date('M j, Y', strtotime($loan['issue_date'])); ?><br>
                                                    <strong>Due Date:</strong> <?php echo date('M j, Y', strtotime($loan['due_date'])); ?>
                                                </p>
                                                <?php if ($is_overdue): ?>
                                                    <?php
                                                    $days_overdue = floor((time() - strtotime($loan['due_date'])) / (60 * 60 * 24));
                                                    
                                                    // Get fine rate from settings (default to $0.25 per day)
                                                    $query = "SELECT setting_value FROM settings WHERE setting_key = 'fine_rate_per_day'";
                                                    $stmt = $db->prepare($query);
                                                    $stmt->execute();
                                                    $fine_rate = $stmt->rowCount() > 0 ? (float)$stmt->fetch()['setting_value'] : 0.25;
                                                    
                                                    // Get grace period from settings (default to 3 days)
                                                    $query = "SELECT setting_value FROM settings WHERE setting_key = 'grace_period_days'";
                                                    $stmt = $db->prepare($query);
                                                    $stmt->execute();
                                                    $grace_period = $stmt->rowCount() > 0 ? (int)$stmt->fetch()['setting_value'] : 3;
                                                    
                                                    // Apply grace period
                                                    $days_overdue = max(0, $days_overdue - $grace_period);
                                                    
                                                    // Calculate fine
                                                    $fine = $days_overdue * $fine_rate;
                                                    
                                                    // Get max fine per book from settings (default to $25)
                                                    $query = "SELECT setting_value FROM settings WHERE setting_key = 'max_fine_per_book'";
                                                    $stmt = $db->prepare($query);
                                                    $stmt->execute();
                                                    $max_fine = $stmt->rowCount() > 0 ? (float)$stmt->fetch()['setting_value'] : 25.00;
                                                    
                                                    // Apply max fine cap
                                                    $fine = min($fine, $max_fine);
                                                    ?>
                                                    <div class="alert alert-danger">
                                                        <strong>Overdue by <?php echo $days_overdue + $grace_period; ?> days</strong><br>
                                                        <?php if ($days_overdue > 0): ?>
                                                            Current Fine: $<?php echo number_format($fine, 2); ?>
                                                        <?php else: ?>
                                                            In grace period (<?php echo $grace_period; ?> days)
                                                        <?php endif; ?>
                                                    </div>
                                                <?php elseif ($due_soon): ?>
                                                    <div class="alert alert-warning">
                                                        <strong>Due in <?php echo floor((strtotime($loan['due_date']) - time()) / (60 * 60 * 24)); ?> days</strong><br>
                                                        Please return soon to avoid fines
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="card-footer bg-transparent">
                                                <a href="<?php echo url('../books/view.php?id=' . $loan['book_id']); ?>" class="btn btn-sm btn-primary">
                                                    <i class="bi bi-eye me-1"></i> View Book
                                                </a>
                                                <a href="#" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#renewModal<?php echo $loan['id']; ?>">
                                                    <i class="bi bi-arrow-clockwise me-1"></i> Renew
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Renew Modal -->
                                    <div class="modal fade" id="renewModal<?php echo $loan['id']; ?>" tabindex="-1" aria-labelledby="renewModalLabel<?php echo $loan['id']; ?>" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="renewModalLabel<?php echo $loan['id']; ?>">Renew Book</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>To renew this book, please contact the library staff.</p>
                                                    <p>Book: <strong><?php echo htmlspecialchars($loan['title']); ?></strong></p>
                                                    <p>Current Due Date: <strong><?php echo date('F j, Y', strtotime($loan['due_date'])); ?></strong></p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                    <a href="mailto:<EMAIL>?subject=Renew%20Book%20Request&body=I%20would%20like%20to%20renew%20the%20following%20book:%0A%0ATitle:%20<?php echo urlencode($loan['title']); ?>%0AISBN:%20<?php echo urlencode($loan['isbn']); ?>%0ACurrent%20Due%20Date:%20<?php echo urlencode(date('F j, Y', strtotime($loan['due_date']))); ?>%0A%0AThank%20you." class="btn btn-primary">
                                                        <i class="bi bi-envelope me-1"></i> Email Library
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>You don't have any active loans.
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Reservations Tab -->
                    <div class="tab-pane fade" id="reservations" role="tabpanel" aria-labelledby="reservations-tab">
                        <?php if (count($reservations) > 0): ?>
                            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                                <?php foreach ($reservations as $reservation): ?>
                                    <div class="col">
                                        <div class="card reservation-card h-100">
                                            <div class="position-relative">
                                                <?php if (!empty($reservation['cover_image']) && file_exists('../uploads/covers/' . $reservation['cover_image'])): ?>
                                                    <img src="<?php echo url('../uploads/covers/' . $reservation['cover_image']); ?>" class="book-cover w-100" alt="<?php echo htmlspecialchars($reservation['title']); ?>">
                                                <?php else: ?>
                                                    <img src="<?php echo url('../assets/img/book-placeholder.jpg'); ?>" class="book-cover w-100" alt="No Cover Available">
                                                <?php endif; ?>
                                                
                                                <?php if ($reservation['status'] === 'pending'): ?>
                                                    <span class="badge bg-warning text-dark status-badge">Pending</span>
                                                <?php elseif ($reservation['status'] === 'ready'): ?>
                                                    <span class="badge bg-success status-badge">Ready for Pickup</span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="card-body">
                                                <h5 class="card-title"><?php echo htmlspecialchars($reservation['title']); ?></h5>
                                                <h6 class="card-subtitle mb-2 text-muted"><?php echo htmlspecialchars($reservation['author']); ?></h6>
                                                <p class="card-text">
                                                    <small class="text-muted">ISBN: <?php echo htmlspecialchars($reservation['isbn']); ?></small>
                                                </p>
                                                <p class="card-text">
                                                    <strong>Reserved:</strong> <?php echo date('M j, Y', strtotime($reservation['created_at'])); ?><br>
                                                    <?php if ($reservation['status'] === 'ready'): ?>
                                                        <strong>Available Until:</strong> <?php echo date('M j, Y', strtotime($reservation['expiry_date'])); ?>
                                                    <?php endif; ?>
                                                </p>
                                                <?php if ($reservation['status'] === 'ready'): ?>
                                                    <div class="alert alert-success">
                                                        <strong>Ready for Pickup!</strong><br>
                                                        Please visit the library to check out this book.
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="card-footer bg-transparent">
                                                <div class="d-flex justify-content-between">
                                                    <a href="<?php echo url('../books/view.php?id=' . $reservation['book_id']); ?>" class="btn btn-sm btn-primary">
                                                        <i class="bi bi-eye me-1"></i> View Book
                                                    </a>
                                                    <form action="" method="post" onsubmit="return confirm('Are you sure you want to cancel this reservation?');">
                                                        <input type="hidden" name="reservation_id" value="<?php echo $reservation['id']; ?>">
                                                        <button type="submit" name="cancel_reservation" class="btn btn-sm btn-danger">
                                                            <i class="bi bi-x-circle me-1"></i> Cancel
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>You don't have any active reservations.
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Loan History Tab -->
                    <div class="tab-pane fade" id="history" role="tabpanel" aria-labelledby="history-tab">
                        <?php if (count($loan_history) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Book</th>
                                            <th>Borrowed</th>
                                            <th>Returned</th>
                                            <th>Fine</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($loan_history as $loan): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <?php if (!empty($loan['cover_image']) && file_exists('../uploads/covers/' . $loan['cover_image'])): ?>
                                                            <img src="<?php echo url('../uploads/covers/' . $loan['cover_image']); ?>" alt="Cover" class="me-2" style="width: 40px; height: 60px; object-fit: cover;">
                                                        <?php else: ?>
                                                            <img src="<?php echo url('../assets/img/book-placeholder.jpg'); ?>" alt="No Cover" class="me-2" style="width: 40px; height: 60px; object-fit: cover;">
                                                        <?php endif; ?>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($loan['title']); ?></strong><br>
                                                            <small class="text-muted"><?php echo htmlspecialchars($loan['author']); ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><?php echo date('M j, Y', strtotime($loan['issue_date'])); ?></td>
                                                <td><?php echo date('M j, Y', strtotime($loan['return_date'])); ?></td>
                                                <td>
                                                    <?php if ($loan['fine'] > 0): ?>
                                                        <span class="text-danger">$<?php echo number_format($loan['fine'], 2); ?></span>
                                                    <?php else: ?>
                                                        <span class="text-success">$0.00</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>You don't have any loan history.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
