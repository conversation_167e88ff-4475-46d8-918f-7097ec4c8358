<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Initialize variables
$isbn = $title = $author = $category = $publication_year = $publisher = '';
$quantity = $available_quantity = 1;
$shelf_location = $description = $cover_image = '';
$errors = [];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $isbn = sanitize($_POST['isbn']);
    $title = sanitize($_POST['title']);
    $author = sanitize($_POST['author']);
    $category = sanitize($_POST['category']);
    $publication_year = sanitize($_POST['publication_year']);
    $publisher = sanitize($_POST['publisher']);
    $quantity = (int)sanitize($_POST['quantity']);
    $available_quantity = $quantity; // Initially all books are available
    $shelf_location = sanitize($_POST['shelf_location']);
    $description = sanitize($_POST['description']);

    // Validate input
    if (empty($title)) {
        $errors[] = 'Book title is required';
    }

    if (empty($author)) {
        $errors[] = 'Author name is required';
    }

    if ($quantity < 1) {
        $errors[] = 'Quantity must be at least 1';
    }

    // Check if ISBN already exists
    if (!empty($isbn)) {
        $query = "SELECT id FROM books WHERE isbn = :isbn";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':isbn', $isbn);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $errors[] = 'ISBN already exists in the database';
        }
    }

    // Handle file upload for cover image
    if (isset($_FILES['cover_image']) && $_FILES['cover_image']['error'] === UPLOAD_ERR_OK) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $file_type = $_FILES['cover_image']['type'];

        if (in_array($file_type, $allowed_types)) {
            $file_name = time() . '_' . $_FILES['cover_image']['name'];
            $upload_dir = '../uploads/covers/';

            // Create directory if it doesn't exist
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }

            $upload_path = $upload_dir . $file_name;

            if (move_uploaded_file($_FILES['cover_image']['tmp_name'], $upload_path)) {
                $cover_image = 'uploads/covers/' . $file_name;
            } else {
                $errors[] = 'Failed to upload cover image';
            }
        } else {
            $errors[] = 'Invalid file type. Only JPEG, PNG, and GIF are allowed';
        }
    }

    // If no errors, insert book into database
    if (empty($errors)) {
        $query = "INSERT INTO books (isbn, title, author, category, publication_year, publisher,
                                    quantity, available_quantity, shelf_location, description, cover_image)
                  VALUES (:isbn, :title, :author, :category, :publication_year, :publisher,
                         :quantity, :available_quantity, :shelf_location, :description, :cover_image)";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':isbn', $isbn);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':author', $author);
        $stmt->bindParam(':category', $category);
        $stmt->bindParam(':publication_year', $publication_year);
        $stmt->bindParam(':publisher', $publisher);
        $stmt->bindParam(':quantity', $quantity);
        $stmt->bindParam(':available_quantity', $available_quantity);
        $stmt->bindParam(':shelf_location', $shelf_location);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':cover_image', $cover_image);

        if ($stmt->execute()) {
            setMessage('Book added successfully', 'success');
            redirect('index.php');
        } else {
            $errors[] = 'Failed to add book';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Book - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .cover-preview {
            max-height: 300px;
            max-width: 100%;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }
        .cover-preview-container {
            text-align: center;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin-bottom: 20px;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .isbn-lookup-btn {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
        .loading-spinner {
            display: none;
            margin-right: 5px;
        }
        .category-badge {
            display: inline-block;
            margin-right: 5px;
            margin-bottom: 5px;
            cursor: pointer;
        }
        .form-floating {
            margin-bottom: 15px;
        }
        .form-floating > label {
            padding-left: 12px;
        }
        .required-field::after {
            content: " *";
            color: red;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Add New Book</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="index.php" class="btn btn-sm btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Books
                        </a>
                    </div>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <ul class="nav nav-tabs card-header-tabs" id="bookTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="manual-tab" data-bs-toggle="tab" data-bs-target="#manual" type="button" role="tab" aria-controls="manual" aria-selected="true">
                                    <i class="bi bi-pencil me-1"></i> Manual Entry
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="isbn-tab" data-bs-toggle="tab" data-bs-target="#isbn" type="button" role="tab" aria-controls="isbn" aria-selected="false">
                                    <i class="bi bi-upc-scan me-1"></i> ISBN Lookup
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="bulk-tab" data-bs-toggle="tab" data-bs-target="#bulk" type="button" role="tab" aria-controls="bulk" aria-selected="false">
                                    <i class="bi bi-file-earmark-spreadsheet me-1"></i> Bulk Import
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="bookTabsContent">
                            <!-- Manual Entry Tab -->
                            <div class="tab-pane fade show active" id="manual" role="tabpanel" aria-labelledby="manual-tab">
                                <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" method="post" enctype="multipart/form-data" id="addBookForm">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="input-group mb-3">
                                                        <input type="text" class="form-control" id="isbn" name="isbn" value="<?php echo $isbn; ?>" placeholder="ISBN">
                                                        <button class="btn btn-outline-secondary isbn-lookup-btn" type="button" id="isbnLookupBtn">
                                                            <span class="spinner-border spinner-border-sm loading-spinner" id="isbnSpinner" role="status" aria-hidden="true"></span>
                                                            <i class="bi bi-search"></i> Lookup
                                                        </button>
                                                    </div>
                                                    <small class="text-muted mb-3 d-block">Enter ISBN-10 or ISBN-13 for automatic lookup</small>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating mb-3">
                                                        <input type="text" class="form-control" id="title" name="title" value="<?php echo $title; ?>" placeholder="Book Title" required>
                                                        <label for="title" class="required-field">Title</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating mb-3">
                                                        <input type="text" class="form-control" id="author" name="author" value="<?php echo $author; ?>" placeholder="Author Name" required>
                                                        <label for="author" class="required-field">Author</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating mb-3">
                                                        <input type="text" class="form-control" id="publisher" name="publisher" value="<?php echo $publisher; ?>" placeholder="Publisher">
                                                        <label for="publisher">Publisher</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating mb-3">
                                                        <input type="number" class="form-control" id="publication_year" name="publication_year" value="<?php echo $publication_year; ?>" min="1000" max="<?php echo date('Y'); ?>" placeholder="Publication Year">
                                                        <label for="publication_year">Publication Year</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating mb-3">
                                                        <input type="text" class="form-control" id="category" name="category" value="<?php echo $category; ?>" placeholder="Category">
                                                        <label for="category">Category</label>
                                                    </div>
                                                    <div id="categoryTags" class="mb-3">
                                                        <span class="badge bg-primary category-badge" data-category="Fiction">Fiction</span>
                                                        <span class="badge bg-success category-badge" data-category="Non-Fiction">Non-Fiction</span>
                                                        <span class="badge bg-info category-badge" data-category="Science">Science</span>
                                                        <span class="badge bg-warning text-dark category-badge" data-category="History">History</span>
                                                        <span class="badge bg-danger category-badge" data-category="Biography">Biography</span>
                                                        <span class="badge bg-secondary category-badge" data-category="Reference">Reference</span>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating mb-3">
                                                        <input type="number" class="form-control" id="quantity" name="quantity" value="<?php echo $quantity; ?>" min="1" placeholder="Quantity" required>
                                                        <label for="quantity" class="required-field">Quantity</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating mb-3">
                                                        <input type="text" class="form-control" id="shelf_location" name="shelf_location" value="<?php echo $shelf_location; ?>" placeholder="Shelf Location">
                                                        <label for="shelf_location">Shelf Location</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="form-floating mb-3">
                                                        <textarea class="form-control" id="description" name="description" style="height: 100px" placeholder="Description"><?php echo $description; ?></textarea>
                                                        <label for="description">Description</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="cover-preview-container" id="coverPreviewContainer">
                                                <i class="bi bi-book fs-1 text-secondary" id="defaultCoverIcon"></i>
                                                <img id="coverPreview" class="cover-preview" style="display: none;" alt="Book Cover Preview">
                                            </div>
                                            <div class="mb-3">
                                                <label for="cover_image" class="form-label">Cover Image</label>
                                                <input type="file" class="form-control" id="cover_image" name="cover_image" accept="image/jpeg,image/png,image/gif">
                                                <small class="text-muted">Accepted formats: JPEG, PNG, GIF</small>
                                            </div>
                                            <input type="hidden" id="cover_image_url" name="cover_image_url" value="">
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="bi bi-plus-circle me-1"></i> Add Book
                                            </button>
                                            <a href="index.php" class="btn btn-secondary">
                                                <i class="bi bi-x-circle me-1"></i> Cancel
                                            </a>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- ISBN Lookup Tab -->
                            <div class="tab-pane fade" id="isbn" role="tabpanel" aria-labelledby="isbn-tab">
                                <div class="row justify-content-center">
                                    <div class="col-md-8">
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle me-2"></i> Enter an ISBN to automatically fetch book details from online databases.
                                        </div>
                                        <div class="input-group mb-3">
                                            <input type="text" class="form-control form-control-lg" id="isbnLookupInput" placeholder="Enter ISBN-10 or ISBN-13">
                                            <button class="btn btn-primary" type="button" id="isbnLookupButton">
                                                <span class="spinner-border spinner-border-sm loading-spinner" id="isbnLookupSpinner" role="status" aria-hidden="true"></span>
                                                <i class="bi bi-search"></i> Search
                                            </button>
                                        </div>
                                        <div id="isbnResults" class="mt-4"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Bulk Import Tab -->
                            <div class="tab-pane fade" id="bulk" role="tabpanel" aria-labelledby="bulk-tab">
                                <div class="row justify-content-center">
                                    <div class="col-md-8">
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle me-2"></i> Upload a CSV file to import multiple books at once.
                                            <a href="../templates/book_import_template.csv" class="alert-link">Download template</a>
                                        </div>
                                        <form action="import.php" method="post" enctype="multipart/form-data">
                                            <div class="mb-3">
                                                <label for="csvFile" class="form-label">CSV File</label>
                                                <input type="file" class="form-control" id="csvFile" name="csvFile" accept=".csv" required>
                                            </div>
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="headerRow" name="headerRow" checked>
                                                <label class="form-check-label" for="headerRow">
                                                    First row contains column headers
                                                </label>
                                            </div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="bi bi-upload me-1"></i> Upload and Import
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Cover image preview
            const coverImageInput = document.getElementById('cover_image');
            const coverPreview = document.getElementById('coverPreview');
            const defaultCoverIcon = document.getElementById('defaultCoverIcon');

            coverImageInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        coverPreview.src = e.target.result;
                        coverPreview.style.display = 'block';
                        defaultCoverIcon.style.display = 'none';
                    }

                    reader.readAsDataURL(this.files[0]);
                } else {
                    coverPreview.style.display = 'none';
                    defaultCoverIcon.style.display = 'block';
                }
            });

            // Category tags
            const categoryTags = document.querySelectorAll('.category-badge');
            const categoryInput = document.getElementById('category');

            categoryTags.forEach(tag => {
                tag.addEventListener('click', function() {
                    categoryInput.value = this.getAttribute('data-category');
                });
            });

            // ISBN lookup in manual entry tab
            const isbnLookupBtn = document.getElementById('isbnLookupBtn');
            const isbnInput = document.getElementById('isbn');
            const isbnSpinner = document.getElementById('isbnSpinner');

            isbnLookupBtn.addEventListener('click', function() {
                const isbn = isbnInput.value.trim();
                if (isbn) {
                    lookupBookByISBN(isbn, true);
                } else {
                    alert('Please enter an ISBN');
                }
            });

            // ISBN lookup in dedicated tab
            const isbnLookupButton = document.getElementById('isbnLookupButton');
            const isbnLookupInput = document.getElementById('isbnLookupInput');
            const isbnLookupSpinner = document.getElementById('isbnLookupSpinner');
            const isbnResults = document.getElementById('isbnResults');

            isbnLookupButton.addEventListener('click', function() {
                const isbn = isbnLookupInput.value.trim();
                if (isbn) {
                    isbnLookupSpinner.style.display = 'inline-block';

                    // Simulate API call (replace with actual API in production)
                    setTimeout(function() {
                        isbnLookupSpinner.style.display = 'none';

                        // Display sample result (replace with actual API response)
                        const sampleBook = {
                            title: 'Sample Book Title',
                            author: 'John Doe',
                            publisher: 'Sample Publisher',
                            publicationYear: '2022',
                            description: 'This is a sample book description.',
                            coverUrl: 'https://via.placeholder.com/150x200'
                        };

                        displayIsbnResult(sampleBook);
                    }, 1500);
                } else {
                    alert('Please enter an ISBN');
                }
            });

            function displayIsbnResult(book) {
                isbnResults.innerHTML = `
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 text-center">
                                    <img src="${book.coverUrl}" alt="${book.title}" class="img-fluid mb-3" style="max-height: 200px;">
                                </div>
                                <div class="col-md-9">
                                    <h4>${book.title}</h4>
                                    <p class="text-muted">By ${book.author}</p>
                                    <p><strong>Publisher:</strong> ${book.publisher}</p>
                                    <p><strong>Publication Year:</strong> ${book.publicationYear}</p>
                                    <p>${book.description}</p>
                                    <button type="button" class="btn btn-primary" onclick="useThisBook()">
                                        <i class="bi bi-plus-circle me-1"></i> Use This Book
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            // Function to lookup book by ISBN
            function lookupBookByISBN(isbn, isManualEntry = false) {
                if (isManualEntry) {
                    isbnSpinner.style.display = 'inline-block';
                }

                // Simulate API call (replace with actual API in production)
                setTimeout(function() {
                    if (isManualEntry) {
                        isbnSpinner.style.display = 'none';
                    }

                    // Sample data (replace with actual API response)
                    const bookData = {
                        title: 'The Great Gatsby',
                        author: 'F. Scott Fitzgerald',
                        publisher: 'Scribner',
                        publication_year: '1925',
                        category: 'Fiction',
                        description: 'The Great Gatsby is a 1925 novel by American writer F. Scott Fitzgerald. Set in the Jazz Age on Long Island, near New York City, the novel depicts first-person narrator Nick Carraway\'s interactions with mysterious millionaire Jay Gatsby and Gatsby\'s obsession to reunite with his former lover, Daisy Buchanan.',
                        cover_url: 'https://via.placeholder.com/150x200'
                    };

                    if (isManualEntry) {
                        // Fill the form with the retrieved data
                        document.getElementById('title').value = bookData.title;
                        document.getElementById('author').value = bookData.author;
                        document.getElementById('publisher').value = bookData.publisher;
                        document.getElementById('publication_year').value = bookData.publication_year;
                        document.getElementById('category').value = bookData.category;
                        document.getElementById('description').value = bookData.description;

                        // Set cover image
                        if (bookData.cover_url) {
                            document.getElementById('cover_image_url').value = bookData.cover_url;
                            coverPreview.src = bookData.cover_url;
                            coverPreview.style.display = 'block';
                            defaultCoverIcon.style.display = 'none';
                        }
                    }
                }, 1500);
            }

            // Make the function available globally
            window.useThisBook = function() {
                // Get the book data from the results
                const title = document.querySelector('#isbnResults h4').textContent;
                const authorText = document.querySelector('#isbnResults .text-muted').textContent;
                const author = authorText.replace('By ', '');
                const publisherText = document.querySelector('#isbnResults p:nth-of-type(1)').textContent;
                const publisher = publisherText.replace('Publisher: ', '');
                const yearText = document.querySelector('#isbnResults p:nth-of-type(2)').textContent;
                const year = yearText.replace('Publication Year: ', '');
                const description = document.querySelector('#isbnResults p:nth-of-type(3)').textContent;
                const coverUrl = document.querySelector('#isbnResults img').src;

                // Switch to manual entry tab
                document.getElementById('manual-tab').click();

                // Fill the form
                document.getElementById('title').value = title;
                document.getElementById('author').value = author;
                document.getElementById('publisher').value = publisher;
                document.getElementById('publication_year').value = year;
                document.getElementById('description').value = description;
                document.getElementById('cover_image_url').value = coverUrl;

                // Set cover image
                coverPreview.src = coverUrl;
                coverPreview.style.display = 'block';
                defaultCoverIcon.style.display = 'none';

                // Set ISBN
                document.getElementById('isbn').value = document.getElementById('isbnLookupInput').value;
            };
        });
    </script>
</body>
</html>
