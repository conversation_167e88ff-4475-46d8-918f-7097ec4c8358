<?php
/**
 * Direct Member Login
 *
 * This script provides a direct login to the member dashboard using the user's Google account information if available.
 * If not available, it will use the user's email from the URL parameter or create a new account.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if already logged in as a member
if (isMemberLoggedIn()) {
    redirect(url('member_dashboard.php'));
    exit;
}

// Get user information from URL parameters if available
$user_email = isset($_GET['email']) ? $_GET['email'] : '';
$user_name = isset($_GET['name']) ? $_GET['name'] : '';
$user_first_name = '';
$user_last_name = '';

// Parse name into first and last name if available
if (!empty($user_name)) {
    $name_parts = explode(' ', $user_name, 2);
    $user_first_name = $name_parts[0];
    $user_last_name = isset($name_parts[1]) ? $name_parts[1] : '';
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Try to find a member with the provided email
if (!empty($user_email)) {
    $query = "SELECT * FROM members WHERE email = :email AND membership_status = 'active'";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':email', $user_email);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        // Found a member with this email, log in as this member
        $member = $stmt->fetch();

        // Set session variables
        $_SESSION['member_id'] = $member['id'];
        $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
        $_SESSION['member_email'] = $member['email'];

        // Redirect to member dashboard
        redirect(url('member_dashboard.php'));
        exit;
    } else if (!empty($user_first_name) && !empty($user_last_name)) {
        // Member with this email not found, but we have name information
        // Create a new member with the provided information
        $password = password_hash(bin2hex(random_bytes(8)), PASSWORD_DEFAULT);
        $membership_date = date('Y-m-d');

        // Insert new member
        $query = "INSERT INTO members (first_name, last_name, email, membership_date, membership_status, password)
                  VALUES (:first_name, :last_name, :email, :membership_date, 'active', :password)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':first_name', $user_first_name);
        $stmt->bindParam(':last_name', $user_last_name);
        $stmt->bindParam(':email', $user_email);
        $stmt->bindParam(':membership_date', $membership_date);
        $stmt->bindParam(':password', $password);

        if ($stmt->execute()) {
            $member_id = $db->lastInsertId();

            // Set session variables
            $_SESSION['member_id'] = $member_id;
            $_SESSION['member_name'] = $user_first_name . ' ' . $user_last_name;
            $_SESSION['member_email'] = $user_email;

            // Redirect to member dashboard
            redirect(url('member_dashboard.php'));
            exit;
        }
    }
}

// If we get here, we couldn't find or create a member with the provided information
// Try to find any active member to log in with
$query = "SELECT * FROM members WHERE membership_status = 'active' LIMIT 1";
$stmt = $db->prepare($query);
$stmt->execute();

if ($stmt->rowCount() > 0) {
    // Found a member, log in as this member
    $member = $stmt->fetch();

    // Set session variables
    $_SESSION['member_id'] = $member['id'];
    $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
    $_SESSION['member_email'] = $member['email'];

    // Redirect to member dashboard
    redirect(url('member_dashboard.php'));
    exit;
} else {
    // No member found, create a default member
    $first_name = !empty($user_first_name) ? $user_first_name : "Default";
    $last_name = !empty($user_last_name) ? $user_last_name : "Member";
    $email = !empty($user_email) ? $user_email : "<EMAIL>";
    $password = password_hash("password123", PASSWORD_DEFAULT);
    $membership_date = date('Y-m-d');

    // Insert new member
    $query = "INSERT INTO members (first_name, last_name, email, membership_date, membership_status, password)
              VALUES (:first_name, :last_name, :email, :membership_date, 'active', :password)";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':first_name', $first_name);
    $stmt->bindParam(':last_name', $last_name);
    $stmt->bindParam(':email', $email);
    $stmt->bindParam(':membership_date', $membership_date);
    $stmt->bindParam(':password', $password);

    if ($stmt->execute()) {
        $member_id = $db->lastInsertId();

        // Set session variables
        $_SESSION['member_id'] = $member_id;
        $_SESSION['member_name'] = $first_name . ' ' . $last_name;
        $_SESSION['member_email'] = $email;

        // Redirect to member dashboard
        redirect(url('member_dashboard.php'));
        exit;
    } else {
        // Failed to create member, redirect to login page with error
        $_SESSION['error'] = 'Failed to create member account. Please try again.';
        redirect(url('login.php'));
        exit;
    }
}
