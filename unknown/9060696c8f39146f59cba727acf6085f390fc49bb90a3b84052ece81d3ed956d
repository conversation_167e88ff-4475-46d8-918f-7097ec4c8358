<?php
/**
 * Fix Login System
 * This script fixes common login issues and ensures the system is functional
 */

session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Fix Login System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .fix-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .button { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>";

echo "<h1>Login System Fix</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<div class='fix-section'>";
    echo "<h2>Fix 1: Ensure Database Tables Exist</h2>";
    
    // Check and create users table
    $stmt = $db->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>Creating users table...</p>";
        $sql = "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(100) NOT NULL UNIQUE,
            role ENUM('admin', 'librarian') NOT NULL,
            full_name VARCHAR(100) NULL,
            remember_token VARCHAR(255) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $db->exec($sql);
        echo "<p class='success'>✅ Users table created</p>";
    } else {
        echo "<p class='success'>✅ Users table exists</p>";
    }
    
    // Check and create members table
    $stmt = $db->query("SHOW TABLES LIKE 'members'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>Creating members table...</p>";
        $sql = "CREATE TABLE members (
            id INT AUTO_INCREMENT PRIMARY KEY,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NULL,
            phone VARCHAR(20),
            address TEXT,
            membership_date DATE NOT NULL,
            membership_status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            remember_token VARCHAR(255) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $db->exec($sql);
        echo "<p class='success'>✅ Members table created</p>";
    } else {
        echo "<p class='success'>✅ Members table exists</p>";
    }
    echo "</div>";
    
    echo "<div class='fix-section'>";
    echo "<h2>Fix 2: Ensure Required Columns Exist</h2>";
    
    // Check if remember_token column exists in users table
    $stmt = $db->query("SHOW COLUMNS FROM users LIKE 'remember_token'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>Adding remember_token column to users table...</p>";
        $db->exec("ALTER TABLE users ADD COLUMN remember_token VARCHAR(255) NULL");
        echo "<p class='success'>✅ remember_token column added to users table</p>";
    }
    
    // Check if full_name column exists in users table
    $stmt = $db->query("SHOW COLUMNS FROM users LIKE 'full_name'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>Adding full_name column to users table...</p>";
        $db->exec("ALTER TABLE users ADD COLUMN full_name VARCHAR(100) NULL");
        echo "<p class='success'>✅ full_name column added to users table</p>";
    }
    
    // Check if password column exists in members table
    $stmt = $db->query("SHOW COLUMNS FROM members LIKE 'password'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>Adding password column to members table...</p>";
        $db->exec("ALTER TABLE members ADD COLUMN password VARCHAR(255) NULL");
        echo "<p class='success'>✅ password column added to members table</p>";
    }
    
    // Check if remember_token column exists in members table
    $stmt = $db->query("SHOW COLUMNS FROM members LIKE 'remember_token'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>Adding remember_token column to members table...</p>";
        $db->exec("ALTER TABLE members ADD COLUMN remember_token VARCHAR(255) NULL");
        echo "<p class='success'>✅ remember_token column added to members table</p>";
    }
    echo "</div>";
    
    echo "<div class='fix-section'>";
    echo "<h2>Fix 3: Create Default Admin User</h2>";
    
    $stmt = $db->query("SELECT COUNT(*) FROM users WHERE role = 'admin'");
    $adminCount = $stmt->fetchColumn();
    
    if ($adminCount == 0) {
        echo "<p class='warning'>Creating default admin user...</p>";
        $username = 'admin';
        $email = '<EMAIL>';
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        $role = 'admin';
        
        $stmt = $db->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->execute([$username, $email, $password, $role]);
        echo "<p class='success'>✅ Default admin created - Username: admin, Password: admin123</p>";
    } else {
        echo "<p class='success'>✅ Admin user exists</p>";
    }
    echo "</div>";
    
    echo "<div class='fix-section'>";
    echo "<h2>Fix 4: Create Sample Member with Password</h2>";
    
    $stmt = $db->query("SELECT COUNT(*) FROM members WHERE email = '<EMAIL>'");
    $memberCount = $stmt->fetchColumn();
    
    if ($memberCount == 0) {
        echo "<p class='warning'>Creating sample member...</p>";
        $password = password_hash('member123', PASSWORD_DEFAULT);
        $stmt = $db->prepare("INSERT INTO members (first_name, last_name, email, password, phone, address, membership_date) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute(['John', 'Doe', '<EMAIL>', $password, '************', '123 Main St', date('Y-m-d')]);
        echo "<p class='success'>✅ Sample member created - Email: <EMAIL>, Password: member123</p>";
    } else {
        // Check if member has password
        $stmt = $db->query("SELECT password FROM members WHERE email = '<EMAIL>'");
        $member = $stmt->fetch();
        if (empty($member['password'])) {
            echo "<p class='warning'>Setting password for existing member...</p>";
            $password = password_hash('member123', PASSWORD_DEFAULT);
            $stmt = $db->prepare("UPDATE members SET password = ? WHERE email = '<EMAIL>'");
            $stmt->execute([$password]);
            echo "<p class='success'>✅ Password set for member - Email: <EMAIL>, Password: member123</p>";
        } else {
            echo "<p class='success'>✅ Sample member exists with password</p>";
        }
    }
    echo "</div>";
    
    echo "<div class='fix-section'>";
    echo "<h2>Fix 5: Test Login Functionality</h2>";
    
    // Test admin login
    $stmt = $db->prepare("SELECT id, username, password, role FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin && password_verify('admin123', $admin['password'])) {
        echo "<p class='success'>✅ Admin login test passed</p>";
    } else {
        echo "<p class='error'>❌ Admin login test failed</p>";
    }
    
    // Test member login
    $stmt = $db->prepare("SELECT id, email, password FROM members WHERE email = '<EMAIL>'");
    $stmt->execute();
    $member = $stmt->fetch();
    
    if ($member && password_verify('member123', $member['password'])) {
        echo "<p class='success'>✅ Member login test passed</p>";
    } else {
        echo "<p class='error'>❌ Member login test failed</p>";
    }
    echo "</div>";
    
    echo "<div class='fix-section'>";
    echo "<h2>Login System Status</h2>";
    echo "<p class='success'><strong>✅ Login system is now functional!</strong></p>";
    echo "<p class='info'><strong>Test Credentials:</strong></p>";
    echo "<p class='info'>Admin: admin / admin123</p>";
    echo "<p class='info'>Member: <EMAIL> / member123</p>";
    echo "<p><a href='login.php' class='button'>Go to Login Page</a></p>";
    echo "<p><a href='test_login.php' class='button'>Run Login Tests</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='fix-section'>";
    echo "<h2>Error</h2>";
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</body></html>";
?>
