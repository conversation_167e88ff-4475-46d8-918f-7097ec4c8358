<?php
/**
 * Simple Logout Fix
 * This file provides a working logout solution
 */

session_start();

// Clear all session variables
$_SESSION = array();

// Destroy the session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy the session
session_destroy();

// Clear any remember me cookies
if (isset($_COOKIE['remember_user'])) {
    setcookie('remember_user', '', time() - 3600, '/');
}

if (isset($_COOKIE['member_email'])) {
    setcookie('member_email', '', time() - 3600, '/');
}

// Redirect to logout success page
header('Location: logout_success.php');
exit;
?>
