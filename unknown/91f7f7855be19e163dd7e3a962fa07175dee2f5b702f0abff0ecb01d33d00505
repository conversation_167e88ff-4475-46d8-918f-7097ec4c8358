<?php
session_start();

// Try to include required files with error handling
try {
    require_once 'config/database.php';
    require_once 'config/config.php';
    require_once 'includes/functions.php';

    // Redirect based on user type
    if (isLoggedIn()) {
        // Staff is logged in, redirect based on role
        if (isAdmin()) {
            header('Location: admin/dashboard.php');
            exit;
        } elseif (isLibrarian()) {
            header('Location: librarian/dashboard.php');
            exit;
        }
        // If role is neither admin nor librarian, continue to this page
    } elseif (isMemberLoggedIn()) {
        // Member is logged in, redirect to member dashboard
        header('Location: member_dashboard.php');
        exit;
    } else {
        // No one is logged in, redirect to public home page
        header('Location: home.php');
        exit;
    }
} catch (Exception $e) {
    // If there's an error, redirect to access page for system setup
    header("Location: access.php");
    exit;
}
?>
