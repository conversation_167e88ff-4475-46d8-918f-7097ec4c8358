<?php
/**
 * Member Statistics Dashboard
 */
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and has appropriate role
if (!isset($_SESSION['user_id']) && !isset($_SESSION['member_id'])) {
    redirect(url('login.php'));
}

// Check if user has permission (admin or librarian)
if (isset($_SESSION['role']) && !in_array($_SESSION['role'], ['admin', 'librarian'])) {
    redirect(url('index.php'));
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Get member statistics
$stats = [];

// Total members
$query = "SELECT COUNT(*) as total FROM members";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['total_members'] = $stmt->fetch()['total'];

// Active members
$query = "SELECT COUNT(*) as total FROM members WHERE membership_status = 'active'";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['active_members'] = $stmt->fetch()['total'];

// Inactive members
$query = "SELECT COUNT(*) as total FROM members WHERE membership_status = 'inactive'";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['inactive_members'] = $stmt->fetch()['total'];

// Suspended members
$query = "SELECT COUNT(*) as total FROM members WHERE membership_status = 'suspended'";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['suspended_members'] = $stmt->fetch()['total'];

// Members with current loans
$query = "SELECT COUNT(DISTINCT member_id) as total FROM book_loans WHERE return_date IS NULL";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['members_with_loans'] = $stmt->fetch()['total'];

// Members with overdue books
$query = "SELECT COUNT(DISTINCT member_id) as total FROM book_loans WHERE return_date IS NULL AND due_date < CURDATE()";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['members_with_overdue'] = $stmt->fetch()['total'];

// New members this month
$query = "SELECT COUNT(*) as total FROM members WHERE MONTH(membership_date) = MONTH(CURDATE()) AND YEAR(membership_date) = YEAR(CURDATE())";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['new_this_month'] = $stmt->fetch()['total'];

// Most active members (by loan count)
$query = "SELECT m.id, m.first_name, m.last_name, m.email, COUNT(bl.id) as loan_count
          FROM members m
          LEFT JOIN book_loans bl ON m.id = bl.member_id
          GROUP BY m.id
          ORDER BY loan_count DESC
          LIMIT 10";
$stmt = $db->prepare($query);
$stmt->execute();
$most_active_members = $stmt->fetchAll();

// Recent new members
$query = "SELECT * FROM members ORDER BY membership_date DESC LIMIT 10";
$stmt = $db->prepare($query);
$stmt->execute();
$recent_members = $stmt->fetchAll();

// Members by status for chart
$query = "SELECT membership_status, COUNT(*) as count FROM members GROUP BY membership_status";
$stmt = $db->prepare($query);
$stmt->execute();
$status_distribution = $stmt->fetchAll();

// Monthly registration trend (last 12 months)
$query = "SELECT 
            DATE_FORMAT(membership_date, '%Y-%m') as month,
            COUNT(*) as count
          FROM members 
          WHERE membership_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
          GROUP BY DATE_FORMAT(membership_date, '%Y-%m')
          ORDER BY month";
$stmt = $db->prepare($query);
$stmt->execute();
$monthly_registrations = $stmt->fetchAll();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Statistics - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Member Statistics</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="index.php" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="bi bi-arrow-left"></i> Back to Members
                        </a>
                        <a href="bulk_operations.php" class="btn btn-sm btn-primary">
                            <i class="bi bi-gear"></i> Bulk Operations
                        </a>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0"><?php echo number_format($stats['total_members']); ?></h4>
                                        <p class="mb-0">Total Members</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-people fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0"><?php echo number_format($stats['active_members']); ?></h4>
                                        <p class="mb-0">Active Members</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-person-check fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0"><?php echo number_format($stats['members_with_loans']); ?></h4>
                                        <p class="mb-0">With Current Loans</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-book fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0"><?php echo number_format($stats['members_with_overdue']); ?></h4>
                                        <p class="mb-0">With Overdue Books</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-exclamation-triangle fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Stats Row -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card border-secondary">
                            <div class="card-body text-center">
                                <h5 class="card-title"><?php echo number_format($stats['inactive_members']); ?></h5>
                                <p class="card-text text-muted">Inactive Members</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <h5 class="card-title"><?php echo number_format($stats['suspended_members']); ?></h5>
                                <p class="card-text text-muted">Suspended Members</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h5 class="card-title"><?php echo number_format($stats['new_this_month']); ?></h5>
                                <p class="card-text text-muted">New This Month</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h5 class="card-title"><?php echo number_format(($stats['active_members'] / max($stats['total_members'], 1)) * 100, 1); ?>%</h5>
                                <p class="card-text text-muted">Active Rate</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Member Status Distribution</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="statusChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Monthly Registrations (Last 12 Months)</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="registrationChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tables Row -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Most Active Members</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Loans</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($most_active_members as $member): ?>
                                            <tr>
                                                <td>
                                                    <a href="view.php?id=<?php echo $member['id']; ?>">
                                                        <?php echo h($member['first_name'] . ' ' . $member['last_name']); ?>
                                                    </a>
                                                </td>
                                                <td><?php echo h($member['email']); ?></td>
                                                <td><span class="badge bg-primary"><?php echo $member['loan_count']; ?></span></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Recent New Members</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Joined</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_members as $member): ?>
                                            <tr>
                                                <td>
                                                    <a href="view.php?id=<?php echo $member['id']; ?>">
                                                        <?php echo h($member['first_name'] . ' ' . $member['last_name']); ?>
                                                    </a>
                                                </td>
                                                <td><?php echo h($member['email']); ?></td>
                                                <td><?php echo date('M j, Y', strtotime($member['membership_date'])); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Status Distribution Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: [<?php echo "'" . implode("','", array_column($status_distribution, 'membership_status')) . "'"; ?>],
                datasets: [{
                    data: [<?php echo implode(',', array_column($status_distribution, 'count')); ?>],
                    backgroundColor: ['#28a745', '#6c757d', '#dc3545'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Monthly Registration Chart
        const regCtx = document.getElementById('registrationChart').getContext('2d');
        const registrationChart = new Chart(regCtx, {
            type: 'line',
            data: {
                labels: [<?php echo "'" . implode("','", array_column($monthly_registrations, 'month')) . "'"; ?>],
                datasets: [{
                    label: 'New Members',
                    data: [<?php echo implode(',', array_column($monthly_registrations, 'count')); ?>],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
