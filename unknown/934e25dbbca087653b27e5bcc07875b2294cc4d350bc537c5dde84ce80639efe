// Dark mode functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check for saved theme preference or use the system preference
    const savedTheme = localStorage.getItem('theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    // Set initial theme based on saved preference or system preference
    if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {
        document.documentElement.setAttribute('data-bs-theme', 'dark');
        document.getElementById('darkModeIcon').classList.remove('bi-moon');
        document.getElementById('darkModeIcon').classList.add('bi-sun');
    } else {
        document.documentElement.setAttribute('data-bs-theme', 'light');
        document.getElementById('darkModeIcon').classList.remove('bi-sun');
        document.getElementById('darkModeIcon').classList.add('bi-moon');
    }
    
    // Toggle theme when the dark mode button is clicked
    document.getElementById('darkModeToggle').addEventListener('click', function() {
        const currentTheme = document.documentElement.getAttribute('data-bs-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        // Update the theme
        document.documentElement.setAttribute('data-bs-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        // Update the icon
        const icon = document.getElementById('darkModeIcon');
        if (newTheme === 'dark') {
            icon.classList.remove('bi-moon');
            icon.classList.add('bi-sun');
        } else {
            icon.classList.remove('bi-sun');
            icon.classList.add('bi-moon');
        }
    });
});

// Add dark mode specific styles
const darkModeStyles = `
    [data-bs-theme="dark"] {
        --bs-body-bg: #121212;
        --bs-body-color: #e0e0e0;
    }
    
    [data-bs-theme="dark"] .card {
        background-color: #1e1e1e;
        border-color: #333;
    }
    
    [data-bs-theme="dark"] .navbar-dark {
        background-color: #1a1a1a !important;
    }
    
    [data-bs-theme="dark"] .bg-light {
        background-color: #1e1e1e !important;
    }
    
    [data-bs-theme="dark"] .text-dark {
        color: #e0e0e0 !important;
    }
    
    [data-bs-theme="dark"] .border {
        border-color: #333 !important;
    }
    
    [data-bs-theme="dark"] .modal-content {
        background-color: #1e1e1e;
        border-color: #333;
    }
    
    [data-bs-theme="dark"] .form-control {
        background-color: #2d2d2d;
        border-color: #444;
        color: #e0e0e0;
    }
    
    [data-bs-theme="dark"] .form-control:focus {
        background-color: #2d2d2d;
        color: #e0e0e0;
    }
    
    [data-bs-theme="dark"] .btn-outline-dark {
        color: #e0e0e0;
        border-color: #e0e0e0;
    }
    
    [data-bs-theme="dark"] .btn-outline-dark:hover {
        background-color: #e0e0e0;
        color: #121212;
    }
    
    [data-bs-theme="dark"] .accordion-button {
        background-color: #1e1e1e;
        color: #e0e0e0;
    }
    
    [data-bs-theme="dark"] .accordion-button:not(.collapsed) {
        background-color: #2d2d2d;
        color: #e0e0e0;
    }
    
    [data-bs-theme="dark"] .accordion-body {
        background-color: #1e1e1e;
        color: #e0e0e0;
    }
`;

// Add the styles to the document
const styleElement = document.createElement('style');
styleElement.textContent = darkModeStyles;
document.head.appendChild(styleElement);
