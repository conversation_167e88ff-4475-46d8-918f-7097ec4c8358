<?php
/**
 * Centralized Dashboard Statistics Service
 * Provides standardized calculations for all dashboards
 * Ensures consistency across admin, librarian, and analytics dashboards
 */

class DashboardStatsService {
    private $db;
    
    public function __construct($database_connection) {
        $this->db = $database_connection;
    }
    
    /**
     * Get all basic statistics
     */
    public function getBasicStats() {
        $stats = [];
        
        // 1. BOOK STATISTICS
        $stats['total_books'] = $this->getTotalBooks();
        $stats['total_copies'] = $this->getTotalCopies();
        $stats['available_books'] = $this->getAvailableCopies();
        
        // 2. MEMBER STATISTICS
        $stats['total_members'] = $this->getTotalMembers();
        
        // 3. LOAN STATISTICS
        $stats['active_loans'] = $this->getActiveLoans();
        $stats['overdue_books'] = $this->getOverdueBooks();
        $stats['returned_loans'] = $this->getReturnedLoans();
        $stats['total_loans'] = $this->getTotalLoans();
        
        // 4. CALCULATED METRICS
        $stats['total_current_loans'] = $stats['active_loans'] + $stats['overdue_books'];
        $stats['utilization_rate'] = $this->calculateUtilizationRate($stats);
        $stats['availability_rate'] = $this->calculateAvailabilityRate($stats);
        
        return $stats;
    }
    
    /**
     * Get member analytics
     */
    public function getMemberAnalytics() {
        $analytics = [];
        
        $analytics['currently_borrowing'] = $this->getCurrentlyBorrowingMembers();
        $analytics['members_with_overdue'] = $this->getMembersWithOverdue();
        $analytics['members_who_returned'] = $this->getMembersWhoReturned();
        $analytics['members_who_borrowed'] = $this->getMembersWhoBorrowed();
        $analytics['members_never_borrowed'] = $this->getMembersNeverBorrowed();
        $analytics['members_with_fines'] = $this->getMembersWithFines();
        $analytics['new_members_30_days'] = $this->getNewMembers(30);
        $analytics['new_members_7_days'] = $this->getNewMembers(7);
        
        return $analytics;
    }
    
    /**
     * Get financial statistics
     */
    public function getFinancialStats() {
        $stats = [];
        
        $stmt = $this->db->prepare("SELECT SUM(fine) as total FROM book_loans WHERE fine > 0");
        $stmt->execute();
        $stats['total_fines'] = $stmt->fetch()['total'] ?? 0;
        
        $stats['members_with_fines'] = $this->getMembersWithFines();
        $stats['avg_fine_per_member'] = $stats['members_with_fines'] > 0 ? 
            round($stats['total_fines'] / $stats['members_with_fines'], 2) : 0;
            
        return $stats;
    }
    
    // PRIVATE METHODS FOR INDIVIDUAL CALCULATIONS
    
    private function getTotalBooks() {
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM books");
        $stmt->execute();
        return $stmt->fetch()['count'];
    }
    
    private function getTotalCopies() {
        $stmt = $this->db->prepare("SELECT SUM(quantity) as count FROM books");
        $stmt->execute();
        return $stmt->fetch()['count'] ?? 0;
    }
    
    private function getAvailableCopies() {
        $stmt = $this->db->prepare("SELECT SUM(available_quantity) as count FROM books");
        $stmt->execute();
        return $stmt->fetch()['count'] ?? 0;
    }
    
    private function getTotalMembers() {
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM members");
        $stmt->execute();
        return $stmt->fetch()['count'];
    }
    
    /**
     * Active loans: Currently borrowed books that are not yet overdue
     */
    private function getActiveLoans() {
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed' AND due_date >= CURDATE()");
        $stmt->execute();
        return $stmt->fetch()['count'];
    }
    
    /**
     * Overdue books: Explicitly marked as overdue OR borrowed past due date
     */
    private function getOverdueBooks() {
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM book_loans WHERE status = 'overdue' OR (status = 'borrowed' AND due_date < CURDATE())");
        $stmt->execute();
        return $stmt->fetch()['count'];
    }
    
    private function getReturnedLoans() {
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM book_loans WHERE status = 'returned'");
        $stmt->execute();
        return $stmt->fetch()['count'];
    }
    
    private function getTotalLoans() {
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM book_loans");
        $stmt->execute();
        return $stmt->fetch()['count'];
    }
    
    /**
     * Members currently borrowing (active loans, not overdue)
     */
    private function getCurrentlyBorrowingMembers() {
        $stmt = $this->db->prepare("SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status = 'borrowed' AND due_date >= CURDATE()");
        $stmt->execute();
        return $stmt->fetch()['count'];
    }
    
    /**
     * Members with overdue books
     */
    private function getMembersWithOverdue() {
        $stmt = $this->db->prepare("SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status = 'overdue' OR (status = 'borrowed' AND due_date < CURDATE())");
        $stmt->execute();
        return $stmt->fetch()['count'];
    }
    
    private function getMembersWhoReturned() {
        $stmt = $this->db->prepare("SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status = 'returned'");
        $stmt->execute();
        return $stmt->fetch()['count'];
    }
    
    private function getMembersWhoBorrowed() {
        $stmt = $this->db->prepare("SELECT COUNT(DISTINCT member_id) as count FROM book_loans");
        $stmt->execute();
        return $stmt->fetch()['count'];
    }
    
    private function getMembersNeverBorrowed() {
        $total_members = $this->getTotalMembers();
        $members_who_borrowed = $this->getMembersWhoBorrowed();
        return $total_members - $members_who_borrowed;
    }
    
    private function getMembersWithFines() {
        $stmt = $this->db->prepare("SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE fine > 0");
        $stmt->execute();
        return $stmt->fetch()['count'];
    }
    
    private function getNewMembers($days) {
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM members WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)");
        $stmt->execute([$days]);
        return $stmt->fetch()['count'];
    }
    
    private function calculateUtilizationRate($stats) {
        return $stats['total_copies'] > 0 ? 
            round(($stats['total_current_loans'] / $stats['total_copies']) * 100, 1) : 0;
    }
    
    private function calculateAvailabilityRate($stats) {
        return $stats['total_copies'] > 0 ? 
            round(($stats['available_books'] / $stats['total_copies']) * 100, 1) : 0;
    }
    
    /**
     * Get comprehensive dashboard data
     */
    public function getAllStats() {
        $basic_stats = $this->getBasicStats();
        $member_analytics = $this->getMemberAnalytics();
        $financial_stats = $this->getFinancialStats();
        
        return array_merge($basic_stats, $member_analytics, $financial_stats);
    }
    
    /**
     * Get formatted statistics with proper labels
     */
    public function getFormattedStats() {
        $stats = $this->getAllStats();
        
        return [
            'books' => [
                'total_titles' => $stats['total_books'],
                'total_copies' => $stats['total_copies'],
                'available_copies' => $stats['available_books'],
                'utilization_rate' => $stats['utilization_rate'],
                'availability_rate' => $stats['availability_rate']
            ],
            'members' => [
                'total_members' => $stats['total_members'],
                'currently_borrowing' => $stats['currently_borrowing'],
                'with_overdue' => $stats['members_with_overdue'],
                'who_returned' => $stats['members_who_returned'],
                'never_borrowed' => $stats['members_never_borrowed'],
                'new_this_month' => $stats['new_members_30_days'],
                'new_this_week' => $stats['new_members_7_days']
            ],
            'loans' => [
                'active_loans' => $stats['active_loans'],
                'overdue_books' => $stats['overdue_books'],
                'returned_loans' => $stats['returned_loans'],
                'total_loans' => $stats['total_loans'],
                'total_current' => $stats['total_current_loans']
            ],
            'financial' => [
                'total_fines' => $stats['total_fines'],
                'members_with_fines' => $stats['members_with_fines'],
                'avg_fine_per_member' => $stats['avg_fine_per_member']
            ]
        ];
    }
}
?>
