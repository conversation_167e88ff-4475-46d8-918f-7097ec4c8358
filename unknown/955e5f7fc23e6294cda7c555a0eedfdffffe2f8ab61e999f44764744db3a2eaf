<?php
/**
 * Testing Dashboard
 * Central hub for all testing and verification tools
 */

session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Quick system status check
$system_status = [
    'database' => false,
    'config' => false,
    'uploads' => false,
    'google_oauth' => false
];

// Check database connection
try {
    $database = new Database();
    $db = $database->getConnection();
    $system_status['database'] = true;
} catch (Exception $e) {
    $system_status['database'] = false;
}

// Check config files
$config_files = ['config/config.php', 'config/database.php', 'includes/functions.php'];
$system_status['config'] = true;
foreach ($config_files as $file) {
    if (!file_exists($file)) {
        $system_status['config'] = false;
        break;
    }
}

// Check uploads directory
$system_status['uploads'] = is_dir('uploads') && is_writable('uploads');

// Check Google OAuth
try {
    require_once 'config/google_oauth.php';
    $system_status['google_oauth'] = defined('GOOGLE_CLIENT_ID') && GOOGLE_CLIENT_ID !== 'YOUR_GOOGLE_CLIENT_ID_HERE';
} catch (Exception $e) {
    $system_status['google_oauth'] = false;
}

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testing Dashboard - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-ok { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .test-card {
            transition: transform 0.2s ease-in-out;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="bi bi-speedometer2 me-3"></i>Testing Dashboard</h1>
                    <p class="lead mb-0">Comprehensive testing and verification tools for your Library Management System</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="card bg-white text-dark">
                        <div class="card-body">
                            <h6 class="card-title">System Status</h6>
                            <div class="d-flex justify-content-between">
                                <small>Database:</small>
                                <span class="status-indicator <?php echo $system_status['database'] ? 'status-ok' : 'status-error'; ?>"></span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small>Config:</small>
                                <span class="status-indicator <?php echo $system_status['config'] ? 'status-ok' : 'status-error'; ?>"></span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small>Uploads:</small>
                                <span class="status-indicator <?php echo $system_status['uploads'] ? 'status-ok' : 'status-error'; ?>"></span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small>Google OAuth:</small>
                                <span class="status-indicator <?php echo $system_status['google_oauth'] ? 'status-ok' : 'status-error'; ?>"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Testing Tools -->
        <div class="row mb-5">
            <div class="col-12">
                <h2><i class="bi bi-tools me-2"></i>Testing Tools</h2>
                <p class="text-muted">Comprehensive testing and verification utilities</p>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card test-card h-100">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-gear-fill text-primary" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title">System Test</h5>
                        <p class="card-text">Comprehensive system functionality test including database, configuration, and core features.</p>
                        <a href="system_test.php" class="btn btn-primary">
                            <i class="bi bi-play-circle me-2"></i>Run System Test
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card test-card h-100">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-database-check text-success" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title">Database Health Check</h5>
                        <p class="card-text">Verify database structure, tables, columns, and data integrity.</p>
                        <a href="database_health_check.php" class="btn btn-success">
                            <i class="bi bi-database me-2"></i>Check Database
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card test-card h-100">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-shield-check text-warning" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title">Login Functionality Test</h5>
                        <p class="card-text">Create test accounts and verify login functionality for all user types.</p>
                        <a href="test_login_functionality.php" class="btn btn-warning">
                            <i class="bi bi-person-check me-2"></i>Test Login
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Access -->
        <div class="row mb-5">
            <div class="col-12">
                <h2><i class="bi bi-lightning me-2"></i>Quick Access</h2>
                <p class="text-muted">Direct access to main system components</p>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card test-card">
                    <div class="card-body text-center">
                        <i class="bi bi-house-door text-info mb-2" style="font-size: 2rem;"></i>
                        <h6 class="card-title">Home Page</h6>
                        <a href="index.php" class="btn btn-outline-info btn-sm">Visit</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card test-card">
                    <div class="card-body text-center">
                        <i class="bi bi-box-arrow-in-right text-primary mb-2" style="font-size: 2rem;"></i>
                        <h6 class="card-title">Login Page</h6>
                        <a href="login.php" class="btn btn-outline-primary btn-sm">Login</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card test-card">
                    <div class="card-body text-center">
                        <i class="bi bi-person-plus text-success mb-2" style="font-size: 2rem;"></i>
                        <h6 class="card-title">Registration</h6>
                        <a href="register.php" class="btn btn-outline-success btn-sm">Register</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card test-card">
                    <div class="card-body text-center">
                        <i class="bi bi-book text-warning mb-2" style="font-size: 2rem;"></i>
                        <h6 class="card-title">Book Catalog</h6>
                        <a href="catalog.php" class="btn btn-outline-warning btn-sm">Browse</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Admin Tools -->
        <div class="row mb-5">
            <div class="col-12">
                <h2><i class="bi bi-shield-lock me-2"></i>Admin Tools</h2>
                <p class="text-muted">Administrative and management interfaces</p>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card test-card">
                    <div class="card-body text-center">
                        <i class="bi bi-speedometer2 text-danger mb-2" style="font-size: 2rem;"></i>
                        <h6 class="card-title">Admin Dashboard</h6>
                        <a href="admin/dashboard.php" class="btn btn-outline-danger btn-sm">Access</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card test-card">
                    <div class="card-body text-center">
                        <i class="bi bi-people text-info mb-2" style="font-size: 2rem;"></i>
                        <h6 class="card-title">Librarian Dashboard</h6>
                        <a href="librarian/dashboard.php" class="btn btn-outline-info btn-sm">Access</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card test-card">
                    <div class="card-body text-center">
                        <i class="bi bi-person-circle text-success mb-2" style="font-size: 2rem;"></i>
                        <h6 class="card-title">Member Dashboard</h6>
                        <a href="member_dashboard.php" class="btn btn-outline-success btn-sm">Access</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- System Information -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-info-circle me-2"></i>System Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>PHP Version</h6>
                                <p><?php echo PHP_VERSION; ?></p>
                                
                                <h6>System Time</h6>
                                <p><?php echo date('Y-m-d H:i:s T'); ?></p>
                            </div>
                            <div class="col-md-6">
                                <h6>Base URL</h6>
                                <p><?php echo defined('BASE_URL') ? BASE_URL : 'Not configured'; ?></p>
                                
                                <h6>Document Root</h6>
                                <p><?php echo $_SERVER['DOCUMENT_ROOT']; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <p class="mb-0">Library Management System Testing Dashboard &copy; <?php echo date('Y'); ?></p>
    </footer>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
