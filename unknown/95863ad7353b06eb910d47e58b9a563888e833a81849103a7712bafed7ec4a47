<?php
/**
 * Email Service for Library Management System
 * Handles sending emails and logging email activities
 */

class EmailService {
    private $db;
    private $from_email;
    private $from_name;
    private $reply_to;
    private $smtp_enabled;
    private $smtp_host;
    private $smtp_port;
    private $smtp_username;
    private $smtp_password;
    private $smtp_secure;
    
    /**
     * Constructor
     * 
     * @param PDO $db Database connection
     */
    public function __construct($db) {
        $this->db = $db;
        
        // Load email settings from database or config
        $this->loadEmailSettings();
    }
    
    /**
     * Load email settings from database or config
     */
    private function loadEmailSettings() {
        // Try to get settings from database
        $query = "SELECT * FROM settings WHERE setting_group = 'email'";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $settings = [];
            while ($row = $stmt->fetch()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
            
            $this->from_email = $settings['from_email'] ?? '<EMAIL>';
            $this->from_name = $settings['from_name'] ?? 'Library Management System';
            $this->reply_to = $settings['reply_to'] ?? '<EMAIL>';
            $this->smtp_enabled = ($settings['smtp_enabled'] ?? 'false') === 'true';
            $this->smtp_host = $settings['smtp_host'] ?? 'localhost';
            $this->smtp_port = (int)($settings['smtp_port'] ?? 25);
            $this->smtp_username = $settings['smtp_username'] ?? '';
            $this->smtp_password = $settings['smtp_password'] ?? '';
            $this->smtp_secure = $settings['smtp_secure'] ?? '';
        } else {
            // Default settings if not in database
            $this->from_email = '<EMAIL>';
            $this->from_name = 'Library Management System';
            $this->reply_to = '<EMAIL>';
            $this->smtp_enabled = false;
            $this->smtp_host = 'localhost';
            $this->smtp_port = 25;
            $this->smtp_username = '';
            $this->smtp_password = '';
            $this->smtp_secure = '';
        }
    }
    
    /**
     * Send an email
     * 
     * @param string $to Recipient email address
     * @param string $subject Email subject
     * @param string $body Email body (HTML)
     * @param string $to_name Recipient name (optional)
     * @param array $attachments Array of file paths to attach (optional)
     * @return bool True if email was sent successfully, false otherwise
     */
    public function sendEmail($to, $subject, $body, $to_name = '', $attachments = []) {
        // Log the email attempt
        $this->logEmail($to, $subject);
        
        // Set up email headers
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: ' . $this->from_name . ' <' . $this->from_email . '>',
            'Reply-To: ' . $this->reply_to,
            'X-Mailer: PHP/' . phpversion()
        ];
        
        // If SMTP is enabled, use PHPMailer (if available)
        if ($this->smtp_enabled && class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
            return $this->sendWithPHPMailer($to, $to_name, $subject, $body, $attachments);
        }
        
        // Otherwise use PHP's mail function
        $success = mail($to, $subject, $body, implode("\r\n", $headers));
        
        // Update the email log with the result
        $this->updateEmailLog($to, $subject, $success);
        
        return $success;
    }
    
    /**
     * Send email using PHPMailer
     * 
     * @param string $to Recipient email address
     * @param string $to_name Recipient name
     * @param string $subject Email subject
     * @param string $body Email body (HTML)
     * @param array $attachments Array of file paths to attach
     * @return bool True if email was sent successfully, false otherwise
     */
    private function sendWithPHPMailer($to, $to_name, $subject, $body, $attachments = []) {
        // Check if PHPMailer is available
        if (!class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
            return false;
        }
        
        try {
            // Create a new PHPMailer instance
            $mail = new PHPMailer\PHPMailer\PHPMailer(true);
            
            // Server settings
            $mail->isSMTP();
            $mail->Host = $this->smtp_host;
            $mail->SMTPAuth = !empty($this->smtp_username);
            $mail->Username = $this->smtp_username;
            $mail->Password = $this->smtp_password;
            $mail->SMTPSecure = $this->smtp_secure;
            $mail->Port = $this->smtp_port;
            
            // Recipients
            $mail->setFrom($this->from_email, $this->from_name);
            $mail->addAddress($to, $to_name);
            $mail->addReplyTo($this->reply_to, $this->from_name);
            
            // Content
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $body;
            
            // Attachments
            foreach ($attachments as $attachment) {
                if (file_exists($attachment)) {
                    $mail->addAttachment($attachment);
                }
            }
            
            // Send the email
            $success = $mail->send();
            
            // Update the email log with the result
            $this->updateEmailLog($to, $subject, $success);
            
            return $success;
        } catch (Exception $e) {
            // Log the error
            $this->updateEmailLog($to, $subject, false, $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log email sending attempt
     * 
     * @param string $to Recipient email address
     * @param string $subject Email subject
     * @return int|bool The ID of the new log entry, or false on failure
     */
    private function logEmail($to, $subject) {
        try {
            $query = "INSERT INTO email_logs (recipient_email, subject, status, created_at)
                      VALUES (:recipient_email, :subject, 'pending', NOW())";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':recipient_email', $to);
            $stmt->bindParam(':subject', $subject);
            $stmt->execute();
            
            return $this->db->lastInsertId();
        } catch (Exception $e) {
            // Silently fail - don't let logging disrupt the application
            return false;
        }
    }
    
    /**
     * Update email log with send result
     * 
     * @param string $to Recipient email address
     * @param string $subject Email subject
     * @param bool $success Whether the email was sent successfully
     * @param string $error_message Error message if any
     * @return bool True if log was updated successfully, false otherwise
     */
    private function updateEmailLog($to, $subject, $success, $error_message = '') {
        try {
            $query = "UPDATE email_logs 
                      SET status = :status, error_message = :error_message, updated_at = NOW()
                      WHERE recipient_email = :recipient_email AND subject = :subject
                      ORDER BY created_at DESC LIMIT 1";
            $stmt = $this->db->prepare($query);
            $status = $success ? 'sent' : 'failed';
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':error_message', $error_message);
            $stmt->bindParam(':recipient_email', $to);
            $stmt->bindParam(':subject', $subject);
            
            return $stmt->execute();
        } catch (Exception $e) {
            // Silently fail - don't let logging disrupt the application
            return false;
        }
    }
    
    /**
     * Send due date reminder emails
     * 
     * @param int $days_before Number of days before due date to send reminder
     * @return array Array with counts of success and failure
     */
    public function sendDueDateReminders($days_before = 3) {
        require_once 'email_templates.php';
        
        $results = [
            'success' => 0,
            'failure' => 0
        ];
        
        // Calculate the target date
        $target_date = date('Y-m-d', strtotime("+{$days_before} days"));
        
        // Get all loans due on the target date
        $query = "SELECT bl.*, b.title, b.author, b.isbn, m.first_name, m.last_name, m.email
                  FROM book_loans bl
                  JOIN books b ON bl.book_id = b.id
                  JOIN members m ON bl.member_id = m.id
                  WHERE bl.due_date = :due_date
                  AND bl.status = 'borrowed'
                  AND m.email IS NOT NULL";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':due_date', $target_date);
        $stmt->execute();
        
        $loans = $stmt->fetchAll();
        
        foreach ($loans as $loan) {
            // Prepare member, loan, and book data for the email template
            $member = [
                'first_name' => $loan['first_name'],
                'last_name' => $loan['last_name'],
                'email' => $loan['email']
            ];
            
            $book = [
                'title' => $loan['title'],
                'author' => $loan['author'],
                'isbn' => $loan['isbn']
            ];
            
            // Generate email content
            $email = getDueDateReminderEmail($member, $loan, $book);
            
            // Send the email
            $success = $this->sendEmail(
                $member['email'],
                $email['subject'],
                $email['body'],
                $member['first_name'] . ' ' . $member['last_name']
            );
            
            if ($success) {
                $results['success']++;
                
                // Log the reminder in the database
                $this->logReminderSent($loan['id'], 'due_date', $days_before);
            } else {
                $results['failure']++;
            }
        }
        
        return $results;
    }
    
    /**
     * Send overdue book notifications
     * 
     * @return array Array with counts of success and failure
     */
    public function sendOverdueNotifications() {
        require_once 'email_templates.php';
        
        $results = [
            'success' => 0,
            'failure' => 0
        ];
        
        // Get all overdue loans
        $query = "SELECT bl.*, b.title, b.author, b.isbn, m.first_name, m.last_name, m.email
                  FROM book_loans bl
                  JOIN books b ON bl.book_id = b.id
                  JOIN members m ON bl.member_id = m.id
                  WHERE bl.due_date < CURDATE()
                  AND bl.status = 'borrowed'
                  AND m.email IS NOT NULL";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        $loans = $stmt->fetchAll();
        
        foreach ($loans as $loan) {
            // Calculate fine if applicable
            $fine = $this->calculateFine($loan['due_date']);
            
            // Prepare member, loan, and book data for the email template
            $member = [
                'first_name' => $loan['first_name'],
                'last_name' => $loan['last_name'],
                'email' => $loan['email']
            ];
            
            $book = [
                'title' => $loan['title'],
                'author' => $loan['author'],
                'isbn' => $loan['isbn']
            ];
            
            // Generate email content
            $email = getOverdueBookEmail($member, $loan, $book, $fine);
            
            // Send the email
            $success = $this->sendEmail(
                $member['email'],
                $email['subject'],
                $email['body'],
                $member['first_name'] . ' ' . $member['last_name']
            );
            
            if ($success) {
                $results['success']++;
                
                // Log the reminder in the database
                $this->logReminderSent($loan['id'], 'overdue');
            } else {
                $results['failure']++;
            }
        }
        
        return $results;
    }
    
    /**
     * Calculate fine for overdue book
     * 
     * @param string $due_date Due date in Y-m-d format
     * @return float Fine amount
     */
    private function calculateFine($due_date) {
        // Get fine rate from settings (default to $0.25 per day)
        $query = "SELECT setting_value FROM settings WHERE setting_key = 'fine_rate_per_day'";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        $fine_rate = $stmt->rowCount() > 0 ? (float)$stmt->fetch()['setting_value'] : 0.25;
        
        // Calculate days overdue
        $days_overdue = max(0, floor((time() - strtotime($due_date)) / (60 * 60 * 24)));
        
        // Calculate fine
        return $days_overdue * $fine_rate;
    }
    
    /**
     * Log reminder sent in the database
     * 
     * @param int $loan_id Loan ID
     * @param string $reminder_type Type of reminder (due_date, overdue)
     * @param int $days_before Days before due date (for due_date reminders)
     * @return bool True if log was created successfully, false otherwise
     */
    private function logReminderSent($loan_id, $reminder_type, $days_before = null) {
        try {
            $query = "INSERT INTO reminder_logs (loan_id, reminder_type, days_before, sent_at)
                      VALUES (:loan_id, :reminder_type, :days_before, NOW())";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':loan_id', $loan_id);
            $stmt->bindParam(':reminder_type', $reminder_type);
            $stmt->bindParam(':days_before', $days_before);
            
            return $stmt->execute();
        } catch (Exception $e) {
            // Silently fail - don't let logging disrupt the application
            return false;
        }
    }
}
