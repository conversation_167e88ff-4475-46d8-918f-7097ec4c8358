<?php
/**
 * Debug version of test_database.php to identify the issue
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type
header('Content-Type: application/json');

$debug_info = [];
$debug_info['step'] = 'Starting debug';

try {
    $debug_info['step'] = 'Session check';
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
        $debug_info['session'] = 'Started';
    } else {
        $debug_info['session'] = 'Already active';
    }

    $debug_info['step'] = 'File path check';
    $config_path = __DIR__ . '/../../config/database.php';
    $debug_info['config_path'] = $config_path;
    $debug_info['config_exists'] = file_exists($config_path);

    if (!file_exists($config_path)) {
        throw new Exception("Config file not found at: $config_path");
    }

    $debug_info['step'] = 'Including config';
    require_once $config_path;
    $debug_info['config_loaded'] = true;

    $debug_info['step'] = 'Creating database instance';
    $database = new Database();
    $debug_info['database_created'] = true;

    $debug_info['step'] = 'Testing connection';
    $connection_test = $database->testConnection();
    $debug_info['connection_test'] = $connection_test;

    if (!$connection_test['success']) {
        throw new Exception($connection_test['message']);
    }

    $debug_info['step'] = 'Getting connection';
    $db = $database->getConnection();
    $debug_info['connection_obtained'] = ($db !== null);

    $debug_info['step'] = 'Testing simple query';
    $stmt = $db->query("SELECT 1 as test");
    $result = $stmt->fetch();
    $debug_info['simple_query'] = $result;

    // Success response
    echo json_encode([
        'success' => true,
        'message' => 'Debug test completed successfully',
        'debug_info' => $debug_info,
        'timestamp' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'debug_info' => $debug_info,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
