<?php
/**
 * Google Sign-In Page
 *
 * This page provides a simple interface for Google sign-in.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/google_auth.php';

// Check if user is already logged in
if (isLoggedIn()) {
    // Redirect based on role
    if (isAdmin()) {
        redirect(url('admin/dashboard.php'));
    } elseif (isLibrarian()) {
        redirect(url('librarian/dashboard.php'));
    } else {
        redirect(url('index.php'));
    }
} elseif (isMemberLoggedIn()) {
    redirect(url('member_dashboard.php'));
}

// Get Google login URL
$google_login_url = getGoogleLoginUrl();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Sign-In - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-image: url('assets/images/library-background.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .signin-container {
            max-width: 400px;
            width: 100%;
            margin: 0 auto;
            animation: fadeIn 0.8s ease-in-out;
        }
        .card {
            border: none;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.8rem 2rem rgba(0, 0, 0, 0.25);
        }
        .card-header {
            background-color: #343a40;
            color: white;
            font-weight: bold;
            padding: 1.2rem;
        }
        .btn-google {
            background-color: #ffffff;
            color: #757575;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-family: 'Roboto', sans-serif;
            font-weight: 500;
            font-size: 16px;
            padding: 12px 16px;
            position: relative;
            text-align: center;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        }
        .btn-google:hover {
            background-color: #f8f9fa;
            box-shadow: 0 2px 4px rgba(0,0,0,0.12);
            border-color: #c6c6c6;
            color: #3c4043;
        }
        .btn-google:active {
            background-color: #f1f3f4;
            box-shadow: 0 1px 2px rgba(0,0,0,0.15);
        }
        .btn-google img {
            vertical-align: middle;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="signin-container">
        <div class="card">
            <div class="card-header text-center py-3">
                <h4 class="mb-0"><i class="bi bi-book me-2"></i>Library Management System</h4>
            </div>
            <div class="card-body p-4 text-center">
                <h5 class="card-title mb-4">Sign in to your account</h5>

                <div class="d-grid mb-3">
                    <a href="<?php echo url('google_auto_login.php'); ?>" class="btn btn-google btn-lg" id="googleLoginBtn">
                        <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="24" height="24" class="me-2">
                        <span>Sign in with Google</span>
                    </a>
                </div>

                <div class="d-grid mb-3">
                    <a href="<?php echo url('direct_google_login.php?email=<EMAIL>'); ?>" class="btn btn-outline-primary btn-lg">
                        <i class="bi bi-person-circle me-2"></i>
                        <span>Sign <NAME_EMAIL></span>
                    </a>
                </div>

                <p class="text-muted small">Continue with your Google account that's already logged in</p>

                <div class="mt-4">
                    <a href="<?php echo url('login.php'); ?>" class="text-decoration-none">
                        <i class="bi bi-arrow-left me-1"></i>Back to Login Page
                    </a>
                </div>
            </div>
            <div class="card-footer text-center py-3">
                <p class="mb-0">Library Management System &copy; <?php echo date('Y'); ?></p>
            </div>
        </div>
    </div>

    <script>
        // Auto-click the specific account button after a short delay
        document.addEventListener('DOMContentLoaded', function() {
            // Get URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const autoLogin = urlParams.get('auto');

            // Only auto-click if auto parameter is present
            if (autoLogin === '1') {
                setTimeout(function() {
                    // Try to find the specific account button first
                    const specificAccountBtn = document.querySelector('a[href*="<EMAIL>"]');
                    if (specificAccountBtn) {
                        specificAccountBtn.click();
                    } else {
                        // Fall back to the generic Google button
                        const googleBtn = document.getElementById('googleLoginBtn');
                        if (googleBtn) {
                            googleBtn.click();
                        }
                    }
                }, 500);
            }
        });
    </script>
</body>
</html>
