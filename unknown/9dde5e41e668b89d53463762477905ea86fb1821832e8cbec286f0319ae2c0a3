<?php
/**
 * Advanced Member Search
 */
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and has appropriate role
if (!isset($_SESSION['user_id']) && !isset($_SESSION['member_id'])) {
    redirect(url('login.php'));
}

// Check if user has permission (admin or librarian)
if (isset($_SESSION['role']) && !in_array($_SESSION['role'], ['admin', 'librarian'])) {
    redirect(url('index.php'));
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize search variables
$search_results = [];
$search_performed = false;
$total_results = 0;

// Process search
if ($_SERVER['REQUEST_METHOD'] === 'GET' && !empty($_GET)) {
    $search_performed = true;
    
    // Get search parameters
    $name = trim($_GET['name'] ?? '');
    $email = trim($_GET['email'] ?? '');
    $phone = trim($_GET['phone'] ?? '');
    $status = $_GET['status'] ?? '';
    $date_from = $_GET['date_from'] ?? '';
    $date_to = $_GET['date_to'] ?? '';
    $has_loans = $_GET['has_loans'] ?? '';
    $has_overdue = $_GET['has_overdue'] ?? '';
    
    // Build query
    $where_conditions = [];
    $params = [];
    
    if (!empty($name)) {
        $where_conditions[] = "(m.first_name LIKE :name OR m.last_name LIKE :name OR CONCAT(m.first_name, ' ', m.last_name) LIKE :name)";
        $params[':name'] = "%{$name}%";
    }
    
    if (!empty($email)) {
        $where_conditions[] = "m.email LIKE :email";
        $params[':email'] = "%{$email}%";
    }
    
    if (!empty($phone)) {
        $where_conditions[] = "m.phone LIKE :phone";
        $params[':phone'] = "%{$phone}%";
    }
    
    if (!empty($status)) {
        $where_conditions[] = "m.membership_status = :status";
        $params[':status'] = $status;
    }
    
    if (!empty($date_from)) {
        $where_conditions[] = "m.membership_date >= :date_from";
        $params[':date_from'] = $date_from;
    }
    
    if (!empty($date_to)) {
        $where_conditions[] = "m.membership_date <= :date_to";
        $params[':date_to'] = $date_to;
    }
    
    // Base query
    $query = "SELECT DISTINCT m.*, 
              COUNT(DISTINCT bl.id) as total_loans,
              COUNT(DISTINCT CASE WHEN bl.return_date IS NULL THEN bl.id END) as current_loans,
              COUNT(DISTINCT CASE WHEN bl.return_date IS NULL AND bl.due_date < CURDATE() THEN bl.id END) as overdue_loans
              FROM members m
              LEFT JOIN book_loans bl ON m.id = bl.member_id";
    
    // Add WHERE clause if conditions exist
    if (!empty($where_conditions)) {
        $query .= " WHERE " . implode(" AND ", $where_conditions);
    }
    
    // Add GROUP BY
    $query .= " GROUP BY m.id";
    
    // Add HAVING clause for loan-related filters
    $having_conditions = [];
    
    if ($has_loans === 'yes') {
        $having_conditions[] = "current_loans > 0";
    } elseif ($has_loans === 'no') {
        $having_conditions[] = "current_loans = 0";
    }
    
    if ($has_overdue === 'yes') {
        $having_conditions[] = "overdue_loans > 0";
    } elseif ($has_overdue === 'no') {
        $having_conditions[] = "overdue_loans = 0";
    }
    
    if (!empty($having_conditions)) {
        $query .= " HAVING " . implode(" AND ", $having_conditions);
    }
    
    // Add ORDER BY
    $query .= " ORDER BY m.last_name, m.first_name";
    
    try {
        $stmt = $db->prepare($query);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        $search_results = $stmt->fetchAll();
        $total_results = count($search_results);
    } catch (PDOException $e) {
        $error_message = 'Search error: ' . $e->getMessage();
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Member Search - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Advanced Member Search</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="index.php" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Members
                        </a>
                    </div>
                </div>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <?php echo h($error_message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Search Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-search me-2"></i>Search Criteria
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Name</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?php echo h($_GET['name'] ?? ''); ?>" 
                                           placeholder="First name, last name, or full name">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo h($_GET['email'] ?? ''); ?>" 
                                           placeholder="Email address">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone</label>
                                    <input type="text" class="form-control" id="phone" name="phone" 
                                           value="<?php echo h($_GET['phone'] ?? ''); ?>" 
                                           placeholder="Phone number">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">Membership Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">All Statuses</option>
                                        <option value="active" <?php echo ($_GET['status'] ?? '') === 'active' ? 'selected' : ''; ?>>Active</option>
                                        <option value="inactive" <?php echo ($_GET['status'] ?? '') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                        <option value="suspended" <?php echo ($_GET['status'] ?? '') === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="date_from" class="form-label">Membership Date From</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" 
                                           value="<?php echo h($_GET['date_from'] ?? ''); ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="date_to" class="form-label">Membership Date To</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" 
                                           value="<?php echo h($_GET['date_to'] ?? ''); ?>">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="has_loans" class="form-label">Has Current Loans</label>
                                    <select class="form-select" id="has_loans" name="has_loans">
                                        <option value="">Any</option>
                                        <option value="yes" <?php echo ($_GET['has_loans'] ?? '') === 'yes' ? 'selected' : ''; ?>>Yes</option>
                                        <option value="no" <?php echo ($_GET['has_loans'] ?? '') === 'no' ? 'selected' : ''; ?>>No</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="has_overdue" class="form-label">Has Overdue Books</label>
                                    <select class="form-select" id="has_overdue" name="has_overdue">
                                        <option value="">Any</option>
                                        <option value="yes" <?php echo ($_GET['has_overdue'] ?? '') === 'yes' ? 'selected' : ''; ?>>Yes</option>
                                        <option value="no" <?php echo ($_GET['has_overdue'] ?? '') === 'no' ? 'selected' : ''; ?>>No</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="advanced_search.php" class="btn btn-secondary me-md-2">Clear</a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search me-2"></i>Search
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Search Results -->
                <?php if ($search_performed): ?>
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-list me-2"></i>Search Results (<?php echo $total_results; ?> found)
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if ($total_results > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Phone</th>
                                                <th>Status</th>
                                                <th>Membership Date</th>
                                                <th>Total Loans</th>
                                                <th>Current Loans</th>
                                                <th>Overdue</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($search_results as $member): ?>
                                            <tr>
                                                <td><?php echo $member['id']; ?></td>
                                                <td><?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></td>
                                                <td><?php echo h($member['email']); ?></td>
                                                <td><?php echo h($member['phone'] ?: 'N/A'); ?></td>
                                                <td>
                                                    <?php if ($member['membership_status'] === 'active'): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php elseif ($member['membership_status'] === 'inactive'): ?>
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Suspended</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('M j, Y', strtotime($member['membership_date'])); ?></td>
                                                <td><span class="badge bg-info"><?php echo $member['total_loans']; ?></span></td>
                                                <td><span class="badge bg-primary"><?php echo $member['current_loans']; ?></span></td>
                                                <td>
                                                    <?php if ($member['overdue_loans'] > 0): ?>
                                                        <span class="badge bg-danger"><?php echo $member['overdue_loans']; ?></span>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">0</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="view.php?id=<?php echo $member['id']; ?>" class="btn btn-sm btn-info" title="View Details">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a href="edit.php?id=<?php echo $member['id']; ?>" class="btn btn-sm btn-warning" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="bi bi-search display-1 text-muted"></i>
                                    <h4 class="mt-3">No members found</h4>
                                    <p class="text-muted">Try adjusting your search criteria.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
