<?php
/**
 * Test Login Functionality
 * This script tests if the login system is working properly
 */

session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
    </style>
</head>
<body>";

echo "<h1>Login System Test</h1>";

// Test 1: Database Connection
echo "<div class='test-section'>";
echo "<h2>Test 1: Database Connection</h2>";
try {
    $database = new Database();
    $db = $database->getConnection();
    if ($db) {
        echo "<p class='success'>✅ Database connection successful</p>";
    } else {
        echo "<p class='error'>❌ Database connection failed</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 2: Check if tables exist
echo "<div class='test-section'>";
echo "<h2>Test 2: Database Tables</h2>";
try {
    // Check users table
    $stmt = $db->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ Users table exists</p>";
        
        // Check users table structure
        $stmt = $db->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p class='info'>Users table columns: " . implode(', ', $columns) . "</p>";
        
        // Count users
        $stmt = $db->query("SELECT COUNT(*) FROM users");
        $count = $stmt->fetchColumn();
        echo "<p class='info'>Total users: $count</p>";
    } else {
        echo "<p class='error'>❌ Users table does not exist</p>";
    }
    
    // Check members table
    $stmt = $db->query("SHOW TABLES LIKE 'members'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ Members table exists</p>";
        
        // Count members
        $stmt = $db->query("SELECT COUNT(*) FROM members");
        $count = $stmt->fetchColumn();
        echo "<p class='info'>Total members: $count</p>";
    } else {
        echo "<p class='error'>❌ Members table does not exist</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Table check error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 3: Check default admin user
echo "<div class='test-section'>";
echo "<h2>Test 3: Default Admin User</h2>";
try {
    $stmt = $db->query("SELECT username, email, role FROM users WHERE role = 'admin'");
    $admins = $stmt->fetchAll();
    
    if (count($admins) > 0) {
        echo "<p class='success'>✅ Admin users found:</p>";
        foreach ($admins as $admin) {
            echo "<p class='info'>- Username: {$admin['username']}, Email: {$admin['email']}, Role: {$admin['role']}</p>";
        }
    } else {
        echo "<p class='error'>❌ No admin users found</p>";
        echo "<p class='info'>Creating default admin user...</p>";
        
        // Create default admin
        $username = 'admin';
        $email = '<EMAIL>';
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        $role = 'admin';
        
        $stmt = $db->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)");
        if ($stmt->execute([$username, $email, $password, $role])) {
            echo "<p class='success'>✅ Default admin created - Username: admin, Password: admin123</p>";
        } else {
            echo "<p class='error'>❌ Failed to create default admin</p>";
        }
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Admin check error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 4: Check sample member
echo "<div class='test-section'>";
echo "<h2>Test 4: Sample Member</h2>";
try {
    $stmt = $db->query("SELECT first_name, last_name, email FROM members WHERE email = '<EMAIL>'");
    $member = $stmt->fetch();
    
    if ($member) {
        echo "<p class='success'>✅ Sample member found: {$member['first_name']} {$member['last_name']} ({$member['email']})</p>";
        
        // Check if member has password
        $stmt = $db->query("SELECT password FROM members WHERE email = '<EMAIL>'");
        $memberData = $stmt->fetch();
        if (!empty($memberData['password'])) {
            echo "<p class='success'>✅ Member has password set</p>";
        } else {
            echo "<p class='error'>❌ Member password not set, setting default password...</p>";
            $password = password_hash('member123', PASSWORD_DEFAULT);
            $stmt = $db->prepare("UPDATE members SET password = ? WHERE email = '<EMAIL>'");
            if ($stmt->execute([$password])) {
                echo "<p class='success'>✅ Member password set to: member123</p>";
            }
        }
    } else {
        echo "<p class='error'>❌ Sample member not found</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Member check error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 5: Functions
echo "<div class='test-section'>";
echo "<h2>Test 5: Authentication Functions</h2>";
if (function_exists('isLoggedIn')) {
    echo "<p class='success'>✅ isLoggedIn() function exists</p>";
} else {
    echo "<p class='error'>❌ isLoggedIn() function missing</p>";
}

if (function_exists('isAdmin')) {
    echo "<p class='success'>✅ isAdmin() function exists</p>";
} else {
    echo "<p class='error'>❌ isAdmin() function missing</p>";
}

if (function_exists('isMemberLoggedIn')) {
    echo "<p class='success'>✅ isMemberLoggedIn() function exists</p>";
} else {
    echo "<p class='error'>❌ isMemberLoggedIn() function missing</p>";
}

if (function_exists('redirect')) {
    echo "<p class='success'>✅ redirect() function exists</p>";
} else {
    echo "<p class='error'>❌ redirect() function missing</p>";
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>Test Results Summary</h2>";
echo "<p class='info'><strong>Login Credentials to Test:</strong></p>";
echo "<p class='info'>Admin Login: admin / admin123</p>";
echo "<p class='info'>Member Login: <EMAIL> / member123</p>";
echo "<p class='info'><a href='login.php'>Go to Login Page</a></p>";
echo "</div>";

echo "</body></html>";
?>
