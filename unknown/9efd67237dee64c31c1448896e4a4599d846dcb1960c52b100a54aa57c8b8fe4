<?php
/**
 * LMS System Fix Script
 * This script automatically fixes common issues in the LMS system
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>LMS System Fix Script</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .fix-button { background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
    .fix-button:hover { background: #218838; }
</style>";

// Auto-fix mode
$auto_fix = isset($_GET['auto_fix']) && $_GET['auto_fix'] === 'true';

echo "<div class='section'>";
echo "<h2>System Status Check</h2>";

// Check 1: Database Connection
echo "<h3>1. Database Connection</h3>";
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    echo "<p class='success'>✓ Database connection successful</p>";
    $db_connected = true;
} catch (Exception $e) {
    echo "<p class='error'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<p class='info'>Fix: Check XAMPP/WAMP is running and database credentials in config/database.php</p>";
    $db_connected = false;
}

if ($db_connected) {
    // Check 2: Required Tables
    echo "<h3>2. Database Tables</h3>";
    $required_tables = ['users', 'members', 'books', 'book_loans'];
    $missing_tables = [];
    
    foreach ($required_tables as $table) {
        try {
            $query = "SHOW TABLES LIKE '$table'";
            $stmt = $db->prepare($query);
            $stmt->execute();
            if ($stmt->rowCount() > 0) {
                echo "<p class='success'>✓ Table '$table' exists</p>";
            } else {
                echo "<p class='error'>✗ Table '$table' missing</p>";
                $missing_tables[] = $table;
            }
        } catch (Exception $e) {
            echo "<p class='error'>✗ Error checking table '$table': " . $e->getMessage() . "</p>";
            $missing_tables[] = $table;
        }
    }
    
    if (!empty($missing_tables)) {
        echo "<p class='warning'>Missing tables: " . implode(', ', $missing_tables) . "</p>";
        echo "<p><a href='setup.php' class='fix-button'>Run Database Setup</a></p>";
    }
    
    // Check 3: Admin Users
    echo "<h3>3. Admin Users</h3>";
    try {
        $query = "SELECT COUNT(*) as count FROM users WHERE role = 'admin'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        $admin_count = $result['count'];
        
        if ($admin_count > 0) {
            echo "<p class='success'>✓ Admin users exist ($admin_count found)</p>";
        } else {
            echo "<p class='error'>✗ No admin users found</p>";
            echo "<p><a href='create_admin.php' class='fix-button'>Create Admin User</a></p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>✗ Error checking admin users: " . $e->getMessage() . "</p>";
    }
    
    // Check 4: Sample Data
    echo "<h3>4. Sample Data</h3>";
    try {
        $query = "SELECT COUNT(*) as count FROM books";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        $book_count = $result['count'];
        
        $query = "SELECT COUNT(*) as count FROM members";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        $member_count = $result['count'];
        
        echo "<p class='info'>Books: $book_count</p>";
        echo "<p class='info'>Members: $member_count</p>";
        
        if ($book_count == 0 || $member_count == 0) {
            echo "<p class='warning'>Consider adding sample data for testing</p>";
            echo "<p><a href='setup_database.php' class='fix-button'>Add Sample Data</a></p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>✗ Error checking sample data: " . $e->getMessage() . "</p>";
    }
}

// Check 5: File Permissions
echo "<h3>5. File Permissions</h3>";
$critical_files = [
    'config/database.php',
    'config/config.php',
    'includes/functions.php',
    'index.php',
    'home.php',
    'login.php'
];

foreach ($critical_files as $file) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "<p class='success'>✓ $file is readable</p>";
        } else {
            echo "<p class='error'>✗ $file is not readable</p>";
        }
    } else {
        echo "<p class='error'>✗ $file not found</p>";
    }
}

// Check 6: Upload Directories
echo "<h3>6. Upload Directories</h3>";
$upload_dirs = ['uploads', 'uploads/covers', 'uploads/members', 'uploads/profiles'];

foreach ($upload_dirs as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<p class='success'>✓ Directory '$dir' exists and is writable</p>";
        } else {
            echo "<p class='warning'>⚠ Directory '$dir' exists but is not writable</p>";
        }
    } else {
        echo "<p class='error'>✗ Directory '$dir' does not exist</p>";
        if ($auto_fix) {
            if (mkdir($dir, 0755, true)) {
                echo "<p class='success'>✓ Created directory '$dir'</p>";
            } else {
                echo "<p class='error'>✗ Failed to create directory '$dir'</p>";
            }
        }
    }
}

echo "</div>";

// Quick Fix Section
echo "<div class='section'>";
echo "<h2>Quick Fixes</h2>";
echo "<p><a href='?auto_fix=true' class='fix-button'>Auto-Fix Common Issues</a></p>";
echo "<p><a href='setup.php' class='fix-button'>Database Setup</a></p>";
echo "<p><a href='create_admin.php' class='fix-button'>Create Admin User</a></p>";
echo "<p><a href='diagnostic.php' class='fix-button'>Run Full Diagnostic</a></p>";
echo "</div>";

// Navigation
echo "<div class='section'>";
echo "<h2>Navigation</h2>";
echo "<p><a href='index.php'>Home Page</a></p>";
echo "<p><a href='login.php'>Login Page</a></p>";
echo "<p><a href='admin/dashboard.php'>Admin Dashboard</a></p>";
echo "</div>";

if ($auto_fix) {
    echo "<div class='section'>";
    echo "<h2>Auto-Fix Results</h2>";
    echo "<p class='info'>Auto-fix completed. Please refresh the page to see updated status.</p>";
    echo "</div>";
}
?>
