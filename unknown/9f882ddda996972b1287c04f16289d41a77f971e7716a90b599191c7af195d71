<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is librarian
if (!isLoggedIn() || !isLibrarian()) {
    redirect('../login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Initialize variables
$loan_id = $return_date = '';
$errors = [];
$success = false;
$loan_details = null;

// Get all active loans
$query = "SELECT bl.id, bl.issue_date, bl.due_date, bl.status, 
          b.title as book_title, b.author as book_author, b.isbn,
          m.first_name, m.last_name, m.email
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          JOIN members m ON bl.member_id = m.id
          WHERE bl.status = 'borrowed'
          ORDER BY bl.due_date ASC";
$stmt = $db->prepare($query);
$stmt->execute();
$active_loans = $stmt->fetchAll();

// Process form submission for loan selection
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['select_loan'])) {
    $loan_id = trim($_POST['loan_id'] ?? '');
    
    if (empty($loan_id)) {
        $errors[] = 'Please select a loan';
    } else {
        // Get loan details
        $query = "SELECT bl.*, b.title as book_title, b.id as book_id, m.first_name, m.last_name
                  FROM book_loans bl
                  JOIN books b ON bl.book_id = b.id
                  JOIN members m ON bl.member_id = m.id
                  WHERE bl.id = :loan_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':loan_id', $loan_id);
        $stmt->execute();
        $loan_details = $stmt->fetch();
        
        if (!$loan_details) {
            $errors[] = 'Loan not found';
        } elseif ($loan_details['status'] !== 'borrowed') {
            $errors[] = 'This book has already been returned';
        }
    }
}

// Process form submission for returning book
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['return_book'])) {
    $loan_id = trim($_POST['loan_id'] ?? '');
    $return_date = trim($_POST['return_date'] ?? '');
    $book_id = trim($_POST['book_id'] ?? '');
    
    // Validate input
    if (empty($loan_id)) {
        $errors[] = 'Loan ID is required';
    }
    
    if (empty($return_date)) {
        $errors[] = 'Return date is required';
    }
    
    if (empty($book_id)) {
        $errors[] = 'Book ID is required';
    }
    
    // If no errors, return the book
    if (empty($errors)) {
        try {
            // Start transaction
            $db->beginTransaction();
            
            // Get loan details for fine calculation
            $query = "SELECT due_date FROM book_loans WHERE id = :loan_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':loan_id', $loan_id);
            $stmt->execute();
            $loan = $stmt->fetch();
            
            // Calculate fine if book is returned late
            $fine = 0;
            if (strtotime($return_date) > strtotime($loan['due_date'])) {
                $fine = calculateFine($loan['due_date'], $return_date);
            }
            
            // Update loan record
            $query = "UPDATE book_loans 
                      SET status = 'returned', return_date = :return_date, fine = :fine
                      WHERE id = :loan_id";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(':return_date', $return_date);
            $stmt->bindParam(':fine', $fine);
            $stmt->bindParam(':loan_id', $loan_id);
            $stmt->execute();
            
            // Update book available quantity
            $query = "UPDATE books SET available_quantity = available_quantity + 1 
                      WHERE id = :book_id";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(':book_id', $book_id);
            $stmt->execute();
            
            // Commit transaction
            $db->commit();
            
            // Set success message
            $fine_message = $fine > 0 ? " with a fine of $" . number_format($fine, 2) : " with no fine";
            setMessage('Book returned successfully' . $fine_message, 'success');
            
            // Reset form
            $loan_id = $return_date = '';
            $loan_details = null;
            $success = true;
            
            // Redirect to avoid form resubmission
            redirect('return_book.php');
            
        } catch (PDOException $e) {
            // Rollback transaction on error
            $db->rollBack();
            $errors[] = 'Error returning book: ' . $e->getMessage();
        }
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Return Book - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .content-container {
            max-width: 800px;
            margin: 20px auto;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            font-weight: bold;
        }
        .overdue {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="issue_book.php">Issue Book</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="return_book.php">Return Book</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_reservations.php">Manage Reservations</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="../logout.php" class="btn btn-outline-light btn-sm">
                        <i class="bi bi-box-arrow-right me-1"></i>Sign out
                    </a>
                </div>
            </div>
        </div>
    </nav>
    
    <div class="content-container">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Return Book</h5>
            </div>
            <div class="card-body">
                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo h($_SESSION['message_type']); ?> alert-dismissible fade show" role="alert">
                        <?php echo h($_SESSION['message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>
                
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo h($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <?php if (!$loan_details): ?>
                    <!-- Step 1: Select a loan -->
                    <form method="post" action="<?php echo h($_SERVER['PHP_SELF']); ?>">
                        <div class="mb-3">
                            <label for="loan_id" class="form-label">Select a Loan</label>
                            <select class="form-select" id="loan_id" name="loan_id" required>
                                <option value="">Select a loan</option>
                                <?php foreach ($active_loans as $loan): ?>
                                    <option value="<?php echo h($loan['id']); ?>" <?php echo $loan_id == $loan['id'] ? 'selected' : ''; ?>>
                                        <?php echo h($loan['book_title']); ?> by <?php echo h($loan['book_author']); ?> - 
                                        Borrowed by <?php echo h($loan['first_name'] . ' ' . $loan['last_name']); ?> - 
                                        Due: <?php echo formatDate($loan['due_date']); ?>
                                        <?php if (strtotime($loan['due_date']) < time()): ?>
                                            <span class="text-danger">(OVERDUE)</span>
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" name="select_loan" class="btn btn-primary">Select Loan</button>
                            <a href="dashboard.php" class="btn btn-outline-secondary">Cancel</a>
                        </div>
                    </form>
                <?php else: ?>
                    <!-- Step 2: Return the book -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Loan Details</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Book:</strong> <?php echo h($loan_details['book_title']); ?></p>
                                    <p><strong>Member:</strong> <?php echo h($loan_details['first_name'] . ' ' . $loan_details['last_name']); ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Issue Date:</strong> <?php echo formatDate($loan_details['issue_date']); ?></p>
                                    <p><strong>Due Date:</strong> 
                                        <span class="<?php echo strtotime($loan_details['due_date']) < time() ? 'overdue' : ''; ?>">
                                            <?php echo formatDate($loan_details['due_date']); ?>
                                            <?php if (strtotime($loan_details['due_date']) < time()): ?>
                                                <span class="badge bg-danger">OVERDUE</span>
                                            <?php endif; ?>
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="post" action="<?php echo h($_SERVER['PHP_SELF']); ?>">
                        <input type="hidden" name="loan_id" value="<?php echo h($loan_details['id']); ?>">
                        <input type="hidden" name="book_id" value="<?php echo h($loan_details['book_id']); ?>">
                        
                        <div class="mb-3">
                            <label for="return_date" class="form-label">Return Date</label>
                            <input type="date" class="form-control" id="return_date" name="return_date" value="<?php echo h($return_date ?: date('Y-m-d')); ?>" required>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" name="return_book" class="btn btn-primary">Return Book</button>
                            <a href="return_book.php" class="btn btn-outline-secondary">Cancel</a>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
