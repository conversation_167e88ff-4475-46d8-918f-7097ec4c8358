<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Settings - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="bi bi-envelope me-2 text-primary"></i>
                    Email & Notification Settings
                </h1>

                <?php
                $message = '';
                $messageType = '';

                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    if (isset($_POST['send_test_email'])) {
                        $to_email = $_POST['test_email'] ?? '';
                        if (!empty($to_email)) {
                            $subject = 'Test Email from Library Management System';
                            $body = '<html><body>
                                <h2>Test Email</h2>
                                <p>This is a test email from the Library Management System.</p>
                                <p>If you received this email, your email settings are working correctly.</p>
                                <p>Time sent: ' . date('Y-m-d H:i:s') . '</p>
                                </body></html>';

                            $headers = "MIME-Version: 1.0\r\n";
                            $headers .= "Content-type:text/html;charset=UTF-8\r\n";
                            $headers .= "From: Library System <<EMAIL>>\r\n";

                            if (mail($to_email, $subject, $body, $headers)) {
                                $message = "Test email sent successfully to $to_email";
                                $messageType = 'success';
                            } else {
                                $message = 'Failed to send test email. Please check your server email configuration.';
                                $messageType = 'danger';
                            }
                        } else {
                            $message = 'Please enter a valid email address.';
                            $messageType = 'warning';
                        }
                    } else {
                        $message = 'Settings saved successfully! (Note: This is a demo version)';
                        $messageType = 'success';
                    }
                }
                ?>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>Demo Version:</strong> This is a simplified email settings page that works without database connection.
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-envelope me-2"></i>Email Configuration</h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    <div class="mb-3">
                                        <label for="from_email" class="form-label">From Email</label>
                                        <input type="email" class="form-control" id="from_email" name="from_email" value="<EMAIL>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="from_name" class="form-label">From Name</label>
                                        <input type="text" class="form-control" id="from_name" name="from_name" value="Library Management System">
                                    </div>
                                    <div class="mb-3">
                                        <label for="reply_to" class="form-label">Reply-To Email</label>
                                        <input type="email" class="form-control" id="reply_to" name="reply_to" value="<EMAIL>">
                                    </div>
                                    <button type="submit" name="email_settings" class="btn btn-primary">
                                        <i class="bi bi-save me-2"></i>Save Email Settings
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="bi bi-send me-2"></i>Test Email</h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    <div class="mb-3">
                                        <label for="test_email" class="form-label">Recipient Email Address</label>
                                        <input type="email" class="form-control" id="test_email" name="test_email" required>
                                        <div class="form-text">Enter an email address to send a test email</div>
                                    </div>
                                    <button type="submit" name="send_test_email" class="btn btn-success">
                                        <i class="bi bi-send me-2"></i>Send Test Email
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0"><i class="bi bi-bell me-2"></i>Notification Settings</h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    <div class="mb-3">
                                        <label for="reminder_days" class="form-label">Due Date Reminder (days before)</label>
                                        <input type="number" class="form-control" id="reminder_days" name="reminder_days" value="3" min="1" max="14">
                                    </div>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="overdue_notifications" name="overdue_notifications" checked>
                                        <label class="form-check-label" for="overdue_notifications">
                                            Send Overdue Notifications
                                        </label>
                                    </div>
                                    <button type="submit" name="notification_settings" class="btn btn-warning">
                                        <i class="bi bi-save me-2"></i>Save Notification Settings
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="bi bi-cash-coin me-2"></i>Fine Settings</h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    <div class="mb-3">
                                        <label for="fine_rate" class="form-label">Fine Rate Per Day ($)</label>
                                        <input type="number" class="form-control" id="fine_rate" name="fine_rate" value="0.25" step="0.01" min="0">
                                    </div>
                                    <div class="mb-3">
                                        <label for="grace_period" class="form-label">Grace Period (days)</label>
                                        <input type="number" class="form-control" id="grace_period" name="grace_period" value="3" min="0" max="7">
                                    </div>
                                    <div class="mb-3">
                                        <label for="max_fine" class="form-label">Maximum Fine Per Book ($)</label>
                                        <input type="number" class="form-control" id="max_fine" name="max_fine" value="25.00" step="0.01" min="0">
                                    </div>
                                    <button type="submit" name="fine_settings" class="btn btn-info">
                                        <i class="bi bi-save me-2"></i>Save Fine Settings
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Quick Links</h5>
                        <a href="../index.php" class="btn btn-outline-primary me-2">
                            <i class="bi bi-house me-1"></i>Home
                        </a>
                        <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                            <i class="bi bi-speedometer2 me-1"></i>Dashboard
                        </a>
                        <a href="../setup_database_simple.php" class="btn btn-outline-success">
                            <i class="bi bi-database me-1"></i>Setup Database
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
