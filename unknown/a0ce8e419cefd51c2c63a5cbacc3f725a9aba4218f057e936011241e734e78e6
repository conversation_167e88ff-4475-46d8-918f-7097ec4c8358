<?php
session_start();

// Include required files with error handling
try {
    require_once '../config/database.php';
    require_once '../config/config.php';
    require_once '../includes/functions.php';
} catch (Exception $e) {
    die('Error loading required files: ' . $e->getMessage());
}

// Check if user is logged in and has admin/librarian privileges
if (!isLoggedIn()) {
    redirect('../login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get filter parameters
$status = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$sort = isset($_GET['sort']) ? sanitize($_GET['sort']) : 'name';

// Build query
$query = "SELECT m.*,
          COUNT(bl.id) as total_loans,
          COUNT(CASE WHEN bl.status = 'borrowed' THEN 1 END) as current_loans,
          COUNT(CASE WHEN bl.status = 'overdue' THEN 1 END) as overdue_loans,
          SUM(CASE WHEN bl.status = 'returned' THEN bl.fine ELSE 0 END) as total_fines
          FROM members m
          LEFT JOIN book_loans bl ON m.id = bl.member_id
          WHERE 1=1";

$params = [];

if (!empty($search)) {
    $query .= " AND (m.first_name LIKE :search OR m.last_name LIKE :search OR m.email LIKE :search OR m.phone LIKE :search)";
    $params[':search'] = '%' . $search . '%';
}

$query .= " GROUP BY m.id";

// Add status filter after GROUP BY
if (!empty($status)) {
    if ($status === 'active') {
        $query .= " HAVING total_loans > 0";
    } elseif ($status === 'inactive') {
        $query .= " HAVING total_loans = 0";
    } elseif ($status === 'current_borrowers') {
        $query .= " HAVING current_loans > 0";
    } elseif ($status === 'overdue') {
        $query .= " HAVING overdue_loans > 0";
    }
}

// Add sorting
switch ($sort) {
    case 'name':
        $query .= " ORDER BY m.first_name, m.last_name";
        break;
    case 'email':
        $query .= " ORDER BY m.email";
        break;
    case 'join_date':
        $query .= " ORDER BY m.created_at DESC";
        break;
    case 'loans':
        $query .= " ORDER BY total_loans DESC";
        break;
    default:
        $query .= " ORDER BY m.first_name, m.last_name";
}

$stmt = $db->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$members = $stmt->fetchAll();

// Calculate statistics
$total_members = count($members);
$active_members = 0;
$current_borrowers = 0;
$members_with_overdue = 0;
$total_fines_collected = 0;

foreach ($members as $member) {
    if ($member['total_loans'] > 0) $active_members++;
    if ($member['current_loans'] > 0) $current_borrowers++;
    if ($member['overdue_loans'] > 0) $members_with_overdue++;
    $total_fines_collected += $member['total_fines'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Members Report - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .stats-card {
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .print-hide {
            display: block;
        }
        @media print {
            .print-hide {
                display: none !important;
            }
            .container-fluid {
                margin: 0;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-people me-2"></i>Members Report</h1>
                    <div class="btn-toolbar mb-2 mb-md-0 print-hide">
                        <button onclick="window.print()" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="bi bi-printer"></i> Print Report
                        </button>
                        <a href="index.php" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> Back to Reports
                        </a>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-people-fill fs-1 text-primary mb-2"></i>
                                <h5 class="card-title">Total Members</h5>
                                <h3 class="text-primary"><?php echo $total_members; ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-person-check-fill fs-1 text-success mb-2"></i>
                                <h5 class="card-title">Active Members</h5>
                                <h3 class="text-success"><?php echo $active_members; ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-book-half fs-1 text-info mb-2"></i>
                                <h5 class="card-title">Current Borrowers</h5>
                                <h3 class="text-info"><?php echo $current_borrowers; ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-exclamation-triangle-fill fs-1 text-warning mb-2"></i>
                                <h5 class="card-title">With Overdue</h5>
                                <h3 class="text-warning"><?php echo $members_with_overdue; ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4 print-hide">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="Name, Email, or Phone">
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Members</option>
                                    <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Active (Has Loans)</option>
                                    <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>Inactive (No Loans)</option>
                                    <option value="current_borrowers" <?php echo $status === 'current_borrowers' ? 'selected' : ''; ?>>Current Borrowers</option>
                                    <option value="overdue" <?php echo $status === 'overdue' ? 'selected' : ''; ?>>With Overdue Books</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="sort" class="form-label">Sort By</label>
                                <select class="form-select" id="sort" name="sort">
                                    <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>Name</option>
                                    <option value="email" <?php echo $sort === 'email' ? 'selected' : ''; ?>>Email</option>
                                    <option value="join_date" <?php echo $sort === 'join_date' ? 'selected' : ''; ?>>Join Date</option>
                                    <option value="loans" <?php echo $sort === 'loans' ? 'selected' : ''; ?>>Total Loans</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="bi bi-search"></i> Filter
                                </button>
                                <a href="members.php" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise"></i> Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Members Table -->
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>Members List
                            <?php if (!empty($search) || !empty($status)): ?>
                                <small class="text-muted">(Filtered Results)</small>
                            <?php endif; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($members)): ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>No members found matching your criteria.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Member Name</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>Join Date</th>
                                            <th>Total Loans</th>
                                            <th>Current Loans</th>
                                            <th>Overdue</th>
                                            <th>Total Fines</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($members as $member): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?></strong>
                                                    <?php if (!empty($member['address'])): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($member['address']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($member['email']); ?></td>
                                                <td><?php echo htmlspecialchars($member['phone'] ?: 'N/A'); ?></td>
                                                <td><?php echo !empty($member['created_at']) ? formatDate($member['created_at']) : 'N/A'; ?></td>
                                                <td>
                                                    <span class="badge bg-primary"><?php echo $member['total_loans']; ?></span>
                                                </td>
                                                <td>
                                                    <?php if ($member['current_loans'] > 0): ?>
                                                        <span class="badge bg-info"><?php echo $member['current_loans']; ?></span>
                                                    <?php else: ?>
                                                        <span class="text-muted">0</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($member['overdue_loans'] > 0): ?>
                                                        <span class="badge bg-danger"><?php echo $member['overdue_loans']; ?></span>
                                                    <?php else: ?>
                                                        <span class="text-muted">0</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($member['total_fines'] > 0): ?>
                                                        $<?php echo number_format($member['total_fines'], 2); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">$0.00</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($member['overdue_loans'] > 0): ?>
                                                        <span class="badge bg-danger">Has Overdue</span>
                                                    <?php elseif ($member['current_loans'] > 0): ?>
                                                        <span class="badge bg-success">Active Borrower</span>
                                                    <?php elseif ($member['total_loans'] > 0): ?>
                                                        <span class="badge bg-secondary">Member</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-light text-dark">New Member</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Summary -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title">Summary</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><strong>Total Members:</strong> <?php echo $total_members; ?></li>
                                    <li><strong>Active Members:</strong> <?php echo $active_members; ?> (<?php echo $total_members > 0 ? round(($active_members / $total_members) * 100, 1) : 0; ?>%)</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><strong>Current Borrowers:</strong> <?php echo $current_borrowers; ?></li>
                                    <li><strong>Total Fines Collected:</strong> $<?php echo number_format($total_fines_collected, 2); ?></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Report Footer -->
                <div class="mt-4 text-center text-muted">
                    <small>
                        Report generated on <?php echo date('F j, Y \a\t g:i A'); ?> |
                        Total records: <?php echo count($members); ?>
                    </small>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
