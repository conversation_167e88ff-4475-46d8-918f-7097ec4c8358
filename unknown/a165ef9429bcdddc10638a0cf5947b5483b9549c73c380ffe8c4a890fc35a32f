<?php
/**
 * Update Book Quantities Script
 * This script updates the quantity of all books to 50
 */

// Database connection
require_once 'config/database.php';
$database = new Database();
$db = $database->getConnection();

// Function to update book quantity
function updateBookQuantity($db, $book_id, $new_quantity) {
    // Update the book quantity
    $query = "UPDATE books SET quantity = :quantity, available_quantity = :available_quantity 
              WHERE id = :id";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':quantity', $new_quantity);
    $stmt->bindParam(':available_quantity', $new_quantity); // Set available quantity to the same as quantity
    $stmt->bindParam(':id', $book_id);
    
    if ($stmt->execute()) {
        return true;
    } else {
        return false;
    }
}

// Get all books
$query = "SELECT id, title, quantity, available_quantity FROM books";
$stmt = $db->prepare($query);
$stmt->execute();
$books = $stmt->fetchAll();

// New quantity for all books
$new_quantity = 50;

// Update each book
$updated_books = [];
$failed_books = [];

foreach ($books as $book) {
    $result = updateBookQuantity($db, $book['id'], $new_quantity);
    
    if ($result) {
        $updated_books[] = [
            'id' => $book['id'],
            'title' => $book['title'],
            'old_quantity' => $book['quantity'],
            'new_quantity' => $new_quantity
        ];
    } else {
        $failed_books[] = [
            'id' => $book['id'],
            'title' => $book['title']
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Book Quantities - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Update Book Quantities</h1>
        
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Updating Book Quantities to <?php echo $new_quantity; ?></h5>
                
                <?php if (count($updated_books) > 0): ?>
                    <div class="alert alert-success mt-3">
                        <strong>Success!</strong> Updated <?php echo count($updated_books); ?> books to quantity <?php echo $new_quantity; ?>.
                    </div>
                    
                    <h6 class="mt-4">Updated Books:</h6>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Title</th>
                                    <th>Old Quantity</th>
                                    <th>New Quantity</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($updated_books as $book): ?>
                                <tr>
                                    <td><?php echo $book['id']; ?></td>
                                    <td><?php echo htmlspecialchars($book['title']); ?></td>
                                    <td><?php echo $book['old_quantity']; ?></td>
                                    <td class="text-success fw-bold"><?php echo $book['new_quantity']; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning mt-3">
                        <strong>Warning!</strong> No books were updated.
                    </div>
                <?php endif; ?>
                
                <?php if (count($failed_books) > 0): ?>
                    <div class="alert alert-danger mt-3">
                        <strong>Error!</strong> Failed to update <?php echo count($failed_books); ?> books.
                    </div>
                    
                    <h6 class="mt-4">Failed Books:</h6>
                    <ul>
                        <?php foreach ($failed_books as $book): ?>
                        <li><?php echo htmlspecialchars($book['title']); ?> (ID: <?php echo $book['id']; ?>)</li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
                
                <div class="mt-4">
                    <a href="books/index.php" class="btn btn-primary">View All Books</a>
                    <a href="admin/dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
