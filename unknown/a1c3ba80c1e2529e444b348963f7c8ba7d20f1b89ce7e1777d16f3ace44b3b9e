<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if user is already logged in
if (isLoggedIn()) {
    redirect(url('admin/dashboard.php'));
} elseif (isMemberLoggedIn()) {
    redirect(url('member_dashboard.php'));
}

$error = '';
$success = '';
$token = isset($_GET['token']) ? $_GET['token'] : '';
$valid_token = false;
$is_member = false;
$user_id = null;

// Validate token
if (!empty($token)) {
    // Connect to database
    $database = new Database();
    $db = $database->getConnection();

    // Check if token exists in users table
    $query = "SELECT id FROM users WHERE reset_token = :token AND reset_expires > NOW()";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':token', $token);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $row = $stmt->fetch();
        $user_id = $row['id'];
        $valid_token = true;
    } else {
        // Check if token exists in members table
        $query = "SELECT id FROM members WHERE reset_token = :token AND reset_expires > NOW()";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':token', $token);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch();
            $user_id = $row['id'];
            $valid_token = true;
            $is_member = true;
        }
    }
}

// Process reset password form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $valid_token) {
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate input
    if (empty($password) || empty($confirm_password)) {
        $error = 'Please enter both password fields';
    } elseif (strlen($password) < 8) {
        $error = 'Password must be at least 8 characters long';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match';
    } else {
        // Hash the new password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        // Connect to database
        $database = new Database();
        $db = $database->getConnection();

        if ($is_member) {
            // Update members table with new password
            $query = "UPDATE members SET password = :password, reset_token = NULL, reset_expires = NULL WHERE id = :id";
        } else {
            // Update users table with new password
            $query = "UPDATE users SET password = :password, reset_token = NULL, reset_expires = NULL WHERE id = :id";
        }

        $stmt = $db->prepare($query);
        $stmt->bindParam(':password', $hashed_password);
        $stmt->bindParam(':id', $user_id);
        
        if ($stmt->execute()) {
            $success = 'Your password has been reset successfully. You can now <a href="' . url('login.php') . '">login</a> with your new password.';
        } else {
            $error = 'An error occurred while resetting your password. Please try again.';
        }
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-image: url('assets/images/library-background.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .reset-password-container {
            max-width: 500px;
            width: 100%;
            margin: 0 auto;
            animation: fadeIn 0.8s ease-in-out;
        }
        .card {
            border: none;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.8rem 2rem rgba(0, 0, 0, 0.25);
        }
        .card-header {
            background-color: #343a40;
            color: white;
            font-weight: bold;
            padding: 1.2rem;
        }
        .btn-primary {
            background-color: #343a40;
            border-color: #343a40;
            padding: 0.6rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #23272b;
            border-color: #23272b;
            transform: translateY(-2px);
        }
        .form-control {
            padding: 0.6rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #343a40;
            box-shadow: 0 0 0 0.25rem rgba(52, 58, 64, 0.25);
        }
        .password-toggle {
            cursor: pointer;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
            color: #6c757d;
        }
        .password-field {
            position: relative;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @media (max-width: 576px) {
            .reset-password-container {
                max-width: 100%;
                padding: 0 15px;
            }
            .card-body {
                padding: 1.5rem !important;
            }
        }
    </style>
</head>
<body>
    <div class="reset-password-container">
        <div class="card">
            <div class="card-header text-center py-3">
                <h4 class="mb-0"><i class="bi bi-book me-2"></i>Library Management System</h4>
            </div>
            <div class="card-body p-4">
                <h5 class="card-title text-center mb-4">Reset Password</h5>

                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <?php echo h($error); ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                    <div class="alert alert-success">
                        <?php echo $success; ?>
                    </div>
                <?php elseif (!$valid_token): ?>
                    <div class="alert alert-danger">
                        Invalid or expired password reset token. Please request a new password reset link.
                    </div>
                    <div class="text-center mt-4">
                        <a href="<?php echo url('forgot_password.php'); ?>" class="btn btn-primary">
                            <i class="bi bi-arrow-repeat me-2"></i>Request New Reset Link
                        </a>
                    </div>
                <?php else: ?>
                    <p class="text-muted mb-4">Please enter your new password below.</p>
                    <form method="post" action="<?php echo h($_SERVER['PHP_SELF'] . '?token=' . h($token)); ?>">
                        <div class="mb-3">
                            <label for="password" class="form-label">New Password</label>
                            <div class="input-group password-field">
                                <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                <input type="password" class="form-control" id="password" name="password" required autofocus>
                                <span class="password-toggle" id="togglePassword">
                                    <i class="bi bi-eye"></i>
                                </span>
                            </div>
                            <div class="form-text">Password must be at least 8 characters long</div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <div class="input-group password-field">
                                <span class="input-group-text"><i class="bi bi-lock-fill"></i></span>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                <span class="password-toggle" id="toggleConfirmPassword">
                                    <i class="bi bi-eye"></i>
                                </span>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>Reset Password
                            </button>
                        </div>
                    </form>
                <?php endif; ?>

                <div class="mt-4 text-center">
                    <p><a href="<?php echo url('login.php'); ?>" class="text-decoration-none"><i class="bi bi-arrow-left me-1"></i> Back to Login</a></p>
                </div>
            </div>
            <div class="card-footer text-center py-3">
                <p class="mb-0">Library Management System &copy; <?php echo date('Y'); ?></p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const togglePassword = document.getElementById('togglePassword');
            const passwordField = document.getElementById('password');
            const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
            const confirmPasswordField = document.getElementById('confirm_password');
            
            if (togglePassword && passwordField) {
                togglePassword.addEventListener('click', function() {
                    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordField.setAttribute('type', type);
                    
                    // Toggle the eye icon
                    const eyeIcon = this.querySelector('i');
                    eyeIcon.classList.toggle('bi-eye');
                    eyeIcon.classList.toggle('bi-eye-slash');
                });
            }
            
            if (toggleConfirmPassword && confirmPasswordField) {
                toggleConfirmPassword.addEventListener('click', function() {
                    const type = confirmPasswordField.getAttribute('type') === 'password' ? 'text' : 'password';
                    confirmPasswordField.setAttribute('type', type);
                    
                    // Toggle the eye icon
                    const eyeIcon = this.querySelector('i');
                    eyeIcon.classList.toggle('bi-eye');
                    eyeIcon.classList.toggle('bi-eye-slash');
                });
            }
        });
    </script>
</body>
</html>
