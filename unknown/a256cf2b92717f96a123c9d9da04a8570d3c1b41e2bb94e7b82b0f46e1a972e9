<?php
/**
 * Logout Success Page
 * This page shows a confirmation that logout was successful
 */
session_start();

// Make sure session is completely cleared
$_SESSION = [];
session_destroy();

// Include functions if available
if (file_exists('includes/functions.php')) {
    require_once 'includes/functions.php';
}
if (file_exists('config/config.php')) {
    require_once 'config/config.php';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logout Successful - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .logout-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .logout-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .logout-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 1rem;
        }
        .countdown {
            font-size: 1.2rem;
            color: #6c757d;
            margin: 1rem 0;
        }
        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            color: white;
            font-weight: 500;
            transition: transform 0.2s;
        }
        .btn-home:hover {
            transform: translateY(-2px);
            color: white;
        }
    </style>
</head>
<body>
    <div class="logout-container">
        <div class="logout-card">
            <div class="logout-icon">
                <i class="bi bi-check-circle"></i>
            </div>
            <h2 class="mb-3">Successfully Logged Out</h2>
            <p class="text-muted mb-4">
                You have been safely logged out of the Library Management System.
                Thank you for using our services!
            </p>

            <div class="countdown mb-4">
                <span id="countdown-text">Redirecting to home page in <span id="countdown">5</span> seconds...</span>
            </div>

            <div class="d-grid gap-2">
                <a href="<?php echo function_exists('url') ? url('home.php') : 'home.php'; ?>" class="btn btn-home">
                    <i class="bi bi-house me-2"></i>Go to Home Page
                </a>
                <a href="<?php echo function_exists('url') ? url('login.php') : 'login.php'; ?>" class="btn btn-outline-primary">
                    <i class="bi bi-box-arrow-in-right me-2"></i>Login Again
                </a>
            </div>

            <div class="mt-4">
                <small class="text-muted">
                    <i class="bi bi-shield-check me-1"></i>
                    Your session has been securely terminated
                </small>
            </div>
        </div>
    </div>

    <script>
        // Countdown timer
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        const countdownText = document.getElementById('countdown-text');

        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;

            if (countdown <= 0) {
                clearInterval(timer);
                countdownText.innerHTML = '<i class="bi bi-arrow-right me-2"></i>Redirecting now...';
                window.location.href = '<?php echo function_exists('url') ? url('home.php') : 'home.php'; ?>';
            }
        }, 1000);

        // Allow user to cancel redirect by clicking anywhere
        document.addEventListener('click', () => {
            clearInterval(timer);
            countdownText.innerHTML = '<i class="bi bi-hand-thumbs-up me-2"></i>Redirect cancelled';
        });
    </script>
</body>
</html>
