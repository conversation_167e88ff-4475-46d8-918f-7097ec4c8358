<?php
/**
 * Dashboard Statistics API
 * Returns real-time statistics for the librarian dashboard
 */

header('Content-Type: application/json');

// Include required files
try {
    require_once '../config/database.php';
    require_once '../config/config.php';
    require_once '../includes/functions.php';
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error loading required files: ' . $e->getMessage()
    ]);
    exit;
}

try {
    // Connect to database
    $database = new Database();
    $db = $database->getConnection();

    // Get statistics for dashboard
    $stats = [];

    // Total books
    $query = "SELECT COUNT(*) as total FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['total_books'] = $stmt->fetch()['total'];

    // Available books
    $query = "SELECT SUM(available_quantity) as available FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['available_books'] = $stmt->fetch()['available'] ?: 0;

    // Total members
    $query = "SELECT COUNT(*) as total FROM members";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['total_members'] = $stmt->fetch()['total'];

    // Active loans (currently borrowed - not overdue yet)
    $query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'borrowed' AND due_date >= CURDATE()";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['active_loans'] = $stmt->fetch()['total'];

    // Overdue books (explicitly overdue OR borrowed past due date)
    $query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'overdue' OR (status = 'borrowed' AND due_date < CURDATE())";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['overdue_books'] = $stmt->fetch()['total'];

    // Book reservations
    $query = "SELECT COUNT(*) as total FROM book_reservations WHERE status = 'pending'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['pending_reservations'] = $stmt->fetch()['total'];

    // Today's due books
    $query = "SELECT COUNT(*) as total FROM book_loans WHERE due_date = CURDATE() AND status = 'borrowed'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['due_today'] = $stmt->fetch()['total'];

    // Additional metrics for enhanced functionality
    // Total copies (sum of all book quantities)
    $query = "SELECT SUM(quantity) as total_copies FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['total_copies'] = $stmt->fetch()['total_copies'] ?: 0;

    // Books currently on loan
    $stats['books_on_loan'] = $stats['active_loans'] + $stats['overdue_books'];

    // Utilization rate
    $stats['utilization_rate'] = $stats['total_copies'] > 0 ? 
        round(($stats['books_on_loan'] / $stats['total_copies']) * 100, 1) : 0;

    // Availability rate
    $stats['availability_rate'] = $stats['total_copies'] > 0 ? 
        round(($stats['available_books'] / $stats['total_copies']) * 100, 1) : 0;

    // Recent activity counts (last 24 hours)
    $query = "SELECT COUNT(*) as count FROM book_loans WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['loans_today'] = $stmt->fetch()['count'];

    $query = "SELECT COUNT(*) as count FROM book_loans WHERE return_date >= DATE_SUB(NOW(), INTERVAL 24 HOUR) AND status = 'returned'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['returns_today'] = $stmt->fetch()['returns_today'];

    $query = "SELECT COUNT(*) as count FROM members WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['new_members_today'] = $stmt->fetch()['count'];

    // Response
    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => 'Statistics updated successfully'
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
