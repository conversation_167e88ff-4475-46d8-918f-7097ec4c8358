<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Test Image Paths</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h2>Image Path Testing</h2>";

echo "<h3>Configuration Test</h3>";
echo "<p><strong>BASE_URL:</strong> " . BASE_URL . "</p>";
echo "<p><strong>COVERS_PATH:</strong> " . COVERS_PATH . "</p>";
echo "<p><strong>url() function test:</strong> " . url('uploads/covers/test.jpg') . "</p>";

echo "<h3>Directory Check</h3>";
$covers_dir = 'uploads/covers/';
echo "<p><strong>Covers directory exists:</strong> " . (is_dir($covers_dir) ? 'Yes' : 'No') . "</p>";
echo "<p><strong>Covers directory readable:</strong> " . (is_readable($covers_dir) ? 'Yes' : 'No') . "</p>";

if (is_dir($covers_dir)) {
    $files = scandir($covers_dir);
    echo "<p><strong>Files in covers directory:</strong></p>";
    echo "<ul>";
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            $file_path = $covers_dir . $file;
            $is_image = in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif']);
            echo "<li>" . htmlspecialchars($file) . " - Size: " . filesize($file_path) . " bytes - Image: " . ($is_image ? 'Yes' : 'No') . "</li>";
        }
    }
    echo "</ul>";
}

echo "<h3>Direct Image Test</h3>";
$test_images = ['1984_cover.jpg', '1747724281_AGameOfThrones.jpg', '1747724303_Good to Great.jpg'];

foreach ($test_images as $image) {
    $file_path = $covers_dir . $image;
    $url_path = url('uploads/covers/' . $image);
    
    echo "<div class='card mb-3'>";
    echo "<div class='card-header'>";
    echo "<strong>Testing: " . htmlspecialchars($image) . "</strong>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<p><strong>File exists:</strong> " . (file_exists($file_path) ? 'Yes' : 'No') . "</p>";
    echo "<p><strong>File path:</strong> " . htmlspecialchars($file_path) . "</p>";
    echo "<p><strong>URL path:</strong> " . htmlspecialchars($url_path) . "</p>";
    
    if (file_exists($file_path)) {
        echo "<p><strong>File size:</strong> " . filesize($file_path) . " bytes</p>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<h6>Using relative path:</h6>";
        echo "<img src='" . htmlspecialchars($covers_dir . $image) . "' style='max-width: 200px; height: auto;' alt='Test image' onerror='this.style.border=\"2px solid red\"; this.alt=\"Failed to load\";'>";
        echo "</div>";
        echo "<div class='col-md-6'>";
        echo "<h6>Using url() function:</h6>";
        echo "<img src='" . htmlspecialchars($url_path) . "' style='max-width: 200px; height: auto;' alt='Test image' onerror='this.style.border=\"2px solid red\"; this.alt=\"Failed to load\";'>";
        echo "</div>";
        echo "</div>";
    } else {
        echo "<p class='text-danger'>File not found!</p>";
    }
    echo "</div>";
    echo "</div>";
}

echo "<h3>Browser Console Test</h3>";
echo "<p>Check the browser console for any JavaScript errors or network issues.</p>";
echo "<script>
console.log('BASE_URL:', '" . BASE_URL . "');
console.log('Testing image load...');

// Test loading an image via JavaScript
var img = new Image();
img.onload = function() {
    console.log('Image loaded successfully:', this.src);
};
img.onerror = function() {
    console.error('Failed to load image:', this.src);
};
img.src = '" . url('uploads/covers/1984_cover.jpg') . "';
</script>";

echo "</div></body></html>";
?>
