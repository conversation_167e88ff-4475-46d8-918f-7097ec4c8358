<?php
require_once 'config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "<h3>Members Table Structure:</h3>";
    $result = $pdo->query('DESCRIBE members');
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
    while($row = $result->fetch()) {
        echo "<tr><td>{$row['Field']}</td><td>{$row['Type']}</td><td>{$row['Null']}</td><td>{$row['Key']}</td></tr>";
    }
    echo "</table>";
    
    echo "<h3>Sample Member Data:</h3>";
    $sample = $pdo->query('SELECT * FROM members LIMIT 3');
    $members = $sample->fetchAll();
    if (!empty($members)) {
        echo "<table border='1'>";
        echo "<tr>";
        foreach (array_keys($members[0]) as $column) {
            echo "<th>$column</th>";
        }
        echo "</tr>";
        foreach ($members as $member) {
            echo "<tr>";
            foreach ($member as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
