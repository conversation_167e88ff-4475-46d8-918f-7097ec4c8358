<?php
/**
 * Simple Server Test - Updated
 */

echo "<h1>🔧 LMS Server Test</h1>";
echo "<p>✅ PHP is working!</p>";
echo "<p>📅 Current time: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>📂 Current directory: " . __DIR__ . "</p>";
echo "<p>🌐 Server: " . $_SERVER['SERVER_NAME'] . "</p>";
echo "<p>🔗 Request URI: " . $_SERVER['REQUEST_URI'] . "</p>";

// Test database connection
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    echo "<p>✅ Database connection successful!</p>";
    
    // Test members table
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM members");
    $stmt->execute();
    $result = $stmt->fetch();
    echo "<p>👥 Members in database: " . $result['count'] . "</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test file paths
$members_file = 'members/index.php';
echo "<p>📁 Members file exists: " . (file_exists($members_file) ? '✅ Yes' : '❌ No') . "</p>";
echo "<p>📁 Members file readable: " . (is_readable($members_file) ? '✅ Yes' : '❌ No') . "</p>";

// Test authentication bypass
require_once 'includes/functions.php';
echo "<p>🔓 Authentication bypassed: " . (isLoggedIn() ? '✅ Yes' : '❌ No') . "</p>";

// Test URLs
echo "<h3>🔗 Test URLs:</h3>";
echo "<ul>";
echo "<li><a href='members/index.php' target='_blank'>Members Page</a></li>";
echo "<li><a href='admin/dashboard.php' target='_blank'>Admin Dashboard</a></li>";
echo "<li><a href='home.php' target='_blank'>Home Page</a></li>";
echo "</ul>";

echo "<h3>📋 Troubleshooting:</h3>";
echo "<p><strong>If you're getting 'Not Found' errors:</strong></p>";
echo "<ol>";
echo "<li>Make sure XAMPP is running</li>";
echo "<li>Check that Apache is started in XAMPP Control Panel</li>";
echo "<li>Verify the URL: <code>http://localhost/Library/lms/members/index.php</code></li>";
echo "<li>Try accessing this test page first: <code>http://localhost/Library/lms/test_server_access.php</code></li>";
echo "</ol>";
?>
