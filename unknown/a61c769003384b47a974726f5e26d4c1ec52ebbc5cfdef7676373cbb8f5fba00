<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

header('Content-Type: application/json');

try {
    $database = new Database();
    $db = $database->getConnection();

    $stats = [];

    // 1. BASIC STATISTICS (STANDARDIZED)
    $basic_queries = [
        'total_books' => "SELECT COUNT(*) as count FROM books",
        'total_copies' => "SELECT SUM(quantity) as count FROM books",
        'available_books' => "SELECT SUM(available_quantity) as count FROM books",
        'total_members' => "SELECT COUNT(*) as count FROM members",
        'active_loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed' AND due_date >= CURDATE()",
        'overdue_books' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'overdue' OR (status = 'borrowed' AND due_date < CURDATE())",
        'returned_loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'returned'",
        'total_loans' => "SELECT COUNT(*) as count FROM book_loans"
    ];

    foreach($basic_queries as $key => $query) {
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats[$key] = $stmt->fetch()['count'];
    }

    // 2. MEMBER ANALYTICS (STANDARDIZED)
    $member_queries = [
        'currently_borrowing' => "SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status = 'borrowed' AND due_date >= CURDATE()",
        'members_with_overdue' => "SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status = 'overdue' OR (status = 'borrowed' AND due_date < CURDATE())",
        'members_who_returned' => "SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status = 'returned'",
        'members_who_borrowed' => "SELECT COUNT(DISTINCT member_id) as count FROM book_loans",
        'members_with_fines' => "SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE fine > 0",
        'new_members_30_days' => "SELECT COUNT(*) as count FROM members WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)",
        'new_members_7_days' => "SELECT COUNT(*) as count FROM members WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)"
    ];

    foreach($member_queries as $key => $query) {
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats[$key] = $stmt->fetch()['count'];
    }

    // Calculate derived member stats
    $stats['members_never_borrowed'] = $stats['total_members'] - $stats['members_who_borrowed'];

    // 3. FINANCIAL DATA
    $stmt = $db->prepare("SELECT SUM(fine) as total FROM book_loans WHERE fine > 0");
    $stmt->execute();
    $stats['total_fines'] = $stmt->fetch()['total'] ?? 0;

    $stats['avg_fine_per_member'] = $stats['members_with_fines'] > 0 ?
        round($stats['total_fines'] / $stats['members_with_fines'], 2) : 0;

    // 4. ACTIVITY METRICS
    $stats['avg_loans_per_member'] = $stats['total_members'] > 0 ?
        round($stats['total_loans'] / $stats['total_members'], 1) : 0;

    // 5. CALCULATED PERCENTAGES
    $stats['book_utilization_rate'] = $stats['total_copies'] > 0 ?
        round((($stats['active_loans'] + $stats['overdue_books']) / $stats['total_copies']) * 100, 1) : 0;

    $stats['book_availability_rate'] = $stats['total_copies'] > 0 ?
        round(($stats['available_books'] / $stats['total_copies']) * 100, 1) : 0;

    $stats['member_engagement_rate'] = $stats['total_members'] > 0 ?
        round(($stats['members_who_borrowed'] / $stats['total_members']) * 100, 1) : 0;

    $stats['currently_borrowing_percentage'] = $stats['total_members'] > 0 ?
        round(($stats['currently_borrowing'] / $stats['total_members']) * 100, 1) : 0;

    $stats['returned_percentage'] = $stats['total_members'] > 0 ?
        round(($stats['members_who_returned'] / $stats['total_members']) * 100, 1) : 0;

    $stats['overdue_percentage'] = $stats['total_members'] > 0 ?
        round(($stats['members_with_overdue'] / $stats['total_members']) * 100, 1) : 0;

    // 6. RISK ANALYSIS
    $risk_query = "
        SELECT
            SUM(CASE WHEN overdue_count > 3 THEN 1 ELSE 0 END) as high_risk_members,
            SUM(CASE WHEN overdue_count BETWEEN 1 AND 3 THEN 1 ELSE 0 END) as medium_risk_members,
            SUM(CASE WHEN total_fine > 50 THEN 1 ELSE 0 END) as high_fine_members
        FROM (
            SELECT m.id,
                   COUNT(CASE WHEN bl.status = 'overdue' THEN 1 END) as overdue_count,
                   COALESCE(SUM(bl.fine), 0) as total_fine
            FROM members m
            LEFT JOIN book_loans bl ON m.id = bl.member_id
            GROUP BY m.id
        ) as member_risk
    ";
    $stmt = $db->prepare($risk_query);
    $stmt->execute();
    $risk_data = $stmt->fetch();

    $stats['high_risk_members'] = $risk_data['high_risk_members'] ?? 0;
    $stats['medium_risk_members'] = $risk_data['medium_risk_members'] ?? 0;
    $stats['high_fine_members'] = $risk_data['high_fine_members'] ?? 0;

    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => 'Enhanced dashboard statistics calculated successfully'
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
