<?php
// Test script to verify dashboard fixes
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Set test session data
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
    $_SESSION['role'] = 'admin';
}

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Dashboard Fix Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css'>";
echo "</head>";
echo "<body>";

echo "<div class='container mt-5'>";
echo "<h1>🔧 Dashboard Fix Test Results</h1>";

// Test 1: Check if timeAgo function exists and works
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h5>Test 1: timeAgo() Function</h5>";
echo "</div>";
echo "<div class='card-body'>";

if (function_exists('timeAgo')) {
    echo "<p class='text-success'>✅ timeAgo() function exists</p>";
    
    // Test the function
    $test_time = date('Y-m-d H:i:s', strtotime('-2 hours'));
    $result = timeAgo($test_time);
    echo "<p><strong>Test:</strong> timeAgo('$test_time') = '$result'</p>";
    
    if (strpos($result, 'hour') !== false) {
        echo "<p class='text-success'>✅ Function working correctly</p>";
    } else {
        echo "<p class='text-warning'>⚠️ Function returned unexpected result</p>";
    }
} else {
    echo "<p class='text-danger'>❌ timeAgo() function not found</p>";
}

echo "</div>";
echo "</div>";

// Test 2: Check database connection
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h5>Test 2: Database Connection</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<p class='text-success'>✅ Database connection successful</p>";
        
        // Test a simple query
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM books");
        $stmt->execute();
        $result = $stmt->fetch();
        echo "<p><strong>Books count:</strong> " . ($result['count'] ?? 0) . "</p>";
    } else {
        echo "<p class='text-danger'>❌ Database connection failed</p>";
    }
} catch (Exception $e) {
    echo "<p class='text-danger'>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "</div>";
echo "</div>";

// Test 3: Check if dashboard.php loads without errors
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h5>Test 3: Dashboard PHP Syntax</h5>";
echo "</div>";
echo "<div class='card-body'>";

$dashboard_file = __DIR__ . '/dashboard.php';
if (file_exists($dashboard_file)) {
    echo "<p class='text-success'>✅ Dashboard file exists</p>";
    
    // Check for syntax errors using php -l equivalent
    $output = shell_exec("php -l \"$dashboard_file\" 2>&1");
    if (strpos($output, 'No syntax errors') !== false) {
        echo "<p class='text-success'>✅ No PHP syntax errors detected</p>";
    } else {
        echo "<p class='text-danger'>❌ PHP syntax errors found:</p>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
    }
} else {
    echo "<p class='text-danger'>❌ Dashboard file not found</p>";
}

echo "</div>";
echo "</div>";

// Test 4: Check CSS file
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-warning text-white'>";
echo "<h5>Test 4: CSS Fixes File</h5>";
echo "</div>";
echo "<div class='card-body'>";

$css_file = __DIR__ . '/css/dashboard-fixes.css';
if (file_exists($css_file)) {
    echo "<p class='text-success'>✅ CSS fixes file exists</p>";
    $css_size = filesize($css_file);
    echo "<p><strong>File size:</strong> " . number_format($css_size) . " bytes</p>";
} else {
    echo "<p class='text-danger'>❌ CSS fixes file not found</p>";
}

echo "</div>";
echo "</div>";

// Test 5: Session check
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-secondary text-white'>";
echo "<h5>Test 5: Session Status</h5>";
echo "</div>";
echo "<div class='card-body'>";

echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>User ID:</strong> " . ($_SESSION['user_id'] ?? 'Not set') . "</p>";
echo "<p><strong>Username:</strong> " . ($_SESSION['username'] ?? 'Not set') . "</p>";
echo "<p><strong>Role:</strong> " . ($_SESSION['role'] ?? 'Not set') . "</p>";

if (function_exists('isLoggedIn')) {
    echo "<p><strong>isLoggedIn():</strong> " . (isLoggedIn() ? 'true' : 'false') . "</p>";
}

if (function_exists('isAdmin')) {
    echo "<p><strong>isAdmin():</strong> " . (isAdmin() ? 'true' : 'false') . "</p>";
}

echo "</div>";
echo "</div>";

// Summary
echo "<div class='alert alert-info'>";
echo "<h4>🎯 Test Summary</h4>";
echo "<p>If all tests show green checkmarks (✅), the dashboard should work properly without the timeAgo redeclaration error.</p>";
echo "<p><strong>Next step:</strong> <a href='dashboard.php' class='btn btn-primary btn-sm'>Test the actual dashboard</a></p>";
echo "</div>";

echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
