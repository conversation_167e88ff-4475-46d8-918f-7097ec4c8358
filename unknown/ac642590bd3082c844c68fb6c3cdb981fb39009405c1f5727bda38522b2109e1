<?php
/**
 * Google OAuth Setup
 * 
 * This page helps users configure Google OAuth credentials.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if form is submitted
$message = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['client_id'], $_POST['client_secret'])) {
    $client_id = trim($_POST['client_id']);
    $client_secret = trim($_POST['client_secret']);
    
    // Validate inputs
    if (empty($client_id) || empty($client_secret)) {
        $message = 'Both Client ID and Client Secret are required.';
    } else {
        // Update the Google OAuth configuration file
        $config_file = __DIR__ . '/config/google_oauth.php';
        $config_content = file_get_contents($config_file);
        
        // Replace the client ID and client secret
        $config_content = preg_replace(
            "/define\('GOOGLE_CLIENT_ID', '.*?'\);/",
            "define('GOOGLE_CLIENT_ID', '$client_id');",
            $config_content
        );
        
        $config_content = preg_replace(
            "/define\('GOOGLE_CLIENT_SECRET', '.*?'\);/",
            "define('GOOGLE_CLIENT_SECRET', '$client_secret');",
            $config_content
        );
        
        // Save the updated configuration
        if (file_put_contents($config_file, $config_content)) {
            $success = true;
            $message = 'Google OAuth credentials have been updated successfully.';
        } else {
            $message = 'Failed to update Google OAuth configuration. Please check file permissions.';
        }
    }
}

// Get the current redirect URI
$redirect_uri = BASE_URL . 'google_callback.php';

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth Setup - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px;
        }
        .setup-container {
            max-width: 800px;
            margin: 40px auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            padding: 30px;
        }
        .step {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        .step:last-child {
            border-bottom: none;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background-color: #007bff;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            margin-right: 10px;
        }
        .code-block {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .alert {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <h1 class="mb-4"><i class="bi bi-google me-2"></i>Google OAuth Setup</h1>
        
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $success ? 'success' : 'danger'; ?>" role="alert">
                <?php echo h($message); ?>
            </div>
        <?php endif; ?>
        
        <div class="step">
            <h3><span class="step-number">1</span>Create a Google Cloud Project</h3>
            <p>Go to the <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a> and create a new project or select an existing one.</p>
        </div>
        
        <div class="step">
            <h3><span class="step-number">2</span>Configure OAuth Consent Screen</h3>
            <ol>
                <li>In the Google Cloud Console, go to "APIs & Services" > "OAuth consent screen"</li>
                <li>Select "External" user type (unless you have a Google Workspace account)</li>
                <li>Fill in the required information (App name, User support email, Developer contact information)</li>
                <li>Add the scopes: <code>.../auth/userinfo.email</code> and <code>.../auth/userinfo.profile</code></li>
                <li>Add your domain to the authorized domains</li>
                <li>Save and continue</li>
            </ol>
        </div>
        
        <div class="step">
            <h3><span class="step-number">3</span>Create OAuth Client ID</h3>
            <ol>
                <li>In the Google Cloud Console, go to "APIs & Services" > "Credentials"</li>
                <li>Click "Create Credentials" > "OAuth client ID"</li>
                <li>Select "Web application" as the application type</li>
                <li>Add a name for your OAuth client</li>
                <li>Add your domain to the "Authorized JavaScript origins" (e.g., <code><?php echo h(BASE_URL); ?></code>)</li>
                <li>Add your redirect URI to the "Authorized redirect URIs":</li>
            </ol>
            <div class="code-block">
                <?php echo h($redirect_uri); ?>
            </div>
            <p>Click "Create" and you'll receive your Client ID and Client Secret.</p>
        </div>
        
        <div class="step">
            <h3><span class="step-number">4</span>Enter Your OAuth Credentials</h3>
            <form method="post" action="">
                <div class="mb-3">
                    <label for="client_id" class="form-label">Client ID</label>
                    <input type="text" class="form-control" id="client_id" name="client_id" placeholder="Enter your Google Client ID" required>
                    <div class="form-text">Example: 123456789012-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com</div>
                </div>
                <div class="mb-3">
                    <label for="client_secret" class="form-label">Client Secret</label>
                    <input type="text" class="form-control" id="client_secret" name="client_secret" placeholder="Enter your Google Client Secret" required>
                    <div class="form-text">Example: GOCSPX-abcdefghijklmnopqrstuvwxyz</div>
                </div>
                <button type="submit" class="btn btn-primary">Save Credentials</button>
            </form>
        </div>
        
        <div class="step">
            <h3><span class="step-number">5</span>Test Google Sign-In</h3>
            <p>After saving your credentials, you can test the Google Sign-In functionality:</p>
            <a href="login.php" class="btn btn-success">Go to Login Page</a>
        </div>
        
        <div class="mt-4 text-center">
            <a href="index.php" class="btn btn-outline-secondary">Back to Home</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
