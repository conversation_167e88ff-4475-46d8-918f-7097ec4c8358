<?php
/**
 * Member Quick Actions Widget
 */
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and has appropriate role
if (!isset($_SESSION['user_id']) && !isset($_SESSION['member_id'])) {
    redirect(url('login.php'));
}

// Check if user has permission (admin or librarian)
if (isset($_SESSION['role']) && !in_array($_SESSION['role'], ['admin', 'librarian'])) {
    redirect(url('index.php'));
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Process quick actions
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'quick_search':
            $search_term = trim($_POST['search_term']);
            if (!empty($search_term)) {
                redirect(url("members/index.php?search=" . urlencode($search_term)));
            }
            break;
            
        case 'activate_member':
            $member_id = (int)$_POST['member_id'];
            if ($member_id > 0) {
                $query = "UPDATE members SET membership_status = 'active' WHERE id = :id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':id', $member_id);
                if ($stmt->execute()) {
                    $message = "Member activated successfully!";
                    $message_type = "success";
                } else {
                    $message = "Failed to activate member.";
                    $message_type = "danger";
                }
            }
            break;
            
        case 'suspend_member':
            $member_id = (int)$_POST['member_id'];
            if ($member_id > 0) {
                $query = "UPDATE members SET membership_status = 'suspended' WHERE id = :id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':id', $member_id);
                if ($stmt->execute()) {
                    $message = "Member suspended successfully!";
                    $message_type = "warning";
                } else {
                    $message = "Failed to suspend member.";
                    $message_type = "danger";
                }
            }
            break;
            
        case 'send_reminder':
            $member_id = (int)$_POST['member_id'];
            if ($member_id > 0) {
                // Get member info
                $query = "SELECT m.*, COUNT(bl.id) as overdue_count 
                         FROM members m 
                         LEFT JOIN book_loans bl ON m.id = bl.member_id 
                         WHERE m.id = :id AND bl.return_date IS NULL AND bl.due_date < CURDATE()
                         GROUP BY m.id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':id', $member_id);
                $stmt->execute();
                $member = $stmt->fetch();
                
                if ($member && $member['overdue_count'] > 0) {
                    // In a real system, you would send an email here
                    $message = "Reminder sent to {$member['first_name']} {$member['last_name']} for {$member['overdue_count']} overdue book(s).";
                    $message_type = "info";
                } else {
                    $message = "No overdue books found for this member.";
                    $message_type = "warning";
                }
            }
            break;
    }
}

// Get recent members for quick access
$query = "SELECT * FROM members ORDER BY created_at DESC LIMIT 10";
$stmt = $db->prepare($query);
$stmt->execute();
$recent_members = $stmt->fetchAll();

// Get members with overdue books
$query = "SELECT DISTINCT m.*, COUNT(bl.id) as overdue_count 
          FROM members m 
          JOIN book_loans bl ON m.id = bl.member_id 
          WHERE bl.return_date IS NULL AND bl.due_date < CURDATE()
          GROUP BY m.id 
          ORDER BY overdue_count DESC 
          LIMIT 5";
$stmt = $db->prepare($query);
$stmt->execute();
$overdue_members = $stmt->fetchAll();

// Get inactive members
$query = "SELECT * FROM members WHERE membership_status = 'inactive' ORDER BY membership_date DESC LIMIT 5";
$stmt = $db->prepare($query);
$stmt->execute();
$inactive_members = $stmt->fetchAll();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Quick Actions - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Member Quick Actions</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="index.php" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Members
                        </a>
                    </div>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <?php echo h($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Quick Search -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-search me-2"></i>Quick Member Search
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="">
                                    <input type="hidden" name="action" value="quick_search">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="search_term" 
                                               placeholder="Enter member name, email, or ID..." required>
                                        <button class="btn btn-primary" type="submit">
                                            <i class="bi bi-search"></i> Search
                                        </button>
                                    </div>
                                </form>
                                <div class="mt-3">
                                    <a href="advanced_search.php" class="btn btn-sm btn-outline-secondary">
                                        <i class="bi bi-funnel"></i> Advanced Search
                                    </a>
                                    <a href="add.php" class="btn btn-sm btn-success">
                                        <i class="bi bi-plus"></i> Add Member
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-lightning me-2"></i>Quick Stats
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php
                                // Get quick stats
                                $stats = [];
                                
                                $query = "SELECT COUNT(*) as count FROM members WHERE membership_status = 'active'";
                                $stmt = $db->prepare($query);
                                $stmt->execute();
                                $stats['active'] = $stmt->fetch()['count'];
                                
                                $query = "SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE return_date IS NULL";
                                $stmt = $db->prepare($query);
                                $stmt->execute();
                                $stats['with_loans'] = $stmt->fetch()['count'];
                                
                                $query = "SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE return_date IS NULL AND due_date < CURDATE()";
                                $stmt = $db->prepare($query);
                                $stmt->execute();
                                $stats['overdue'] = $stmt->fetch()['count'];
                                ?>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <h4 class="text-success"><?php echo $stats['active']; ?></h4>
                                        <small>Active Members</small>
                                    </div>
                                    <div class="col-4">
                                        <h4 class="text-primary"><?php echo $stats['with_loans']; ?></h4>
                                        <small>With Loans</small>
                                    </div>
                                    <div class="col-4">
                                        <h4 class="text-danger"><?php echo $stats['overdue']; ?></h4>
                                        <small>Overdue</small>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a href="statistics.php" class="btn btn-sm btn-outline-info w-100">
                                        <i class="bi bi-graph-up"></i> View Full Statistics
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Panels -->
                <div class="row">
                    <!-- Recent Members -->
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-success text-white">
                                <h6 class="card-title mb-0">
                                    <i class="bi bi-person-plus me-2"></i>Recent Members
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if (count($recent_members) > 0): ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach (array_slice($recent_members, 0, 5) as $member): ?>
                                            <div class="list-group-item px-0 py-2">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <h6 class="mb-0"><?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></h6>
                                                        <small class="text-muted"><?php echo h($member['email']); ?></small>
                                                    </div>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="view.php?id=<?php echo $member['id']; ?>" class="btn btn-outline-primary btn-sm">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                        <a href="edit.php?id=<?php echo $member['id']; ?>" class="btn btn-outline-warning btn-sm">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No recent members found.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Members with Overdue Books -->
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-danger text-white">
                                <h6 class="card-title mb-0">
                                    <i class="bi bi-exclamation-triangle me-2"></i>Overdue Books
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if (count($overdue_members) > 0): ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($overdue_members as $member): ?>
                                            <div class="list-group-item px-0 py-2">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <h6 class="mb-0"><?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></h6>
                                                        <small class="text-danger"><?php echo $member['overdue_count']; ?> overdue book(s)</small>
                                                    </div>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="action" value="send_reminder">
                                                        <input type="hidden" name="member_id" value="<?php echo $member['id']; ?>">
                                                        <button type="submit" class="btn btn-outline-warning btn-sm" title="Send Reminder">
                                                            <i class="bi bi-envelope"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No overdue books found.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Inactive Members -->
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-secondary text-white">
                                <h6 class="card-title mb-0">
                                    <i class="bi bi-person-x me-2"></i>Inactive Members
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if (count($inactive_members) > 0): ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($inactive_members as $member): ?>
                                            <div class="list-group-item px-0 py-2">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <h6 class="mb-0"><?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></h6>
                                                        <small class="text-muted">Since <?php echo date('M j, Y', strtotime($member['membership_date'])); ?></small>
                                                    </div>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="action" value="activate_member">
                                                        <input type="hidden" name="member_id" value="<?php echo $member['id']; ?>">
                                                        <button type="submit" class="btn btn-outline-success btn-sm" title="Activate Member">
                                                            <i class="bi bi-check-circle"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No inactive members found.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Action Buttons -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="bi bi-lightning-charge me-2"></i>Quick Actions
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <a href="add.php" class="btn btn-success w-100">
                                            <i class="bi bi-person-plus me-2"></i>Add New Member
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="bulk_operations.php" class="btn btn-warning w-100">
                                            <i class="bi bi-gear me-2"></i>Bulk Operations
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="statistics.php" class="btn btn-info w-100">
                                            <i class="bi bi-graph-up me-2"></i>View Statistics
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="activity_log.php" class="btn btn-primary w-100">
                                            <i class="bi bi-clock-history me-2"></i>Activity Log
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
