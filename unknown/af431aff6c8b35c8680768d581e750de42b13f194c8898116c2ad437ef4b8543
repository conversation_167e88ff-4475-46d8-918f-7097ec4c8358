<?php
/**
 * Session Fix - Clear and Reset Sessions
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Clear all session data
$_SESSION = array();

// Destroy the session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy the session
session_destroy();

// Clear any remember me cookies
if (isset($_COOKIE['member_email'])) {
    setcookie('member_email', '', time() - 3600, '/');
}

if (isset($_COOKIE['remember_user'])) {
    setcookie('remember_user', '', time() - 3600, '/');
}

if (isset($_COOKIE['persistent_google'])) {
    setcookie('persistent_google', '', time() - 3600, '/');
}

if (isset($_COOKIE['google_remember_token'])) {
    setcookie('google_remember_token', '', time() - 3600, '/');
}

// Start a fresh session
session_start();
session_regenerate_id(true);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Fixed - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 600px; margin: 100px auto; }
        .card { border: none; box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); }
        .card-header { background-color: #28a745; color: white; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header text-center py-3">
                <h4 class="mb-0"><i class="bi bi-check-circle me-2"></i>Session Fixed</h4>
            </div>
            <div class="card-body p-4 text-center">
                <div class="alert alert-success">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <strong>Success!</strong> All sessions and cookies have been cleared.
                </div>
                
                <p>Your session has been reset. You can now try logging in again.</p>
                
                <div class="d-grid gap-2 mt-4">
                    <a href="google_login_fix.php" class="btn btn-primary">
                        <i class="bi bi-google me-2"></i>Try Google Login Fix
                    </a>
                    <a href="member_login.php" class="btn btn-outline-primary">
                        <i class="bi bi-person-circle me-2"></i>Member Login
                    </a>
                    <a href="quick_member_access.php" class="btn btn-outline-success">
                        <i class="bi bi-lightning-charge me-2"></i>Quick Access
                    </a>
                    <a href="home.php" class="btn btn-outline-secondary">
                        <i class="bi bi-house me-2"></i>Home Page
                    </a>
                </div>
                
                <div class="mt-4">
                    <h6>What was cleared:</h6>
                    <ul class="list-unstyled text-start">
                        <li><i class="bi bi-check text-success me-2"></i>All session variables</li>
                        <li><i class="bi bi-check text-success me-2"></i>Session cookies</li>
                        <li><i class="bi bi-check text-success me-2"></i>Remember me cookies</li>
                        <li><i class="bi bi-check text-success me-2"></i>Google authentication cookies</li>
                        <li><i class="bi bi-check text-success me-2"></i>Fresh session started</li>
                    </ul>
                </div>
            </div>
            <div class="card-footer text-center py-3">
                <p class="mb-0 text-muted">Library Management System &copy; <?php echo date('Y'); ?></p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
