<?php
/**
 * Test URL Generation from Admin Directory
 * This file tests URL generation when called from within the admin directory
 */

require_once '../config/config.php';

echo "<h2>URL Generation Test from Admin Directory</h2>";

// Show current script info
echo "<h3>Current Script Info:</h3>";
echo "<p><strong>SCRIPT_NAME:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";
echo "<p><strong>Current Directory:</strong> " . dirname($_SERVER['SCRIPT_NAME'] ?? '') . "</p>";

// Test URL function from admin directory
echo "<h3>URL Generation from Admin Directory:</h3>";
$test_paths = [
    'admin/dashboard.php',
    'admin/settings.php',
    'admin/users.php',
    'admin/email_settings.php',
    'members/index.php',
    'books/index.php',
    'login.php',
    'index.php'
];

foreach ($test_paths as $path) {
    $generated_url = url($path);
    echo "<p><strong>$path</strong> → <code>$generated_url</code></p>";
}

echo "<h3>Expected Results:</h3>";
echo "<p>✅ <code>admin/settings.php</code> should generate <code>../admin/settings.php</code></p>";
echo "<p>✅ <code>login.php</code> should generate <code>../login.php</code></p>";
echo "<p>❌ Should NOT generate paths like <code>admin/admin/settings.php</code></p>";

echo "<h3>Test Links from Admin Directory:</h3>";
echo "<p><a href='settings.php' class='btn btn-primary'>Settings (Direct)</a></p>";
echo "<p><a href='" . url('admin/settings.php') . "' class='btn btn-success'>Settings (URL Function)</a></p>";
echo "<p><a href='" . url('login.php') . "' class='btn btn-info'>Back to Login</a></p>";
echo "<p><a href='" . url('index.php') . "' class='btn btn-warning'>Back to Home</a></p>";

echo "<h3>Navigation Test:</h3>";
echo "<p>These links should work without duplication:</p>";
echo "<ul>";
echo "<li><a href='" . url('admin/dashboard.php') . "'>Admin Dashboard</a></li>";
echo "<li><a href='" . url('admin/users.php') . "'>Manage Users</a></li>";
echo "<li><a href='" . url('admin/email_settings.php') . "'>Email Settings</a></li>";
echo "<li><a href='" . url('members/index.php') . "'>Manage Members</a></li>";
echo "</ul>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px; }
.btn-success { background: #28a745; }
.btn-info { background: #17a2b8; }
.btn-warning { background: #ffc107; color: #212529; }
h3 { color: #333; margin-top: 20px; }
p { margin: 5px 0; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
