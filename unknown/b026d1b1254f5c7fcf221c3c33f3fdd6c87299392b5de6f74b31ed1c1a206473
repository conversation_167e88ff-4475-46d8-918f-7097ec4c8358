<?php
/**
 * Simple Email Settings Page - Works without database
 */
session_start();

// Initialize variables
$success_message = '';
$error_message = '';

// Default settings (no database required)
$email_settings = [
    'from_email' => '<EMAIL>',
    'from_name' => 'Library Management System',
    'reply_to' => '<EMAIL>',
    'smtp_enabled' => 'false',
    'smtp_host' => 'smtp.example.com',
    'smtp_port' => '587',
    'smtp_username' => '',
    'smtp_password' => '',
    'smtp_secure' => 'tls'
];

$notification_settings = [
    'due_date_reminder_days' => '3',
    'send_overdue_notifications' => 'true',
    'overdue_notification_frequency' => '7'
];

$fine_settings = [
    'fine_rate_per_day' => '0.25',
    'grace_period_days' => '3',
    'max_fine_per_book' => '25.00'
];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['email_settings'])) {
        $success_message = 'Email settings would be saved (database not connected)';
    } elseif (isset($_POST['notification_settings'])) {
        $success_message = 'Notification settings would be saved (database not connected)';
    } elseif (isset($_POST['fine_settings'])) {
        $success_message = 'Fine settings would be saved (database not connected)';
    } elseif (isset($_POST['send_test_email'])) {
        // Send test email
        $to_email = $_POST['test_email'];
        $subject = 'Test Email from Library Management System';
        $body = '<html><body>
            <h2>Test Email</h2>
            <p>This is a test email from the Library Management System.</p>
            <p>If you received this email, your email settings are configured correctly.</p>
            <p>Time sent: ' . date('Y-m-d H:i:s') . '</p>
            </body></html>';

        // Simple email sending
        $headers = "MIME-Version: 1.0\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8\r\n";
        $headers .= "From: " . $email_settings['from_name'] . " <" . $email_settings['from_email'] . ">\r\n";

        $result = mail($to_email, $subject, $body, $headers);

        if ($result) {
            $success_message = "Test email sent successfully to $to_email";
        } else {
            $error_message = 'Failed to send test email. Please check your email settings.';
        }
    }
}

// Check for success message in URL
if (isset($_GET['success'])) {
    $success_message = $_GET['success'];
}

$page_title = 'Email & Notification Settings (Simple Version)';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        .settings-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .settings-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        .nav-pills .nav-link.active {
            background-color: #007bff;
        }
        .nav-pills .nav-link {
            color: #495057;
        }
        .nav-pills .nav-link:hover:not(.active) {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <main class="col-12 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-envelope me-2 text-primary"></i><?php echo $page_title; ?></h1>
                </div>

                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>Simple Mode:</strong> This version works without database connection. Settings are not permanently saved.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-3">
                        <div class="card settings-card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-gear me-2"></i>Settings</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                                    <button class="nav-link active" id="v-pills-email-tab" data-bs-toggle="pill" data-bs-target="#v-pills-email" type="button" role="tab" aria-controls="v-pills-email" aria-selected="true">
                                        <i class="bi bi-envelope me-2"></i>Email Configuration
                                    </button>
                                    <button class="nav-link" id="v-pills-notifications-tab" data-bs-toggle="pill" data-bs-target="#v-pills-notifications" type="button" role="tab" aria-controls="v-pills-notifications" aria-selected="false">
                                        <i class="bi bi-bell me-2"></i>Notification Settings
                                    </button>
                                    <button class="nav-link" id="v-pills-fines-tab" data-bs-toggle="pill" data-bs-target="#v-pills-fines" type="button" role="tab" aria-controls="v-pills-fines" aria-selected="false">
                                        <i class="bi bi-cash-coin me-2"></i>Fine Settings
                                    </button>
                                    <button class="nav-link" id="v-pills-test-tab" data-bs-toggle="pill" data-bs-target="#v-pills-test" type="button" role="tab" aria-controls="v-pills-test" aria-selected="false">
                                        <i class="bi bi-send me-2"></i>Test Email
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="tab-content" id="v-pills-tabContent">
                            <!-- Email Settings Tab -->
                            <div class="tab-pane fade show active" id="v-pills-email" role="tabpanel" aria-labelledby="v-pills-email-tab">
                                <div class="card settings-card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><i class="bi bi-envelope me-2"></i>Email Configuration</h5>
                                    </div>
                                    <div class="card-body">
                                        <form action="" method="post">
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="from_email" class="form-label">From Email</label>
                                                    <input type="email" class="form-control" id="from_email" name="from_email" value="<?php echo htmlspecialchars($email_settings['from_email']); ?>" required>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="from_name" class="form-label">From Name</label>
                                                    <input type="text" class="form-control" id="from_name" name="from_name" value="<?php echo htmlspecialchars($email_settings['from_name']); ?>" required>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="reply_to" class="form-label">Reply-To Email</label>
                                                <input type="email" class="form-control" id="reply_to" name="reply_to" value="<?php echo htmlspecialchars($email_settings['reply_to']); ?>" required>
                                            </div>
                                            <div class="d-grid gap-2">
                                                <button type="submit" name="email_settings" class="btn btn-primary">
                                                    <i class="bi bi-save me-2"></i>Save Email Settings
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Test Email Tab -->
                            <div class="tab-pane fade" id="v-pills-test" role="tabpanel" aria-labelledby="v-pills-test-tab">
                                <div class="card settings-card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><i class="bi bi-send me-2"></i>Send Test Email</h5>
                                    </div>
                                    <div class="card-body">
                                        <form action="" method="post">
                                            <div class="mb-3">
                                                <label for="test_email" class="form-label">Recipient Email Address</label>
                                                <input type="email" class="form-control" id="test_email" name="test_email" required>
                                                <div class="form-text">Enter an email address to send a test email to</div>
                                            </div>
                                            <div class="d-grid gap-2">
                                                <button type="submit" name="send_test_email" class="btn btn-primary">
                                                    <i class="bi bi-send me-2"></i>Send Test Email
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Quick Links</h5>
                            <a href="email_settings.php" class="btn btn-outline-primary me-2">Full Version (with Database)</a>
                            <a href="../setup_database_simple.php" class="btn btn-outline-success me-2">Setup Database</a>
                            <a href="../index.php" class="btn btn-outline-secondary">Back to Home</a>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
