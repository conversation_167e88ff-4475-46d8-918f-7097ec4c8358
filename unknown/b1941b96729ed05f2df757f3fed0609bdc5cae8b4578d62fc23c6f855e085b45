<?php
// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
// Include dashboard-specific files if they exist
if (file_exists(__DIR__ . '/includes/dashboard_functions.php')) {
    require_once 'includes/dashboard_functions.php';
}
if (file_exists(__DIR__ . '/templates/dashboard_widgets.php')) {
    require_once 'templates/dashboard_widgets.php';
}
if (file_exists(__DIR__ . '/templates/dashboard_modals.php')) {
    require_once 'templates/dashboard_modals.php';
}

// Check if user is logged in and is librarian
if (!isLoggedIn() || !isLibrarian()) {
    redirect('../login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get statistics for dashboard
$stats = [];

// Total books
$query = "SELECT COUNT(*) as total FROM books";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['total_books'] = $stmt->fetch()['total'];

// Available books
$query = "SELECT SUM(available_quantity) as available FROM books";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['available_books'] = $stmt->fetch()['available'];

// Total members
$query = "SELECT COUNT(*) as total FROM members";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['total_members'] = $stmt->fetch()['total'];

// Active loans (currently borrowed - not overdue yet) - STANDARDIZED
$query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'borrowed' AND due_date >= CURDATE()";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['active_loans'] = $stmt->fetch()['total'];

// Overdue books (explicitly overdue OR borrowed past due date) - STANDARDIZED
$query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'overdue' OR (status = 'borrowed' AND due_date < CURDATE())";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['overdue_books'] = $stmt->fetch()['total'];

// Book reservations
$query = "SELECT COUNT(*) as total FROM book_reservations WHERE status = 'pending'";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['pending_reservations'] = $stmt->fetch()['total'];

// Today's due books
$query = "SELECT COUNT(*) as total FROM book_loans WHERE due_date = CURDATE() AND status = 'borrowed'";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['due_today'] = $stmt->fetch()['total'];

// Recent books
$query = "SELECT * FROM books ORDER BY created_at DESC LIMIT 5";
$stmt = $db->prepare($query);
$stmt->execute();
$recent_books = $stmt->fetchAll();

// Recent loans
$query = "SELECT bl.*, b.title as book_title, m.first_name, m.last_name
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          JOIN members m ON bl.member_id = m.id
          ORDER BY bl.issue_date DESC LIMIT 5";
$stmt = $db->prepare($query);
$stmt->execute();
$recent_loans = $stmt->fetchAll();

// Recent system activity
$query = "SELECT al.*, u.username
          FROM activity_log al
          LEFT JOIN users u ON al.user_id = u.id
          WHERE al.entity_type IN ('book', 'member', 'loan', 'reservation')
          OR al.user_id = :user_id
          ORDER BY al.timestamp DESC LIMIT 5";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $_SESSION['user_id']);
$stmt->execute();
$recent_activities = $stmt->fetchAll();

// Get popular books (most borrowed)
$query = "SELECT b.*, COUNT(bl.id) as borrow_count
          FROM books b
          JOIN book_loans bl ON b.id = bl.book_id
          GROUP BY b.id
          ORDER BY borrow_count DESC
          LIMIT 5";
$stmt = $db->prepare($query);
$stmt->execute();
$popular_books = $stmt->fetchAll();

// Get notifications
$query = "SELECT * FROM notifications WHERE (user_id = :user_id OR user_id IS NULL) AND is_read = 0 ORDER BY created_at DESC LIMIT 5";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $_SESSION['user_id']);
$stmt->execute();
$notifications = $stmt->fetchAll();

// Count unread notifications
$unread_count = getUnreadNotificationsCount($db, $_SESSION['user_id']);

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Librarian Dashboard - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="<?php echo url('assets/css/notifications.css'); ?>">
    <link rel="stylesheet" href="<?php echo url('assets/css/dashboard-enhancements.css'); ?>">
    <style>
        body {
            font-size: .875rem;
        }

        .feather {
            width: 16px;
            height: 16px;
            vertical-align: text-bottom;
        }

        /* Static Notifications Container */
        .static-notifications-container {
            max-height: 400px;
            overflow-y: auto;
            position: fixed;
            top: 60px;
            right: 10px;
            z-index: 1050;
        }

        /* Responsive adjustments for notifications */
        @media (max-width: 767.98px) {
            .static-notifications-container {
                width: 90%;
                right: 5%;
                left: 5%;
            }
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
        }

        @media (max-width: 767.98px) {
            .sidebar {
                top: 5rem;
            }
        }

        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .sidebar .nav-link {
            font-weight: 500;
            color: #333;
            padding: .75rem 1rem;
        }

        .sidebar .nav-link.active {
            color: #2470dc;
        }

        .sidebar .nav-link:hover {
            color: #007bff;
        }

        .sidebar-heading {
            font-size: .75rem;
            text-transform: uppercase;
        }

        /* Navbar */
        .navbar-brand {
            padding-top: .75rem;
            padding-bottom: .75rem;
            font-size: 1rem;
            background-color: rgba(0, 0, 0, .25);
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);
        }

        .navbar .navbar-toggler {
            top: .25rem;
            right: 1rem;
        }

        /* Dashboard specific styles */
        .stats-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 10px;
            overflow: hidden;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .card-footer {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(0, 0, 0, 0.1);
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        .cover-preview {
            display: block;
            transition: transform 0.2s;
        }

        .cover-preview:hover {
            transform: scale(1.1);
        }

        .img-thumbnail {
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        /* Dark mode form control */
        .form-control-dark {
            background-color: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .form-control-dark:focus {
            border-color: rgba(255, 255, 255, 0.25);
            box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25);
            color: #fff;
        }

        /* Responsive adjustments */
        @media (max-width: 767.98px) {
            .btn-toolbar {
                margin-top: 1rem;
                justify-content: center;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="#">Library MS Librarian</a>
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="w-100">
            <form class="d-none d-md-flex w-50 mx-auto" action="../search.php" method="get">
                <input class="form-control form-control-dark" type="text" name="q" placeholder="Search books, members..." aria-label="Search" required>
                <button class="btn btn-outline-light ms-2" type="submit"><i class="bi bi-search"></i></button>
            </form>
        </div>
        <div class="navbar-nav">
            <div class="nav-item text-nowrap d-flex align-items-center">
                <!-- Static Notifications Panel -->
                <div class="nav-item me-2">
                    <a class="nav-link position-relative" href="../notifications/index.php">
                        <span class="bell-icon"><i class="bi bi-bell fs-5 text-white"></i></span>
                        <?php if ($unread_count > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge">
                                <?php echo $unread_count > 9 ? '9+' : $unread_count; ?>
                            </span>
                        <?php endif; ?>
                    </a>
                </div>



                <!-- Static Notifications Container -->
                <div class="static-notifications-container" id="staticNotifications" style="display: none;">
                    <div class="notifications-header">
                        <div class="d-flex align-items-center">
                            <h6 class="mb-0">Notifications</h6>
                            <?php if ($unread_count > 0): ?>
                                <span class="badge bg-danger ms-2 notification-counter"><?php echo $unread_count; ?></span>
                            <?php endif; ?>
                        </div>
                        <div class="d-flex align-items-center">
                            <?php if ($unread_count > 0): ?>
                                <a href="../notifications/mark_all_read.php" class="mark-all-read me-2">Mark all as read</a>
                            <?php endif; ?>
                            <button type="button" class="btn btn-sm btn-outline-light me-1" id="refreshNotifications" title="Refresh">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-light me-1" id="minimizeNotifications" title="Minimize">
                                <i class="bi bi-dash"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-light" id="closeNotifications" title="Close">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>
                    <div class="notifications-body">
                        <?php if (count($notifications) > 0): ?>
                            <?php foreach ($notifications as $notification): ?>
                                <div class="notification-item <?php echo $notification['is_read'] ? '' : 'unread'; ?> type-<?php echo h($notification['type']); ?>" data-notification-type="<?php echo h($notification['type']); ?>" data-notification-id="<?php echo h($notification['id']); ?>">
                                    <div class="d-flex">
                                        <div class="notification-icon bg-<?php echo h($notification['type']); ?>">
                                            <?php if ($notification['type'] === 'warning'): ?>
                                                <i class="bi bi-exclamation-triangle"></i>
                                            <?php elseif ($notification['type'] === 'danger'): ?>
                                                <i class="bi bi-exclamation-circle"></i>
                                            <?php elseif ($notification['type'] === 'success'): ?>
                                                <i class="bi bi-check-circle"></i>
                                            <?php else: ?>
                                                <i class="bi bi-info-circle"></i>
                                            <?php endif; ?>
                                        </div>
                                        <div class="notification-content">
                                            <p class="notification-message"><?php echo h($notification['message']); ?></p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="notification-time"><?php echo timeAgo($notification['created_at']); ?></span>
                                                <?php if (!$notification['is_read']): ?>
                                                    <button class="btn btn-sm btn-link p-0 mark-read-btn" data-id="<?php echo h($notification['id']); ?>">Mark as read</button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="empty-notifications">
                                <i class="bi bi-bell-slash"></i>
                                <p>No new notifications</p>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="notifications-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="../notifications/index.php">View all notifications</a>
                            <span class="text-muted notification-count-display"><?php echo count($notifications); ?> total</span>
                        </div>
                    </div>
                </div>
                <span class="nav-link px-3 text-white">Welcome, Librarian</span>
                <a class="btn btn-danger btn-sm mx-2" href="../logout.php">
                    <i class="bi bi-box-arrow-right me-1"></i>Sign out
                </a>
            </div>
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="bi bi-speedometer2 me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../books/index.php">
                                <i class="bi bi-book me-2"></i>
                                Books
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../members/index.php">
                                <i class="bi bi-people me-2"></i>
                                Members
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../loans/index.php">
                                <i class="bi bi-journal-arrow-up me-2"></i>
                                Book Loans
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/index.php">
                                <i class="bi bi-file-earmark-bar-graph me-2"></i>
                                Reports
                            </a>
                        </li>
                    </ul>

                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>Financial Management</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="../admin/payment_processing.php">
                                <i class="bi bi-credit-card me-2"></i>
                                Payment Processing
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../admin/payment_reports.php">
                                <i class="bi bi-file-earmark-bar-graph me-2"></i>
                                Payment Reports
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../admin/financial_management.php">
                                <i class="bi bi-currency-dollar me-2"></i>
                                Financial Overview
                            </a>
                        </li>
                    </ul>

                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>Librarian Tasks</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="issue_book.php">
                                <i class="bi bi-arrow-right-circle me-2"></i>
                                Issue Book
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="return_book.php">
                                <i class="bi bi-arrow-left-circle me-2"></i>
                                Return Book
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="manage_reservations.php">
                                <i class="bi bi-bookmark me-2"></i>
                                Manage Reservations
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="activity_log.php">
                                <i class="bi bi-list-check me-2"></i>
                                Activity Log
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Librarian Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="issue_book.php" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-arrow-right-circle me-1"></i> Issue Book
                            </a>
                            <a href="return_book.php" class="btn btn-sm btn-outline-success">
                                <i class="bi bi-arrow-left-circle me-1"></i> Return Book
                            </a>
                            <a href="manage_reservations.php" class="btn btn-sm btn-outline-info">
                                <i class="bi bi-bookmark me-1"></i> Manage Reservations
                            </a>
                        </div>
                        <div class="btn-group me-2">
                            <a href="../admin/payment_processing.php" class="btn btn-sm btn-outline-warning">
                                <i class="bi bi-credit-card me-1"></i> Process Payments
                            </a>
                            <a href="../admin/payment_reports.php" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-file-earmark-bar-graph me-1"></i> Payment Reports
                            </a>
                        </div>
                        <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#quickMemberRegistrationModal">
                            <i class="bi bi-person-plus me-1"></i> Quick Member Registration
                        </button>
                    </div>
                </div>

                <!-- Notification Container -->
                <div id="notificationContainer"></div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo h($_SESSION['message_type']); ?> alert-dismissible fade show" role="alert">
                        <?php echo h($_SESSION['message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <!-- Enhanced Stats Cards -->
                <div class="row" role="region" aria-label="Dashboard Statistics">
                    <!-- Total Books Card -->
                    <div class="col-md-4 col-xl-3 mb-4">
                        <div class="card text-white bg-primary stats-card clickable-card h-100"
                             role="button"
                             tabindex="0"
                             aria-label="Total Books: <?php echo h($stats['total_books']); ?>. Click to view all books."
                             data-href="../books/index.php"
                             data-bs-toggle="tooltip"
                             data-bs-placement="top"
                             title="Click to manage all books in the library">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50 mb-1">Total Books</h6>
                                        <h3 class="mb-0 fw-bold" data-stat="total_books"><?php echo h($stats['total_books']); ?></h3>
                                        <small class="text-white-75">Unique titles</small>
                                    </div>
                                    <div class="stats-icon">
                                        <i class="bi bi-book fs-1 opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent d-flex align-items-center justify-content-between">
                                <span class="text-white-75 small">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </span>
                                <i class="bi bi-chevron-right text-white-75"></i>
                            </div>
                            <div class="card-overlay"></div>
                        </div>
                    </div>

                    <!-- Available Books Card -->
                    <div class="col-md-4 col-xl-3 mb-4">
                        <div class="card text-white bg-success stats-card clickable-card h-100"
                             role="button"
                             tabindex="0"
                             aria-label="Available Books: <?php echo h($stats['available_books']); ?>. Click to view available books."
                             data-href="../books/index.php?filter=available"
                             data-bs-toggle="tooltip"
                             data-bs-placement="top"
                             title="Click to view all available books for lending">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50 mb-1">Available Books</h6>
                                        <h3 class="mb-0 fw-bold" data-stat="available_books"><?php echo h($stats['available_books']); ?></h3>
                                        <small class="text-white-75">Ready to lend</small>
                                    </div>
                                    <div class="stats-icon">
                                        <i class="bi bi-bookshelf fs-1 opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent d-flex align-items-center justify-content-between">
                                <span class="text-white-75 small">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </span>
                                <i class="bi bi-chevron-right text-white-75"></i>
                            </div>
                            <div class="card-overlay"></div>
                        </div>
                    </div>

                    <!-- Total Members Card -->
                    <div class="col-md-4 col-xl-3 mb-4">
                        <div class="card text-white bg-info stats-card clickable-card h-100"
                             role="button"
                             tabindex="0"
                             aria-label="Total Members: <?php echo h($stats['total_members']); ?>. Click to view all members."
                             data-href="../members/index.php"
                             data-bs-toggle="tooltip"
                             data-bs-placement="top"
                             title="Click to manage library members">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50 mb-1">Total Members</h6>
                                        <h3 class="mb-0 fw-bold" data-stat="total_members"><?php echo h($stats['total_members']); ?></h3>
                                        <small class="text-white-75">Registered users</small>
                                    </div>
                                    <div class="stats-icon">
                                        <i class="bi bi-people fs-1 opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent d-flex align-items-center justify-content-between">
                                <span class="text-white-75 small">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </span>
                                <i class="bi bi-chevron-right text-white-75"></i>
                            </div>
                            <div class="card-overlay"></div>
                        </div>
                    </div>

                    <!-- Active Loans Card -->
                    <div class="col-md-4 col-xl-3 mb-4">
                        <div class="card text-white bg-warning stats-card clickable-card h-100"
                             role="button"
                             tabindex="0"
                             aria-label="Active Loans: <?php echo h($stats['active_loans']); ?>. Click to view all active loans."
                             data-href="../loans/index.php?status=borrowed"
                             data-bs-toggle="tooltip"
                             data-bs-placement="top"
                             title="Click to view all currently borrowed books">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50 mb-1">Active Loans</h6>
                                        <h3 class="mb-0 fw-bold" data-stat="active_loans"><?php echo h($stats['active_loans']); ?></h3>
                                        <small class="text-white-75">Currently borrowed</small>
                                    </div>
                                    <div class="stats-icon">
                                        <i class="bi bi-journal-arrow-up fs-1 opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent d-flex align-items-center justify-content-between">
                                <span class="text-white-75 small">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </span>
                                <i class="bi bi-chevron-right text-white-75"></i>
                            </div>
                            <div class="card-overlay"></div>
                        </div>
                    </div>

                    <!-- Overdue Books Card -->
                    <div class="col-md-4 col-xl-3 mb-4">
                        <div class="card text-white bg-danger stats-card clickable-card h-100 <?php echo $stats['overdue_books'] > 0 ? 'urgent-attention' : ''; ?>"
                             role="button"
                             tabindex="0"
                             aria-label="Overdue Books: <?php echo h($stats['overdue_books']); ?>. <?php echo $stats['overdue_books'] > 0 ? 'Urgent attention required.' : ''; ?> Click to view overdue books."
                             data-href="../reports/overdue.php"
                             data-bs-toggle="tooltip"
                             data-bs-placement="top"
                             title="<?php echo $stats['overdue_books'] > 0 ? 'Urgent: Books past due date require immediate attention' : 'No overdue books - great job!'; ?>">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50 mb-1">Overdue Books</h6>
                                        <h3 class="mb-0 fw-bold" data-stat="overdue_books"><?php echo h($stats['overdue_books']); ?></h3>
                                        <small class="text-white-75"><?php echo $stats['overdue_books'] > 0 ? 'Need attention' : 'All on time'; ?></small>
                                    </div>
                                    <div class="stats-icon">
                                        <i class="bi bi-exclamation-triangle fs-1 opacity-75 <?php echo $stats['overdue_books'] > 0 ? 'pulse-animation' : ''; ?>"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent d-flex align-items-center justify-content-between">
                                <span class="text-white-75 small">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </span>
                                <i class="bi bi-chevron-right text-white-75"></i>
                            </div>
                            <div class="card-overlay"></div>
                        </div>
                    </div>

                    <!-- Pending Reservations Card -->
                    <div class="col-md-4 col-xl-3 mb-4">
                        <div class="card text-white stats-card clickable-card h-100"
                             style="background: linear-gradient(135deg, #6f42c1 0%, #8e44ad 100%);"
                             role="button"
                             tabindex="0"
                             aria-label="Pending Reservations: <?php echo h($stats['pending_reservations']); ?>. Click to manage reservations."
                             data-href="manage_reservations.php"
                             data-bs-toggle="tooltip"
                             data-bs-placement="top"
                             title="Click to manage book reservations and holds">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50 mb-1">Pending Reservations</h6>
                                        <h3 class="mb-0 fw-bold" data-stat="pending_reservations"><?php echo h($stats['pending_reservations']); ?></h3>
                                        <small class="text-white-75">Awaiting books</small>
                                    </div>
                                    <div class="stats-icon">
                                        <i class="bi bi-bookmark-star fs-1 opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent d-flex align-items-center justify-content-between">
                                <span class="text-white-75 small">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </span>
                                <i class="bi bi-chevron-right text-white-75"></i>
                            </div>
                            <div class="card-overlay"></div>
                        </div>
                    </div>

                    <!-- Due Today Card -->
                    <div class="col-md-4 col-xl-3 mb-4">
                        <div class="card text-white stats-card clickable-card h-100 <?php echo $stats['due_today'] > 0 ? 'attention-needed' : ''; ?>"
                             style="background: linear-gradient(135deg, #fd7e14 0%, #ff8c00 100%);"
                             role="button"
                             tabindex="0"
                             aria-label="Due Today: <?php echo h($stats['due_today']); ?>. Click to view books due today."
                             data-href="../reports/due_today.php"
                             data-bs-toggle="tooltip"
                             data-bs-placement="top"
                             title="Click to view books that are due for return today">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50 mb-1">Due Today</h6>
                                        <h3 class="mb-0 fw-bold" data-stat="due_today"><?php echo h($stats['due_today']); ?></h3>
                                        <small class="text-white-75"><?php echo $stats['due_today'] > 0 ? 'Need follow-up' : 'All clear'; ?></small>
                                    </div>
                                    <div class="stats-icon">
                                        <i class="bi bi-calendar-check fs-1 opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent d-flex align-items-center justify-content-between">
                                <span class="text-white-75 small">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </span>
                                <i class="bi bi-chevron-right text-white-75"></i>
                            </div>
                            <div class="card-overlay"></div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Dashboard Widgets -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <?php
                        if (function_exists('renderTaskManagementWidget')) {
                            renderTaskManagementWidget();
                        } else {
                            echo '<div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0"><i class="bi bi-check2-square me-2"></i>My Tasks</h5>
                                </div>
                                <div class="card-body">
                                    <p>Task management widget is not available.</p>
                                </div>
                            </div>';
                        }
                        ?>
                    </div>
                    <div class="col-md-6 mb-4">
                        <?php
                        if (function_exists('renderMemberCommunicationWidget')) {
                            renderMemberCommunicationWidget();
                        } else {
                            echo '<div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0"><i class="bi bi-chat-dots me-2"></i>Member Communication</h5>
                                </div>
                                <div class="card-body">
                                    <p>Member communication widget is not available.</p>
                                </div>
                            </div>';
                        }
                        ?>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-4">
                        <?php
                        if (function_exists('renderAcquisitionSuggestionsWidget')) {
                            renderAcquisitionSuggestionsWidget();
                        } else {
                            echo '<div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0"><i class="bi bi-lightbulb me-2"></i>Acquisition Suggestions</h5>
                                </div>
                                <div class="card-body">
                                    <p>Acquisition suggestions widget is not available.</p>
                                </div>
                            </div>';
                        }
                        ?>
                    </div>
                    <!-- Book Condition Tracking Widget has been removed -->
                </div>

                <div class="row">
                    <!-- Recent Books -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Recently Added Books</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Cover</th>
                                                <th>Title</th>
                                                <th>Author</th>
                                                <th>Available</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_books as $book): ?>
                                            <tr>
                                                <td class="text-center" style="width: 60px;">
                                                    <?php if (!empty($book['cover_image'])): ?>
                                                        <?php
                                                        // Check if the cover_image is a URL or a local file
                                                        $image_src = (strpos($book['cover_image'], 'http') === 0)
                                                            ? $book['cover_image']
                                                            : url('uploads/covers/' . $book['cover_image']);
                                                        ?>
                                                        <a href="#" class="cover-preview" data-bs-toggle="modal" data-bs-target="#coverModal"
                                                           data-img-src="<?php echo h($image_src); ?>"
                                                           data-title="<?php echo h($book['title']); ?>">
                                                            <img src="<?php echo h($image_src); ?>" alt="<?php echo h($book['title']); ?>"
                                                                 class="img-thumbnail" style="width: 40px; height: 60px; object-fit: cover;">
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary"><i class="bi bi-book"></i></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="../books/view.php?id=<?php echo h($book['id']); ?>" class="text-decoration-none">
                                                        <?php echo h($book['title']); ?>
                                                    </a>
                                                </td>
                                                <td><?php echo h($book['author']); ?></td>
                                                <td><?php echo h($book['available_quantity']); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="card-footer text-end">
                                <a href="../books/index.php" class="btn btn-sm btn-primary">View All Books</a>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Loans -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Recent Loans</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Book</th>
                                                <th>Member</th>
                                                <th>Due Date</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_loans as $loan): ?>
                                            <tr>
                                                <td><?php echo h($loan['book_title']); ?></td>
                                                <td><?php echo h($loan['first_name'] . ' ' . $loan['last_name']); ?></td>
                                                <td><?php echo formatDate($loan['due_date']); ?></td>
                                                <td>
                                                    <?php if ($loan['status'] === 'borrowed'): ?>
                                                        <span class="badge bg-primary">Borrowed</span>
                                                    <?php elseif ($loan['status'] === 'returned'): ?>
                                                        <span class="badge bg-success">Returned</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Overdue</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="card-footer text-end">
                                <a href="../loans/index.php" class="btn btn-sm btn-primary">View All Loans</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Quick Actions</h5>
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#quickActionsCollapse" aria-expanded="true" aria-controls="quickActionsCollapse">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse show" id="quickActionsCollapse">
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <a href="../books/add.php" class="btn btn-primary">
                                                    <i class="bi bi-plus-circle me-2"></i> Add New Book
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <a href="../members/add.php" class="btn btn-success">
                                                    <i class="bi bi-person-plus me-2"></i> Register Member
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <a href="issue_book.php" class="btn btn-info text-white">
                                                    <i class="bi bi-journal-arrow-up me-2"></i> Issue Book
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <a href="return_book.php" class="btn btn-warning text-white">
                                                    <i class="bi bi-journal-arrow-down me-2"></i> Return Book
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <hr class="my-3">

                                    <div class="row g-3">
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <a href="../reports/overdue.php" class="btn btn-danger">
                                                    <i class="bi bi-exclamation-triangle me-2"></i> Overdue Books
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <a href="../reports/index.php" class="btn btn-secondary">
                                                    <i class="bi bi-file-earmark-bar-graph me-2"></i> Generate Reports
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <a href="../books/search.php" class="btn btn-dark">
                                                    <i class="bi bi-search me-2"></i> Advanced Search
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <a href="manage_reservations.php" class="btn btn-outline-secondary">
                                                    <i class="bi bi-bookmark me-2"></i> Manage Reservations
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Member Search -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0"><i class="bi bi-search me-2"></i> Quick Member Search</h5>
                            </div>
                            <div class="card-body">
                                <form action="../members/index.php" method="get" class="row g-3">
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" name="search" placeholder="Enter member name, email or ID..." required>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="submit" class="btn btn-primary w-100">
                                            <i class="bi bi-search me-1"></i> Search
                                        </button>
                                    </div>
                                </form>
                                <hr>
                                <div class="d-grid gap-2 d-md-flex justify-content-md-between">
                                    <a href="../members/add.php" class="btn btn-outline-success">
                                        <i class="bi bi-person-plus me-1"></i> Add New Member
                                    </a>
                                    <a href="../members/index.php" class="btn btn-outline-secondary">
                                        <i class="bi bi-people me-1"></i> View All Members
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0"><i class="bi bi-calendar-check me-2"></i> Books Due Today</h5>
                            </div>
                            <div class="card-body">
                                <?php
                                // Get books due today
                                $query = "SELECT bl.*, b.title as book_title, m.first_name, m.last_name, m.email, m.phone
                                          FROM book_loans bl
                                          JOIN books b ON bl.book_id = b.id
                                          JOIN members m ON bl.member_id = m.id
                                          WHERE bl.due_date = CURDATE() AND bl.status = 'borrowed'
                                          ORDER BY bl.issue_date DESC LIMIT 5";
                                $stmt = $db->prepare($query);
                                $stmt->execute();
                                $due_today = $stmt->fetchAll();

                                if (empty($due_today)) {
                                    echo '<div class="alert alert-info"><i class="bi bi-info-circle me-2"></i> No books due today.</div>';
                                } else {
                                ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Book</th>
                                                <th>Member</th>
                                                <th>Contact</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($due_today as $loan): ?>
                                            <tr>
                                                <td><?php echo h($loan['book_title']); ?></td>
                                                <td><?php echo h($loan['first_name'] . ' ' . $loan['last_name']); ?></td>
                                                <td>
                                                    <a href="mailto:<?php echo h($loan['email']); ?>" class="text-decoration-none">
                                                        <i class="bi bi-envelope me-1"></i><?php echo h($loan['email']); ?>
                                                    </a>
                                                    <br>
                                                    <a href="tel:<?php echo h($loan['phone']); ?>" class="text-decoration-none">
                                                        <i class="bi bi-telephone me-1"></i><?php echo h($loan['phone']); ?>
                                                    </a>
                                                </td>
                                                <td>
                                                    <a href="return_book.php?loan_id=<?php echo h($loan['id']); ?>" class="btn btn-sm btn-success">
                                                        <i class="bi bi-arrow-left-circle me-1"></i> Return
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php } ?>
                                <div class="text-end mt-3">
                                    <a href="../reports/due_today.php" class="btn btn-outline-primary">View All Due Today</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent System Activity -->
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="bi bi-activity me-2"></i> Recent System Activity</h5>
                                <a href="activity_log.php" class="btn btn-sm btn-secondary">View All Activity</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_activities)): ?>
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle me-2"></i> No recent activities found.
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Date & Time</th>
                                                    <th>User</th>
                                                    <th>Action</th>
                                                    <th>Description</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recent_activities as $activity): ?>
                                                    <tr>
                                                        <td><?php echo formatDateTime($activity['timestamp']); ?></td>
                                                        <td><?php echo h($activity['username'] ?? 'System'); ?></td>
                                                        <td>
                                                            <?php if ($activity['action'] === 'login'): ?>
                                                                <span class="badge bg-success">Login</span>
                                                            <?php elseif ($activity['action'] === 'logout'): ?>
                                                                <span class="badge bg-secondary">Logout</span>
                                                            <?php elseif ($activity['action'] === 'add'): ?>
                                                                <span class="badge bg-primary">Add</span>
                                                            <?php elseif ($activity['action'] === 'edit'): ?>
                                                                <span class="badge bg-warning text-dark">Edit</span>
                                                            <?php elseif ($activity['action'] === 'delete'): ?>
                                                                <span class="badge bg-danger">Delete</span>
                                                            <?php elseif ($activity['action'] === 'view'): ?>
                                                                <span class="badge bg-info text-dark">View</span>
                                                            <?php elseif ($activity['action'] === 'issue'): ?>
                                                                <span class="badge bg-primary">Issue</span>
                                                            <?php elseif ($activity['action'] === 'return'): ?>
                                                                <span class="badge bg-success">Return</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-dark"><?php echo h(ucfirst($activity['action'])); ?></span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td><?php echo h($activity['description']); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Book Reservation Management Widget -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="bi bi-bookmark-star me-2"></i> Pending Reservations</h5>
                                <div>
                                    <a href="manage_reservations.php" class="btn btn-sm btn-primary me-2">View All</a>
                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#reservationsCollapse" aria-expanded="true" aria-controls="reservationsCollapse">
                                        <i class="bi bi-chevron-down"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="collapse show" id="reservationsCollapse">
                                <div class="card-body">
                                    <?php
                                    // Get pending reservations
                                    $query = "SELECT br.*, b.title as book_title, b.author as book_author,
                                              m.first_name, m.last_name, m.email
                                              FROM book_reservations br
                                              JOIN books b ON br.book_id = b.id
                                              JOIN members m ON br.member_id = m.id
                                              WHERE br.status = 'pending'
                                              ORDER BY br.reservation_date ASC
                                              LIMIT 5";
                                    $stmt = $db->prepare($query);
                                    $stmt->execute();
                                    $pending_reservations = $stmt->fetchAll();
                                    ?>

                                    <?php if (count($pending_reservations) > 0): ?>
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>Book</th>
                                                        <th>Member</th>
                                                        <th>Reserved On</th>
                                                        <th>Expires On</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($pending_reservations as $reservation): ?>
                                                        <tr>
                                                            <td>
                                                                <strong><?php echo h($reservation['book_title']); ?></strong><br>
                                                                <small class="text-muted"><?php echo h($reservation['book_author']); ?></small>
                                                            </td>
                                                            <td>
                                                                <?php echo h($reservation['first_name'] . ' ' . $reservation['last_name']); ?><br>
                                                                <small class="text-muted"><?php echo h($reservation['email']); ?></small>
                                                            </td>
                                                            <td><?php echo date('M d, Y', strtotime($reservation['reservation_date'])); ?></td>
                                                            <td>
                                                                <?php
                                                                $expiry_date = strtotime($reservation['expiry_date']);
                                                                $today = strtotime(date('Y-m-d'));
                                                                $days_left = round(($expiry_date - $today) / (60 * 60 * 24));

                                                                echo date('M d, Y', $expiry_date);

                                                                if ($days_left < 0) {
                                                                    echo ' <span class="badge bg-danger">Expired</span>';
                                                                } elseif ($days_left == 0) {
                                                                    echo ' <span class="badge bg-warning text-dark">Today</span>';
                                                                } elseif ($days_left == 1) {
                                                                    echo ' <span class="badge bg-warning text-dark">Tomorrow</span>';
                                                                } elseif ($days_left <= 3) {
                                                                    echo ' <span class="badge bg-info">' . $days_left . ' days left</span>';
                                                                }
                                                                ?>
                                                            </td>
                                                            <td>
                                                                <div class="btn-group">
                                                                    <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                                        Actions
                                                                    </button>
                                                                    <ul class="dropdown-menu">
                                                                        <li>
                                                                            <a href="manage_reservations.php?action=ready&id=<?php echo h($reservation['id']); ?>" class="dropdown-item">
                                                                                Mark as Ready
                                                                            </a>
                                                                        </li>
                                                                        <li>
                                                                            <a href="manage_reservations.php?action=cancel&id=<?php echo h($reservation['id']); ?>" class="dropdown-item">
                                                                                Cancel Reservation
                                                                            </a>
                                                                        </li>
                                                                        <li><hr class="dropdown-divider"></li>
                                                                        <li>
                                                                            <a href="../members/view.php?id=<?php echo h($reservation['member_id']); ?>" class="dropdown-item">
                                                                                View Member
                                                                            </a>
                                                                        </li>
                                                                        <li>
                                                                            <a href="../books/view.php?id=<?php echo h($reservation['book_id']); ?>" class="dropdown-item">
                                                                                View Book
                                                                            </a>
                                                                        </li>
                                                                    </ul>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle me-2"></i> No pending reservations at this time.
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <?php if (count($pending_reservations) > 0): ?>
                                    <div class="card-footer text-end">
                                        <a href="manage_reservations.php" class="btn btn-sm btn-primary">Manage All Reservations</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Calendar View for Due Dates -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="bi bi-calendar-event me-2"></i> Due Date Calendar</h5>
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#calendarCollapse" aria-expanded="true" aria-controls="calendarCollapse">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse show" id="calendarCollapse">
                                <div class="card-body">
                                    <div id="calendar-container">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <button id="prevMonth" class="btn btn-sm btn-outline-secondary"><i class="bi bi-chevron-left"></i></button>
                                            <h5 id="currentMonth" class="mb-0"></h5>
                                            <button id="nextMonth" class="btn btn-sm btn-outline-secondary"><i class="bi bi-chevron-right"></i></button>
                                        </div>
                                        <div class="table-responsive">
                                            <table id="calendar" class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Sun</th>
                                                        <th>Mon</th>
                                                        <th>Tue</th>
                                                        <th>Wed</th>
                                                        <th>Thu</th>
                                                        <th>Fri</th>
                                                        <th>Sat</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="calendarBody">
                                                    <!-- Calendar will be generated by JavaScript -->
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="mt-2">
                                            <span class="badge bg-danger me-2">Overdue</span>
                                            <span class="badge bg-warning text-dark me-2">Due Today</span>
                                            <span class="badge bg-info me-2">Due Soon</span>
                                            <span class="badge bg-success me-2">Returned</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Book Repair/Maintenance Tracker -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="bi bi-tools me-2"></i> Book Repair & Maintenance</h5>
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#repairTrackerCollapse" aria-expanded="true" aria-controls="repairTrackerCollapse">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse show" id="repairTrackerCollapse">
                                <div class="card-body">
                                    <div class="d-flex justify-content-end mb-3">
                                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addRepairModal">
                                            <i class="bi bi-plus-circle me-1"></i> Add New Repair
                                        </button>
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Book</th>
                                                    <th>Issue</th>
                                                    <th>Status</th>
                                                    <th>Added On</th>
                                                    <th>Priority</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Sample data - in a real implementation, this would come from the database -->
                                                <tr>
                                                    <td>To Kill a Mockingbird</td>
                                                    <td>Torn cover and binding</td>
                                                    <td><span class="badge bg-warning text-dark">In Progress</span></td>
                                                    <td>Jun 15, 2023</td>
                                                    <td><span class="badge bg-danger">High</span></td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                                Actions
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item" href="#">Mark as Completed</a></li>
                                                                <li><a class="dropdown-item" href="#">Edit Details</a></li>
                                                                <li><hr class="dropdown-divider"></li>
                                                                <li><a class="dropdown-item text-danger" href="#">Remove</a></li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>The Great Gatsby</td>
                                                    <td>Water damage on pages 45-60</td>
                                                    <td><span class="badge bg-success">Completed</span></td>
                                                    <td>May 28, 2023</td>
                                                    <td><span class="badge bg-warning text-dark">Medium</span></td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                                Actions
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item" href="#">View Details</a></li>
                                                                <li><a class="dropdown-item" href="#">Edit Details</a></li>
                                                                <li><hr class="dropdown-divider"></li>
                                                                <li><a class="dropdown-item text-danger" href="#">Remove</a></li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Pride and Prejudice</td>
                                                    <td>Missing pages 120-122</td>
                                                    <td><span class="badge bg-info text-white">Pending</span></td>
                                                    <td>Jun 20, 2023</td>
                                                    <td><span class="badge bg-info">Low</span></td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                                Actions
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item" href="#">Mark as In Progress</a></li>
                                                                <li><a class="dropdown-item" href="#">Edit Details</a></li>
                                                                <li><hr class="dropdown-divider"></li>
                                                                <li><a class="dropdown-item text-danger" href="#">Remove</a></li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="card-footer text-end">
                                    <a href="#" class="btn btn-sm btn-primary">View All Repairs</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Visualization Section -->
                <div class="row">
                    <!-- Book Categories Chart -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Books by Category</h5>
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#categoriesChartCollapse" aria-expanded="true" aria-controls="categoriesChartCollapse">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse show" id="categoriesChartCollapse">
                                <div class="card-body">
                                    <div style="height: 300px;">
                                        <canvas id="categoriesChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Loans Activity Chart -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Loans Activity</h5>
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#loansChartCollapse" aria-expanded="true" aria-controls="loansChartCollapse">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse show" id="loansChartCollapse">
                                <div class="card-body">
                                    <div style="height: 300px;">
                                        <canvas id="loansChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Most Popular Books Chart -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Most Popular Books</h5>
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#popularBooksChartCollapse" aria-expanded="true" aria-controls="popularBooksChartCollapse">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse show" id="popularBooksChartCollapse">
                                <div class="card-body">
                                    <div style="height: 300px;">
                                        <canvas id="popularBooksChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Book Recommendations -->
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="bi bi-star me-2"></i> Popular Books (Recommendations)</h5>
                                <a href="../books/index.php" class="btn btn-sm btn-secondary">View All Books</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($popular_books)): ?>
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle me-2"></i> No popular books data available.
                                    </div>
                                <?php else: ?>
                                    <div class="row">
                                        <?php foreach ($popular_books as $book): ?>
                                            <div class="col-md-4 col-lg-2 mb-4">
                                                <div class="card h-100 book-card">
                                                    <div class="position-relative">
                                                        <?php if (!empty($book['cover_image']) && file_exists('../' . $book['cover_image'])): ?>
                                                            <img src="<?php echo url($book['cover_image']); ?>" class="card-img-top" alt="<?php echo h($book['title']); ?>" style="height: 200px; object-fit: cover;">
                                                        <?php else: ?>
                                                            <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 200px;">
                                                                <i class="bi bi-book fs-1 text-muted"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                        <span class="position-absolute top-0 end-0 badge bg-primary m-2">
                                                            <?php echo h($book['borrow_count']); ?> borrows
                                                        </span>
                                                    </div>
                                                    <div class="card-body">
                                                        <h6 class="card-title text-truncate" title="<?php echo h($book['title']); ?>"><?php echo h($book['title']); ?></h6>
                                                        <p class="card-text small text-muted mb-0">By <?php echo h($book['author']); ?></p>
                                                        <div class="d-flex justify-content-between align-items-center mt-2">
                                                            <span class="badge <?php echo $book['available_quantity'] > 0 ? 'bg-success' : 'bg-danger'; ?>">
                                                                <?php echo $book['available_quantity'] > 0 ? 'Available' : 'Unavailable'; ?>
                                                            </span>
                                                            <a href="../books/view.php?id=<?php echo h($book['id']); ?>" class="btn btn-sm btn-outline-primary">Details</a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Cover Modal -->
    <div class="modal fade" id="coverModal" tabindex="-1" aria-labelledby="coverModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="coverModalLabel">Book Cover</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img src="" id="coverModalImage" class="img-fluid" alt="Book Cover" style="max-height: 500px;">
                </div>
            </div>
        </div>
    </div>

    <!-- Add Repair Modal -->
    <div class="modal fade" id="addRepairModal" tabindex="-1" aria-labelledby="addRepairModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addRepairModalLabel">Add New Book Repair</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="repairForm">
                        <div class="mb-3">
                            <label for="bookSelect" class="form-label">Book</label>
                            <select class="form-select" id="bookSelect" required>
                                <option value="" selected disabled>Select a book</option>
                                <option value="1">To Kill a Mockingbird</option>
                                <option value="2">The Great Gatsby</option>
                                <option value="3">Pride and Prejudice</option>
                                <option value="4">1984</option>
                                <option value="5">The Catcher in the Rye</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="issueDescription" class="form-label">Issue Description</label>
                            <textarea class="form-control" id="issueDescription" rows="3" required></textarea>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="prioritySelect" class="form-label">Priority</label>
                                <select class="form-select" id="prioritySelect" required>
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="statusSelect" class="form-label">Status</label>
                                <select class="form-select" id="statusSelect" required>
                                    <option value="pending" selected>Pending</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="completed">Completed</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="repairNotes" class="form-label">Additional Notes</label>
                            <textarea class="form-control" id="repairNotes" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="estimatedCost" class="form-label">Estimated Cost (optional)</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" id="estimatedCost" step="0.01" min="0">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveRepairBtn">Save Repair</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="<?php echo url('assets/js/dark-mode.js'); ?>"></script>
    <script src="<?php echo url('assets/js/notifications.js'); ?>"></script>

    <script>
    // Add responsive behavior for mobile devices
    document.addEventListener('DOMContentLoaded', function() {
        // Handle sidebar toggle on mobile
        const sidebarToggle = document.querySelector('.navbar-toggler');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                document.querySelector('.sidebar').classList.toggle('show');
            });
        }

        // Initialize tooltips
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // Handle notification bell click
        const notificationBell = document.getElementById('notificationBell');
        const staticNotificationsContainer = document.getElementById('staticNotifications');

        if (notificationBell && staticNotificationsContainer) {
            notificationBell.addEventListener('click', function(e) {
                e.preventDefault();

                if (staticNotificationsContainer.style.display === 'none') {
                    staticNotificationsContainer.style.display = 'block';
                    staticNotificationsContainer.classList.add('show');
                    localStorage.setItem('notificationsHidden', 'false');
                } else {
                    staticNotificationsContainer.classList.remove('show');
                    setTimeout(() => {
                        staticNotificationsContainer.style.display = 'none';
                    }, 300);
                    localStorage.setItem('notificationsHidden', 'true');
                }
            });
        }

        // Handle close button click
        const closeNotificationsBtn = document.getElementById('closeNotifications');
        if (closeNotificationsBtn) {
            closeNotificationsBtn.addEventListener('click', function() {
                if (staticNotificationsContainer) {
                    staticNotificationsContainer.classList.remove('show');
                    setTimeout(() => {
                        staticNotificationsContainer.style.display = 'none';
                    }, 300);
                    localStorage.setItem('notificationsHidden', 'true');
                }
            });
        }

        // Handle minimize button click
        const minimizeNotificationsBtn = document.getElementById('minimizeNotifications');
        if (minimizeNotificationsBtn) {
            minimizeNotificationsBtn.addEventListener('click', function() {
                if (staticNotificationsContainer) {
                    if (staticNotificationsContainer.classList.contains('minimized')) {
                        // Expand
                        staticNotificationsContainer.classList.remove('minimized');
                        this.innerHTML = '<i class="bi bi-dash"></i>';
                        this.title = 'Minimize';
                        localStorage.setItem('notificationsMinimized', 'false');
                    } else {
                        // Minimize
                        staticNotificationsContainer.classList.add('minimized');
                        this.innerHTML = '<i class="bi bi-arrows-angle-expand"></i>';
                        this.title = 'Expand';
                        localStorage.setItem('notificationsMinimized', 'true');
                    }
                }
            });
        }

        // Handle refresh button click
        const refreshNotificationsBtn = document.getElementById('refreshNotifications');
        if (refreshNotificationsBtn) {
            refreshNotificationsBtn.addEventListener('click', function() {
                // Add loading animation
                this.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
                this.disabled = true;

                // Reload the page after a short delay to show the loading animation
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            });
        }

        // Handle mark as read button clicks
        const markReadButtons = document.querySelectorAll('.mark-read-btn');
        markReadButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const notificationId = this.getAttribute('data-id');
                const notificationItem = this.closest('.notification-item');

                // Mark as read in UI
                notificationItem.classList.remove('unread');
                this.style.display = 'none';

                // Update notification count
                const badge = document.querySelector('.notification-badge');
                const counter = document.querySelector('.notification-counter');

                if (badge && counter) {
                    let count = parseInt(counter.textContent) - 1;
                    counter.textContent = count;

                    if (count <= 0) {
                        badge.style.display = 'none';
                        counter.style.display = 'none';
                        document.querySelector('.mark-all-read').style.display = 'none';
                    } else {
                        badge.textContent = count > 9 ? '9+' : count;
                    }
                }

                // Send AJAX request to mark as read
                fetch('../notifications/mark_read.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'notification_id=' + notificationId
                })
                .catch(error => {
                    console.error('Error marking notification as read:', error);
                });
            });
        });

        // Handle book cover preview
        const coverModal = document.getElementById('coverModal');
        if (coverModal) {
            coverModal.addEventListener('show.bs.modal', function (event) {
                const button = event.relatedTarget;
                const imgSrc = button.getAttribute('data-img-src');
                const title = button.getAttribute('data-title');

                const modalImage = document.getElementById('coverModalImage');
                const modalTitle = coverModal.querySelector('.modal-title');

                modalImage.src = imgSrc;
                modalTitle.textContent = title ? title + ' - Cover' : 'Book Cover';
            });
        }

        // Initialize Charts
        <?php
        // Get book categories for chart
        $query = "SELECT category, COUNT(*) as count FROM books GROUP BY category ORDER BY count DESC";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $categories = $stmt->fetchAll();

        // Format category data for chart
        $category_labels = [];
        $category_data = [];
        foreach ($categories as $category) {
            $category_labels[] = empty($category['category']) ? 'Uncategorized' : $category['category'];
            $category_data[] = $category['count'];
        }

        // Get loan data for the last 6 months
        $query = "SELECT
                    DATE_FORMAT(issue_date, '%b') as month,
                    COUNT(*) as count
                  FROM
                    book_loans
                  WHERE
                    issue_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
                  GROUP BY
                    MONTH(issue_date), YEAR(issue_date)
                  ORDER BY
                    YEAR(issue_date), MONTH(issue_date)";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $loan_data = $stmt->fetchAll();

        // Format loan data for chart
        $loan_labels = [];
        $loan_counts = [];
        foreach ($loan_data as $data) {
            $loan_labels[] = $data['month'];
            $loan_counts[] = $data['count'];
        }

        // If we have less than 6 months of data, fill in the missing months
        if (count($loan_labels) < 6) {
            $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            $current_month = date('n') - 1; // 0-based month index

            $loan_labels = [];
            $loan_counts = array_fill(0, 6, 0); // Initialize with zeros

            // Fill in the last 6 months
            for ($i = 5; $i >= 0; $i--) {
                $month_index = ($current_month - $i + 12) % 12; // Handle wrapping around to previous year
                $loan_labels[] = $months[$month_index];

                // Update counts for months we have data for
                foreach ($loan_data as $data) {
                    if ($data['month'] === $months[$month_index]) {
                        $loan_counts[5 - $i] = $data['count'];
                        break;
                    }
                }
            }
        }
        ?>

        // Categories Chart
        const categoriesChart = document.getElementById('categoriesChart');
        if (categoriesChart) {
            new Chart(categoriesChart, {
                type: 'pie',
                data: {
                    labels: <?php echo json_encode($category_labels); ?>,
                    datasets: [{
                        data: <?php echo json_encode($category_data); ?>,
                        backgroundColor: [
                            '#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1', '#fd7e14',
                            '#20c997', '#17a2b8', '#6c757d', '#343a40', '#f8f9fa', '#e83e8c'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 15,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        title: {
                            display: true,
                            text: 'Book Distribution by Category',
                            font: {
                                size: 16
                            }
                        }
                    }
                }
            });
        }

        // Loans Chart
        const loansChart = document.getElementById('loansChart');
        if (loansChart) {
            new Chart(loansChart, {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode($loan_labels); ?>,
                    datasets: [{
                        label: 'Number of Loans',
                        data: <?php echo json_encode($loan_counts); ?>,
                        backgroundColor: 'rgba(0, 123, 255, 0.5)',
                        borderColor: 'rgba(0, 123, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Book Loans - Last 6 Months',
                            font: {
                                size: 16
                            }
                        }
                    }
                }
            });
        }

        // Popular Books Chart
        <?php
        // Get most popular books data
        $query = "SELECT b.title, COUNT(bl.id) as borrow_count
                  FROM book_loans bl
                  JOIN books b ON bl.book_id = b.id
                  GROUP BY bl.book_id
                  ORDER BY borrow_count DESC
                  LIMIT 5";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $popular_books_data = $stmt->fetchAll();

        // Format data for chart
        $popular_books_labels = [];
        $popular_books_counts = [];
        foreach ($popular_books_data as $book) {
            // Truncate long titles
            $title = strlen($book['title']) > 20 ? substr($book['title'], 0, 20) . '...' : $book['title'];
            $popular_books_labels[] = $title;
            $popular_books_counts[] = $book['borrow_count'];
        }
        ?>

        const popularBooksChart = document.getElementById('popularBooksChart');
        if (popularBooksChart) {
            new Chart(popularBooksChart, {
                type: 'doughnut',
                data: {
                    labels: <?php echo json_encode($popular_books_labels); ?>,
                    datasets: [{
                        data: <?php echo json_encode($popular_books_counts); ?>,
                        backgroundColor: [
                            '#fd7e14', '#20c997', '#6f42c1', '#dc3545', '#ffc107'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 15,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        title: {
                            display: true,
                            text: 'Most Borrowed Books',
                            font: {
                                size: 16
                            }
                        }
                    }
                }
            });
        }

        // Calendar functionality
        let currentDate = new Date();
        let currentMonth = currentDate.getMonth();
        let currentYear = currentDate.getFullYear();

        // Get due dates from database
        <?php
        // Get all active loans with due dates
        $query = "SELECT bl.id, bl.book_id, bl.member_id, bl.due_date, bl.status,
                  b.title as book_title, m.first_name, m.last_name
                  FROM book_loans bl
                  JOIN books b ON bl.book_id = b.id
                  JOIN members m ON bl.member_id = m.id
                  WHERE bl.return_date IS NULL
                  ORDER BY bl.due_date ASC";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $due_dates = $stmt->fetchAll();

        // Format due dates for JavaScript
        $due_dates_js = [];
        foreach ($due_dates as $loan) {
            $due_date = date('Y-m-d', strtotime($loan['due_date']));
            $status = $loan['status'];
            $title = $loan['book_title'];
            $member = $loan['first_name'] . ' ' . $loan['last_name'];

            $due_dates_js[] = [
                'date' => $due_date,
                'status' => $status,
                'title' => $title,
                'member' => $member,
                'id' => $loan['id']
            ];
        }
        ?>

        // Due dates data
        const dueDates = <?php echo json_encode($due_dates_js); ?>;

        // Function to generate calendar
        function generateCalendar(month, year) {
            const firstDay = new Date(year, month, 1).getDay();
            const daysInMonth = new Date(year, month + 1, 0).getDate();

            // Update current month display
            const monthNames = ["January", "February", "March", "April", "May", "June",
                               "July", "August", "September", "October", "November", "December"];
            document.getElementById('currentMonth').textContent = `${monthNames[month]} ${year}`;

            // Clear previous calendar
            const calendarBody = document.getElementById('calendarBody');
            calendarBody.innerHTML = '';

            // Create calendar rows and cells
            let date = 1;
            for (let i = 0; i < 6; i++) {
                // Create table row
                const row = document.createElement('tr');

                // Create cells for each day of the week
                for (let j = 0; j < 7; j++) {
                    const cell = document.createElement('td');
                    cell.style.height = '80px';
                    cell.style.width = '14.28%';
                    cell.style.padding = '5px';
                    cell.style.verticalAlign = 'top';

                    if (i === 0 && j < firstDay) {
                        // Empty cells before the first day of the month
                        cell.textContent = '';
                    } else if (date > daysInMonth) {
                        // Empty cells after the last day of the month
                        break;
                    } else {
                        // Cells with dates
                        const dateDiv = document.createElement('div');
                        dateDiv.textContent = date;
                        dateDiv.style.fontWeight = 'bold';
                        cell.appendChild(dateDiv);

                        // Check if there are any due dates for this day
                        const currentDateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(date).padStart(2, '0')}`;
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);

                        // Highlight today's date
                        if (date === today.getDate() && month === today.getMonth() && year === today.getFullYear()) {
                            cell.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
                            cell.style.borderRadius = '5px';
                        }

                        // Add due dates to the calendar
                        const dueItemsForDay = dueDates.filter(item => item.date === currentDateStr);

                        if (dueItemsForDay.length > 0) {
                            const dueList = document.createElement('div');
                            dueList.style.marginTop = '5px';

                            dueItemsForDay.forEach((item, index) => {
                                if (index < 3) { // Show max 3 items per day
                                    const dueItem = document.createElement('div');
                                    dueItem.style.fontSize = '0.7rem';
                                    dueItem.style.padding = '2px 5px';
                                    dueItem.style.marginBottom = '2px';
                                    dueItem.style.borderRadius = '3px';
                                    dueItem.style.cursor = 'pointer';
                                    dueItem.title = `${item.title} - ${item.member}`;

                                    // Set background color based on status
                                    if (item.status === 'overdue') {
                                        dueItem.style.backgroundColor = '#dc3545';
                                        dueItem.style.color = 'white';
                                    } else {
                                        const dueDate = new Date(item.date);
                                        const diffTime = dueDate - today;
                                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                                        if (diffDays === 0) {
                                            dueItem.style.backgroundColor = '#ffc107';
                                        } else if (diffDays <= 3) {
                                            dueItem.style.backgroundColor = '#17a2b8';
                                            dueItem.style.color = 'white';
                                        } else {
                                            dueItem.style.backgroundColor = '#e9ecef';
                                        }
                                    }

                                    dueItem.textContent = item.title.substring(0, 10) + '...';
                                    dueItem.addEventListener('click', () => {
                                        window.location.href = `../loans/view.php?id=${item.id}`;
                                    });

                                    dueList.appendChild(dueItem);
                                } else if (index === 3) {
                                    // Show "more" indicator if there are more than 3 items
                                    const moreItem = document.createElement('div');
                                    moreItem.style.fontSize = '0.7rem';
                                    moreItem.style.textAlign = 'center';
                                    moreItem.style.color = '#6c757d';
                                    moreItem.textContent = `+${dueItemsForDay.length - 3} more`;
                                    dueList.appendChild(moreItem);
                                }
                            });

                            cell.appendChild(dueList);
                        }

                        date++;
                    }

                    row.appendChild(cell);
                }

                calendarBody.appendChild(row);

                // Stop creating rows if we've reached the end of the month
                if (date > daysInMonth) {
                    break;
                }
            }
        }

        // Initialize calendar
        generateCalendar(currentMonth, currentYear);

        // Handle previous month button
        document.getElementById('prevMonth').addEventListener('click', () => {
            currentMonth--;
            if (currentMonth < 0) {
                currentMonth = 11;
                currentYear--;
            }
            generateCalendar(currentMonth, currentYear);
        });

        // Handle next month button
        document.getElementById('nextMonth').addEventListener('click', () => {
            currentMonth++;
            if (currentMonth > 11) {
                currentMonth = 0;
                currentYear++;
            }
            generateCalendar(currentMonth, currentYear);
        });

        // Book Repair Form Handling
        const saveRepairBtn = document.getElementById('saveRepairBtn');
        if (saveRepairBtn) {
            saveRepairBtn.addEventListener('click', () => {
                const form = document.getElementById('repairForm');

                // Basic form validation
                const bookSelect = document.getElementById('bookSelect');
                const issueDescription = document.getElementById('issueDescription');

                if (!bookSelect.value) {
                    alert('Please select a book');
                    bookSelect.focus();
                    return;
                }

                if (!issueDescription.value.trim()) {
                    alert('Please enter an issue description');
                    issueDescription.focus();
                    return;
                }

                // In a real implementation, this would send data to the server
                // For now, we'll just show a success message and close the modal
                alert('Repair record added successfully!');

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('addRepairModal'));
                modal.hide();

                // Optionally, refresh the page or update the UI
                // location.reload();
            });
        }

    });
    </script>

    <!-- Enhanced Dashboard Cards Styles and Scripts -->
    <style>
        /* Enhanced Stats Cards Styling */
        .stats-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .clickable-card {
            cursor: pointer;
            user-select: none;
        }

        .clickable-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .clickable-card:focus {
            outline: 3px solid rgba(255, 255, 255, 0.5);
            outline-offset: 2px;
        }

        .clickable-card:active {
            transform: translateY(-2px);
        }

        .card-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .clickable-card:hover .card-overlay {
            opacity: 1;
        }

        .stats-icon {
            transition: transform 0.3s ease;
        }

        .clickable-card:hover .stats-icon {
            transform: scale(1.1);
        }

        .text-white-75 {
            color: rgba(255, 255, 255, 0.75) !important;
        }

        /* Urgent attention animations */
        .urgent-attention {
            animation: urgentPulse 2s infinite;
        }

        .attention-needed {
            animation: attentionGlow 3s infinite;
        }

        .pulse-animation {
            animation: iconPulse 1.5s infinite;
        }

        @keyframes urgentPulse {
            0%, 100% { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
            50% { box-shadow: 0 4px 20px rgba(220, 53, 69, 0.4); }
        }

        @keyframes attentionGlow {
            0%, 100% { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
            50% { box-shadow: 0 4px 20px rgba(253, 126, 20, 0.3); }
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Loading state */
        .stats-card.loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .stats-card.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .stats-card {
                margin-bottom: 1rem;
            }

            .clickable-card:hover {
                transform: none;
            }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .stats-card {
                border: 2px solid currentColor;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .stats-card,
            .stats-icon,
            .card-overlay {
                transition: none;
            }

            .urgent-attention,
            .attention-needed,
            .pulse-animation {
                animation: none;
            }

            .clickable-card:hover {
                transform: none;
            }
        }
    </style>

    <script>
        // Enhanced Dashboard Cards Functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Make cards clickable
            const clickableCards = document.querySelectorAll('.clickable-card');

            clickableCards.forEach(card => {
                // Handle click events
                card.addEventListener('click', function(e) {
                    e.preventDefault();
                    const href = this.getAttribute('data-href');
                    if (href) {
                        // Add loading state
                        this.classList.add('loading');

                        // Navigate after a short delay to show loading state
                        setTimeout(() => {
                            window.location.href = href;
                        }, 200);
                    }
                });

                // Handle keyboard navigation
                card.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.click();
                    }
                });

                // Handle focus management
                card.addEventListener('focus', function() {
                    this.style.outline = '3px solid rgba(255, 255, 255, 0.5)';
                    this.style.outlineOffset = '2px';
                });

                card.addEventListener('blur', function() {
                    this.style.outline = '';
                    this.style.outlineOffset = '';
                });
            });

            // Auto-refresh stats every 30 seconds
            setInterval(function() {
                refreshDashboardStats();
            }, 30000);

            // Function to refresh dashboard statistics
            function refreshDashboardStats() {
                fetch('dashboard_stats.php')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Update stats with smooth animation
                            updateStatWithAnimation('total_books', data.stats.total_books);
                            updateStatWithAnimation('available_books', data.stats.available_books);
                            updateStatWithAnimation('total_members', data.stats.total_members);
                            updateStatWithAnimation('active_loans', data.stats.active_loans);
                            updateStatWithAnimation('overdue_books', data.stats.overdue_books);
                            updateStatWithAnimation('pending_reservations', data.stats.pending_reservations);
                            updateStatWithAnimation('due_today', data.stats.due_today);

                            // Update urgent attention classes
                            updateUrgentAttention(data.stats);
                        }
                    })
                    .catch(error => {
                        console.log('Stats refresh failed:', error);
                    });
            }

            // Function to update stat with animation
            function updateStatWithAnimation(statName, newValue) {
                const element = document.querySelector(`[data-stat="${statName}"]`);
                if (element) {
                    const currentValue = parseInt(element.textContent);
                    if (currentValue !== newValue) {
                        // Add a subtle animation
                        element.style.transform = 'scale(1.1)';
                        element.style.transition = 'transform 0.3s ease';

                        setTimeout(() => {
                            element.textContent = newValue;
                            element.style.transform = 'scale(1)';
                        }, 150);
                    }
                }
            }

            // Function to update urgent attention indicators
            function updateUrgentAttention(stats) {
                // Update overdue books card
                const overdueCard = document.querySelector('[data-stat="overdue_books"]').closest('.stats-card');
                if (stats.overdue_books > 0) {
                    overdueCard.classList.add('urgent-attention');
                } else {
                    overdueCard.classList.remove('urgent-attention');
                }

                // Update due today card
                const dueTodayCard = document.querySelector('[data-stat="due_today"]').closest('.stats-card');
                if (stats.due_today > 0) {
                    dueTodayCard.classList.add('attention-needed');
                } else {
                    dueTodayCard.classList.remove('attention-needed');
                }
            }

            // Add quick action buttons on hover (for larger screens)
            if (window.innerWidth > 768) {
                clickableCards.forEach(card => {
                    card.addEventListener('mouseenter', function() {
                        // Could add quick action buttons here in the future
                    });
                });
            }

            // Accessibility improvements
            // Announce important changes to screen readers
            function announceToScreenReader(message) {
                const announcement = document.createElement('div');
                announcement.setAttribute('aria-live', 'polite');
                announcement.setAttribute('aria-atomic', 'true');
                announcement.style.position = 'absolute';
                announcement.style.left = '-10000px';
                announcement.style.width = '1px';
                announcement.style.height = '1px';
                announcement.style.overflow = 'hidden';
                announcement.textContent = message;

                document.body.appendChild(announcement);

                setTimeout(() => {
                    document.body.removeChild(announcement);
                }, 1000);
            }

            // Monitor for urgent changes and announce them
            let lastOverdueCount = parseInt(document.querySelector('[data-stat="overdue_books"]').textContent);
            let lastDueTodayCount = parseInt(document.querySelector('[data-stat="due_today"]').textContent);

            setInterval(() => {
                const currentOverdue = parseInt(document.querySelector('[data-stat="overdue_books"]').textContent);
                const currentDueToday = parseInt(document.querySelector('[data-stat="due_today"]').textContent);

                if (currentOverdue > lastOverdueCount) {
                    announceToScreenReader(`Alert: Overdue books increased to ${currentOverdue}`);
                }

                if (currentDueToday > lastDueTodayCount) {
                    announceToScreenReader(`Notice: Books due today increased to ${currentDueToday}`);
                }

                lastOverdueCount = currentOverdue;
                lastDueTodayCount = currentDueToday;
            }, 60000); // Check every minute
        });
    </script>

    <!-- Dashboard Modals -->
    <?php if (function_exists('renderAddTaskModal')) renderAddTaskModal(); ?>
    <?php if (function_exists('renderSaveFilterModal')) renderSaveFilterModal(); ?>
    <?php if (function_exists('renderQuickMemberRegistrationModal')) renderQuickMemberRegistrationModal(); ?>
</body>
</html>
