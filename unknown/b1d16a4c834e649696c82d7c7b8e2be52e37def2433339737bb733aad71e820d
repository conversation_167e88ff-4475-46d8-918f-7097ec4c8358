<?php
/**
 * Google Authentication Status Page
 * 
 * This page is shown during Google authentication process.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/config.php';

// Get status from query parameter
$status = isset($_GET['status']) ? $_GET['status'] : 'processing';
$message = isset($_GET['message']) ? $_GET['message'] : '';
$redirect = isset($_GET['redirect']) ? $_GET['redirect'] : 'login.php';

// Set default messages based on status
if (empty($message)) {
    switch ($status) {
        case 'success':
            $message = 'Successfully authenticated with Google. Redirecting...';
            break;
        case 'error':
            $message = 'An error occurred during Google authentication. Please try again.';
            break;
        case 'linking':
            $message = 'Linking your Google account...';
            break;
        case 'creating':
            $message = 'Creating your account...';
            break;
        default:
            $message = 'Processing your Google sign-in...';
            break;
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Authentication - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Roboto', sans-serif;
        }
        .auth-status-container {
            max-width: 500px;
            width: 100%;
            padding: 20px;
            text-align: center;
        }
        .status-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        .google-logo {
            width: 40px;
            height: 40px;
            margin-bottom: 20px;
        }
        .status-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        .status-success {
            color: #28a745;
        }
        .status-error {
            color: #dc3545;
        }
        .status-processing {
            color: #007bff;
        }
        .status-message {
            margin-bottom: 25px;
            font-size: 1.1rem;
        }
        .redirect-message {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="auth-status-container">
        <div class="status-card">
            <?php if ($status === 'success'): ?>
                <i class="bi bi-check-circle-fill status-icon status-success"></i>
            <?php elseif ($status === 'error'): ?>
                <i class="bi bi-exclamation-circle-fill status-icon status-error"></i>
            <?php else: ?>
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            <?php endif; ?>
            
            <h4><?php echo h($message); ?></h4>
            
            <?php if ($status === 'error'): ?>
                <p class="redirect-message">You will be redirected to the login page in <span id="countdown">5</span> seconds.</p>
                <a href="<?php echo url($redirect); ?>" class="btn btn-primary mt-3">Return to Login</a>
            <?php else: ?>
                <p class="redirect-message">Please wait while we process your request...</p>
            <?php endif; ?>
        </div>
        <div class="text-center">
            <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" class="google-logo">
            <p class="mb-0 text-muted">Google Authentication</p>
        </div>
    </div>

    <script>
        // Redirect after delay
        <?php if ($status === 'success' || $status === 'error'): ?>
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        
        const redirectTimer = setInterval(() => {
            countdown--;
            if (countdownElement) {
                countdownElement.textContent = countdown;
            }
            
            if (countdown <= 0) {
                clearInterval(redirectTimer);
                window.location.href = '<?php echo url($redirect); ?>';
            }
        }, 1000);
        <?php endif; ?>
    </script>
</body>
</html>
