<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin or librarian
if (!isLoggedIn() || (!isAdmin() && !isLibrarian())) {
    redirect('../login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$records_per_page = 20;
$offset = ($page - 1) * $records_per_page;

// Get overdue books (both explicitly marked as overdue AND borrowed books past due)
$query = "SELECT bl.*, b.title as book_title, b.author, b.isbn,
          m.first_name, m.last_name, m.email, m.phone,
          DATEDIFF(CURDATE(), bl.due_date) as days_overdue
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          JOIN members m ON bl.member_id = m.id
          WHERE (bl.status = 'overdue' OR (bl.due_date < CURDATE() AND bl.status = 'borrowed'))
          ORDER BY days_overdue DESC";

// Count total records for pagination
$count_query = str_replace("bl.*, b.title as book_title, b.author, b.isbn, m.first_name, m.last_name, m.email, m.phone, DATEDIFF(CURDATE(), bl.due_date) as days_overdue", "COUNT(*) as total", $query);
$stmt = $db->prepare($count_query);
$stmt->execute();
$total_records = $stmt->fetch()['total'];
$total_pages = ceil($total_records / $records_per_page);

// Get loans with pagination
$query .= " LIMIT :offset, :limit";
$stmt = $db->prepare($query);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':limit', $records_per_page, PDO::PARAM_INT);
$stmt->execute();
$loans = $stmt->fetchAll();

// Log view activity
logActivity($db, 'view', 'Viewed overdue books report');

// Page title
$page_title = "Overdue Books";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
</head>
<body>
    <?php if (isAdmin()): ?>
        <?php include '../includes/header.php'; ?>
    <?php else: ?>
    <header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="#">Library MS Librarian</a>
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="w-100">
            <form class="d-none d-md-flex w-50 mx-auto" action="../search.php" method="get">
                <input class="form-control form-control-dark" type="text" name="q" placeholder="Search books, members..." aria-label="Search" required>
                <button class="btn btn-dark" type="submit"><i class="bi bi-search"></i></button>
            </form>
        </div>
        <div class="navbar-nav">
            <div class="nav-item text-nowrap d-flex align-items-center">
                <button id="darkModeToggle" class="btn btn-sm btn-outline-light me-2" title="Toggle Dark Mode">
                    <i id="darkModeIcon" class="bi bi-moon"></i>
                </button>
                <span class="nav-link px-3 text-white">Welcome, Librarian</span>
                <a class="btn btn-danger btn-sm mx-2" href="../logout.php">
                    <i class="bi bi-box-arrow-right me-1"></i>Sign out
                </a>
            </div>
        </div>
    </header>
    <?php endif; ?>

    <div class="container-fluid">
        <div class="row">
            <?php if (isAdmin()): ?>
                <?php include '../includes/sidebar.php'; ?>
            <?php else: ?>
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../librarian/dashboard.php">
                                <i class="bi bi-speedometer2 me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../books/index.php">
                                <i class="bi bi-book me-2"></i>
                                Books
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../members/index.php">
                                <i class="bi bi-people me-2"></i>
                                Members
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../loans/index.php">
                                <i class="bi bi-journal-arrow-up me-2"></i>
                                Book Loans
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="../reports/index.php">
                                <i class="bi bi-file-earmark-bar-graph me-2"></i>
                                Reports
                            </a>
                        </li>
                    </ul>

                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>Librarian Tasks</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="../librarian/issue_book.php">
                                <i class="bi bi-arrow-right-circle me-2"></i>
                                Issue Book
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../librarian/return_book.php">
                                <i class="bi bi-arrow-left-circle me-2"></i>
                                Return Book
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../librarian/manage_reservations.php">
                                <i class="bi bi-bookmark me-2"></i>
                                Manage Reservations
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../librarian/activity_log.php">
                                <i class="bi bi-list-check me-2"></i>
                                Activity Log
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            <?php endif; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><?php echo $page_title; ?></h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="javascript:window.print();" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-printer me-1"></i> Print
                            </a>
                            <a href="index.php" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-file-earmark-bar-graph me-1"></i> All Reports
                            </a>
                        </div>
                        <button id="darkModeToggle" class="btn btn-sm btn-outline-secondary" title="Toggle Dark Mode">
                            <i id="darkModeIcon" class="bi bi-moon"></i>
                        </button>
                    </div>
                </div>

                <!-- Overdue Books Table -->
                <div class="card mb-4">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i> Overdue Books</h5>
                        <span class="badge bg-danger"><?php echo $total_records; ?> overdue books</span>
                    </div>
                    <div class="card-body">
                        <?php if (empty($loans)): ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i> No overdue books found.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Book Title</th>
                                            <th>Author</th>
                                            <th>ISBN</th>
                                            <th>Member</th>
                                            <th>Contact</th>
                                            <th>Due Date</th>
                                            <th>Days Overdue</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($loans as $loan): ?>
                                            <tr>
                                                <td><?php echo h($loan['book_title']); ?></td>
                                                <td><?php echo h($loan['author']); ?></td>
                                                <td><?php echo h($loan['isbn']); ?></td>
                                                <td><?php echo h($loan['first_name'] . ' ' . $loan['last_name']); ?></td>
                                                <td>
                                                    <a href="mailto:<?php echo h($loan['email']); ?>" class="text-decoration-none">
                                                        <i class="bi bi-envelope me-1"></i><?php echo h($loan['email']); ?>
                                                    </a>
                                                    <br>
                                                    <a href="tel:<?php echo h($loan['phone']); ?>" class="text-decoration-none">
                                                        <i class="bi bi-telephone me-1"></i><?php echo h($loan['phone']); ?>
                                                    </a>
                                                </td>
                                                <td><?php echo formatDate($loan['due_date']); ?></td>
                                                <td>
                                                    <span class="badge bg-danger"><?php echo h($loan['days_overdue']); ?> days</span>
                                                </td>
                                                <td>
                                                    <a href="<?php echo isAdmin() ? '../loans/return.php?id=' . h($loan['id']) : '../librarian/return_book.php?loan_id=' . h($loan['id']); ?>" class="btn btn-sm btn-success">
                                                        <i class="bi bi-arrow-left-circle me-1"></i> Return
                                                    </a>
                                                    <a href="<?php echo isAdmin() ? '../loans/view.php?id=' . h($loan['id']) : '../loans/view.php?id=' . h($loan['id']); ?>" class="btn btn-sm btn-info">
                                                        <i class="bi bi-eye me-1"></i> View
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                                <nav aria-label="Overdue books pagination">
                                    <ul class="pagination justify-content-center mt-4">
                                        <?php if ($page > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=1" aria-label="First">
                                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                                </a>
                                            </li>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page - 1; ?>" aria-label="Previous">
                                                    <span aria-hidden="true">&laquo;</span>
                                                </a>
                                            </li>
                                        <?php endif; ?>

                                        <?php
                                        $start_page = max(1, $page - 2);
                                        $end_page = min($total_pages, $page + 2);

                                        for ($i = $start_page; $i <= $end_page; $i++):
                                        ?>
                                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $i; ?>">
                                                    <?php echo $i; ?>
                                                </a>
                                            </li>
                                        <?php endfor; ?>

                                        <?php if ($page < $total_pages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page + 1; ?>" aria-label="Next">
                                                    <span aria-hidden="true">&raquo;</span>
                                                </a>
                                            </li>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $total_pages; ?>" aria-label="Last">
                                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo url('assets/js/dark-mode.js'); ?>"></script>
</body>
</html>
