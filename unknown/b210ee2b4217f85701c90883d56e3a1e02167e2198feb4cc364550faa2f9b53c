/* Enhanced Notifications Styling */

/* Notification dropdown container */
.notifications-dropdown {
    width: 380px !important;
    max-width: 90vw !important;
    padding: 0 !important;
    border: none !important;
    border-radius: 12px !important;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
    background-color: #ffffff !important;
    margin-top: 8px !important;
    animation: dropdownSlideIn 0.2s ease-out;
}

@keyframes dropdownSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

[data-bs-theme="dark"] .notifications-dropdown {
    background-color: #1e1e1e !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4) !important;
}

/* Dropdown arrow/caret styling */
.notifications-dropdown::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #4361ee;
    z-index: 1001;
}

/* Notifications header */
.notifications-header {
    background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
    color: white;
    padding: 16px 20px;
    border-radius: 12px 12px 0 0;
    position: relative;
}

.notifications-header h6 {
    font-weight: 600;
    font-size: 1rem;
    margin: 0;
}

.notifications-header .badge {
    font-size: 0.75rem;
    padding: 4px 8px;
}

/* Notifications body */
.notifications-body {
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
    background-color: inherit;
}

/* Custom scrollbar for notifications body */
.notifications-body::-webkit-scrollbar {
    width: 6px;
}

.notifications-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.notifications-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.notifications-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

[data-bs-theme="dark"] .notifications-body::-webkit-scrollbar-track {
    background: #2d2d2d;
}

[data-bs-theme="dark"] .notifications-body::-webkit-scrollbar-thumb {
    background: #555;
}

[data-bs-theme="dark"] .notifications-body::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* Static Notifications Container */
.static-notifications-container {
    width: 350px;
    position: fixed;
    top: 60px;
    right: 10px;
    z-index: 1000;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    background-color: #fff;
    max-height: 80vh;
    overflow-y: auto;
    display: block; /* Always display the notifications */
    transition: all 0.3s ease;
    animation: slideIn 0.3s ease-out;
}

/* Animation for notifications panel */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animation for when notifications are shown */
.static-notifications-container.show {
    animation: slideIn 0.3s ease-out;
}

/* Minimized state for notifications */
.static-notifications-container.minimized .notifications-body,
.static-notifications-container.minimized .notifications-footer {
    display: none;
}

.static-notifications-container.minimized {
    width: 250px;
    max-height: 60px;
    overflow: hidden;
}

.static-notifications-container.minimized .notifications-header {
    border-radius: 0 0 12px 12px;
}

[data-bs-theme="dark"] .static-notifications-container {
    background-color: #1e1e1e;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

/* Notification header */
.notifications-header {
    background: linear-gradient(135deg, #4361ee, #3a0ca3) !important;
    color: white !important;
    padding: 15px !important;
    border-bottom: none !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notifications-header h6 {
    margin: 0;
    font-weight: 600;
    font-size: 1rem;
    letter-spacing: 0.5px;
}

.notifications-header a {
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 0.8rem;
    transition: all 0.2s ease;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
}

.notifications-header a:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: white !important;
}

/* Notification filter bar */
.notifications-filter-bar {
    padding: 10px;
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: center;
}

[data-bs-theme="dark"] .notifications-filter-bar {
    background-color: #2d2d2d;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.notifications-filter-bar .btn-group {
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.notifications-filter-bar .btn {
    flex: 1;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* Notification body */
.notifications-body {
    max-height: 350px;
    overflow-y: auto;
    padding: 0 !important;
    scrollbar-width: thin;
}

.notifications-body::-webkit-scrollbar {
    width: 6px;
}

.notifications-body::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
}

.notifications-body::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

[data-bs-theme="dark"] .notifications-body::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
}

[data-bs-theme="dark"] .notifications-body::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Notification item */
.notification-item {
    padding: 16px 20px !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;
    position: relative;
    background-color: transparent;
    cursor: default;
}

[data-bs-theme="dark"] .notification-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item:hover {
    background-color: rgba(67, 97, 238, 0.04);
}

[data-bs-theme="dark"] .notification-item:hover {
    background-color: rgba(67, 97, 238, 0.12);
}

.notification-item.unread {
    background-color: rgba(67, 97, 238, 0.06);
    border-left: 3px solid #4361ee;
    padding-left: 17px;
}

[data-bs-theme="dark"] .notification-item.unread {
    background-color: rgba(67, 97, 238, 0.15);
}

.notification-item.unread .notification-title {
    font-weight: 600;
}

/* Notification types */
.notification-item.type-info {
    border-left-color: #4361ee;
}

.notification-item.type-warning {
    border-left-color: #fb8500;
}

.notification-item.type-danger {
    border-left-color: #e63946;
}

.notification-item.type-success {
    border-left-color: #2a9d8f;
}

/* Notification icon */
.notification-icon {
    width: 32px;
    height: 32px;
    min-width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-right: 12px;
    margin-top: 2px;
    background-color: rgba(67, 97, 238, 0.1);
}

.notification-icon i {
    font-size: 14px;
}

/* Notification content */
.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-size: 0.9rem;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 4px;
    line-height: 1.3;
}

[data-bs-theme="dark"] .notification-title {
    color: #e2e8f0;
}

.notification-message {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 6px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

[data-bs-theme="dark"] .notification-message {
    color: #94a3b8;
}

.notification-time {
    font-size: 0.75rem;
    color: #9ca3af;
    display: flex;
    align-items: center;
}

[data-bs-theme="dark"] .notification-time {
    color: #64748b;
}

.notification-time i {
    font-size: 0.7rem;
}

/* Notification actions */
.notification-actions {
    display: flex;
    align-items: flex-start;
    margin-top: 2px;
}

.mark-read-btn {
    padding: 4px 8px;
    font-size: 0.75rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    border: 1px solid #4361ee;
    color: #4361ee;
    background: transparent;
}

.mark-read-btn:hover {
    background-color: #4361ee;
    border-color: #4361ee;
    color: white;
}

/* Notification footer */
.notifications-footer {
    padding: 12px 20px !important;
    background-color: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 0 0 12px 12px;
}

[data-bs-theme="dark"] .notifications-footer {
    background-color: #2d2d2d;
    border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.notifications-footer .btn {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 500;
}

.notifications-footer .btn-outline-primary {
    border-color: #4361ee;
    color: #4361ee;
}

.notifications-footer .btn-outline-primary:hover {
    background-color: #4361ee;
    border-color: #4361ee;
    color: white;
}

.notifications-footer .btn-primary {
    background-color: #4361ee;
    border-color: #4361ee;
}

.notifications-footer .btn-primary:hover {
    background-color: #3a0ca3;
    border-color: #3a0ca3;
}

.notifications-footer .text-muted {
    font-size: 0.8rem;
}

/* Bell animation */
@keyframes bell-ring {
    0% { transform: rotate(0); }
    10% { transform: rotate(10deg); }
    20% { transform: rotate(-10deg); }
    30% { transform: rotate(6deg); }
    40% { transform: rotate(-6deg); }
    50% { transform: rotate(0); }
    100% { transform: rotate(0); }
}

.bell-icon {
    display: inline-block;
}

.bell-animate {
    animation: bell-ring 1s ease;
}

/* Badge pulse animation */
@keyframes badge-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.badge-pulse {
    animation: badge-pulse 1.5s infinite;
}

/* Spin animation for refresh icon */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spin {
    animation: spin 1s linear infinite;
}

/* Empty state */
.empty-notifications {
    padding: 30px 20px;
    text-align: center;
    color: #6c757d;
}

.empty-notifications i {
    font-size: 2.5rem;
    margin-bottom: 10px;
    opacity: 0.5;
}

.empty-notifications p {
    margin: 0;
    font-size: 0.9rem;
}
