<?php
/**
 * Troubleshooting Guide for LMS Database Issues
 */
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LMS Troubleshooting Guide</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <h1 class="mb-4"><i class="bi bi-tools"></i> LMS Troubleshooting Guide</h1>
                
                <div class="alert alert-info">
                    <h5><i class="bi bi-info-circle"></i> Common Database Issues</h5>
                    <p>If you're seeing "Failed to refresh statistics: Database error" messages, follow these steps:</p>
                </div>

                <div class="accordion" id="troubleshootingAccordion">
                    <!-- Step 1: Check Database Connection -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingOne">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                                <i class="bi bi-1-circle me-2"></i> Check Database Connection
                            </button>
                        </h2>
                        <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body">
                                <p><strong>Test your database connection:</strong></p>
                                <a href="database_status.php" class="btn btn-primary mb-3" target="_blank">
                                    <i class="bi bi-database"></i> Run Database Status Check
                                </a>
                                <p>This will tell you if:</p>
                                <ul>
                                    <li>Database connection is working</li>
                                    <li>Required tables exist</li>
                                    <li>Table structure is correct</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Check XAMPP/WAMP -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingTwo">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                                <i class="bi bi-2-circle me-2"></i> Verify XAMPP/WAMP is Running
                            </button>
                        </h2>
                        <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body">
                                <p><strong>Make sure your local server is running:</strong></p>
                                <ul>
                                    <li>Open XAMPP Control Panel</li>
                                    <li>Start Apache and MySQL services</li>
                                    <li>Check that MySQL shows "Running" status</li>
                                </ul>
                                <div class="alert alert-warning">
                                    <strong>Note:</strong> If MySQL won't start, try changing the port or check for conflicts with other services.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Database Setup -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingThree">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree">
                                <i class="bi bi-3-circle me-2"></i> Run Database Setup
                            </button>
                        </h2>
                        <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body">
                                <p><strong>If tables are missing, run the setup:</strong></p>
                                <a href="setup.php" class="btn btn-success mb-3" target="_blank">
                                    <i class="bi bi-gear"></i> Run Database Setup
                                </a>
                                <p>This will:</p>
                                <ul>
                                    <li>Create the lms_db database</li>
                                    <li>Create all required tables</li>
                                    <li>Set up the proper table structure</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Step 4: Check Configuration -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingFour">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour">
                                <i class="bi bi-4-circle me-2"></i> Verify Configuration
                            </button>
                        </h2>
                        <div id="collapseFour" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body">
                                <p><strong>Check database configuration in config/database.php:</strong></p>
                                <pre class="bg-light p-3 rounded">
private $host = "localhost";
private $db_name = "lms_db";
private $username = "root";
private $password = "";
                                </pre>
                                <p>Make sure these settings match your MySQL configuration.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Step 5: Advanced Diagnostics -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingFive">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFive">
                                <i class="bi bi-5-circle me-2"></i> Advanced Diagnostics
                            </button>
                        </h2>
                        <div id="collapseFive" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body">
                                <p><strong>For detailed technical information:</strong></p>
                                <a href="admin/ajax/test_database.php" class="btn btn-info mb-3" target="_blank">
                                    <i class="bi bi-bug"></i> Run Advanced Database Test
                                </a>
                                <p>This provides detailed technical diagnostics including:</p>
                                <ul>
                                    <li>Connection testing</li>
                                    <li>Table structure validation</li>
                                    <li>Query testing</li>
                                    <li>Column existence checks</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <div class="alert alert-success">
                        <h5><i class="bi bi-check-circle"></i> Quick Fix</h5>
                        <p>Most issues can be resolved by:</p>
                        <ol>
                            <li>Starting XAMPP MySQL service</li>
                            <li>Running the database setup</li>
                            <li>Refreshing the statistics</li>
                        </ol>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="index.php" class="btn btn-primary">
                        <i class="bi bi-house"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
