<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Quick Fix Book Covers</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h2>Quick Fix Book Covers</h2>";

if (isset($_POST['apply_fix'])) {
    echo "<h3>Applying Quick Fix...</h3>";
    
    // Direct mappings for the most common books
    $direct_updates = [
        "UPDATE books SET cover_image = '1984_cover.jpg' WHERE title LIKE '%1984%'",
        "UPDATE books SET cover_image = '1747724281_AGameOfThrones.jpg' WHERE title LIKE '%Game of Thrones%'",
        "UPDATE books SET cover_image = '1747724303_Good to Great.jpg' WHERE title LIKE '%Good to Great%'",
        "UPDATE books SET cover_image = '1747724335_Pride and Prejudice.jpg' WHERE title LIKE '%Pride and Prejudice%'",
        "UPDATE books SET cover_image = '1747725415_steve-jobs-9781451648553_hr.jpg' WHERE title LIKE '%Steve Jobs%'",
        "UPDATE books SET cover_image = '1747725431_the-alchemist-a-graphic-novel.jpg' WHERE title LIKE '%Alchemist%'",
        "UPDATE books SET cover_image = '1747725459_Book-Review-The-Catcher-in-the-Rye-by-J-D-Salinger.jpg' WHERE title LIKE '%Catcher in the Rye%'",
        "UPDATE books SET cover_image = '1747725503_the-great-gatsby-and-other-works-9781645173519_hr.jpg' WHERE title LIKE '%Great Gatsby%'",
        "UPDATE books SET cover_image = '1747725673_The_Martian_(Weir_novel).jpg' WHERE title LIKE '%Martian%'",
        "UPDATE books SET cover_image = '1747725698_fuck-640x996.jpg' WHERE title LIKE '%To Kill a Mockingbird%'",
        "UPDATE books SET cover_image = '1747725734_71qFBdNS+dL.jpg' WHERE title LIKE '%Harry Potter%'",
        "UPDATE books SET cover_image = '1747725751_9781784870799.jpg' WHERE title LIKE '%Sapiens%'"
    ];
    
    $total_updated = 0;
    foreach ($direct_updates as $sql) {
        try {
            $stmt = $db->prepare($sql);
            $stmt->execute();
            $affected = $stmt->rowCount();
            if ($affected > 0) {
                echo "<div class='alert alert-success'>✅ Updated {$affected} books with: " . htmlspecialchars($sql) . "</div>";
                $total_updated += $affected;
            }
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
    }
    
    // For remaining books without covers, assign random covers
    $available_covers = [
        '1984_cover.jpg',
        '1747724281_AGameOfThrones.jpg',
        '1747724303_Good to Great.jpg',
        '1747724335_Pride and Prejudice.jpg',
        '1747725415_steve-jobs-9781451648553_hr.jpg',
        '1747725431_the-alchemist-a-graphic-novel.jpg',
        '1747725459_Book-Review-The-Catcher-in-the-Rye-by-J-D-Salinger.jpg',
        '1747725503_the-great-gatsby-and-other-works-9781645173519_hr.jpg',
        '1747725673_The_Martian_(Weir_novel).jpg',
        '1747725698_fuck-640x996.jpg',
        '1747725734_71qFBdNS+dL.jpg',
        '1747725751_9781784870799.jpg'
    ];
    
    // Get books without covers
    $query = "SELECT id, title FROM books WHERE cover_image IS NULL OR cover_image = ''";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $books_without_covers = $stmt->fetchAll();
    
    $cover_index = 0;
    foreach ($books_without_covers as $book) {
        $cover = $available_covers[$cover_index % count($available_covers)];
        $update_sql = "UPDATE books SET cover_image = :cover WHERE id = :id";
        $update_stmt = $db->prepare($update_sql);
        $update_stmt->bindParam(':cover', $cover);
        $update_stmt->bindParam(':id', $book['id']);
        
        if ($update_stmt->execute()) {
            echo "<div class='alert alert-info'>📚 Assigned '{$cover}' to '{$book['title']}'</div>";
            $total_updated++;
        }
        $cover_index++;
    }
    
    echo "<div class='alert alert-success'><strong>Total books updated: {$total_updated}</strong></div>";
    echo "<div class='mt-3'>";
    echo "<a href='catalog.php' class='btn btn-primary'>View Catalog Now</a> ";
    echo "<a href='books/index.php' class='btn btn-secondary'>Manage Books</a>";
    echo "</div>";
} else {
    // Show current status
    $query = "SELECT COUNT(*) as total FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $total_books = $stmt->fetch()['total'];
    
    $query = "SELECT COUNT(*) as with_covers FROM books WHERE cover_image IS NOT NULL AND cover_image != ''";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $books_with_covers = $stmt->fetch()['with_covers'];
    
    echo "<div class='alert alert-info'>";
    echo "<h4>Current Status:</h4>";
    echo "<p>Total books: {$total_books}</p>";
    echo "<p>Books with covers: {$books_with_covers}</p>";
    echo "<p>Books without covers: " . ($total_books - $books_with_covers) . "</p>";
    echo "</div>";
    
    // Show sample books
    echo "<h3>Sample Books (first 10):</h3>";
    $query = "SELECT id, title, author, cover_image FROM books ORDER BY id LIMIT 10";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $sample_books = $stmt->fetchAll();
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped'>";
    echo "<thead><tr><th>ID</th><th>Title</th><th>Author</th><th>Cover Image</th></tr></thead>";
    echo "<tbody>";
    foreach ($sample_books as $book) {
        echo "<tr>";
        echo "<td>" . $book['id'] . "</td>";
        echo "<td>" . htmlspecialchars($book['title']) . "</td>";
        echo "<td>" . htmlspecialchars($book['author']) . "</td>";
        echo "<td>" . ($book['cover_image'] ? htmlspecialchars($book['cover_image']) : '<span class="text-muted">NULL</span>') . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    echo "</div>";
    
    echo "<form method='post' class='mt-4'>";
    echo "<button type='submit' name='apply_fix' class='btn btn-primary btn-lg'>Apply Quick Fix to All Books</button>";
    echo "</form>";
    
    echo "<div class='mt-3'>";
    echo "<a href='catalog.php' class='btn btn-secondary'>View Current Catalog</a>";
    echo "</div>";
}

echo "</div></body></html>";
?>
