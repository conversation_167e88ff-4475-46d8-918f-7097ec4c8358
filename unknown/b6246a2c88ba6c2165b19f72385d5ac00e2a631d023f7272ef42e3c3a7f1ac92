<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get overdue loans (both explicitly marked as overdue AND borrowed books past due)
$query = "SELECT bl.*, b.title as book_title, b.cover_image, m.first_name, m.last_name, m.email
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          JOIN members m ON bl.member_id = m.id
          WHERE (bl.status = 'overdue' OR (bl.due_date < CURDATE() AND bl.status = 'borrowed'))
          ORDER BY bl.due_date ASC";
$stmt = $db->prepare($query);
$stmt->execute();
$overdue_loans = $stmt->fetchAll();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// Calculate days overdue and fine
function calculateOverdue($due_date) {
    $due = new DateTime($due_date);
    $today = new DateTime();
    $interval = $today->diff($due);
    return $interval->days;
}

function calculateOverdueFine($days_overdue) {
    $fine_per_day = FINE_PER_DAY;
    return $days_overdue * $fine_per_day;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Overdue Books - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Overdue Books</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button id="darkModeToggle" class="btn btn-outline-secondary me-2" title="Toggle Dark Mode">
                            <i id="darkModeIcon" class="bi bi-moon"></i>
                        </button>
                        <div class="btn-group me-2">
                            <a href="index.php" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-arrow-left me-1"></i> Back to Loans
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                                <i class="bi bi-printer me-1"></i> Print Report
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo h($_SESSION['message_type']); ?> alert-dismissible fade show" role="alert">
                        <?php echo h($_SESSION['message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <div class="card mb-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i> Overdue Books</h5>
                    </div>
                    <div class="card-body">
                        <?php if (count($overdue_loans) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Book</th>
                                            <th>Cover</th>
                                            <th>Member</th>
                                            <th>Contact</th>
                                            <th>Due Date</th>
                                            <th>Days Overdue</th>
                                            <th>Fine</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($overdue_loans as $loan): ?>
                                            <?php
                                                $days_overdue = calculateOverdue($loan['due_date']);
                                                $fine = calculateOverdueFine($days_overdue);
                                            ?>
                                            <tr>
                                                <td><?php echo h($loan['book_title']); ?></td>
                                                <td class="text-center" style="width: 60px;">
                                                    <?php if (!empty($loan['cover_image'])): ?>
                                                        <?php
                                                        // Check if the cover_image is a URL or a local file
                                                        $image_src = (strpos($loan['cover_image'], 'http') === 0)
                                                            ? $loan['cover_image']
                                                            : url('uploads/covers/' . $loan['cover_image']);
                                                        ?>
                                                        <a href="#" class="cover-preview" data-bs-toggle="modal" data-bs-target="#coverModal"
                                                           data-img-src="<?php echo h($image_src); ?>"
                                                           data-title="<?php echo h($loan['book_title']); ?>">
                                                            <img src="<?php echo h($image_src); ?>" alt="<?php echo h($loan['book_title']); ?>"
                                                                 class="img-thumbnail" style="width: 40px; height: 60px; object-fit: cover;">
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary"><i class="bi bi-book"></i></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo h($loan['first_name'] . ' ' . $loan['last_name']); ?></td>
                                                <td><?php echo h($loan['email']); ?></td>
                                                <td><?php echo formatDate($loan['due_date']); ?></td>
                                                <td><span class="badge bg-danger"><?php echo $days_overdue; ?> days</span></td>
                                                <td>$<?php echo number_format($fine, 2); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="return.php?id=<?php echo h($loan['id']); ?>" class="btn btn-outline-success">
                                                            <i class="bi bi-arrow-return-left"></i> Return
                                                        </a>
                                                        <a href="send_reminder.php?id=<?php echo h($loan['id']); ?>" class="btn btn-outline-primary">
                                                            <i class="bi bi-envelope"></i> Send Reminder
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle me-2"></i> No overdue books at the moment.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Book Cover Modal -->
    <div class="modal fade" id="coverModal" tabindex="-1" aria-labelledby="coverModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="coverModalLabel">Book Cover</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalCoverImage" src="" class="img-fluid" alt="Book Cover">
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo url('../assets/js/dark-mode.js'); ?>"></script>
    <script>
        // Function to show book cover in modal
        document.addEventListener('DOMContentLoaded', function() {
            const coverLinks = document.querySelectorAll('.cover-preview');
            coverLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const imgSrc = this.getAttribute('data-img-src');
                    const title = this.getAttribute('data-title');
                    document.getElementById('modalCoverImage').src = imgSrc;
                    document.getElementById('coverModalLabel').textContent = title;
                });
            });
        });
    </script>
</body>
</html>
