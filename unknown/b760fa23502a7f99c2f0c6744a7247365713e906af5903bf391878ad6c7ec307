<?php
/**
 * Member Bulk Operations
 */
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and has appropriate role
if (!isset($_SESSION['user_id']) && !isset($_SESSION['member_id'])) {
    redirect(url('login.php'));
}

// Check if user has permission (admin or librarian)
if (isset($_SESSION['role']) && !in_array($_SESSION['role'], ['admin', 'librarian'])) {
    redirect(url('index.php'));
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize variables
$errors = [];
$success_messages = [];

// Process bulk operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    // Export Members to CSV
    if (isset($_POST['export_csv'])) {
        $status_filter = $_POST['status_filter'] ?? '';
        
        // Build query
        $query = "SELECT id, first_name, last_name, email, phone, address, membership_date, membership_status, created_at FROM members";
        if (!empty($status_filter)) {
            $query .= " WHERE membership_status = :status";
        }
        $query .= " ORDER BY last_name, first_name";
        
        $stmt = $db->prepare($query);
        if (!empty($status_filter)) {
            $stmt->bindParam(':status', $status_filter);
        }
        $stmt->execute();
        $members = $stmt->fetchAll();
        
        // Generate CSV
        $filename = 'members_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, ['ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Address', 'Membership Date', 'Status', 'Created At']);
        
        // CSV data
        foreach ($members as $member) {
            fputcsv($output, [
                $member['id'],
                $member['first_name'],
                $member['last_name'],
                $member['email'],
                $member['phone'],
                $member['address'],
                $member['membership_date'],
                $member['membership_status'],
                $member['created_at']
            ]);
        }
        
        fclose($output);
        exit;
    }
    
    // Bulk Status Update
    if (isset($_POST['bulk_status_update'])) {
        $member_ids = $_POST['member_ids'] ?? [];
        $new_status = $_POST['new_status'] ?? '';
        
        if (empty($member_ids)) {
            $errors[] = 'Please select at least one member.';
        } elseif (empty($new_status)) {
            $errors[] = 'Please select a status.';
        } else {
            try {
                $placeholders = str_repeat('?,', count($member_ids) - 1) . '?';
                $query = "UPDATE members SET membership_status = ? WHERE id IN ($placeholders)";
                
                $stmt = $db->prepare($query);
                $params = array_merge([$new_status], $member_ids);
                
                if ($stmt->execute($params)) {
                    $affected_rows = $stmt->rowCount();
                    $success_messages[] = "Successfully updated status for $affected_rows member(s).";
                    
                    // Log the activity
                    if (function_exists('logActivity')) {
                        logActivity($db, 'update', "Bulk status update: $affected_rows members to $new_status", 'member', 0);
                    }
                } else {
                    $errors[] = 'Failed to update member statuses.';
                }
            } catch (PDOException $e) {
                $errors[] = 'Database error: ' . $e->getMessage();
            }
        }
    }
    
    // Import Members from CSV
    if (isset($_POST['import_csv']) && isset($_FILES['csv_file'])) {
        $file = $_FILES['csv_file'];
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = 'File upload error.';
        } elseif ($file['type'] !== 'text/csv' && pathinfo($file['name'], PATHINFO_EXTENSION) !== 'csv') {
            $errors[] = 'Please upload a CSV file.';
        } else {
            $handle = fopen($file['tmp_name'], 'r');
            
            if ($handle !== FALSE) {
                $header = fgetcsv($handle); // Skip header row
                $imported_count = 0;
                $error_count = 0;
                
                while (($data = fgetcsv($handle)) !== FALSE) {
                    if (count($data) >= 4) { // Minimum required fields
                        $first_name = trim($data[0]);
                        $last_name = trim($data[1]);
                        $email = trim($data[2]);
                        $phone = isset($data[3]) ? trim($data[3]) : '';
                        $address = isset($data[4]) ? trim($data[4]) : '';
                        $membership_status = isset($data[5]) ? trim($data[5]) : 'active';
                        
                        // Validate required fields
                        if (!empty($first_name) && !empty($last_name) && !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                            try {
                                // Check if email already exists
                                $check_query = "SELECT COUNT(*) as count FROM members WHERE email = :email";
                                $check_stmt = $db->prepare($check_query);
                                $check_stmt->bindParam(':email', $email);
                                $check_stmt->execute();
                                
                                if ($check_stmt->fetch()['count'] == 0) {
                                    // Insert new member
                                    $insert_query = "INSERT INTO members (first_name, last_name, email, phone, address, membership_date, membership_status) 
                                                   VALUES (:first_name, :last_name, :email, :phone, :address, CURDATE(), :membership_status)";
                                    
                                    $insert_stmt = $db->prepare($insert_query);
                                    $insert_stmt->bindParam(':first_name', $first_name);
                                    $insert_stmt->bindParam(':last_name', $last_name);
                                    $insert_stmt->bindParam(':email', $email);
                                    $insert_stmt->bindParam(':phone', $phone);
                                    $insert_stmt->bindParam(':address', $address);
                                    $insert_stmt->bindParam(':membership_status', $membership_status);
                                    
                                    if ($insert_stmt->execute()) {
                                        $imported_count++;
                                    } else {
                                        $error_count++;
                                    }
                                } else {
                                    $error_count++; // Email already exists
                                }
                            } catch (PDOException $e) {
                                $error_count++;
                            }
                        } else {
                            $error_count++; // Invalid data
                        }
                    } else {
                        $error_count++; // Insufficient data
                    }
                }
                
                fclose($handle);
                
                if ($imported_count > 0) {
                    $success_messages[] = "Successfully imported $imported_count member(s).";
                }
                if ($error_count > 0) {
                    $errors[] = "$error_count row(s) could not be imported (invalid data or duplicate emails).";
                }
                
                // Log the activity
                if (function_exists('logActivity')) {
                    logActivity($db, 'create', "Bulk import: $imported_count members imported", 'member', 0);
                }
            } else {
                $errors[] = 'Could not read the CSV file.';
            }
        }
    }
}

// Get all members for bulk operations
$query = "SELECT id, first_name, last_name, email, membership_status FROM members ORDER BY last_name, first_name";
$stmt = $db->prepare($query);
$stmt->execute();
$all_members = $stmt->fetchAll();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Bulk Operations - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Member Bulk Operations</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="index.php" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="bi bi-arrow-left"></i> Back to Members
                        </a>
                        <a href="statistics.php" class="btn btn-sm btn-info">
                            <i class="bi bi-graph-up"></i> Statistics
                        </a>
                    </div>
                </div>

                <?php if (!empty($success_messages)): ?>
                    <?php foreach ($success_messages as $message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            <?php echo h($message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>

                <?php if (!empty($errors)): ?>
                    <?php foreach ($errors as $error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <?php echo h($error); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>

                <!-- Operation Cards -->
                <div class="row">
                    <!-- Export Members -->
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-download me-2"></i>Export Members
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="card-text">Export member data to CSV file for backup or external use.</p>
                                <form method="POST" action="">
                                    <div class="mb-3">
                                        <label for="status_filter" class="form-label">Filter by Status</label>
                                        <select class="form-select" id="status_filter" name="status_filter">
                                            <option value="">All Members</option>
                                            <option value="active">Active Only</option>
                                            <option value="inactive">Inactive Only</option>
                                            <option value="suspended">Suspended Only</option>
                                        </select>
                                    </div>
                                    <button type="submit" name="export_csv" class="btn btn-primary">
                                        <i class="bi bi-download me-2"></i>Export to CSV
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Import Members -->
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-upload me-2"></i>Import Members
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="card-text">Import members from CSV file. Required columns: First Name, Last Name, Email, Phone.</p>
                                <form method="POST" action="" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="csv_file" class="form-label">CSV File</label>
                                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                                        <div class="form-text">Max file size: 2MB</div>
                                    </div>
                                    <button type="submit" name="import_csv" class="btn btn-success">
                                        <i class="bi bi-upload me-2"></i>Import from CSV
                                    </button>
                                </form>
                                <hr>
                                <a href="../templates/member_import_template.csv" class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-download me-1"></i>Download Template
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Status Update -->
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-gear me-2"></i>Bulk Status Update
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="card-text">Update membership status for multiple members at once.</p>
                                <form method="POST" action="" id="bulkStatusForm">
                                    <div class="mb-3">
                                        <label for="new_status" class="form-label">New Status</label>
                                        <select class="form-select" id="new_status" name="new_status" required>
                                            <option value="">Select Status</option>
                                            <option value="active">Active</option>
                                            <option value="inactive">Inactive</option>
                                            <option value="suspended">Suspended</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <small class="text-muted">Select members from the table below, then click update.</small>
                                    </div>
                                    <button type="submit" name="bulk_status_update" class="btn btn-warning" id="bulkUpdateBtn" disabled>
                                        <i class="bi bi-gear me-2"></i>Update Selected
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Members Selection Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Select Members for Bulk Operations</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <button type="button" class="btn btn-sm btn-outline-primary" id="selectAll">Select All</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="selectNone">Select None</button>
                            <span class="ms-3 text-muted">Selected: <span id="selectedCount">0</span> members</span>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAllCheckbox" class="form-check-input">
                                        </th>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($all_members as $member): ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="member_ids[]" value="<?php echo $member['id']; ?>" 
                                                   class="form-check-input member-checkbox" form="bulkStatusForm">
                                        </td>
                                        <td><?php echo $member['id']; ?></td>
                                        <td><?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></td>
                                        <td><?php echo h($member['email']); ?></td>
                                        <td>
                                            <?php if ($member['membership_status'] === 'active'): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php elseif ($member['membership_status'] === 'inactive'): ?>
                                                <span class="badge bg-secondary">Inactive</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Suspended</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Bulk selection functionality
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const memberCheckboxes = document.querySelectorAll('.member-checkbox');
        const selectedCountSpan = document.getElementById('selectedCount');
        const bulkUpdateBtn = document.getElementById('bulkUpdateBtn');
        
        function updateSelectedCount() {
            const checkedBoxes = document.querySelectorAll('.member-checkbox:checked');
            selectedCountSpan.textContent = checkedBoxes.length;
            bulkUpdateBtn.disabled = checkedBoxes.length === 0;
        }
        
        selectAllCheckbox.addEventListener('change', function() {
            memberCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedCount();
        });
        
        memberCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedCount);
        });
        
        document.getElementById('selectAll').addEventListener('click', function() {
            memberCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            selectAllCheckbox.checked = true;
            updateSelectedCount();
        });
        
        document.getElementById('selectNone').addEventListener('click', function() {
            memberCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            selectAllCheckbox.checked = false;
            updateSelectedCount();
        });
        
        // Initial count update
        updateSelectedCount();
    </script>
</body>
</html>
