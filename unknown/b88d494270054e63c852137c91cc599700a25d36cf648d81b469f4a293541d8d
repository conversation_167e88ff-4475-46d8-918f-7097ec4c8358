<?php
/**
 * Direct User Access with Google Authentication
 *
 * This script provides direct access to a specific user account using Google authentication.
 * It can be used to quickly access a specific user's account using their email.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Get the email and user type from the URL parameters
$email = isset($_GET['email']) ? $_GET['email'] : '';
$user_type = isset($_GET['type']) ? $_GET['type'] : 'member'; // Default to member

// Validate user type
if ($user_type !== 'member' && $user_type !== 'staff' && $user_type !== 'admin' && $user_type !== 'librarian') {
    $user_type = 'member'; // Default to member if invalid type
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// If an email was provided, try to find the user
if (!empty($email)) {
    try {
        if ($user_type === 'member') {
            // Find member by email
            $query = "SELECT * FROM members WHERE email = :email AND membership_status = 'active'";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':email', $email);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch();
                
                // Set session variables for member
                $_SESSION['member_id'] = $user['id'];
                $_SESSION['member_name'] = $user['first_name'] . ' ' . $user['last_name'];
                $_SESSION['member_email'] = $user['email'];
                
                // Set a flag indicating this was a direct access
                $_SESSION['direct_access'] = true;
                $_SESSION['google_login'] = true;
                
                // Redirect to member dashboard
                redirect(url('member_dashboard.php?direct=1'));
                exit;
            }
        } else {
            // Find staff user by email and role
            $query = "SELECT * FROM users WHERE email = :email";
            if ($user_type === 'admin' || $user_type === 'librarian') {
                $query .= " AND role = :role";
            }
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(':email', $email);
            
            if ($user_type === 'admin' || $user_type === 'librarian') {
                $stmt->bindParam(':role', $user_type);
            }
            
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch();
                
                // Set session variables for staff
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['role'] = $user['role'];
                
                // Set full name for librarian
                if ($user['role'] === 'librarian') {
                    $_SESSION['full_name'] = !empty($user['full_name']) ? $user['full_name'] : $user['username'];
                }
                
                // Set a flag indicating this was a direct access
                $_SESSION['direct_access'] = true;
                
                // Redirect based on role
                if ($user['role'] === 'admin') {
                    redirect(url('admin/dashboard.php?direct=1'));
                } elseif ($user['role'] === 'librarian') {
                    redirect(url('librarian/dashboard.php?direct=1'));
                } else {
                    redirect(url('index.php?direct=1'));
                }
                exit;
            }
        }
        
        // If we get here, user was not found
        $_SESSION['error'] = 'No ' . ucfirst($user_type) . ' account found with the email: ' . $email;
        redirect(url('login.php'));
        exit;
    } catch (Exception $e) {
        // Set error message
        $_SESSION['error'] = 'An error occurred while accessing the account. Please try again.';
        error_log('Direct User Access Error: ' . $e->getMessage());
        
        // Redirect to login page
        redirect(url('login.php'));
        exit;
    }
} else {
    // No email provided, redirect to login page with error
    $_SESSION['error'] = 'No email was provided. Please try again.';
    redirect(url('login.php'));
    exit;
}
?>
