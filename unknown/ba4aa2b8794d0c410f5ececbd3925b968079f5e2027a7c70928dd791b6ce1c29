<?php
/**
 * Simple Settings Page (No Authentication)
 */
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Settings - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body { background-color: #f8f9fa; }
        .settings-card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0"><i class="bi bi-gear me-2"></i>System Settings</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle me-2"></i>
                            <strong>Success!</strong> The settings page is working properly.
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card settings-card">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">General Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <form>
                                            <div class="mb-3">
                                                <label for="library_name" class="form-label">Library Name</label>
                                                <input type="text" class="form-control" id="library_name" value="Library Management System">
                                            </div>
                                            <div class="mb-3">
                                                <label for="library_email" class="form-label">Library Email</label>
                                                <input type="email" class="form-control" id="library_email" value="<EMAIL>">
                                            </div>
                                            <div class="mb-3">
                                                <label for="loan_period" class="form-label">Loan Period (days)</label>
                                                <input type="number" class="form-control" id="loan_period" value="14">
                                            </div>
                                            <button type="button" class="btn btn-primary">
                                                <i class="bi bi-check-lg me-2"></i>Save Settings
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card settings-card">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">System Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <form>
                                            <div class="mb-3">
                                                <label for="items_per_page" class="form-label">Items Per Page</label>
                                                <select class="form-select" id="items_per_page">
                                                    <option value="10" selected>10</option>
                                                    <option value="25">25</option>
                                                    <option value="50">50</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="enable_notifications" checked>
                                                    <label class="form-check-label" for="enable_notifications">
                                                        Enable Notifications
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="enable_email_reminders" checked>
                                                    <label class="form-check-label" for="enable_email_reminders">
                                                        Enable Email Reminders
                                                    </label>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-success">
                                                <i class="bi bi-check-lg me-2"></i>Save Settings
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle me-2"></i>File Information:</h6>
                                <ul class="mb-0">
                                    <li><strong>File:</strong> <?php echo __FILE__; ?></li>
                                    <li><strong>Directory:</strong> <?php echo __DIR__; ?></li>
                                    <li><strong>URL:</strong> <?php echo $_SERVER['REQUEST_URI']; ?></li>
                                    <li><strong>Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <h6>Navigation Links:</h6>
                            <div class="btn-group" role="group">
                                <a href="test.php" class="btn btn-outline-primary">Test Page</a>
                                <a href="users.php" class="btn btn-outline-secondary">User Management</a>
                                <a href="dashboard.php" class="btn btn-outline-success">Dashboard</a>
                                <a href="../login.php" class="btn btn-outline-info">Login</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
