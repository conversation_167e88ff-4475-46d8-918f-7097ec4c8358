<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Fix Image Display</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h2>Fix Book Cover Image Display</h2>";

if (isset($_POST['fix_images'])) {
    echo "<h3>Applying Image Fix...</h3>";
    
    // Step 1: Update all books with proper cover images
    $book_cover_mapping = [
        '1984' => '1984_cover.jpg',
        'A Game of Thrones' => '1747724281_AGameOfThrones.jpg',
        'Good to Great' => '1747724303_Good to Great.jpg',
        'Pride and Prejudice' => '1747724335_Pride and Prejudice.jpg',
        '<PERSON>' => '1747725415_steve-jobs-9781451648553_hr.jpg',
        'The Alchemist' => '1747725431_the-alchemist-a-graphic-novel.jpg',
        'The Catcher in the Rye' => '1747725459_Book-Review-The-Catcher-in-the-Rye-by-J-D-Salinger.jpg',
        'The Great Gatsby' => '1747725503_the-great-gatsby-and-other-works-9781645173519_hr.jpg',
        'The Martian' => '1747725673_The_Martian_(Weir_novel).jpg',
        'To Kill a Mockingbird' => '1747725698_fuck-640x996.jpg',
        'Harry Potter' => '1747725734_71qFBdNS+dL.jpg',
        'Sapiens' => '1747725751_9781784870799.jpg'
    ];
    
    $updated_count = 0;
    
    // Update specific books first
    foreach ($book_cover_mapping as $title_pattern => $cover_file) {
        $query = "UPDATE books SET cover_image = :cover_image WHERE title LIKE :title";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':cover_image', $cover_file);
        $like_title = '%' . $title_pattern . '%';
        $stmt->bindParam(':title', $like_title);
        
        if ($stmt->execute()) {
            $affected = $stmt->rowCount();
            if ($affected > 0) {
                echo "<div class='alert alert-success'>✅ Updated {$affected} books matching '{$title_pattern}' with cover: {$cover_file}</div>";
                $updated_count += $affected;
            }
        }
    }
    
    // Get all available covers
    $available_covers = array_values($book_cover_mapping);
    
    // Update remaining books without covers
    $query = "SELECT id, title FROM books WHERE cover_image IS NULL OR cover_image = ''";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $books_without_covers = $stmt->fetchAll();
    
    $cover_index = 0;
    foreach ($books_without_covers as $book) {
        $cover = $available_covers[$cover_index % count($available_covers)];
        $update_query = "UPDATE books SET cover_image = :cover WHERE id = :id";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':cover', $cover);
        $update_stmt->bindParam(':id', $book['id']);
        
        if ($update_stmt->execute()) {
            echo "<div class='alert alert-info'>📚 Assigned '{$cover}' to '{$book['title']}'</div>";
            $updated_count++;
        }
        $cover_index++;
    }
    
    echo "<div class='alert alert-success'><strong>Total books updated: {$updated_count}</strong></div>";
    
    // Test the images
    echo "<h3>Testing Image Display</h3>";
    $query = "SELECT * FROM books WHERE cover_image IS NOT NULL LIMIT 6";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $test_books = $stmt->fetchAll();
    
    echo "<div class='row'>";
    foreach ($test_books as $book) {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card'>";
        
        // Test different image paths
        $image_paths = [
            'uploads/covers/' . $book['cover_image'],
            './uploads/covers/' . $book['cover_image'],
            '/Library/lms/uploads/covers/' . $book['cover_image']
        ];
        
        $working_path = null;
        foreach ($image_paths as $path) {
            if (file_exists($path)) {
                $working_path = $path;
                break;
            }
        }
        
        if ($working_path) {
            echo "<img src='" . htmlspecialchars($working_path) . "' class='card-img-top' style='height: 200px; object-fit: cover;' alt='" . htmlspecialchars($book['title']) . "'>";
        } else {
            echo "<div class='card-img-top d-flex align-items-center justify-content-center bg-light' style='height: 200px;'>";
            echo "<span class='text-muted'>Image not found</span>";
            echo "</div>";
        }
        
        echo "<div class='card-body'>";
        echo "<h6 class='card-title'>" . htmlspecialchars($book['title']) . "</h6>";
        echo "<p class='card-text'><small>" . htmlspecialchars($book['author']) . "</small></p>";
        echo "<p class='card-text'><small class='text-muted'>Cover: " . htmlspecialchars($book['cover_image']) . "</small></p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='mt-4'>";
    echo "<a href='catalog.php' class='btn btn-primary btn-lg'>View Catalog Now</a>";
    echo "</div>";
    
} else {
    // Show current status
    $query = "SELECT COUNT(*) as total FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $total_books = $stmt->fetch()['total'];
    
    $query = "SELECT COUNT(*) as with_covers FROM books WHERE cover_image IS NOT NULL AND cover_image != ''";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $books_with_covers = $stmt->fetch()['with_covers'];
    
    echo "<div class='alert alert-info'>";
    echo "<h4>Current Status:</h4>";
    echo "<p>Total books: {$total_books}</p>";
    echo "<p>Books with covers: {$books_with_covers}</p>";
    echo "<p>Books without covers: " . ($total_books - $books_with_covers) . "</p>";
    echo "</div>";
    
    echo "<h3>Image Path Testing</h3>";
    $test_image = '1984_cover.jpg';
    $test_paths = [
        'uploads/covers/' . $test_image,
        './uploads/covers/' . $test_image,
        '/Library/lms/uploads/covers/' . $test_image
    ];
    
    echo "<div class='row'>";
    foreach ($test_paths as $i => $path) {
        $exists = file_exists($path);
        echo "<div class='col-md-4'>";
        echo "<div class='card mb-3'>";
        echo "<div class='card-header'>";
        echo "<small>Path " . ($i + 1) . ": " . htmlspecialchars($path) . "</small><br>";
        echo "<small>Exists: " . ($exists ? '✅ Yes' : '❌ No') . "</small>";
        echo "</div>";
        
        if ($exists) {
            echo "<img src='" . htmlspecialchars($path) . "' class='card-img-top' style='height: 150px; object-fit: cover;' alt='Test'>";
        } else {
            echo "<div class='card-img-top d-flex align-items-center justify-content-center bg-light' style='height: 150px;'>";
            echo "<span class='text-muted'>Not found</span>";
            echo "</div>";
        }
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<form method='post' class='mt-4'>";
    echo "<button type='submit' name='fix_images' class='btn btn-primary btn-lg'>Fix All Book Cover Images</button>";
    echo "</form>";
}

echo "</div></body></html>";
?>
