<?php
/**
 * Dashboard Widgets Templates
 * Contains HTML templates for the enhanced dashboard widgets
 */

/**
 * Renders the task management widget
 */
function renderTaskManagementWidget() {
    ?>
    <div id="taskManagementWidget" class="card dashboard-widget">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-check2-square me-2"></i>My Tasks</h5>
            <div>
                <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addTaskModal">
                    <i class="bi bi-plus-lg me-1"></i>New Task
                </button>
                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#taskManagementCollapse" aria-expanded="true">
                    <i class="bi bi-chevron-down"></i>
                </button>
            </div>
        </div>
        <div class="collapse show" id="taskManagementCollapse">
            <div class="card-body">
                <ul class="nav nav-tabs mb-3" id="taskTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="pending-tasks-tab" data-bs-toggle="tab" data-bs-target="#pending-tasks" type="button" role="tab" aria-controls="pending-tasks" aria-selected="true">Pending</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="completed-tasks-tab" data-bs-toggle="tab" data-bs-target="#completed-tasks" type="button" role="tab" aria-controls="completed-tasks" aria-selected="false">Completed</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="all-tasks-tab" data-bs-toggle="tab" data-bs-target="#all-tasks" type="button" role="tab" aria-controls="all-tasks" aria-selected="false">All</button>
                    </li>
                </ul>
                <div class="tab-content" id="taskTabsContent">
                    <div class="tab-pane fade show active" id="pending-tasks" role="tabpanel" aria-labelledby="pending-tasks-tab">
                        <div class="task-list">
                            <!-- Sample tasks - would be populated from database -->
                            <div class="task-item priority-high">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="" id="task1">
                                            <label class="form-check-label task-title" for="task1">
                                                Process new book shipment
                                            </label>
                                        </div>
                                        <div class="task-due-date overdue">
                                            <i class="bi bi-calendar-event me-1"></i>Due: Yesterday
                                        </div>
                                    </div>
                                    <div class="task-actions">
                                        <button class="btn btn-sm btn-outline-secondary me-1" title="Edit Task">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" title="Delete Task">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="task-item priority-medium">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="" id="task2">
                                            <label class="form-check-label task-title" for="task2">
                                                Contact members with overdue books
                                            </label>
                                        </div>
                                        <div class="task-due-date">
                                            <i class="bi bi-calendar-event me-1"></i>Due: Today
                                        </div>
                                    </div>
                                    <div class="task-actions">
                                        <button class="btn btn-sm btn-outline-secondary me-1" title="Edit Task">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" title="Delete Task">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="task-item priority-low">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="" id="task3">
                                            <label class="form-check-label task-title" for="task3">
                                                Update reading recommendations list
                                            </label>
                                        </div>
                                        <div class="task-due-date">
                                            <i class="bi bi-calendar-event me-1"></i>Due: Next week
                                        </div>
                                    </div>
                                    <div class="task-actions">
                                        <button class="btn btn-sm btn-outline-secondary me-1" title="Edit Task">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" title="Delete Task">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="completed-tasks" role="tabpanel" aria-labelledby="completed-tasks-tab">
                        <div class="task-list">
                            <div class="task-item completed">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="" id="task4" checked>
                                            <label class="form-check-label task-title" for="task4">
                                                Organize summer reading program
                                            </label>
                                        </div>
                                        <div class="task-due-date">
                                            <i class="bi bi-calendar-check me-1"></i>Completed: Yesterday
                                        </div>
                                    </div>
                                    <div class="task-actions">
                                        <button class="btn btn-sm btn-outline-secondary me-1" title="Edit Task">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" title="Delete Task">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="all-tasks" role="tabpanel" aria-labelledby="all-tasks-tab">
                        <!-- Combined tasks would be shown here -->
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>View all your tasks here
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer text-end">
                <a href="#" class="btn btn-sm btn-outline-primary">Manage All Tasks</a>
            </div>
        </div>
    </div>
    <?php
}

/**
 * Renders the book acquisition suggestions widget
 */
function renderAcquisitionSuggestionsWidget() {
    ?>
    <div id="acquisitionSuggestionsWidget" class="card dashboard-widget">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-lightbulb me-2"></i>Acquisition Suggestions</h5>
            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#acquisitionSuggestionsCollapse" aria-expanded="true">
                <i class="bi bi-chevron-down"></i>
            </button>
        </div>
        <div class="collapse show" id="acquisitionSuggestionsCollapse">
            <div class="card-body">
                <div class="acquisition-item">
                    <h6 class="mb-1">The Midnight Library</h6>
                    <p class="mb-1">by Matt Haig</p>
                    <div class="acquisition-reason">
                        <i class="bi bi-graph-up me-1"></i>High demand: 12 member requests
                    </div>
                    <div class="acquisition-stats">
                        <div class="acquisition-stat">
                            <i class="bi bi-search"></i>15 searches
                        </div>
                        <div class="acquisition-stat">
                            <i class="bi bi-star-fill"></i>4.5/5 rating
                        </div>
                    </div>
                </div>
                <div class="acquisition-item">
                    <h6 class="mb-1">Project Hail Mary</h6>
                    <p class="mb-1">by Andy Weir</p>
                    <div class="acquisition-reason">
                        <i class="bi bi-people me-1"></i>Waitlist: 8 members
                    </div>
                    <div class="acquisition-stats">
                        <div class="acquisition-stat">
                            <i class="bi bi-search"></i>10 searches
                        </div>
                        <div class="acquisition-stat">
                            <i class="bi bi-star-fill"></i>4.8/5 rating
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer text-end">
                <a href="#" class="btn btn-sm btn-outline-primary">View All Suggestions</a>
            </div>
        </div>
    </div>
    <?php
}

/**
 * Renders the member communication center widget
 */
function renderMemberCommunicationWidget() {
    ?>
    <div id="memberCommunicationWidget" class="card dashboard-widget">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-chat-dots me-2"></i>Member Communication</h5>
            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#memberCommunicationCollapse" aria-expanded="true">
                <i class="bi bi-chevron-down"></i>
            </button>
        </div>
        <div class="collapse show" id="memberCommunicationCollapse">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>Message Templates</h6>
                        <div class="communication-templates">
                            <div class="template-item selected">
                                <strong>Overdue Notice</strong>
                                <div class="small text-muted">For books past due date</div>
                            </div>
                            <div class="template-item">
                                <strong>Reservation Ready</strong>
                                <div class="small text-muted">Book is ready for pickup</div>
                            </div>
                            <div class="template-item">
                                <strong>New Arrivals</strong>
                                <div class="small text-muted">Notify about new books</div>
                            </div>
                            <div class="template-item">
                                <strong>Membership Renewal</strong>
                                <div class="small text-muted">Membership expiring soon</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <h6>Compose Message</h6>
                        <form>
                            <div class="mb-3">
                                <label for="messageRecipients" class="form-label">Recipients</label>
                                <select class="form-select" id="messageRecipients" multiple>
                                    <option value="overdue">Members with overdue books</option>
                                    <option value="reservations">Members with ready reservations</option>
                                    <option value="inactive">Inactive members (3+ months)</option>
                                    <option value="all">All members</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="messageSubject" class="form-label">Subject</label>
                                <input type="text" class="form-control" id="messageSubject" value="Overdue Book Notice">
                            </div>
                            <div class="mb-3">
                                <label for="messageBody" class="form-label">Message</label>
                                <textarea class="form-control" id="messageBody" rows="5">Dear {member_name},

This is a reminder that the following book(s) are overdue:
{book_list}

Please return them at your earliest convenience or renew online.

Thank you,
Library Staff</textarea>
                            </div>
                            <div class="text-end">
                                <button type="button" class="btn btn-outline-secondary me-2">Save Draft</button>
                                <button type="button" class="btn btn-primary">Send Message</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}
