<?php
/**
 * Simple Dashboard Balance Fix
 * Direct approach to reduce members and balance loans
 */

require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<h1>🔧 Simple Dashboard Balance Fix</h1>";

// Check current state first
$check_query = "SELECT COUNT(*) as count FROM members";
$check_stmt = $db->prepare($check_query);
$check_stmt->execute();
$current_members = $check_stmt->fetch()['count'];

echo "<p><strong>Current Members: {$current_members}</strong></p>";

if ($current_members <= 1200) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Members already balanced!</h3>";
    echo "<p>Current count ({$current_members}) is already good.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>⚠️ Need to reduce from {$current_members} to ~1200</h3>";
    echo "</div>";
    
    try {
        // Step 1: Delete excess members (keep first 1200)
        echo "<h3>Step 1: Deleting excess members...</h3>";
        
        $delete_members = "DELETE FROM members WHERE id > (
            SELECT id FROM (
                SELECT id FROM members ORDER BY id LIMIT 1200
            ) AS temp ORDER BY id DESC LIMIT 1
        )";
        
        $stmt = $db->prepare($delete_members);
        $stmt->execute();
        $deleted = $stmt->rowCount();
        
        echo "<p>✅ Deleted {$deleted} excess members</p>";
        
        // Step 2: Delete orphaned loans
        echo "<h3>Step 2: Cleaning up orphaned loans...</h3>";
        
        $delete_orphaned = "DELETE FROM book_loans WHERE member_id NOT IN (SELECT id FROM members)";
        $stmt2 = $db->prepare($delete_orphaned);
        $stmt2->execute();
        $deleted_loans = $stmt2->rowCount();
        
        echo "<p>✅ Deleted {$deleted_loans} orphaned loans</p>";
        
        // Step 3: Reduce active loans to reasonable number
        echo "<h3>Step 3: Reducing active loans...</h3>";
        
        // Keep only some active loans, mark others as returned
        $reduce_active = "UPDATE book_loans 
                         SET status = 'returned', 
                             return_date = DATE_SUB(CURDATE(), INTERVAL RAND() * 30 DAY)
                         WHERE status = 'borrowed' 
                         AND id NOT IN (
                             SELECT id FROM (
                                 SELECT id FROM book_loans 
                                 WHERE status = 'borrowed' 
                                 ORDER BY RAND() 
                                 LIMIT 300
                             ) AS temp
                         )";
        
        $stmt3 = $db->prepare($reduce_active);
        $stmt3->execute();
        $reduced_active = $stmt3->rowCount();
        
        echo "<p>✅ Converted {$reduced_active} active loans to returned</p>";
        
        // Step 4: Update overdue status
        echo "<h3>Step 4: Updating overdue status...</h3>";
        
        $update_overdue = "UPDATE book_loans 
                          SET status = 'overdue',
                              fine = DATEDIFF(CURDATE(), due_date) * 1.00
                          WHERE status = 'borrowed' 
                          AND due_date < CURDATE()";
        
        $stmt4 = $db->prepare($update_overdue);
        $stmt4->execute();
        $overdue_updated = $stmt4->rowCount();
        
        echo "<p>✅ Updated {$overdue_updated} loans to overdue</p>";
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>✅ Balance Complete!</h3>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ Error: " . $e->getMessage() . "</h3>";
        echo "</div>";
    }
}

// Show final stats
echo "<h2>📊 Final Statistics</h2>";

$final_queries = [
    'members' => "SELECT COUNT(*) as count FROM members",
    'active_loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed'",
    'overdue_loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'overdue'",
    'returned_loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'returned'",
    'total_loans' => "SELECT COUNT(*) as count FROM book_loans"
];

echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px;'>";
echo "<h3>Current Dashboard Numbers:</h3>";

foreach ($final_queries as $label => $query) {
    $stmt = $db->prepare($query);
    $stmt->execute();
    $count = $stmt->fetch()['count'];
    
    $icon = '';
    switch($label) {
        case 'members': $icon = '👥'; break;
        case 'active_loans': $icon = '✅'; break;
        case 'overdue_loans': $icon = '⚠️'; break;
        case 'returned_loans': $icon = '📚'; break;
        case 'total_loans': $icon = '📋'; break;
    }
    
    echo "<p><strong>{$icon} " . ucwords(str_replace('_', ' ', $label)) . ":</strong> {$count}</p>";
}

echo "</div>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🎉 Dashboard Should Now Be Balanced!</h3>";
echo "<p>Refresh your admin dashboard to see the updated numbers.</p>";
echo "<p><a href='admin/dashboard.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Dashboard</a></p>";
echo "</div>";
?>

<script>
// Auto-refresh after 3 seconds to show final results
setTimeout(function() {
    window.location.reload();
}, 3000);
</script>
