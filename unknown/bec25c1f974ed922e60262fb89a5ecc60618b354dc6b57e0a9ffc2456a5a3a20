<?php
/**
 * Update Members Table Script
 * This script adds a password field to the members table
 */

// Database connection parameters
$host = 'localhost';
$dbname = 'lms_db';
$username = 'root';
$password = '';

try {
    // Connect to the database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Update Members Table</h2>";
    
    // Check if password column already exists
    $stmt = $pdo->query("SHOW COLUMNS FROM members LIKE 'password'");
    if ($stmt->rowCount() > 0) {
        echo "<p>Password column already exists in members table.</p>";
    } else {
        // Add password column to members table
        $pdo->exec("ALTER TABLE members ADD COLUMN password VARCHAR(255) NOT NULL DEFAULT ''");
        echo "<p>✅ Successfully added password column to members table.</p>";
    }
    
    // Update existing members with default password (hashed version of their email)
    $stmt = $pdo->query("SELECT id, email FROM members WHERE password = ''");
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $updated_count = 0;
    foreach ($members as $member) {
        // Hash the email as a default password
        $default_password = password_hash($member['email'], PASSWORD_DEFAULT);
        
        // Update the member's password
        $update_stmt = $pdo->prepare("UPDATE members SET password = :password WHERE id = :id");
        $update_stmt->bindParam(':password', $default_password);
        $update_stmt->bindParam(':id', $member['id']);
        $update_stmt->execute();
        
        $updated_count++;
    }
    
    echo "<p>✅ Updated $updated_count existing members with default passwords.</p>";
    echo "<p>Default password for each member is their email address.</p>";
    
    echo "<p>You can now log in as a member using their email and their email as password.</p>";
    echo "<p>For example, for member with email '<EMAIL>', the password is '<EMAIL>'.</p>";
    
    echo "<p><a href='login.php'>Go to Login Page</a></p>";
    
} catch (PDOException $e) {
    echo "<h2>Error!</h2>";
    echo "<p>An error occurred: " . $e->getMessage() . "</p>";
}
?>
