<?php
session_start();

// Include required files with error handling
try {
    require_once '../config/database.php';
    require_once '../config/config.php';
    require_once '../includes/functions.php';
} catch (Exception $e) {
    die('Error loading required files: ' . $e->getMessage());
}

// Check if user is logged in and has admin/librarian privileges
if (!isLoggedIn()) {
    redirect('../login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get filter parameters
$status = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$date_from = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : '';
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Set default date range (last 30 days)
if (empty($date_from)) {
    $date_from = date('Y-m-d', strtotime('-30 days'));
}
if (empty($date_to)) {
    $date_to = date('Y-m-d');
}

// Build query
$query = "SELECT bl.*, 
          b.title as book_title, b.isbn, b.author,
          m.first_name, m.last_name, m.email, m.phone
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          JOIN members m ON bl.member_id = m.id
          WHERE 1=1";

$params = [];

if (!empty($status)) {
    $query .= " AND bl.status = :status";
    $params[':status'] = $status;
}

if (!empty($date_from)) {
    $query .= " AND bl.issue_date >= :date_from";
    $params[':date_from'] = $date_from;
}

if (!empty($date_to)) {
    $query .= " AND bl.issue_date <= :date_to";
    $params[':date_to'] = $date_to;
}

if (!empty($search)) {
    $query .= " AND (b.title LIKE :search OR b.author LIKE :search OR m.first_name LIKE :search OR m.last_name LIKE :search OR m.email LIKE :search)";
    $params[':search'] = '%' . $search . '%';
}

$query .= " ORDER BY bl.issue_date DESC";

$stmt = $db->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$loans = $stmt->fetchAll();

// Calculate statistics
$total_loans = count($loans);
$borrowed_count = 0;
$returned_count = 0;
$overdue_count = 0;
$total_fines = 0;

foreach ($loans as $loan) {
    switch ($loan['status']) {
        case 'borrowed':
            $borrowed_count++;
            break;
        case 'returned':
            $returned_count++;
            break;
        case 'overdue':
            $overdue_count++;
            break;
    }
    if ($loan['fine'] > 0) {
        $total_fines += $loan['fine'];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loans Report - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .stats-card {
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .print-hide {
            display: block;
        }
        @media print {
            .print-hide {
                display: none !important;
            }
            .container-fluid {
                margin: 0;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-journal-arrow-up me-2"></i>Loans Report</h1>
                    <div class="btn-toolbar mb-2 mb-md-0 print-hide">
                        <button onclick="window.print()" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="bi bi-printer"></i> Print Report
                        </button>
                        <a href="index.php" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> Back to Reports
                        </a>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-journal-text fs-1 text-primary mb-2"></i>
                                <h5 class="card-title">Total Loans</h5>
                                <h3 class="text-primary"><?php echo $total_loans; ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-arrow-up-circle fs-1 text-info mb-2"></i>
                                <h5 class="card-title">Currently Borrowed</h5>
                                <h3 class="text-info"><?php echo $borrowed_count; ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-check-circle fs-1 text-success mb-2"></i>
                                <h5 class="card-title">Returned</h5>
                                <h3 class="text-success"><?php echo $returned_count; ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-exclamation-triangle fs-1 text-danger mb-2"></i>
                                <h5 class="card-title">Overdue</h5>
                                <h3 class="text-danger"><?php echo $overdue_count; ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4 print-hide">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="Book, Author, or Member">
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="borrowed" <?php echo $status === 'borrowed' ? 'selected' : ''; ?>>Borrowed</option>
                                    <option value="returned" <?php echo $status === 'returned' ? 'selected' : ''; ?>>Returned</option>
                                    <option value="overdue" <?php echo $status === 'overdue' ? 'selected' : ''; ?>>Overdue</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">From Date</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="<?php echo htmlspecialchars($date_from); ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">To Date</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="<?php echo htmlspecialchars($date_to); ?>">
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="bi bi-search"></i> Filter
                                </button>
                                <a href="loans.php" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise"></i> Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Loans Table -->
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>Loan Records
                            <small class="text-muted">(<?php echo formatDate($date_from); ?> to <?php echo formatDate($date_to); ?>)</small>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($loans)): ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>No loan records found for the selected criteria.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Book</th>
                                            <th>Member</th>
                                            <th>Issue Date</th>
                                            <th>Due Date</th>
                                            <th>Return Date</th>
                                            <th>Status</th>
                                            <th>Fine</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($loans as $loan): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($loan['book_title']); ?></strong>
                                                    <br><small class="text-muted">by <?php echo htmlspecialchars($loan['author']); ?></small>
                                                    <?php if (!empty($loan['isbn'])): ?>
                                                        <br><small class="text-muted">ISBN: <?php echo htmlspecialchars($loan['isbn']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($loan['first_name'] . ' ' . $loan['last_name']); ?></strong>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($loan['email']); ?></small>
                                                </td>
                                                <td><?php echo formatDate($loan['issue_date']); ?></td>
                                                <td>
                                                    <?php echo formatDate($loan['due_date']); ?>
                                                    <?php if ($loan['status'] === 'borrowed' && strtotime($loan['due_date']) < time()): ?>
                                                        <br><small class="text-danger">Past Due</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (!empty($loan['return_date'])): ?>
                                                        <?php echo formatDate($loan['return_date']); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">Not returned</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_class = '';
                                                    $status_text = '';
                                                    switch ($loan['status']) {
                                                        case 'borrowed':
                                                            $status_class = 'bg-info';
                                                            $status_text = 'Borrowed';
                                                            break;
                                                        case 'returned':
                                                            $status_class = 'bg-success';
                                                            $status_text = 'Returned';
                                                            break;
                                                        case 'overdue':
                                                            $status_class = 'bg-danger';
                                                            $status_text = 'Overdue';
                                                            break;
                                                        default:
                                                            $status_class = 'bg-secondary';
                                                            $status_text = ucfirst($loan['status']);
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                                </td>
                                                <td>
                                                    <?php if ($loan['fine'] > 0): ?>
                                                        <span class="text-danger">$<?php echo number_format($loan['fine'], 2); ?></span>
                                                    <?php else: ?>
                                                        <span class="text-muted">$0.00</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Summary -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title">Summary</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><strong>Total Loans:</strong> <?php echo $total_loans; ?></li>
                                    <li><strong>Currently Borrowed:</strong> <?php echo $borrowed_count; ?></li>
                                    <li><strong>Returned:</strong> <?php echo $returned_count; ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><strong>Overdue:</strong> <?php echo $overdue_count; ?></li>
                                    <li><strong>Total Fines:</strong> $<?php echo number_format($total_fines, 2); ?></li>
                                    <li><strong>Return Rate:</strong> <?php echo $total_loans > 0 ? round(($returned_count / $total_loans) * 100, 1) : 0; ?>%</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Report Footer -->
                <div class="mt-4 text-center text-muted">
                    <small>
                        Report generated on <?php echo date('F j, Y \a\t g:i A'); ?> | 
                        Total records: <?php echo count($loans); ?>
                    </small>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
