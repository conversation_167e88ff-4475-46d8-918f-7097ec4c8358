<?php
/**
 * Create Admin User Script
 * This script creates a default admin user for the LMS system
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';
require_once 'config/config.php';

echo "<h1>Create Admin User</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    .form-container { max-width: 500px; margin: 20px 0; }
    .form-group { margin-bottom: 15px; }
    label { display: block; margin-bottom: 5px; font-weight: bold; }
    input[type='text'], input[type='email'], input[type='password'] { 
        width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; 
    }
    button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
    button:hover { background: #0056b3; }
</style>";

try {
    $database = new Database();
    $db = $database->getConnection();
    echo "<p class='success'>✓ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p class='error'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username']);
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validate input
    if (empty($username) || empty($email) || empty($password)) {
        echo "<p class='error'>All fields are required</p>";
    } elseif ($password !== $confirm_password) {
        echo "<p class='error'>Passwords do not match</p>";
    } elseif (strlen($password) < 6) {
        echo "<p class='error'>Password must be at least 6 characters long</p>";
    } else {
        try {
            // Check if user already exists
            $check_query = "SELECT id FROM users WHERE username = :username OR email = :email";
            $check_stmt = $db->prepare($check_query);
            $check_stmt->bindParam(':username', $username);
            $check_stmt->bindParam(':email', $email);
            $check_stmt->execute();
            
            if ($check_stmt->rowCount() > 0) {
                echo "<p class='error'>User with this username or email already exists</p>";
            } else {
                // Hash password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // Insert new admin user
                $insert_query = "INSERT INTO users (username, email, password, role) VALUES (:username, :email, :password, 'admin')";
                $insert_stmt = $db->prepare($insert_query);
                $insert_stmt->bindParam(':username', $username);
                $insert_stmt->bindParam(':email', $email);
                $insert_stmt->bindParam(':password', $hashed_password);
                
                if ($insert_stmt->execute()) {
                    echo "<p class='success'>✓ Admin user created successfully!</p>";
                    echo "<p class='info'>Username: $username</p>";
                    echo "<p class='info'>Email: $email</p>";
                    echo "<p class='info'>You can now <a href='login.php'>login</a> with these credentials.</p>";
                } else {
                    echo "<p class='error'>Failed to create admin user</p>";
                }
            }
        } catch (Exception $e) {
            echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
        }
    }
}

// Check if admin users exist
try {
    $query = "SELECT COUNT(*) as count FROM users WHERE role = 'admin'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch();
    $admin_count = $result['count'];
    
    echo "<p class='info'>Current admin users: $admin_count</p>";
    
    if ($admin_count > 0) {
        echo "<p class='success'>Admin users already exist in the system.</p>";
        echo "<p><a href='login.php'>Go to Login Page</a></p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>Error checking admin users: " . $e->getMessage() . "</p>";
}
?>

<div class="form-container">
    <h2>Create New Admin User</h2>
    <form method="post" action="">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" required>
        </div>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
        </div>
        
        <div class="form-group">
            <label for="confirm_password">Confirm Password:</label>
            <input type="password" id="confirm_password" name="confirm_password" required>
        </div>
        
        <button type="submit">Create Admin User</button>
    </form>
</div>

<div style="margin-top: 30px;">
    <h3>Quick Links</h3>
    <p><a href="index.php">Home Page</a></p>
    <p><a href="login.php">Login Page</a></p>
    <p><a href="diagnostic.php">System Diagnostic</a></p>
    <p><a href="setup.php">Database Setup</a></p>
</div>
