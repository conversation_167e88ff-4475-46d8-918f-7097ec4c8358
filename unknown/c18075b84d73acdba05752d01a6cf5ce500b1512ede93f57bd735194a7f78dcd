<?php
/**
 * Database Health Check
 * Comprehensive database structure and data verification
 */

require_once 'config/database.php';

// Connect to database
try {
    $database = new Database();
    $db = $database->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function checkTable($db, $table_name, $required_columns = []) {
    try {
        // Check if table exists
        $stmt = $db->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table_name]);
        
        if ($stmt->rowCount() == 0) {
            return ['status' => 'missing', 'message' => "Table '$table_name' does not exist"];
        }
        
        // Get table structure
        $stmt = $db->prepare("DESCRIBE $table_name");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Check required columns
        $missing_columns = array_diff($required_columns, $columns);
        
        if (!empty($missing_columns)) {
            return ['status' => 'incomplete', 'message' => "Table '$table_name' missing columns: " . implode(', ', $missing_columns)];
        }
        
        // Get row count
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM $table_name");
        $stmt->execute();
        $count = $stmt->fetch()['count'];
        
        return ['status' => 'ok', 'message' => "Table '$table_name' is complete", 'count' => $count, 'columns' => $columns];
        
    } catch (Exception $e) {
        return ['status' => 'error', 'message' => "Error checking table '$table_name': " . $e->getMessage()];
    }
}

// Define required tables and their essential columns
$required_tables = [
    'users' => ['id', 'username', 'password', 'email', 'role', 'status'],
    'members' => ['id', 'first_name', 'last_name', 'email', 'password', 'membership_status'],
    'books' => ['id', 'title', 'author', 'isbn', 'quantity', 'available_quantity'],
    'book_loans' => ['id', 'book_id', 'member_id', 'issue_date', 'due_date', 'status'],
    'book_reservations' => ['id', 'book_id', 'member_id', 'reservation_date', 'status']
];

$table_results = [];
foreach ($required_tables as $table => $columns) {
    $table_results[$table] = checkTable($db, $table, $columns);
}

// Check for optional tables
$optional_tables = ['activity_log', 'notifications', 'email_logs'];
foreach ($optional_tables as $table) {
    $table_results[$table] = checkTable($db, $table);
}

// Database size calculation
try {
    $stmt = $db->prepare("SELECT SUM(data_length + index_length) AS size FROM information_schema.TABLES WHERE table_schema = DATABASE()");
    $stmt->execute();
    $db_size = $stmt->fetch()['size'] ?? 0;
} catch (Exception $e) {
    $db_size = 0;
}

function formatBytes($bytes) {
    if ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Health Check - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .status-ok { color: #28a745; }
        .status-missing { color: #dc3545; }
        .status-incomplete { color: #ffc107; }
        .status-error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1><i class="bi bi-database-check me-2"></i>Database Health Check</h1>
        <p class="text-muted">Comprehensive database structure and data verification</p>
        
        <!-- Database Overview -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="bi bi-info-circle me-2"></i>Database Overview</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>Database Size</h6>
                        <p class="h4 text-primary"><?php echo formatBytes($db_size); ?></p>
                    </div>
                    <div class="col-md-4">
                        <h6>Total Tables</h6>
                        <p class="h4 text-info"><?php echo count($table_results); ?></p>
                    </div>
                    <div class="col-md-4">
                        <h6>Connection Status</h6>
                        <p class="h4 text-success"><i class="bi bi-check-circle"></i> Connected</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Required Tables -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="bi bi-table me-2"></i>Required Tables</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Table Name</th>
                                <th>Status</th>
                                <th>Row Count</th>
                                <th>Columns</th>
                                <th>Message</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($required_tables as $table => $required_cols): ?>
                                <?php $result = $table_results[$table]; ?>
                                <tr>
                                    <td><strong><?php echo h($table); ?></strong></td>
                                    <td>
                                        <span class="status-<?php echo $result['status']; ?>">
                                            <?php if ($result['status'] === 'ok'): ?>
                                                <i class="bi bi-check-circle"></i> OK
                                            <?php elseif ($result['status'] === 'missing'): ?>
                                                <i class="bi bi-x-circle"></i> Missing
                                            <?php elseif ($result['status'] === 'incomplete'): ?>
                                                <i class="bi bi-exclamation-triangle"></i> Incomplete
                                            <?php else: ?>
                                                <i class="bi bi-exclamation-circle"></i> Error
                                            <?php endif; ?>
                                        </span>
                                    </td>
                                    <td><?php echo isset($result['count']) ? number_format($result['count']) : 'N/A'; ?></td>
                                    <td><?php echo isset($result['columns']) ? count($result['columns']) : 'N/A'; ?></td>
                                    <td><?php echo h($result['message']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Optional Tables -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="bi bi-plus-circle me-2"></i>Optional Tables</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Table Name</th>
                                <th>Status</th>
                                <th>Row Count</th>
                                <th>Purpose</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $table_purposes = [
                                'activity_log' => 'System activity tracking',
                                'notifications' => 'User notifications',
                                'email_logs' => 'Email delivery tracking'
                            ];
                            foreach ($optional_tables as $table): 
                                $result = $table_results[$table];
                            ?>
                                <tr>
                                    <td><strong><?php echo h($table); ?></strong></td>
                                    <td>
                                        <span class="status-<?php echo $result['status']; ?>">
                                            <?php if ($result['status'] === 'ok'): ?>
                                                <i class="bi bi-check-circle"></i> Available
                                            <?php else: ?>
                                                <i class="bi bi-dash-circle"></i> Not Available
                                            <?php endif; ?>
                                        </span>
                                    </td>
                                    <td><?php echo isset($result['count']) ? number_format($result['count']) : 'N/A'; ?></td>
                                    <td><?php echo $table_purposes[$table] ?? 'Optional feature'; ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Data Summary -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="bi bi-bar-chart me-2"></i>Data Summary</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php 
                    $data_tables = ['users', 'members', 'books', 'book_loans'];
                    foreach ($data_tables as $table):
                        if (isset($table_results[$table]['count'])):
                    ?>
                        <div class="col-md-3 mb-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <h5 class="card-title"><?php echo ucfirst(str_replace('_', ' ', $table)); ?></h5>
                                    <h3 class="text-primary"><?php echo number_format($table_results[$table]['count']); ?></h3>
                                </div>
                            </div>
                        </div>
                    <?php 
                        endif;
                    endforeach; 
                    ?>
                </div>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-tools me-2"></i>Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="system_test.php" class="btn btn-primary w-100 mb-2">
                            <i class="bi bi-gear me-2"></i>System Test
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="test_login_functionality.php" class="btn btn-success w-100 mb-2">
                            <i class="bi bi-shield-check me-2"></i>Test Login
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="setup.php" class="btn btn-warning w-100 mb-2">
                            <i class="bi bi-wrench me-2"></i>Database Setup
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="index.php" class="btn btn-info w-100 mb-2">
                            <i class="bi bi-house me-2"></i>Home Page
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
