<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/google_auth.php';

// Initialize variables
$first_name = $last_name = $email = $phone = $address = $password = $confirm_password = '';
$errors = [];
$success = false;

// Get Google login URLs (regular and direct)
$google_login_url = getGoogleLoginUrl();
$google_direct_url = getGoogleLoginUrl('', true);


// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $first_name = trim($_POST['first_name'] ?? '');
    $last_name = trim($_POST['last_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    // Validate input
    if (empty($first_name)) {
        $errors[] = 'First name is required';
    }

    if (empty($last_name)) {
        $errors[] = 'Last name is required';
    }

    if (empty($email)) {
        $errors[] = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format';
    }

    if (empty($password)) {
        $errors[] = 'Password is required';
    } elseif (strlen($password) < 6) {
        $errors[] = 'Password must be at least 6 characters long';
    }

    if ($password !== $confirm_password) {
        $errors[] = 'Passwords do not match';
    }

    // Check if terms checkbox was checked
    if (!isset($_POST['terms'])) {
        $errors[] = 'You must agree to the terms and policies to register';
    }

    // Connect to database
    $database = new Database();
    $db = $database->getConnection();

    // Check if email already exists
    if (!empty($email)) {
        $query = "SELECT id FROM members WHERE email = :email";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $errors[] = 'Email already exists. Please use a different email address.';
        }
    }

    // If no errors, register the member
    if (empty($errors)) {
        try {
            // Set membership date to today
            $membership_date = date('Y-m-d');

            // Hash the password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            // Insert member into database
            $query = "INSERT INTO members (first_name, last_name, email, phone, address, membership_date, membership_status, password)
                      VALUES (:first_name, :last_name, :email, :phone, :address, :membership_date, 'active', :password)";

            $stmt = $db->prepare($query);
            $stmt->bindParam(':first_name', $first_name);
            $stmt->bindParam(':last_name', $last_name);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':phone', $phone);
            $stmt->bindParam(':address', $address);
            $stmt->bindParam(':membership_date', $membership_date);
            $stmt->bindParam(':password', $hashed_password);

            if ($stmt->execute()) {
                $success = true;

                // Clear form data after successful submission
                $first_name = $last_name = $email = $phone = $address = '';
            } else {
                $errors[] = 'Failed to register. Please try again.';
            }
        } catch (PDOException $e) {
            $errors[] = 'Database error: ' . $e->getMessage();
        }
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Registration - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .registration-container {
            max-width: 800px;
            margin: 50px auto;
        }
        .card {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .card-header {
            background-color: #343a40;
            color: white;
            font-weight: bold;
        }
        .btn-primary {
            background-color: #343a40;
            border-color: #343a40;
        }
        .btn-primary:hover {
            background-color: #23272b;
            border-color: #23272b;
        }
        /* Google Button Styles */
        .btn-google {
            background-color: #ffffff;
            color: #757575;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-family: 'Roboto', sans-serif;
            font-weight: 500;
            font-size: 14px;
            padding: 10px 12px;
            position: relative;
            text-align: center;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        }
        .btn-google:hover {
            background-color: #f8f9fa;
            box-shadow: 0 2px 4px rgba(0,0,0,0.12);
            border-color: #c6c6c6;
            color: #3c4043;
        }
        .btn-google:active {
            background-color: #f1f3f4;
            box-shadow: 0 1px 2px rgba(0,0,0,0.15);
        }
        .btn-google img {
            vertical-align: middle;
        }
        /* Separator Line */
        .separator {
            display: flex;
            align-items: center;
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
            margin: 15px 0;
        }
        .separator::before,
        .separator::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid #dee2e6;
        }
        .separator span {
            padding: 0 10px;
        }
        .direct-link, .lite-link {
            text-decoration: none;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }
        .direct-link:hover, .lite-link:hover {
            color: #0d6efd !important;
        }
        .connection-note {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="registration-container">
        <div class="card">
            <div class="card-header text-center py-3">
                <h4 class="mb-0"><i class="bi bi-book me-2"></i>Library Management System</h4>
            </div>
            <div class="card-body p-4">
                <h5 class="card-title text-center mb-4">Member Registration</h5>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <h5><i class="bi bi-check-circle-fill me-2"></i>Registration Successful!</h5>
                        <p>Your library membership has been created successfully.</p>
                        <p>You can now borrow books and access library services.</p>
                        <div class="text-center mt-3">
                            <a href="index.php" class="btn btn-primary">Go to Homepage</a>
                        </div>
                    </div>
                <?php else: ?>
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo h($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="<?php echo h($_SERVER['PHP_SELF']); ?>">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="first_name" name="first_name" value="<?php echo h($first_name); ?>" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="last_name" name="last_name" value="<?php echo h($last_name); ?>" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo h($email); ?>" required>
                            <div class="form-text">We'll never share your email with anyone else.</div>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo h($phone); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?php echo h($address); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="form-text">Password must be at least 6 characters long</div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the library's <a href="terms.php" target="_blank">terms and policies</a>
                                <button type="button" class="btn btn-sm btn-link p-0 ms-1" data-bs-toggle="modal" data-bs-target="#termsModal">
                                    <i class="bi bi-info-circle"></i> Quick View
                                </button>
                            </label>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Register</button>
                        </div>
                    </form>



                    <div class="mt-4 text-center">
                        <div class="separator mb-3">
                            <span>Or</span>
                        </div>

                        <?php if (isGoogleOAuthConfigured()): ?>
                            <a href="<?php echo h($google_login_url); ?>" class="btn btn-google w-100" id="googleLoginBtn">
                                <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="20" height="20" class="me-2">
                                <span>Sign up with Google</span>
                            </a>
                            <div class="text-center mt-2">
                                <a href="<?php echo h($google_direct_url); ?>" class="direct-link small text-muted">
                                    <i class="bi bi-lightning-charge-fill me-1 small"></i>Direct link (faster)
                                </a>
                                <span class="mx-2 text-muted">|</span>
                                <a href="<?php echo url('google_lite.php'); ?>" class="lite-link small text-muted">
                                    <i class="bi bi-speedometer2 me-1 small"></i>Lite mode
                                </a>
                            </div>
                        <?php else: ?>
                            <a href="<?php echo url('google_setup.php'); ?>" class="btn btn-outline-secondary w-100">
                                <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="20" height="20" class="me-2">
                                <span>Configure Google Sign-In</span>
                            </a>
                            <div class="alert alert-info mt-3 small text-start">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                Google Sign-In is not configured yet. Click the button above to set up your Google OAuth credentials.
                            </div>
                        <?php endif; ?>

                        <div class="text-center mt-2">
                            <a href="<?php echo url('google_setup.php'); ?>" class="small text-muted">
                                <i class="bi bi-gear-fill me-1 small"></i>Configure Google Sign-In
                            </a>
                        </div>
                        <noscript>
                            <div class="alert alert-warning mt-3 small">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                JavaScript is disabled. <a href="google_auth_fallback.php">Click here</a> for Google registration.
                            </div>
                        </noscript>
                    </div>

                    <div class="mt-4 text-center">
                        <p>Already have an account? <a href="login.php">Login here</a></p>
                    </div>
                <?php endif; ?>
            </div>
            <div class="card-footer text-center py-3">
                <p class="mb-0">Library Management System &copy; <?php echo date('Y'); ?></p>
            </div>
        </div>
    </div>

    <!-- Terms Modal -->
    <div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header bg-dark text-white">
                    <h5 class="modal-title" id="termsModalLabel">Library Terms and Policies - Summary</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        This is a summary of our terms and policies. For the complete version, please visit our <a href="terms.php" target="_blank">Terms and Policies</a> page.
                    </div>

                    <h5 class="mt-4">Key Points:</h5>
                    <ul class="list-group list-group-flush mb-3">
                        <li class="list-group-item"><strong>Membership:</strong> Valid for one year, available to residents 13+ years old</li>
                        <li class="list-group-item"><strong>Borrowing:</strong> Up to 5 books for 14 days, $1.00/day late fee</li>
                        <li class="list-group-item"><strong>Privacy:</strong> Your information is used only for library services</li>
                        <li class="list-group-item"><strong>Conduct:</strong> Respect for all users, staff, and property is required</li>
                        <li class="list-group-item"><strong>Online Services:</strong> Keep your account credentials confidential</li>
                    </ul>

                    <p>By checking the terms checkbox, you agree to abide by all library terms and policies.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="terms.php" target="_blank" class="btn btn-primary">View Full Terms</a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo url('assets/js/google-auth.js'); ?>"></script>
</body>
</html>
