<?php
/**
 * AJAX endpoint for refreshing member statistics
 * Returns updated member analytics data in JSON format
 */

session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

header('Content-Type: application/json');

try {
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        throw new Exception("Failed to establish database connection");
    }

    // Get basic statistics
    $stats = [];

    // Total members
    $stmt = $db->query("SELECT COUNT(*) as count FROM members");
    $stats['total_members'] = $stmt->fetch()['count'];

    // Members with active loans
    $stmt = $db->query("
        SELECT COUNT(DISTINCT member_id) as count
        FROM book_loans
        WHERE status = 'borrowed'
    ");
    $stats['members_with_active_loans'] = $stmt->fetch()['count'];

    // Members who have returned books
    $stmt = $db->query("
        SELECT COUNT(DISTINCT member_id) as count
        FROM book_loans
        WHERE status = 'returned'
    ");
    $stats['members_who_returned'] = $stmt->fetch()['count'];

    // Members with overdue books
    $stmt = $db->query("
        SELECT COUNT(DISTINCT member_id) as count
        FROM book_loans
        WHERE status = 'overdue'
    ");
    $stats['members_with_overdue'] = $stmt->fetch()['count'];

    // Total fines (with fallback if fine_amount column doesn't exist)
    try {
        $stmt = $db->query("
            SELECT COALESCE(SUM(fine_amount), 0) as total_fines
            FROM book_loans
            WHERE fine_amount > 0
        ");
        $stats['total_fines'] = $stmt->fetch()['total_fines'];
    } catch (Exception $e) {
        $stats['total_fines'] = 0; // Fallback if fine_amount column doesn't exist
    }

    // Members with fines (with fallback)
    try {
        $stmt = $db->query("
            SELECT COUNT(DISTINCT member_id) as count
            FROM book_loans
            WHERE fine_amount > 0
        ");
        $stats['members_with_fines'] = $stmt->fetch()['count'];
    } catch (Exception $e) {
        $stats['members_with_fines'] = 0; // Fallback if fine_amount column doesn't exist
    }

    // Members who never borrowed
    $stmt = $db->query("
        SELECT COUNT(*) as count
        FROM members m
        WHERE NOT EXISTS (
            SELECT 1 FROM book_loans bl WHERE bl.member_id = m.id
        )
    ");
    $stats['members_never_borrowed'] = $stmt->fetch()['count'];

    // Active loans count
    $stmt = $db->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed'");
    $stats['active_loans'] = $stmt->fetch()['count'];

    // Overdue books count
    $stmt = $db->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'overdue'");
    $stats['overdue_books'] = $stmt->fetch()['count'];

    // Get advanced analytics
    $analytics = [];

    // Activity levels
    $activity_query = "
        SELECT
            CASE
                WHEN loan_count = 0 THEN 'Inactive'
                WHEN loan_count BETWEEN 1 AND 2 THEN 'Light Reader'
                WHEN loan_count BETWEEN 3 AND 5 THEN 'Regular Reader'
                WHEN loan_count BETWEEN 6 AND 10 THEN 'Active Reader'
                WHEN loan_count > 10 THEN 'Power Reader'
            END as activity_level,
            COUNT(*) as member_count
        FROM (
            SELECT m.id, COUNT(bl.id) as loan_count
            FROM members m
            LEFT JOIN book_loans bl ON m.id = bl.member_id
            GROUP BY m.id
        ) as member_activity
        GROUP BY activity_level
        ORDER BY
            CASE activity_level
                WHEN 'Inactive' THEN 1
                WHEN 'Light Reader' THEN 2
                WHEN 'Regular Reader' THEN 3
                WHEN 'Active Reader' THEN 4
                WHEN 'Power Reader' THEN 5
            END
    ";

    $stmt = $db->prepare($activity_query);
    $stmt->execute();
    $analytics['activity_levels'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Top borrowers
    $top_borrowers_query = "
        SELECT
            m.id, m.first_name, m.last_name, m.email,
            COUNT(bl.id) as total_loans,
            SUM(CASE WHEN bl.status = 'borrowed' THEN 1 ELSE 0 END) as active_loans,
            SUM(CASE WHEN bl.status = 'overdue' THEN 1 ELSE 0 END) as overdue_loans
        FROM members m
        LEFT JOIN book_loans bl ON m.id = bl.member_id
        GROUP BY m.id
        HAVING total_loans > 0
        ORDER BY total_loans DESC
        LIMIT 5
    ";

    $stmt = $db->prepare($top_borrowers_query);
    $stmt->execute();
    $analytics['top_borrowers'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Recent activity (last 7 days)
    $recent_activity_query = "
        SELECT
            bl.id, bl.issue_date, bl.due_date, bl.status,
            m.first_name, m.last_name,
            b.title
        FROM book_loans bl
        JOIN members m ON bl.member_id = m.id
        JOIN books b ON bl.book_id = b.id
        WHERE bl.issue_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        ORDER BY bl.issue_date DESC
        LIMIT 10
    ";

    $stmt = $db->prepare($recent_activity_query);
    $stmt->execute();
    $analytics['recent_activity'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Risk analysis
    $risk_analysis = [];

    // High risk members (multiple overdue books or high fines)
    $stmt = $db->query("
        SELECT COUNT(DISTINCT member_id) as count
        FROM book_loans
        WHERE (status = 'overdue' AND fine_amount > 20)
           OR (SELECT COUNT(*) FROM book_loans bl2 WHERE bl2.member_id = book_loans.member_id AND bl2.status = 'overdue') > 2
    ");
    $risk_analysis['high_risk_members'] = $stmt->fetch()['count'];

    // Medium risk members
    $stmt = $db->query("
        SELECT COUNT(DISTINCT member_id) as count
        FROM book_loans
        WHERE status = 'overdue' AND fine_amount BETWEEN 5 AND 20
    ");
    $risk_analysis['medium_risk_members'] = $stmt->fetch()['count'];

    // High fine members (over $50)
    $stmt = $db->query("
        SELECT COUNT(DISTINCT member_id) as count
        FROM book_loans
        WHERE fine_amount > 50
    ");
    $risk_analysis['high_fine_members'] = $stmt->fetch()['count'];

    $analytics['risk_analysis'] = $risk_analysis;

    // Member statistics
    $member_stats = [];

    // New members this month (with fallback if created_at doesn't exist)
    try {
        $stmt = $db->query("
            SELECT COUNT(*) as count
            FROM members
            WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
        ");
        $member_stats['new_members_this_month'] = $stmt->fetch()['count'];
    } catch (Exception $e) {
        $member_stats['new_members_this_month'] = 0; // Fallback if created_at column doesn't exist
    }

    // New members this week (with fallback)
    try {
        $stmt = $db->query("
            SELECT COUNT(*) as count
            FROM members
            WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 1 WEEK)
        ");
        $member_stats['new_members_this_week'] = $stmt->fetch()['count'];
    } catch (Exception $e) {
        $member_stats['new_members_this_week'] = 0; // Fallback if created_at column doesn't exist
    }

    // Average loans per member (total loans / total members)
    $stmt = $db->query("
        SELECT ROUND((SELECT COUNT(*) FROM book_loans) / (SELECT COUNT(*) FROM members), 1) as avg_loans
    ");
    $member_stats['avg_loans_per_member'] = $stmt->fetch()['avg_loans'];

    // Members who have borrowed
    $stmt = $db->query("
        SELECT COUNT(DISTINCT member_id) as count
        FROM book_loans
    ");
    $member_stats['members_who_borrowed'] = $stmt->fetch()['count'];

    // Prepare response
    $response = [
        'success' => true,
        'stats' => $stats,
        'analytics' => $analytics,
        'member_stats' => $member_stats,
        'timestamp' => date('Y-m-d H:i:s')
    ];

    echo json_encode($response);

} catch (Exception $e) {
    error_log("Error refreshing member stats: " . $e->getMessage());

    // Provide more specific error information for debugging
    $error_message = 'Database error occurred while refreshing statistics';

    // Check if it's a connection error
    if (strpos($e->getMessage(), 'connection') !== false || strpos($e->getMessage(), 'connect') !== false) {
        $error_message = 'Database connection failed. Please check if MySQL is running.';
    }
    // Check if it's a table error
    else if (strpos($e->getMessage(), "doesn't exist") !== false) {
        $error_message = 'Database tables are missing. Please run the setup script.';
    }
    // Check if it's a column error
    else if (strpos($e->getMessage(), 'Unknown column') !== false) {
        $error_message = 'Database schema is outdated. Please update the database structure.';
    }

    echo json_encode([
        'success' => false,
        'message' => $error_message,
        'debug_info' => $e->getMessage() // Include for debugging
    ]);
}
?>
