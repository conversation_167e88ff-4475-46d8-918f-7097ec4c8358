<?php
/**
 * Working Email Settings Page
 */

// Error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Initialize variables
$success_message = '';
$error_message = '';
$current_settings = [];

// Connect to database
try {
    require_once '../config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    // Create settings table if it doesn't exist
    $create_table_sql = "CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_group VARCHAR(50) NOT NULL,
        setting_key VARCHAR(100) NOT NULL,
        setting_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_setting (setting_group, setting_key)
    )";
    $db->exec($create_table_sql);
    
} catch (Exception $e) {
    $error_message = "Database Error: " . $e->getMessage();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($db)) {
    try {
        if (isset($_POST['save_email_settings'])) {
            // Email settings
            $email_settings = [
                'from_email' => $_POST['from_email'] ?? '<EMAIL>',
                'from_name' => $_POST['from_name'] ?? 'Library Management System',
                'reply_to' => $_POST['reply_to'] ?? '<EMAIL>',
                'smtp_enabled' => isset($_POST['smtp_enabled']) ? 'true' : 'false',
                'smtp_host' => $_POST['smtp_host'] ?? '',
                'smtp_port' => $_POST['smtp_port'] ?? '587',
                'smtp_username' => $_POST['smtp_username'] ?? '',
                'smtp_password' => $_POST['smtp_password'] ?? '',
                'smtp_secure' => $_POST['smtp_secure'] ?? 'tls'
            ];
            
            foreach ($email_settings as $key => $value) {
                $query = "INSERT INTO settings (setting_group, setting_key, setting_value) 
                          VALUES ('email', ?, ?) 
                          ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
                $stmt = $db->prepare($query);
                $stmt->execute([$key, $value]);
            }
            
            $success_message = "Email settings saved successfully!";
        }
        
        if (isset($_POST['save_notification_settings'])) {
            // Notification settings
            $notification_settings = [
                'due_date_reminder_days' => $_POST['due_date_reminder_days'] ?? '3',
                'send_overdue_notifications' => isset($_POST['send_overdue_notifications']) ? 'true' : 'false',
                'overdue_notification_frequency' => $_POST['overdue_notification_frequency'] ?? '7'
            ];
            
            foreach ($notification_settings as $key => $value) {
                $query = "INSERT INTO settings (setting_group, setting_key, setting_value) 
                          VALUES ('notifications', ?, ?) 
                          ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
                $stmt = $db->prepare($query);
                $stmt->execute([$key, $value]);
            }
            
            $success_message = "Notification settings saved successfully!";
        }
        
        if (isset($_POST['save_fine_settings'])) {
            // Fine settings
            $fine_settings = [
                'fine_rate_per_day' => $_POST['fine_rate_per_day'] ?? '1.00',
                'grace_period_days' => $_POST['grace_period_days'] ?? '3',
                'max_fine_per_book' => $_POST['max_fine_per_book'] ?? '25.00'
            ];
            
            foreach ($fine_settings as $key => $value) {
                $query = "INSERT INTO settings (setting_group, setting_key, setting_value) 
                          VALUES ('fines', ?, ?) 
                          ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
                $stmt = $db->prepare($query);
                $stmt->execute([$key, $value]);
            }
            
            $success_message = "Fine settings saved successfully!";
        }
        
    } catch (Exception $e) {
        $error_message = "Error saving settings: " . $e->getMessage();
    }
}

// Load current settings
if (isset($db)) {
    try {
        $query = "SELECT setting_group, setting_key, setting_value FROM settings 
                  WHERE setting_group IN ('email', 'notifications', 'fines')";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        while ($row = $stmt->fetch()) {
            $current_settings[$row['setting_group']][$row['setting_key']] = $row['setting_value'];
        }
    } catch (Exception $e) {
        // Ignore errors, use defaults
    }
}

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function getSetting($group, $key, $default = '') {
    global $current_settings;
    return $current_settings[$group][$key] ?? $default;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Working Email Settings - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body { background-color: #f8f9fa; }
        .settings-card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .nav-pills .nav-link.active {
            background-color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card settings-card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0"><i class="bi bi-envelope me-2"></i>Working Email Settings</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($success_message): ?>
                            <div class="alert alert-success alert-dismissible fade show">
                                <i class="bi bi-check-circle me-2"></i><?php echo h($success_message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if ($error_message): ?>
                            <div class="alert alert-danger alert-dismissible fade show">
                                <i class="bi bi-exclamation-triangle me-2"></i><?php echo h($error_message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <!-- Navigation Tabs -->
                        <ul class="nav nav-pills mb-4" id="settingsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="email-tab" data-bs-toggle="pill" data-bs-target="#email" type="button" role="tab">
                                    <i class="bi bi-envelope me-2"></i>Email Settings
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="notifications-tab" data-bs-toggle="pill" data-bs-target="#notifications" type="button" role="tab">
                                    <i class="bi bi-bell me-2"></i>Notifications
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="fines-tab" data-bs-toggle="pill" data-bs-target="#fines" type="button" role="tab">
                                    <i class="bi bi-cash-coin me-2"></i>Fines
                                </button>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content" id="settingsTabContent">
                            <!-- Email Settings Tab -->
                            <div class="tab-pane fade show active" id="email" role="tabpanel">
                                <form method="POST">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="from_email" class="form-label">From Email</label>
                                            <input type="email" class="form-control" id="from_email" name="from_email" 
                                                   value="<?php echo h(getSetting('email', 'from_email', '<EMAIL>')); ?>" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="from_name" class="form-label">From Name</label>
                                            <input type="text" class="form-control" id="from_name" name="from_name" 
                                                   value="<?php echo h(getSetting('email', 'from_name', 'Library Management System')); ?>" required>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="reply_to" class="form-label">Reply-To Email</label>
                                        <input type="email" class="form-control" id="reply_to" name="reply_to" 
                                               value="<?php echo h(getSetting('email', 'reply_to', '<EMAIL>')); ?>" required>
                                    </div>

                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="smtp_enabled" name="smtp_enabled" 
                                               <?php echo getSetting('email', 'smtp_enabled', 'false') === 'true' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="smtp_enabled">Use SMTP Server</label>
                                    </div>

                                    <div id="smtp_settings" class="<?php echo getSetting('email', 'smtp_enabled', 'false') === 'true' ? '' : 'd-none'; ?>">
                                        <div class="row mb-3">
                                            <div class="col-md-8">
                                                <label for="smtp_host" class="form-label">SMTP Host</label>
                                                <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                                       value="<?php echo h(getSetting('email', 'smtp_host', 'smtp.gmail.com')); ?>">
                                            </div>
                                            <div class="col-md-4">
                                                <label for="smtp_port" class="form-label">SMTP Port</label>
                                                <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                                       value="<?php echo h(getSetting('email', 'smtp_port', '587')); ?>">
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="smtp_username" class="form-label">SMTP Username</label>
                                                <input type="text" class="form-control" id="smtp_username" name="smtp_username" 
                                                       value="<?php echo h(getSetting('email', 'smtp_username', '')); ?>">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="smtp_password" class="form-label">SMTP Password</label>
                                                <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                                       value="<?php echo h(getSetting('email', 'smtp_password', '')); ?>">
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="smtp_secure" class="form-label">SMTP Security</label>
                                            <select class="form-select" id="smtp_secure" name="smtp_secure">
                                                <option value="" <?php echo getSetting('email', 'smtp_secure', 'tls') === '' ? 'selected' : ''; ?>>None</option>
                                                <option value="tls" <?php echo getSetting('email', 'smtp_secure', 'tls') === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                                <option value="ssl" <?php echo getSetting('email', 'smtp_secure', 'tls') === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                            </select>
                                        </div>
                                    </div>

                                    <button type="submit" name="save_email_settings" class="btn btn-primary">
                                        <i class="bi bi-save me-2"></i>Save Email Settings
                                    </button>
                                </form>
                            </div>

                            <!-- Notifications Tab -->
                            <div class="tab-pane fade" id="notifications" role="tabpanel">
                                <form method="POST">
                                    <div class="mb-3">
                                        <label for="due_date_reminder_days" class="form-label">Due Date Reminder (days before)</label>
                                        <input type="number" class="form-control" id="due_date_reminder_days" name="due_date_reminder_days" 
                                               min="1" max="14" value="<?php echo h(getSetting('notifications', 'due_date_reminder_days', '3')); ?>" required>
                                        <div class="form-text">Recommended: 3 days before due date</div>
                                    </div>

                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="send_overdue_notifications" name="send_overdue_notifications" 
                                               <?php echo getSetting('notifications', 'send_overdue_notifications', 'true') === 'true' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="send_overdue_notifications">Send Overdue Notifications</label>
                                    </div>

                                    <div class="mb-3">
                                        <label for="overdue_notification_frequency" class="form-label">Overdue Notification Frequency (days)</label>
                                        <input type="number" class="form-control" id="overdue_notification_frequency" name="overdue_notification_frequency" 
                                               min="1" max="30" value="<?php echo h(getSetting('notifications', 'overdue_notification_frequency', '7')); ?>" required>
                                        <div class="form-text">How often to send overdue notifications</div>
                                    </div>

                                    <button type="submit" name="save_notification_settings" class="btn btn-primary">
                                        <i class="bi bi-save me-2"></i>Save Notification Settings
                                    </button>
                                </form>
                            </div>

                            <!-- Fines Tab -->
                            <div class="tab-pane fade" id="fines" role="tabpanel">
                                <form method="POST">
                                    <div class="mb-3">
                                        <label for="fine_rate_per_day" class="form-label">Fine Rate Per Day ($)</label>
                                        <input type="number" class="form-control" id="fine_rate_per_day" name="fine_rate_per_day" 
                                               min="0" step="0.01" value="<?php echo h(getSetting('fines', 'fine_rate_per_day', '1.00')); ?>" required>
                                        <div class="form-text">Amount charged per day for overdue books</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="grace_period_days" class="form-label">Grace Period (days)</label>
                                        <input type="number" class="form-control" id="grace_period_days" name="grace_period_days" 
                                               min="0" max="7" value="<?php echo h(getSetting('fines', 'grace_period_days', '3')); ?>" required>
                                        <div class="form-text">Days after due date before fines start</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="max_fine_per_book" class="form-label">Maximum Fine Per Book ($)</label>
                                        <input type="number" class="form-control" id="max_fine_per_book" name="max_fine_per_book" 
                                               min="0" step="0.01" value="<?php echo h(getSetting('fines', 'max_fine_per_book', '25.00')); ?>" required>
                                        <div class="form-text">Maximum fine amount per book</div>
                                    </div>

                                    <button type="submit" name="save_fine_settings" class="btn btn-primary">
                                        <i class="bi bi-save me-2"></i>Save Fine Settings
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Current Settings Summary -->
                        <div class="mt-4">
                            <h6>Current Settings Summary:</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <small class="text-muted">
                                        <strong>Email:</strong><br>
                                        From: <?php echo h(getSetting('email', 'from_email', 'Not set')); ?><br>
                                        SMTP: <?php echo getSetting('email', 'smtp_enabled', 'false') === 'true' ? 'Enabled' : 'Disabled'; ?>
                                    </small>
                                </div>
                                <div class="col-md-4">
                                    <small class="text-muted">
                                        <strong>Notifications:</strong><br>
                                        Reminder: <?php echo h(getSetting('notifications', 'due_date_reminder_days', '3')); ?> days<br>
                                        Overdue: <?php echo getSetting('notifications', 'send_overdue_notifications', 'true') === 'true' ? 'Enabled' : 'Disabled'; ?>
                                    </small>
                                </div>
                                <div class="col-md-4">
                                    <small class="text-muted">
                                        <strong>Fines:</strong><br>
                                        Rate: $<?php echo h(getSetting('fines', 'fine_rate_per_day', '1.00')); ?>/day<br>
                                        Grace: <?php echo h(getSetting('fines', 'grace_period_days', '3')); ?> days
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Navigation -->
                        <div class="mt-4">
                            <h6>Navigation:</h6>
                            <div class="btn-group" role="group">
                                <a href="email_settings.php" class="btn btn-outline-primary">Original Email Settings</a>
                                <a href="simple_email_settings.php" class="btn btn-outline-secondary">Simple Email Settings</a>
                                <a href="settings.php" class="btn btn-outline-success">General Settings</a>
                                <a href="dashboard.php" class="btn btn-outline-info">Dashboard</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle SMTP settings
            const smtpCheckbox = document.getElementById('smtp_enabled');
            const smtpSettings = document.getElementById('smtp_settings');
            
            if (smtpCheckbox && smtpSettings) {
                smtpCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        smtpSettings.classList.remove('d-none');
                    } else {
                        smtpSettings.classList.add('d-none');
                    }
                });
            }
        });
    </script>
</body>
</html>
