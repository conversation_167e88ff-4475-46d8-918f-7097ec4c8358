<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get notifications for the current user
$query = "SELECT * FROM notifications
          WHERE (user_id = :user_id OR user_id IS NULL)
          ORDER BY created_at DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $_SESSION['user_id']);
$stmt->execute();
$notifications = $stmt->fetchAll();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// Mark all as read if requested
if (isset($_GET['mark_all_read'])) {
    $query = "UPDATE notifications
              SET is_read = 1
              WHERE (user_id = :user_id OR user_id IS NULL) AND is_read = 0";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $_SESSION['user_id']);
    $stmt->execute();

    // Log activity
    logActivity($db, 'mark_read', 'Marked all notifications as read');

    // Redirect to remove the query parameter
    redirect('index.php');
}

// Mark a single notification as read
if (isset($_GET['mark_read']) && is_numeric($_GET['mark_read'])) {
    $notification_id = $_GET['mark_read'];

    $query = "UPDATE notifications
              SET is_read = 1
              WHERE id = :id AND (user_id = :user_id OR user_id IS NULL)";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $notification_id);
    $stmt->bindParam(':user_id', $_SESSION['user_id']);
    $stmt->execute();

    // Log activity
    logActivity($db, 'mark_read', 'Marked notification as read', 'notification', $notification_id);

    // Redirect to remove the query parameter
    redirect('index.php');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications - Library Management System</title>
    <?php include '../includes/head.php'; ?>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Notifications</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <?php
                        $unread_count = count(array_filter($notifications, function($n) { return $n['is_read'] == 0; }));
                        if ($unread_count > 0):
                        ?>
                            <a href="index.php?mark_all_read=1" class="btn btn-sm btn-primary me-2">
                                <i class="bi bi-check-all me-1"></i> Mark All as Read
                            </a>
                        <?php endif; ?>
                        <button id="darkModeToggle" class="btn btn-sm btn-outline-secondary" title="Toggle Dark Mode">
                            <i id="darkModeIcon" class="bi bi-moon"></i>
                        </button>
                    </div>
                </div>

                <!-- Notification Summary -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card border-primary h-100">
                            <div class="card-body text-center">
                                <h1 class="display-4 fw-bold text-primary"><?php echo count($notifications); ?></h1>
                                <p class="mb-0">Total Notifications</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-danger h-100">
                            <div class="card-body text-center">
                                <h1 class="display-4 fw-bold text-danger"><?php echo $unread_count; ?></h1>
                                <p class="mb-0">Unread Notifications</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-success h-100">
                            <div class="card-body text-center">
                                <h1 class="display-4 fw-bold text-success"><?php echo count($notifications) - $unread_count; ?></h1>
                                <p class="mb-0">Read Notifications</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-warning h-100">
                            <div class="card-body text-center">
                                <h1 class="display-4 fw-bold text-warning">
                                    <?php
                                    echo count(array_filter($notifications, function($n) {
                                        return $n['type'] === 'warning' || $n['type'] === 'danger';
                                    }));
                                    ?>
                                </h1>
                                <p class="mb-0">Important Alerts</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Filter Notifications</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="statusFilter" class="form-label">Status</label>
                                <select id="statusFilter" class="form-select">
                                    <option value="all" selected>All Notifications</option>
                                    <option value="unread">Unread Only</option>
                                    <option value="read">Read Only</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="typeFilter" class="form-label">Type</label>
                                <select id="typeFilter" class="form-select">
                                    <option value="all" selected>All Types</option>
                                    <option value="info">Information</option>
                                    <option value="success">Success</option>
                                    <option value="warning">Warning</option>
                                    <option value="danger">Danger</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="sortOrder" class="form-label">Sort By</label>
                                <select id="sortOrder" class="form-select">
                                    <option value="newest" selected>Newest First</option>
                                    <option value="oldest">Oldest First</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (empty($notifications)): ?>
                    <div class="empty-notifications text-center py-5">
                        <i class="bi bi-bell-slash fs-1 text-muted mb-3 d-block"></i>
                        <h4>No Notifications</h4>
                        <p class="text-muted">You don't have any notifications at the moment.</p>
                    </div>
                <?php else: ?>
                    <!-- Batch Actions -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="selectAllNotifications">
                            <label class="form-check-label" for="selectAllNotifications">
                                Select All
                            </label>
                        </div>
                        <div class="btn-group">
                            <button type="button" id="markSelectedRead" class="btn btn-sm btn-outline-primary" disabled>
                                <i class="bi bi-check-all me-1"></i> Mark Selected as Read
                            </button>
                        </div>
                    </div>

                    <!-- Notifications List -->
                    <div class="notifications-container">
                        <?php foreach ($notifications as $notification): ?>
                            <div class="notification-item <?php echo $notification['is_read'] ? '' : 'unread'; ?> type-<?php echo h($notification['type']); ?> mb-3"
                                 data-status="<?php echo $notification['is_read'] ? 'read' : 'unread'; ?>"
                                 data-type="<?php echo h($notification['type']); ?>">
                                <div class="card">
                                    <div class="card-body p-3">
                                        <div class="d-flex">
                                            <div class="form-check me-2 align-self-center">
                                                <input class="form-check-input notification-checkbox" type="checkbox" value="<?php echo h($notification['id']); ?>">
                                            </div>
                                            <div class="notification-icon bg-<?php echo h($notification['type']); ?>">
                                                <?php if ($notification['type'] === 'warning'): ?>
                                                    <i class="bi bi-exclamation-triangle"></i>
                                                <?php elseif ($notification['type'] === 'danger'): ?>
                                                    <i class="bi bi-exclamation-circle"></i>
                                                <?php elseif ($notification['type'] === 'success'): ?>
                                                    <i class="bi bi-check-circle"></i>
                                                <?php else: ?>
                                                    <i class="bi bi-info-circle"></i>
                                                <?php endif; ?>
                                            </div>
                                            <div class="notification-content">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <h5 class="notification-message mb-1"><?php echo h($notification['message']); ?></h5>
                                                    <span class="notification-time"><?php echo timeAgo($notification['created_at']); ?></span>
                                                </div>

                                                <div class="d-flex justify-content-between align-items-center mt-2">
                                                    <div>
                                                        <?php if ($notification['entity_type']): ?>
                                                            <?php if ($notification['entity_type'] === 'book'): ?>
                                                                <a href="../books/view.php?id=<?php echo h($notification['entity_id']); ?>" class="btn btn-sm btn-outline-secondary">
                                                                    <i class="bi bi-book me-1"></i> View Book
                                                                </a>
                                                            <?php elseif ($notification['entity_type'] === 'member'): ?>
                                                                <a href="../members/view.php?id=<?php echo h($notification['entity_id']); ?>" class="btn btn-sm btn-outline-secondary">
                                                                    <i class="bi bi-person me-1"></i> View Member
                                                                </a>
                                                            <?php elseif ($notification['entity_type'] === 'loan'): ?>
                                                                <a href="../loans/view.php?id=<?php echo h($notification['entity_id']); ?>" class="btn btn-sm btn-outline-secondary">
                                                                    <i class="bi bi-journal-text me-1"></i> View Loan
                                                                </a>
                                                            <?php elseif ($notification['entity_type'] === 'report'): ?>
                                                                <a href="../reports/index.php" class="btn btn-sm btn-outline-secondary">
                                                                    <i class="bi bi-file-earmark-text me-1"></i> View Reports
                                                                </a>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                    </div>
                                                    <?php if (!$notification['is_read']): ?>
                                                        <a href="index.php?mark_read=<?php echo h($notification['id']); ?>" class="btn btn-sm btn-primary">
                                                            <i class="bi bi-check me-1"></i> Mark as Read
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="badge bg-light text-dark">Read</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo url('assets/js/dark-mode.js'); ?>"></script>
    <script src="<?php echo url('assets/js/notifications.js'); ?>"></script>
    <script src="<?php echo url('assets/js/notifications-page.js'); ?>"></script>
</body>
</html>
