<?php
/**
 * Comprehensive System Test for Library Management System
 * This script tests all major functionality and reports any issues
 */

// Start output buffering to capture any errors
ob_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>\n";
echo "<html lang='en'>\n";
echo "<head>\n";
echo "    <meta charset='UTF-8'>\n";
echo "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "    <title>Library Management System - Comprehensive Test</title>\n";
echo "    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>\n";
echo "    <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css'>\n";
echo "    <style>\n";
echo "        .test-pass { color: #28a745; }\n";
echo "        .test-fail { color: #dc3545; }\n";
echo "        .test-warning { color: #ffc107; }\n";
echo "        .test-section { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }\n";
echo "    </style>\n";
echo "</head>\n";
echo "<body>\n";
echo "<div class='container mt-4'>\n";
echo "<h1><i class='bi bi-gear-fill me-2'></i>Library Management System - Comprehensive Test</h1>\n";
echo "<p class='text-muted'>Testing all major functionality...</p>\n";

$test_results = [];
$total_tests = 0;
$passed_tests = 0;
$failed_tests = 0;
$warnings = 0;

function runTest($test_name, $test_function) {
    global $test_results, $total_tests, $passed_tests, $failed_tests, $warnings;
    
    $total_tests++;
    echo "<div class='test-section'>\n";
    echo "<h4><i class='bi bi-play-circle me-2'></i>$test_name</h4>\n";
    
    try {
        $result = $test_function();
        if ($result['status'] === 'pass') {
            echo "<p class='test-pass'><i class='bi bi-check-circle me-2'></i>{$result['message']}</p>\n";
            $passed_tests++;
        } elseif ($result['status'] === 'warning') {
            echo "<p class='test-warning'><i class='bi bi-exclamation-triangle me-2'></i>{$result['message']}</p>\n";
            $warnings++;
        } else {
            echo "<p class='test-fail'><i class='bi bi-x-circle me-2'></i>{$result['message']}</p>\n";
            $failed_tests++;
        }
        
        if (isset($result['details'])) {
            echo "<small class='text-muted'>{$result['details']}</small>\n";
        }
    } catch (Exception $e) {
        echo "<p class='test-fail'><i class='bi bi-x-circle me-2'></i>Test failed with exception: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        $failed_tests++;
    }
    
    echo "</div>\n";
    $test_results[] = ['name' => $test_name, 'result' => $result ?? ['status' => 'fail', 'message' => 'Exception occurred']];
}

// Test 1: Database Connection
runTest("Database Connection", function() {
    try {
        require_once 'config/database.php';
        $database = new Database();
        $db = $database->getConnection();
        
        if ($db) {
            return ['status' => 'pass', 'message' => 'Database connection successful'];
        } else {
            return ['status' => 'fail', 'message' => 'Database connection failed'];
        }
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'Database connection error: ' . $e->getMessage()];
    }
});

// Test 2: Configuration Files
runTest("Configuration Files", function() {
    $config_files = [
        'config/config.php',
        'config/database.php',
        'config/google_oauth.php',
        'includes/functions.php'
    ];
    
    $missing_files = [];
    foreach ($config_files as $file) {
        if (!file_exists($file)) {
            $missing_files[] = $file;
        }
    }
    
    if (empty($missing_files)) {
        return ['status' => 'pass', 'message' => 'All configuration files found'];
    } else {
        return ['status' => 'fail', 'message' => 'Missing files: ' . implode(', ', $missing_files)];
    }
});

// Test 3: Database Tables
runTest("Database Tables", function() {
    try {
        require_once 'config/database.php';
        $database = new Database();
        $db = $database->getConnection();
        
        $required_tables = ['users', 'members', 'books', 'book_loans', 'book_reservations'];
        $missing_tables = [];
        
        foreach ($required_tables as $table) {
            $stmt = $db->prepare("SHOW TABLES LIKE '$table'");
            $stmt->execute();
            if ($stmt->rowCount() == 0) {
                $missing_tables[] = $table;
            }
        }
        
        if (empty($missing_tables)) {
            return ['status' => 'pass', 'message' => 'All required database tables exist'];
        } else {
            return ['status' => 'fail', 'message' => 'Missing tables: ' . implode(', ', $missing_tables)];
        }
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'Database table check failed: ' . $e->getMessage()];
    }
});

// Test 4: Admin User Exists
runTest("Admin User Check", function() {
    try {
        require_once 'config/database.php';
        $database = new Database();
        $db = $database->getConnection();
        
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['count'] > 0) {
            return ['status' => 'pass', 'message' => 'Admin user(s) found in database'];
        } else {
            return ['status' => 'warning', 'message' => 'No admin users found. You may need to create one.'];
        }
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'Admin user check failed: ' . $e->getMessage()];
    }
});

// Test 5: Sample Data Check
runTest("Sample Data Check", function() {
    try {
        require_once 'config/database.php';
        $database = new Database();
        $db = $database->getConnection();
        
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM books");
        $stmt->execute();
        $book_count = $stmt->fetch()['count'];
        
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM members");
        $stmt->execute();
        $member_count = $stmt->fetch()['count'];
        
        if ($book_count > 0 && $member_count > 0) {
            return ['status' => 'pass', 'message' => "Sample data found: $book_count books, $member_count members"];
        } elseif ($book_count > 0 || $member_count > 0) {
            return ['status' => 'warning', 'message' => "Partial data: $book_count books, $member_count members"];
        } else {
            return ['status' => 'warning', 'message' => 'No sample data found. System is ready for data entry.'];
        }
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'Sample data check failed: ' . $e->getMessage()];
    }
});

// Test 6: Google OAuth Configuration
runTest("Google OAuth Configuration", function() {
    try {
        require_once 'config/config.php';
        require_once 'config/google_oauth.php';
        
        if (defined('GOOGLE_CLIENT_ID') && defined('GOOGLE_CLIENT_SECRET')) {
            if (GOOGLE_CLIENT_ID !== 'YOUR_GOOGLE_CLIENT_ID_HERE' && GOOGLE_CLIENT_SECRET !== 'YOUR_GOOGLE_CLIENT_SECRET_HERE') {
                return ['status' => 'pass', 'message' => 'Google OAuth credentials are configured'];
            } else {
                return ['status' => 'warning', 'message' => 'Google OAuth credentials need to be updated with real values'];
            }
        } else {
            return ['status' => 'fail', 'message' => 'Google OAuth constants not defined'];
        }
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'Google OAuth check failed: ' . $e->getMessage()];
    }
});

// Test 7: File Permissions
runTest("File Permissions", function() {
    $upload_dirs = ['uploads', 'uploads/covers', 'uploads/members'];
    $permission_issues = [];
    
    foreach ($upload_dirs as $dir) {
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                $permission_issues[] = "Cannot create directory: $dir";
            }
        } elseif (!is_writable($dir)) {
            $permission_issues[] = "Directory not writable: $dir";
        }
    }
    
    if (empty($permission_issues)) {
        return ['status' => 'pass', 'message' => 'All upload directories are writable'];
    } else {
        return ['status' => 'warning', 'message' => 'Permission issues: ' . implode(', ', $permission_issues)];
    }
});

// Test 8: Session Functionality
runTest("Session Functionality", function() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    $_SESSION['test_key'] = 'test_value';
    
    if (isset($_SESSION['test_key']) && $_SESSION['test_key'] === 'test_value') {
        unset($_SESSION['test_key']);
        return ['status' => 'pass', 'message' => 'Session functionality working correctly'];
    } else {
        return ['status' => 'fail', 'message' => 'Session functionality not working'];
    }
});

echo "<div class='mt-4 p-4 bg-light rounded'>\n";
echo "<h3>Test Summary</h3>\n";
echo "<div class='row'>\n";
echo "<div class='col-md-3'><strong>Total Tests:</strong> $total_tests</div>\n";
echo "<div class='col-md-3 test-pass'><strong>Passed:</strong> $passed_tests</div>\n";
echo "<div class='col-md-3 test-fail'><strong>Failed:</strong> $failed_tests</div>\n";
echo "<div class='col-md-3 test-warning'><strong>Warnings:</strong> $warnings</div>\n";
echo "</div>\n";

$success_rate = $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 1) : 0;
echo "<div class='mt-3'>\n";
echo "<strong>Success Rate:</strong> $success_rate%\n";
echo "<div class='progress mt-2'>\n";
echo "<div class='progress-bar bg-success' style='width: {$success_rate}%'></div>\n";
echo "</div>\n";
echo "</div>\n";
echo "</div>\n";

echo "<div class='mt-4'>\n";
echo "<h3>Quick Access Links</h3>\n";
echo "<div class='row'>\n";
echo "<div class='col-md-4'><a href='index.php' class='btn btn-primary w-100'>Home Page</a></div>\n";
echo "<div class='col-md-4'><a href='login.php' class='btn btn-secondary w-100'>Login Page</a></div>\n";
echo "<div class='col-md-4'><a href='admin/dashboard.php' class='btn btn-success w-100'>Admin Dashboard</a></div>\n";
echo "</div>\n";
echo "<div class='row mt-2'>\n";
echo "<div class='col-md-4'><a href='member_dashboard.php' class='btn btn-info w-100'>Member Dashboard</a></div>\n";
echo "<div class='col-md-4'><a href='catalog.php' class='btn btn-warning w-100'>Book Catalog</a></div>\n";
echo "<div class='col-md-4'><a href='register.php' class='btn btn-outline-primary w-100'>Registration</a></div>\n";
echo "</div>\n";
echo "</div>\n";

echo "</div>\n";
echo "</body>\n";
echo "</html>\n";

// Capture any output and clean buffer
$output = ob_get_clean();
echo $output;
?>
