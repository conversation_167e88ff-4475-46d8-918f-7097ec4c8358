<?php
/**
 * Test Access to Diagnostic Files
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Access Test for Diagnostic Files</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .test-link { display: inline-block; margin: 5px; padding: 8px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
    .test-link:hover { background: #0056b3; color: white; text-decoration: none; }
</style>";

echo "<div class='test-section'>";
echo "<h2>🔍 File Access Test</h2>";

$diagnostic_files = [
    'diagnostic.php' => 'Main Diagnostic Page',
    'database_status.php' => 'Database Status Check',
    'troubleshoot.php' => 'Troubleshooting Guide',
    'admin/ajax/test_database.php' => 'Advanced Database Test',
    'test_diagnostics.php' => 'Diagnostics Test Suite'
];

foreach ($diagnostic_files as $file => $description) {
    echo "<div class='info'>";
    echo "<h4>Testing: $description ($file)</h4>";
    
    // Check if file exists
    if (file_exists($file)) {
        echo "<div class='success'>✅ File exists</div>";
        
        // Check if file is readable
        if (is_readable($file)) {
            echo "<div class='success'>✅ File is readable</div>";
        } else {
            echo "<div class='error'>❌ File is not readable</div>";
        }
        
        // Test HTTP access using cURL if available
        if (function_exists('curl_init')) {
            $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $file;
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
            
            $result = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code == 200) {
                echo "<div class='success'>✅ HTTP access successful (Status: $http_code)</div>";
            } else {
                echo "<div class='error'>❌ HTTP access failed (Status: $http_code)</div>";
            }
        }
        
        echo "<p><a href='$file' target='_blank' class='test-link'>🔗 Test Access</a></p>";
        
    } else {
        echo "<div class='error'>❌ File does not exist</div>";
    }
    echo "</div><hr>";
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>🔧 .htaccess Configuration Check</h2>";

$htaccess_files = [
    '.htaccess' => 'Main .htaccess',
    'admin/.htaccess' => 'Admin .htaccess',
    'admin/ajax/.htaccess' => 'Admin AJAX .htaccess'
];

foreach ($htaccess_files as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ $description exists</div>";
        $content = file_get_contents($file);
        if (strpos($content, 'Allow from all') !== false) {
            echo "<div class='success'>✅ Contains 'Allow from all' rules</div>";
        }
    } else {
        echo "<div class='warning'>⚠️ $description missing</div>";
    }
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>📊 Server Information</h2>";
echo "<div class='info'>";
echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";
echo "<p><strong>Request URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>";
echo "</div>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>🎯 Quick Actions</h2>";
echo "<p><a href='admin/dashboard.php' class='test-link'>📊 Go to Admin Dashboard</a></p>";
echo "<p><a href='test_diagnostics.php' class='test-link'>🔧 Run Full Diagnostics Test</a></p>";
echo "</div>";
