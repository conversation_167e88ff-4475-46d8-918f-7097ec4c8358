<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get a few books to test
$query = "SELECT * FROM books WHERE cover_image IS NOT NULL AND cover_image != '' LIMIT 5";
$stmt = $db->prepare($query);
$stmt->execute();
$books = $stmt->fetchAll();

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Catalog Images</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .card-img-top {
            height: 250px;
            object-fit: cover;
        }
        .card-img-placeholder {
            height: 250px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>Test Catalog Images</h2>
        
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4 mb-4">
            <?php if (count($books) > 0): ?>
                <?php foreach ($books as $book): ?>
                    <div class="col">
                        <div class="card h-100">
                            <?php if (!empty($book['cover_image'])): ?>
                                <?php
                                // Check if the cover_image is a URL or a local file
                                $image_src = (strpos($book['cover_image'], 'http') === 0)
                                    ? $book['cover_image']
                                    : url('uploads/covers/' . $book['cover_image']);
                                ?>
                                <div class="text-center p-2">
                                    <small>Image source: <?php echo h($image_src); ?></small><br>
                                    <small>File exists: <?php echo file_exists('uploads/covers/' . $book['cover_image']) ? 'Yes' : 'No'; ?></small>
                                </div>
                                <img src="<?php echo h($image_src); ?>" class="card-img-top" alt="<?php echo h($book['title']); ?>" 
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div class="card-img-placeholder" style="display: none;">
                                    <i class="bi bi-book fs-1 text-secondary"></i>
                                </div>
                            <?php else: ?>
                                <div class="card-img-placeholder">
                                    <i class="bi bi-book fs-1 text-secondary"></i>
                                </div>
                            <?php endif; ?>
                            <div class="card-body">
                                <h5 class="card-title"><?php echo h($book['title']); ?></h5>
                                <h6 class="card-subtitle mb-2 text-muted"><?php echo h($book['author']); ?></h6>
                                <p class="card-text">
                                    <small class="text-muted">
                                        <?php if (!empty($book['category'])): ?>
                                            <span class="badge bg-secondary"><?php echo h($book['category']); ?></span>
                                        <?php endif; ?>
                                        <?php if (!empty($book['publication_year'])): ?>
                                            <span class="badge bg-light text-dark"><?php echo h($book['publication_year']); ?></span>
                                        <?php endif; ?>
                                    </small>
                                </p>
                                <p class="card-text">
                                    <small class="text-success">
                                        Available: <?php echo h($book['available_quantity']); ?> of <?php echo h($book['quantity']); ?>
                                    </small>
                                </p>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12">
                    <div class="alert alert-info">
                        No books with cover images found.
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <h3>Debug Information</h3>
        <div class="alert alert-info">
            <p><strong>Base URL:</strong> <?php echo BASE_URL; ?></p>
            <p><strong>Covers Path:</strong> <?php echo COVERS_PATH; ?></p>
            <p><strong>url() function test:</strong> <?php echo url('uploads/covers/test.jpg'); ?></p>
        </div>
        
        <h3>Direct Image Tests</h3>
        <div class="row">
            <?php
            $covers_dir = 'uploads/covers/';
            if (is_dir($covers_dir)) {
                $files = scandir($covers_dir);
                $count = 0;
                foreach ($files as $file) {
                    if ($file != '.' && $file != '..' && in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif']) && $count < 3) {
                        echo "<div class='col-md-4 mb-3'>";
                        echo "<div class='card'>";
                        echo "<img src='" . $covers_dir . $file . "' class='card-img-top' style='height: 200px; object-fit: cover;' alt='" . htmlspecialchars($file) . "'>";
                        echo "<div class='card-body'>";
                        echo "<p class='card-text'><small>" . htmlspecialchars($file) . "</small></p>";
                        echo "</div>";
                        echo "</div>";
                        echo "</div>";
                        $count++;
                    }
                }
            }
            ?>
        </div>
    </div>
</body>
</html>
