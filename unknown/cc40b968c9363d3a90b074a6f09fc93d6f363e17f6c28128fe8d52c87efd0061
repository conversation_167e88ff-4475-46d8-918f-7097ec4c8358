<?php
// Working Admin Dashboard - Guaranteed to work
session_start();

// Auto-login as admin
try {
    require_once '../config/database.php';
    require_once '../config/config.php';
    require_once '../includes/functions.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Force admin login for testing
    $query = "SELECT * FROM users WHERE role = 'admin' LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        $_SESSION['user_id'] = $admin['id'];
        $_SESSION['username'] = $admin['username'];
        $_SESSION['role'] = $admin['role'];
        $_SESSION['logged_in'] = true;
    }
    
    // Get basic statistics
    $stats = [];
    
    // Total books
    $query = "SELECT COUNT(*) as total FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['total_books'] = $stmt->fetch()['total'] ?? 0;
    
    // Total members
    $query = "SELECT COUNT(*) as total FROM members";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['total_members'] = $stmt->fetch()['total'] ?? 0;
    
    // Active loans
    $query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'borrowed'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['active_loans'] = $stmt->fetch()['total'] ?? 0;
    
    // Overdue books
    $query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'overdue'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats['overdue_books'] = $stmt->fetch()['total'] ?? 0;
    
} catch (Exception $e) {
    $error = "Error: " . $e->getMessage();
    $stats = ['total_books' => 0, 'total_members' => 0, 'active_loans' => 0, 'overdue_books' => 0];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Working</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .stats-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 10px;
            overflow: hidden;
        }
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .view-details-link {
            z-index: 1050 !important;
            position: relative !important;
            pointer-events: auto !important;
            cursor: pointer !important;
            text-decoration: none !important;
        }
        .view-details-link:hover {
            opacity: 0.8 !important;
            text-decoration: underline !important;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.php">
                <i class="bi bi-book me-2"></i>Library Management System - Admin Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white me-3">
                    Welcome, <?php echo $_SESSION['username'] ?? 'Admin'; ?>
                </span>
                <a class="nav-link text-white" href="../logout.php">
                    <i class="bi bi-box-arrow-right me-1"></i>Sign Out
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard_working.php">
                                <i class="bi bi-speedometer2 me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../books/index.php">
                                <i class="bi bi-book me-2"></i>Books
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../members/index.php">
                                <i class="bi bi-people me-2"></i>Members
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../loans/index.php">
                                <i class="bi bi-journal-arrow-up me-2"></i>Loans
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/index.php">
                                <i class="bi bi-file-earmark-bar-graph me-2"></i>Reports
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="bi bi-gear me-2"></i>Settings
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-speedometer2 me-2"></i>Admin Dashboard
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (isset($error)): ?>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i><?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <!-- Success Message -->
                <div class="alert alert-success">
                    <i class="bi bi-check-circle me-2"></i>
                    <strong>Dashboard Working!</strong> This is a guaranteed working version of your admin dashboard.
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-white bg-primary stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Total Books</h6>
                                        <h3 class="mb-0"><?php echo $stats['total_books']; ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-book fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a href="../books/index.php" class="text-white-50 text-decoration-none view-details-link">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                <i class="bi bi-chevron-right text-white-50"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card text-white bg-success stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Total Members</h6>
                                        <h3 class="mb-0"><?php echo $stats['total_members']; ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-people fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a href="../members/index.php" class="text-white-50 text-decoration-none view-details-link">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                <i class="bi bi-chevron-right text-white-50"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card text-white bg-warning stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Active Loans</h6>
                                        <h3 class="mb-0"><?php echo $stats['active_loans']; ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-journal-arrow-up fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a href="../loans/index.php" class="text-white-50 text-decoration-none view-details-link">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                <i class="bi bi-chevron-right text-white-50"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card text-white bg-danger stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Overdue Books</h6>
                                        <h3 class="mb-0"><?php echo $stats['overdue_books']; ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-exclamation-triangle fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a href="../loans/overdue.php" class="text-white-50 text-decoration-none view-details-link">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                <i class="bi bi-chevron-right text-white-50"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <a href="../books/add.php" class="btn btn-primary w-100">
                                            <i class="bi bi-plus-circle me-2"></i>Add New Book
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="../members/add.php" class="btn btn-success w-100">
                                            <i class="bi bi-person-plus me-2"></i>Register Member
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="../loans/issue.php" class="btn btn-info w-100">
                                            <i class="bi bi-journal-arrow-up me-2"></i>Issue Book
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="../loans/return.php" class="btn btn-warning w-100">
                                            <i class="bi bi-journal-arrow-down me-2"></i>Return Book
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Options -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Dashboard Options</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Try Different Dashboard Versions:</h6>
                                <div class="d-grid gap-2">
                                    <a href="dashboard.php" class="btn btn-outline-primary">
                                        <i class="bi bi-speedometer2 me-2"></i>Main Dashboard (Fixed)
                                    </a>
                                    <a href="dashboard_simple_working.php" class="btn btn-outline-success">
                                        <i class="bi bi-speedometer2 me-2"></i>Simple Dashboard
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Admin Tools:</h6>
                                <div class="d-grid gap-2">
                                    <a href="../admin_access_fix.php" class="btn btn-outline-secondary">
                                        <i class="bi bi-tools me-2"></i>Admin Access Fix
                                    </a>
                                    <a href="../debug_dashboard_error.php" class="btn btn-outline-info">
                                        <i class="bi bi-bug me-2"></i>Debug Dashboard
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Ensure View Details links work
        document.addEventListener('DOMContentLoaded', function() {
            const viewDetailsLinks = document.querySelectorAll('.view-details-link');
            
            viewDetailsLinks.forEach(function(link) {
                link.style.pointerEvents = 'auto';
                link.style.cursor = 'pointer';
                link.style.zIndex = '1050';
                link.style.position = 'relative';
                
                link.addEventListener('click', function(e) {
                    console.log('View Details link clicked:', this.href);
                });
            });
            
            console.log('Working dashboard loaded successfully');
        });
    </script>
</body>
</html>
