<?php
/**
 * Add Google Authentication Support
 *
 * This script adds the necessary database columns for Google authentication.
 */

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Create database connection
$database = new Database();
$db = $database->getConnection();

// Check if the script is being run directly
$is_direct_access = (basename($_SERVER['SCRIPT_FILENAME']) === basename(__FILE__));

// Function to check if a column exists in a table
function columnExists($db, $table, $column) {
    $query = "SHOW COLUMNS FROM $table LIKE '$column'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    return $stmt->rowCount() > 0;
}

// Function to add a column if it doesn't exist
function addColumnIfNotExists($db, $table, $column, $definition) {
    if (!columnExists($db, $table, $column)) {
        $query = "ALTER TABLE $table ADD COLUMN $column $definition";
        $stmt = $db->prepare($query);
        return $stmt->execute();
    }
    return false;
}

// Add Google authentication columns to users table
$usersColumnsAdded = [
    'google_id' => addColumnIfNotExists($db, 'users', 'google_id', 'VARCHAR(255) NULL'),
    'google_token' => addColumnIfNotExists($db, 'users', 'google_token', 'TEXT NULL'),
    'google_picture' => addColumnIfNotExists($db, 'users', 'google_picture', 'VARCHAR(255) NULL')
];

// Add Google authentication columns to members table
$membersColumnsAdded = [
    'google_id' => addColumnIfNotExists($db, 'members', 'google_id', 'VARCHAR(255) NULL'),
    'google_token' => addColumnIfNotExists($db, 'members', 'google_token', 'TEXT NULL'),
    'google_picture' => addColumnIfNotExists($db, 'members', 'google_picture', 'VARCHAR(255) NULL'),
    'remember_token' => addColumnIfNotExists($db, 'members', 'remember_token', 'VARCHAR(255) NULL')
];

// Add indexes for faster lookups if columns were added
if ($usersColumnsAdded['google_id']) {
    try {
        $query = "ALTER TABLE users ADD INDEX idx_google_id (google_id)";
        $stmt = $db->prepare($query);
        $stmt->execute();
    } catch (PDOException $e) {
        // Index might already exist, ignore
    }
}

if ($membersColumnsAdded['google_id']) {
    try {
        $query = "ALTER TABLE members ADD INDEX idx_google_id (google_id)";
        $stmt = $db->prepare($query);
        $stmt->execute();
    } catch (PDOException $e) {
        // Index might already exist, ignore
    }
}

// Output results if script is being run directly
if ($is_direct_access) {
    echo "<h1>Google Authentication Setup</h1>";

    echo "<h2>Users Table Changes:</h2>";
    echo "<ul>";
    foreach ($usersColumnsAdded as $column => $added) {
        if ($added) {
            echo "<li>Added column '$column' to users table</li>";
        } else {
            echo "<li>Column '$column' already exists in users table</li>";
        }
    }
    echo "</ul>";

    echo "<h2>Members Table Changes:</h2>";
    echo "<ul>";
    foreach ($membersColumnsAdded as $column => $added) {
        if ($added) {
            echo "<li>Added column '$column' to members table</li>";
        } else {
            echo "<li>Column '$column' already exists in members table</li>";
        }
    }
    echo "</ul>";

    echo "<p>Google authentication database setup is complete.</p>";
    echo "<p><a href='login.php' class='btn btn-primary'>Go to Login Page</a></p>";
}
?>
