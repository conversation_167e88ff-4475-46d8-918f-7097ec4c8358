<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>View Details Links Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css'>";
echo "<link rel='stylesheet' href='admin/css/dashboard-fixes.css'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<h1 class='text-center mb-4'>🔗 View Details Links Test</h1>";

// Test database connection
echo "<div class='row mb-4'>";
echo "<div class='col-12'>";
echo "<div class='alert alert-info'>";
echo "<h4><i class='bi bi-info-circle'></i> Testing Database Connection</h4>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<p class='text-success'>✅ Database connected successfully</p>";
        
        // Test basic queries
        $query = "SELECT COUNT(*) as count FROM books";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $book_count = $stmt->fetch()['count'];
        
        $query = "SELECT COUNT(*) as count FROM members";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $member_count = $stmt->fetch()['count'];
        
        $query = "SELECT COUNT(*) as count FROM book_loans WHERE status != 'returned'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $loan_count = $stmt->fetch()['count'];
        
        echo "<p>📚 Books: $book_count | 👥 Members: $member_count | 📖 Active Loans: $loan_count</p>";
        
    } else {
        echo "<p class='text-danger'>❌ Database connection failed</p>";
        exit;
    }
} catch (Exception $e) {
    echo "<p class='text-danger'>❌ Database error: " . $e->getMessage() . "</p>";
    exit;
}

echo "</div>";
echo "</div>";
echo "</div>";

// Test the View Details links with the same structure as dashboard
echo "<div class='row mb-4'>";

// Books card
echo "<div class='col-md-3'>";
echo "<div class='card text-white bg-primary stats-card'>";
echo "<div class='card-body'>";
echo "<div class='d-flex justify-content-between align-items-center'>";
echo "<div>";
echo "<h6 class='text-white-50'>Book Titles</h6>";
echo "<h3 class='mb-0'>$book_count</h3>";
echo "<small class='text-white-50'>Unique books</small>";
echo "</div>";
echo "<div>";
echo "<i class='bi bi-book fs-1'></i>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "<div class='card-footer d-flex align-items-center justify-content-between'>";
echo "<a href='" . url('books/index.php') . "' class='text-white-50 text-decoration-none view-details-link'>";
echo "<i class='bi bi-eye me-1'></i>View Details";
echo "</a>";
echo "<i class='bi bi-chevron-right text-white-50'></i>";
echo "</div>";
echo "</div>";
echo "</div>";

// Members card
echo "<div class='col-md-3'>";
echo "<div class='card text-white bg-info stats-card'>";
echo "<div class='card-body'>";
echo "<div class='d-flex justify-content-between align-items-center'>";
echo "<div>";
echo "<h6 class='text-white-50'>Total Members</h6>";
echo "<h3 class='mb-0'>$member_count</h3>";
echo "</div>";
echo "<div>";
echo "<i class='bi bi-people fs-1'></i>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "<div class='card-footer d-flex align-items-center justify-content-between'>";
echo "<a href='" . url('members/index.php') . "' class='text-white-50 text-decoration-none view-details-link'>";
echo "<i class='bi bi-eye me-1'></i>View Details";
echo "</a>";
echo "<i class='bi bi-chevron-right text-white-50'></i>";
echo "</div>";
echo "</div>";
echo "</div>";

// Loans card
echo "<div class='col-md-3'>";
echo "<div class='card text-white bg-warning stats-card'>";
echo "<div class='card-body'>";
echo "<div class='d-flex justify-content-between align-items-center'>";
echo "<div>";
echo "<h6 class='text-white-50'>Active Loans</h6>";
echo "<h3 class='mb-0'>$loan_count</h3>";
echo "</div>";
echo "<div>";
echo "<i class='bi bi-journal-arrow-up fs-1'></i>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "<div class='card-footer d-flex align-items-center justify-content-between'>";
echo "<a href='" . url('loans/index.php') . "' class='text-white-50 text-decoration-none view-details-link'>";
echo "<i class='bi bi-eye me-1'></i>View Details";
echo "</a>";
echo "<i class='bi bi-chevron-right text-white-50'></i>";
echo "</div>";
echo "</div>";
echo "</div>";

// Overdue card
echo "<div class='col-md-3'>";
echo "<div class='card text-white bg-danger stats-card'>";
echo "<div class='card-body'>";
echo "<div class='d-flex justify-content-between align-items-center'>";
echo "<div>";
echo "<h6 class='text-white-50'>Overdue Books</h6>";
echo "<h3 class='mb-0'>75</h3>";
echo "</div>";
echo "<div>";
echo "<i class='bi bi-exclamation-triangle fs-1'></i>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "<div class='card-footer d-flex align-items-center justify-content-between'>";
echo "<a href='" . url('loans/overdue.php') . "' class='text-white-50 text-decoration-none view-details-link'>";
echo "<i class='bi bi-eye me-1'></i>View Details";
echo "</a>";
echo "<i class='bi bi-chevron-right text-white-50'></i>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>";

// Instructions
echo "<div class='alert alert-success'>";
echo "<h4><i class='bi bi-check-circle'></i> Testing Instructions</h4>";
echo "<ol>";
echo "<li>Click each 'View Details' link above</li>";
echo "<li>Each link should navigate to the correct management page</li>";
echo "<li>Check the browser console (F12) for any JavaScript errors</li>";
echo "<li>If links work here, go to your admin dashboard and test there</li>";
echo "</ol>";
echo "</div>";

// Quick links
echo "<div class='text-center mt-4'>";
echo "<a href='admin/dashboard.php' class='btn btn-primary btn-lg me-2'>";
echo "<i class='bi bi-speedometer2 me-2'></i>Go to Admin Dashboard";
echo "</a>";
echo "<a href='quick_admin_login.php' class='btn btn-success btn-lg'>";
echo "<i class='bi bi-box-arrow-in-right me-2'></i>Quick Admin Login";
echo "</a>";
echo "</div>";

echo "</div>";

// JavaScript to test the links
echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js'></script>";
echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    console.log('Testing View Details links...');";
echo "    ";
echo "    const viewDetailsLinks = document.querySelectorAll('.view-details-link');";
echo "    const cardFooterLinks = document.querySelectorAll('.card-footer a');";
echo "    const allLinks = [...viewDetailsLinks, ...cardFooterLinks];";
echo "    ";
echo "    allLinks.forEach(function(link, index) {";
echo "        link.style.pointerEvents = 'auto';";
echo "        link.style.cursor = 'pointer';";
echo "        link.style.zIndex = '1050';";
echo "        link.style.position = 'relative';";
echo "        ";
echo "        link.addEventListener('click', function(e) {";
echo "            console.log('Link clicked:', this.href);";
echo "            ";
echo "            if (!this.href || this.href === '#' || this.href.includes('undefined')) {";
echo "                e.preventDefault();";
echo "                alert('Error: Invalid link - ' + this.href);";
echo "                return false;";
echo "            }";
echo "            ";
echo "            this.style.transform = 'scale(0.95)';";
echo "            setTimeout(() => {";
echo "                this.style.transform = 'scale(1)';";
echo "            }, 150);";
echo "            ";
echo "            return true;";
echo "        });";
echo "        ";
echo "        link.addEventListener('mouseenter', function() {";
echo "            this.style.opacity = '0.8';";
echo "        });";
echo "        ";
echo "        link.addEventListener('mouseleave', function() {";
echo "            this.style.opacity = '1';";
echo "        });";
echo "    });";
echo "    ";
echo "    console.log('Processed', allLinks.length, 'View Details links');";
echo "    allLinks.forEach((link, i) => {";
echo "        console.log('Link ' + (i + 1) + ':', link.href, '|', link.textContent.trim());";
echo "    });";
echo "});";
echo "</script>";

echo "</body>";
echo "</html>";
?>
