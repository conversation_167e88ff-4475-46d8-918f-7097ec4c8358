<?php
/**
 * Simple Email Settings Page (Backup)
 */

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Initialize variables
$success_message = '';
$error_message = '';

// Connect to database
try {
    $database = new Database();
    $db = $database->getConnection();
} catch (Exception $e) {
    $error_message = "Database connection failed: " . $e->getMessage();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($db)) {
    try {
        // Create settings table if it doesn't exist
        $create_table_sql = "CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_group VARCHAR(50) NOT NULL,
            setting_key VARCHAR(100) NOT NULL,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_setting (setting_group, setting_key)
        )";
        $db->exec($create_table_sql);

        if (isset($_POST['save_email_settings'])) {
            // Save email settings
            $settings = [
                'from_email' => $_POST['from_email'] ?? '<EMAIL>',
                'from_name' => $_POST['from_name'] ?? 'Library Management System',
                'smtp_enabled' => isset($_POST['smtp_enabled']) ? 'true' : 'false',
                'smtp_host' => $_POST['smtp_host'] ?? '',
                'smtp_port' => $_POST['smtp_port'] ?? '587'
            ];

            foreach ($settings as $key => $value) {
                $query = "INSERT INTO settings (setting_group, setting_key, setting_value) 
                          VALUES ('email', :key, :value)
                          ON DUPLICATE KEY UPDATE setting_value = :value";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':key', $key);
                $stmt->bindParam(':value', $value);
                $stmt->execute();
            }

            $success_message = "Email settings saved successfully!";
        }
    } catch (Exception $e) {
        $error_message = "Error saving settings: " . $e->getMessage();
    }
}

// Get current settings
$current_settings = [];
if (isset($db)) {
    try {
        $query = "SELECT setting_key, setting_value FROM settings WHERE setting_group = 'email'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        while ($row = $stmt->fetch()) {
            $current_settings[$row['setting_key']] = $row['setting_value'];
        }
    } catch (Exception $e) {
        // Ignore errors, use defaults
    }
}

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Email Settings - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body { background-color: #f8f9fa; }
        .settings-card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card settings-card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0"><i class="bi bi-envelope me-2"></i>Simple Email Settings</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($success_message): ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle me-2"></i><?php echo h($success_message); ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($error_message): ?>
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle me-2"></i><?php echo h($error_message); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="from_email" class="form-label">From Email</label>
                                    <input type="email" class="form-control" id="from_email" name="from_email" 
                                           value="<?php echo h($current_settings['from_email'] ?? '<EMAIL>'); ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="from_name" class="form-label">From Name</label>
                                    <input type="text" class="form-control" id="from_name" name="from_name" 
                                           value="<?php echo h($current_settings['from_name'] ?? 'Library Management System'); ?>" required>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="smtp_enabled" name="smtp_enabled" 
                                       <?php echo (($current_settings['smtp_enabled'] ?? 'false') === 'true') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="smtp_enabled">
                                    Use SMTP Server (Advanced)
                                </label>
                            </div>

                            <div id="smtp_settings" class="<?php echo (($current_settings['smtp_enabled'] ?? 'false') === 'true') ? '' : 'd-none'; ?>">
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label for="smtp_host" class="form-label">SMTP Host</label>
                                        <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                               value="<?php echo h($current_settings['smtp_host'] ?? 'smtp.gmail.com'); ?>" 
                                               placeholder="smtp.gmail.com">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="smtp_port" class="form-label">SMTP Port</label>
                                        <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                               value="<?php echo h($current_settings['smtp_port'] ?? '587'); ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" name="save_email_settings" class="btn btn-primary">
                                    <i class="bi bi-save me-2"></i>Save Email Settings
                                </button>
                            </div>
                        </form>

                        <hr>

                        <div class="mt-4">
                            <h6>Current Configuration:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>From Email:</strong> <?php echo h($current_settings['from_email'] ?? 'Not set'); ?><br>
                                        <strong>From Name:</strong> <?php echo h($current_settings['from_name'] ?? 'Not set'); ?><br>
                                        <strong>SMTP Enabled:</strong> <?php echo (($current_settings['smtp_enabled'] ?? 'false') === 'true') ? 'Yes' : 'No'; ?>
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>SMTP Host:</strong> <?php echo h($current_settings['smtp_host'] ?? 'Not set'); ?><br>
                                        <strong>SMTP Port:</strong> <?php echo h($current_settings['smtp_port'] ?? 'Not set'); ?><br>
                                        <strong>Database:</strong> <?php echo isset($db) ? 'Connected' : 'Not connected'; ?>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h6>Navigation:</h6>
                            <div class="btn-group" role="group">
                                <a href="email_settings.php" class="btn btn-outline-primary">Full Email Settings</a>
                                <a href="settings.php" class="btn btn-outline-secondary">General Settings</a>
                                <a href="dashboard.php" class="btn btn-outline-success">Dashboard</a>
                                <a href="../login.php" class="btn btn-outline-info">Login</a>
                            </div>
                        </div>

                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle me-2"></i>Debug Information:</h6>
                                <small>
                                    <strong>File:</strong> <?php echo __FILE__; ?><br>
                                    <strong>Time:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
                                    <strong>Settings Count:</strong> <?php echo count($current_settings); ?><br>
                                    <strong>Database Status:</strong> <?php echo isset($db) ? 'Connected' : 'Failed'; ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const smtpCheckbox = document.getElementById('smtp_enabled');
            const smtpSettings = document.getElementById('smtp_settings');
            
            if (smtpCheckbox && smtpSettings) {
                smtpCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        smtpSettings.classList.remove('d-none');
                    } else {
                        smtpSettings.classList.add('d-none');
                    }
                });
            }
        });
    </script>
</body>
</html>
