<?php
/**
 * Remove 3000 Members - Keep Only 1000+
 * Simple script to remove excess members and auto-adjust loans
 */

require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<h1>🗑️ Remove 3000 Members</h1>";
echo "<p><strong>Target: Keep only 1000+ members, remove the rest</strong></p>";

try {
    // Step 1: Check current count
    $current_query = "SELECT COUNT(*) as count FROM members";
    $current_stmt = $db->prepare($current_query);
    $current_stmt->execute();
    $current_count = $current_stmt->fetch()['count'];
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>📊 Current Status</h3>";
    echo "<p><strong>Current Members:</strong> {$current_count}</p>";
    
    if ($current_count <= 1100) {
        echo "<p style='color: green;'>✅ Member count is already good! No need to remove.</p>";
        echo "</div>";
        exit;
    }
    
    $to_remove = $current_count - 1000;
    echo "<p><strong>Members to Remove:</strong> {$to_remove}</p>";
    echo "<p><strong>Members to Keep:</strong> 1000</p>";
    echo "</div>";
    
    // Step 2: Get current loan stats
    $loan_stats = [
        'active' => $db->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed'")->fetch()['count'],
        'overdue' => $db->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'overdue'")->fetch()['count'],
        'returned' => $db->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'returned'")->fetch()['count'],
        'total' => $db->query("SELECT COUNT(*) as count FROM book_loans")->fetch()['count']
    ];
    
    echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>📚 Current Loan Stats</h3>";
    echo "<p><strong>Active Loans:</strong> {$loan_stats['active']}</p>";
    echo "<p><strong>Overdue Loans:</strong> {$loan_stats['overdue']}</p>";
    echo "<p><strong>Returned Loans:</strong> {$loan_stats['returned']}</p>";
    echo "<p><strong>Total Loans:</strong> {$loan_stats['total']}</p>";
    echo "</div>";
    
    echo "<h2>🚀 Starting Removal Process...</h2>";
    
    // Step 3: Delete members (keep first 1000 by ID)
    echo "<h3>Step 1: Removing excess members...</h3>";
    
    // First, delete loans for members that will be removed
    $delete_loans_sql = "DELETE FROM book_loans 
                        WHERE member_id NOT IN (
                            SELECT id FROM (
                                SELECT id FROM members ORDER BY id LIMIT 1000
                            ) AS temp
                        )";
    
    $delete_loans_stmt = $db->prepare($delete_loans_sql);
    $delete_loans_stmt->execute();
    $deleted_loans = $delete_loans_stmt->rowCount();
    
    echo "<p>🗑️ Deleted {$deleted_loans} loans from members that will be removed</p>";
    
    // Then delete the excess members
    $delete_members_sql = "DELETE FROM members 
                          WHERE id NOT IN (
                              SELECT id FROM (
                                  SELECT id FROM members ORDER BY id LIMIT 1000
                              ) AS temp
                          )";
    
    $delete_members_stmt = $db->prepare($delete_members_sql);
    $delete_members_stmt->execute();
    $deleted_members = $delete_members_stmt->rowCount();
    
    echo "<p>🗑️ Deleted {$deleted_members} excess members</p>";
    
    // Step 4: Update overdue status for remaining loans
    echo "<h3>Step 2: Updating loan statuses...</h3>";
    
    $update_overdue_sql = "UPDATE book_loans 
                          SET status = 'overdue'
                          WHERE status = 'borrowed' 
                          AND due_date < CURDATE()";
    
    $update_overdue_stmt = $db->prepare($update_overdue_sql);
    $update_overdue_stmt->execute();
    $updated_overdue = $update_overdue_stmt->rowCount();
    
    echo "<p>📅 Updated {$updated_overdue} loans to overdue status</p>";
    
    // Step 5: Calculate fines for overdue books
    $calculate_fines_sql = "UPDATE book_loans 
                           SET fine = DATEDIFF(CURDATE(), due_date) * 1.00
                           WHERE status = 'overdue' 
                           AND due_date < CURDATE()
                           AND fine = 0";
    
    $calculate_fines_stmt = $db->prepare($calculate_fines_sql);
    $calculate_fines_stmt->execute();
    $calculated_fines = $calculate_fines_stmt->rowCount();
    
    echo "<p>💰 Calculated fines for {$calculated_fines} overdue books</p>";
    
    echo "<hr>";
    
    // Step 6: Show final results
    echo "<h2>📊 Final Results</h2>";
    
    $final_members = $db->query("SELECT COUNT(*) as count FROM members")->fetch()['count'];
    $final_active = $db->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed'")->fetch()['count'];
    $final_overdue = $db->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'overdue'")->fetch()['count'];
    $final_returned = $db->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'returned'")->fetch()['count'];
    $final_total = $db->query("SELECT COUNT(*) as count FROM book_loans")->fetch()['count'];
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 5px;'>";
    echo "<h3>✅ Removal Complete!</h3>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0;'>";
    echo "<div><strong>👥 Total Members:</strong> {$final_members}</div>";
    echo "<div><strong>✅ Active Loans:</strong> {$final_active}</div>";
    echo "<div><strong>⚠️ Overdue Books:</strong> {$final_overdue}</div>";
    echo "<div><strong>📚 Returned Loans:</strong> {$final_returned}</div>";
    echo "<div><strong>📋 Total Loans:</strong> {$final_total}</div>";
    echo "</div>";
    echo "</div>";
    
    // Show the changes
    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📈 Summary of Changes</h3>";
    echo "<ul>";
    echo "<li><strong>Members:</strong> {$current_count} → {$final_members} (-{$deleted_members})</li>";
    echo "<li><strong>Active Loans:</strong> {$loan_stats['active']} → {$final_active} (-" . ($loan_stats['active'] - $final_active) . ")</li>";
    echo "<li><strong>Total Loans:</strong> {$loan_stats['total']} → {$final_total} (-{$deleted_loans})</li>";
    echo "</ul>";
    echo "<p><strong>🎉 Your dashboard is now balanced!</strong></p>";
    echo "<p><a href='admin/dashboard.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 10px;'>🔄 View Updated Dashboard</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error Occurred</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

p {
    margin: 10px 0;
}

hr {
    margin: 30px 0;
    border: none;
    border-top: 2px solid #dee2e6;
}

ul {
    margin: 15px 0;
    padding-left: 20px;
}

li {
    margin: 5px 0;
}
</style>
