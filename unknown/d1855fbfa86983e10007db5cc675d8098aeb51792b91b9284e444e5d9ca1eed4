<?php
/**
 * Test Email Settings Page - Quick diagnostic tool
 */

// Include database connection
require_once 'config/database.php';
require_once 'includes/functions.php';

// Start session
session_start();

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Test Email Settings - Library Management System</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { padding: 40px 0; background-color: #f8f9fa; }
        .test-card { max-width: 800px; margin: 0 auto; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
    </style>
</head>
<body>
    <div class='container'>
        <div class='card test-card'>
            <div class='card-header bg-info text-white'>
                <h4 class='mb-0'>🧪 Email Settings Test</h4>
            </div>
            <div class='card-body'>";

// Test 1: Check if user is logged in
echo "<h5>1. Authentication Test</h5>";
if (isset($_SESSION['user_id'])) {
    echo "<p class='text-success'>✅ User is logged in (ID: " . $_SESSION['user_id'] . ")</p>";
    
    // Check if user is admin
    if (function_exists('isAdmin') && isAdmin()) {
        echo "<p class='text-success'>✅ User has admin privileges</p>";
    } else {
        echo "<p class='text-warning'>⚠️ User does not have admin privileges</p>";
        echo "<p class='text-info'>ℹ️ You need admin access to use Email Settings</p>";
    }
} else {
    echo "<p class='text-danger'>❌ User is not logged in</p>";
    echo "<p class='text-info'>ℹ️ Please <a href='auth/login.php'>login</a> to test Email Settings</p>";
}

// Test 2: Check database tables
echo "<hr><h5>2. Database Tables Test</h5>";

$required_tables = ['settings', 'reminder_logs', 'email_logs'];
foreach ($required_tables as $table) {
    try {
        $query = "SHOW TABLES LIKE '$table'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            echo "<p class='text-success'>✅ Table '$table' exists</p>";
        } else {
            echo "<p class='text-danger'>❌ Table '$table' does not exist</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='text-danger'>❌ Error checking table '$table': " . $e->getMessage() . "</p>";
    }
}

// Test 3: Check if settings data exists
echo "<hr><h5>3. Settings Data Test</h5>";
try {
    $query = "SELECT COUNT(*) as count FROM settings WHERE setting_group = 'email'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result['count'] > 0) {
        echo "<p class='text-success'>✅ Email settings data exists (" . $result['count'] . " settings)</p>";
    } else {
        echo "<p class='text-warning'>⚠️ No email settings data found</p>";
        echo "<p class='text-info'>ℹ️ Run the fix script to create default settings</p>";
    }
} catch (PDOException $e) {
    echo "<p class='text-danger'>❌ Error checking settings data: " . $e->getMessage() . "</p>";
}

// Test 4: Check file permissions and includes
echo "<hr><h5>4. File System Test</h5>";

$required_files = [
    'admin/email_settings.php',
    'includes/head.php',
    'includes/footer.php',
    'includes/header.php',
    'includes/sidebar.php',
    'includes/functions.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "<p class='text-success'>✅ File '$file' exists</p>";
    } else {
        echo "<p class='text-danger'>❌ File '$file' not found</p>";
    }
}

// Test 5: Direct link test
echo "<hr><h5>5. Direct Access Test</h5>";
echo "<p>Try accessing the Email Settings page directly:</p>";
echo "<div class='d-grid gap-2'>
        <a href='admin/email_settings.php' class='btn btn-primary' target='_blank'>
            <i class='bi bi-envelope me-2'></i>Open Email Settings Page
        </a>
      </div>";

echo "        </div>
            <div class='card-footer'>
                <div class='d-flex justify-content-between'>
                    <a href='index.php' class='btn btn-secondary'>Go to Dashboard</a>
                    <a href='fix_reminder_system.php' class='btn btn-warning'>Run Fix Script</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
