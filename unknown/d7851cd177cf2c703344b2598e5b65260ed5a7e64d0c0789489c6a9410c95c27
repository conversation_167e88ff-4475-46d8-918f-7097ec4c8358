<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>Session Debug Information</h2>";
echo "<h3>Current Session Data:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>Authentication Status:</h3>";
echo "Is Logged In: " . (isLoggedIn() ? 'YES' : 'NO') . "<br>";
echo "Is Admin: " . (isAdmin() ? 'YES' : 'NO') . "<br>";
echo "User ID: " . ($_SESSION['user_id'] ?? 'Not set') . "<br>";
echo "Role: " . ($_SESSION['role'] ?? 'Not set') . "<br>";
echo "Username: " . ($_SESSION['username'] ?? 'Not set') . "<br>";

echo "<h3>Database Connection Test:</h3>";
try {
    $database = new Database();
    $db = $database->getConnection();
    if ($db) {
        echo "Database connection: SUCCESS<br>";
        
        // Check if users table exists and has admin users
        $query = "SELECT * FROM users WHERE role = 'admin'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $admin_users = $stmt->fetchAll();
        
        echo "Admin users found: " . count($admin_users) . "<br>";
        if (count($admin_users) > 0) {
            echo "<h4>Available Admin Users:</h4>";
            foreach ($admin_users as $user) {
                echo "ID: " . $user['id'] . ", Username: " . $user['username'] . ", Role: " . $user['role'] . "<br>";
            }
        }
    } else {
        echo "Database connection: FAILED<br>";
    }
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "<br>";
}

echo "<h3>Quick Actions:</h3>";
echo '<a href="login.php">Go to Login Page</a><br>';
echo '<a href="admin/dashboard.php">Try Admin Dashboard</a><br>';
echo '<a href="logout.php">Logout</a><br>';
?>
