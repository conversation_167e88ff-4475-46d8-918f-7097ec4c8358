<?php
/**
 * Accessibility Improvements Script
 * Enhances the system for better accessibility and usability
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

$improvements_applied = [];
$errors = [];

// Function to create accessibility CSS file
function createAccessibilityCSS() {
    $css_content = '
/* Accessibility Improvements CSS */

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn {
        border-width: 2px !important;
    }
    .card {
        border-width: 2px !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus indicators */
.btn:focus,
.form-control:focus,
.form-select:focus,
.nav-link:focus,
a:focus {
    outline: 3px solid #0066cc !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.3) !important;
}

/* Skip to content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    z-index: 9999;
    border-radius: 4px;
}

.skip-link:focus {
    top: 6px;
}

/* Screen reader only content */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Better color contrast */
.text-muted {
    color: #495057 !important;
}

.btn-outline-secondary {
    color: #495057 !important;
    border-color: #495057 !important;
}

/* Larger click targets */
.btn-sm {
    min-height: 44px;
    min-width: 44px;
}

/* Better form labels */
.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Error states */
.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

/* Success states */
.is-valid {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

/* Loading states */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .bg-light {
        background-color: #343a40 !important;
        color: #fff !important;
    }
    
    .card {
        background-color: #495057 !important;
        color: #fff !important;
        border-color: #6c757d !important;
    }
    
    .table {
        color: #fff !important;
    }
    
    .table-striped > tbody > tr:nth-of-type(odd) > td {
        background-color: rgba(255, 255, 255, 0.05) !important;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .btn {
        display: none !important;
    }
    
    .navbar {
        display: none !important;
    }
    
    .sidebar {
        display: none !important;
    }
}

/* Mobile accessibility */
@media (max-width: 768px) {
    .btn {
        min-height: 48px;
        font-size: 16px;
    }
    
    .form-control {
        min-height: 48px;
        font-size: 16px;
    }
    
    .nav-link {
        min-height: 48px;
        display: flex;
        align-items: center;
    }
}

/* Keyboard navigation */
.keyboard-nav .nav-link:focus,
.keyboard-nav .btn:focus {
    background-color: rgba(0, 123, 255, 0.1) !important;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-available {
    background-color: #28a745;
}

.status-borrowed {
    background-color: #ffc107;
}

.status-overdue {
    background-color: #dc3545;
}

/* Tooltip improvements */
.tooltip {
    font-size: 14px;
}

.tooltip-inner {
    max-width: 300px;
    text-align: left;
}
';

    $css_file = 'assets/css/accessibility.css';
    
    // Create assets/css directory if it doesn\'t exist
    if (!is_dir('assets')) {
        mkdir('assets', 0755, true);
    }
    if (!is_dir('assets/css')) {
        mkdir('assets/css', 0755, true);
    }
    
    return file_put_contents($css_file, $css_content) !== false;
}

// Function to create accessibility JavaScript file
function createAccessibilityJS() {
    $js_content = '
// Accessibility Improvements JavaScript

document.addEventListener("DOMContentLoaded", function() {
    // Add keyboard navigation support
    document.body.addEventListener("keydown", function(e) {
        if (e.key === "Tab") {
            document.body.classList.add("keyboard-nav");
        }
    });
    
    document.body.addEventListener("mousedown", function() {
        document.body.classList.remove("keyboard-nav");
    });
    
    // Add ARIA labels to buttons without text
    const iconButtons = document.querySelectorAll("button:not([aria-label]):not([title])");
    iconButtons.forEach(button => {
        const icon = button.querySelector("i[class*=\'bi-\']");
        if (icon) {
            const iconClass = Array.from(icon.classList).find(cls => cls.startsWith("bi-"));
            if (iconClass) {
                const action = iconClass.replace("bi-", "").replace("-", " ");
                button.setAttribute("aria-label", action);
            }
        }
    });
    
    // Add loading states to forms
    const forms = document.querySelectorAll("form");
    forms.forEach(form => {
        form.addEventListener("submit", function() {
            const submitBtn = form.querySelector("button[type=\'submit\'], input[type=\'submit\']");
            if (submitBtn) {
                submitBtn.classList.add("loading");
                submitBtn.disabled = true;
            }
        });
    });
    
    // Add status indicators to book availability
    const availabilityElements = document.querySelectorAll("[data-availability]");
    availabilityElements.forEach(element => {
        const availability = element.getAttribute("data-availability");
        const indicator = document.createElement("span");
        indicator.className = "status-indicator";
        
        if (availability === "available") {
            indicator.classList.add("status-available");
            indicator.setAttribute("aria-label", "Available");
        } else if (availability === "borrowed") {
            indicator.classList.add("status-borrowed");
            indicator.setAttribute("aria-label", "Currently borrowed");
        } else if (availability === "overdue") {
            indicator.classList.add("status-overdue");
            indicator.setAttribute("aria-label", "Overdue");
        }
        
        element.insertBefore(indicator, element.firstChild);
    });
    
    // Improve table accessibility
    const tables = document.querySelectorAll("table");
    tables.forEach(table => {
        if (!table.getAttribute("role")) {
            table.setAttribute("role", "table");
        }
        
        const headers = table.querySelectorAll("th");
        headers.forEach((header, index) => {
            if (!header.getAttribute("scope")) {
                header.setAttribute("scope", "col");
            }
        });
    });
    
    // Add live region for dynamic content
    if (!document.getElementById("live-region")) {
        const liveRegion = document.createElement("div");
        liveRegion.id = "live-region";
        liveRegion.setAttribute("aria-live", "polite");
        liveRegion.setAttribute("aria-atomic", "true");
        liveRegion.className = "sr-only";
        document.body.appendChild(liveRegion);
    }
    
    // Announce page changes
    function announcePageChange(message) {
        const liveRegion = document.getElementById("live-region");
        if (liveRegion) {
            liveRegion.textContent = message;
        }
    }
    
    // Add skip link
    if (!document.querySelector(".skip-link")) {
        const skipLink = document.createElement("a");
        skipLink.href = "#main-content";
        skipLink.className = "skip-link";
        skipLink.textContent = "Skip to main content";
        document.body.insertBefore(skipLink, document.body.firstChild);
    }
    
    // Add main content landmark if missing
    const main = document.querySelector("main");
    if (main && !main.id) {
        main.id = "main-content";
    } else if (!main) {
        const content = document.querySelector(".container, .content, [role=\'main\']");
        if (content) {
            content.id = "main-content";
            content.setAttribute("role", "main");
        }
    }
});
';

    $js_file = 'assets/js/accessibility.js';
    
    // Create assets/js directory if it doesn\'t exist
    if (!is_dir('assets/js')) {
        mkdir('assets/js', 0755, true);
    }
    
    return file_put_contents($js_file, $js_content) !== false;
}

// Apply improvements
try {
    if (createAccessibilityCSS()) {
        $improvements_applied[] = "Created accessibility CSS file with enhanced styles";
    } else {
        $errors[] = "Failed to create accessibility CSS file";
    }
    
    if (createAccessibilityJS()) {
        $improvements_applied[] = "Created accessibility JavaScript file with enhanced functionality";
    } else {
        $errors[] = "Failed to create accessibility JavaScript file";
    }
    
} catch (Exception $e) {
    $errors[] = "Error applying accessibility improvements: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accessibility Improvements - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="assets/css/accessibility.css">
</head>
<body class="bg-light">
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <main id="main-content">
                    <div class="card shadow">
                        <div class="card-header bg-success text-white">
                            <h1 class="h4 mb-0"><i class="bi bi-universal-access me-2" aria-hidden="true"></i>Accessibility Improvements</h1>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($improvements_applied)): ?>
                                <div class="alert alert-success" role="alert">
                                    <h2 class="h5"><i class="bi bi-check-circle me-2" aria-hidden="true"></i>Improvements Applied Successfully!</h2>
                                    <ul class="mb-0">
                                        <?php foreach ($improvements_applied as $improvement): ?>
                                            <li><?php echo htmlspecialchars($improvement); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($errors)): ?>
                                <div class="alert alert-danger" role="alert">
                                    <h2 class="h5"><i class="bi bi-exclamation-triangle me-2" aria-hidden="true"></i>Errors Encountered</h2>
                                    <ul class="mb-0">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?php echo htmlspecialchars($error); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <div class="mt-4">
                                <h2 class="h5">Accessibility Features Added:</h2>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item"><i class="bi bi-check text-success me-2" aria-hidden="true"></i>Skip to content links</li>
                                            <li class="list-group-item"><i class="bi bi-check text-success me-2" aria-hidden="true"></i>Enhanced focus indicators</li>
                                            <li class="list-group-item"><i class="bi bi-check text-success me-2" aria-hidden="true"></i>Screen reader support</li>
                                            <li class="list-group-item"><i class="bi bi-check text-success me-2" aria-hidden="true"></i>Keyboard navigation</li>
                                            <li class="list-group-item"><i class="bi bi-check text-success me-2" aria-hidden="true"></i>High contrast mode support</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item"><i class="bi bi-check text-success me-2" aria-hidden="true"></i>Reduced motion support</li>
                                            <li class="list-group-item"><i class="bi bi-check text-success me-2" aria-hidden="true"></i>Dark mode compatibility</li>
                                            <li class="list-group-item"><i class="bi bi-check text-success me-2" aria-hidden="true"></i>Mobile accessibility</li>
                                            <li class="list-group-item"><i class="bi bi-check text-success me-2" aria-hidden="true"></i>ARIA labels and landmarks</li>
                                            <li class="list-group-item"><i class="bi bi-check text-success me-2" aria-hidden="true"></i>Status indicators</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <h2 class="h5">Next Steps:</h2>
                                <div class="d-grid gap-2 d-md-flex">
                                    <a href="ui_enhancements.php" class="btn btn-primary">
                                        <i class="bi bi-palette me-2" aria-hidden="true"></i>Apply UI Enhancements
                                    </a>
                                    <a href="security_hardening.php" class="btn btn-warning">
                                        <i class="bi bi-shield-check me-2" aria-hidden="true"></i>Security Hardening
                                    </a>
                                    <a href="system_health_check.php" class="btn btn-info">
                                        <i class="bi bi-heart-pulse me-2" aria-hidden="true"></i>Health Check
                                    </a>
                                    <a href="index.php" class="btn btn-secondary">
                                        <i class="bi bi-house me-2" aria-hidden="true"></i>Back to Home
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>
    
    <div id="live-region" aria-live="polite" aria-atomic="true" class="sr-only"></div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/accessibility.js"></script>
</body>
</html>
