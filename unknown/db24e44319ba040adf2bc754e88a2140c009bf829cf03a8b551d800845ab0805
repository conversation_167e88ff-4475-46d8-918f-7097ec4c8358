<?php
/**
 * Update Google OAuth Credentials Form
 * 
 * This script provides a simple form to update Google OAuth credentials.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/config.php';

// Get the current redirect URI
$redirect_uri = BASE_URL . 'google_callback.php';

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Google OAuth Credentials - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px;
        }
        .setup-container {
            max-width: 800px;
            margin: 40px auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            padding: 30px;
        }
        .code-block {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .alert {
            margin-top: 20px;
        }
        .step {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        .step:last-child {
            border-bottom: none;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background-color: #007bff;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <h1 class="mb-4"><i class="bi bi-google me-2"></i>Update Google OAuth Credentials</h1>
        
        <div class="alert alert-info">
            <i class="bi bi-info-circle-fill me-2"></i>
            <strong>Important:</strong> You need to create a Google Cloud project and configure OAuth credentials before proceeding.
        </div>
        
        <div class="step">
            <h3><span class="step-number">1</span>Create a Google Cloud Project</h3>
            <p>Go to the <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a> and create a new project or select an existing one.</p>
        </div>
        
        <div class="step">
            <h3><span class="step-number">2</span>Configure OAuth Consent Screen</h3>
            <ol>
                <li>In the Google Cloud Console, go to "APIs & Services" > "OAuth consent screen"</li>
                <li>Select "External" user type (unless you have a Google Workspace account)</li>
                <li>Fill in the required information (App name, User support email, Developer contact information)</li>
                <li>Add the scopes: <code>.../auth/userinfo.email</code> and <code>.../auth/userinfo.profile</code></li>
                <li>Add your domain to the authorized domains</li>
                <li>Save and continue</li>
            </ol>
        </div>
        
        <div class="step">
            <h3><span class="step-number">3</span>Create OAuth Client ID</h3>
            <ol>
                <li>In the Google Cloud Console, go to "APIs & Services" > "Credentials"</li>
                <li>Click "Create Credentials" > "OAuth client ID"</li>
                <li>Select "Web application" as the application type</li>
                <li>Add a name for your OAuth client</li>
                <li>Add your domain to the "Authorized JavaScript origins" (e.g., <code>http://localhost</code>)</li>
                <li>Add your redirect URI to the "Authorized redirect URIs":</li>
            </ol>
            <div class="code-block">
                <?php echo h($redirect_uri); ?>
            </div>
            <p>Click "Create" and you'll receive your Client ID and Client Secret.</p>
        </div>
        
        <div class="step">
            <h3><span class="step-number">4</span>Enter Your OAuth Credentials</h3>
            <form method="post" action="update_google_credentials.php">
                <div class="mb-3">
                    <label for="client_id" class="form-label">Client ID</label>
                    <input type="text" class="form-control" id="client_id" name="client_id" placeholder="Enter your Google Client ID" required>
                    <div class="form-text">Example: 123456789012-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com</div>
                </div>
                <div class="mb-3">
                    <label for="client_secret" class="form-label">Client Secret</label>
                    <input type="text" class="form-control" id="client_secret" name="client_secret" placeholder="Enter your Google Client Secret" required>
                    <div class="form-text">Example: GOCSPX-abcdefghijklmnopqrstuvwxyz</div>
                </div>
                <button type="submit" class="btn btn-primary">Save Credentials</button>
            </form>
        </div>
        
        <div class="mt-4 text-center">
            <a href="index.php" class="btn btn-outline-secondary">Back to Home</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
