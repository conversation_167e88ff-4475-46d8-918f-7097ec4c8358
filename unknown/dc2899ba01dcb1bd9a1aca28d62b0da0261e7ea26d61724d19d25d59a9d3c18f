<?php
require_once '../config/database.php';

// Create database connection
$database = new Database();
$db = $database->getConnection();

// SQL to create notifications table
$sql = "CREATE TABLE IF NOT EXISTS notifications (
    id INT(11) NOT NULL AUTO_INCREMENT,
    user_id INT(11) DEFAULT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'info',
    is_read TINYINT(1) NOT NULL DEFAULT 0,
    entity_type VARCHAR(50) DEFAULT NULL,
    entity_id INT(11) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_is_read (is_read),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

// Execute query
try {
    $db->exec($sql);
    echo "Notifications table created successfully!";
    
    // Add some sample notifications
    $sample_notifications = [
        [
            'user_id' => NULL, // NULL means for all users
            'message' => 'Welcome to the new Library Management System dashboard!',
            'type' => 'info'
        ],
        [
            'user_id' => NULL,
            'message' => 'There are 3 books overdue today. Please check the overdue books report.',
            'type' => 'warning',
            'entity_type' => 'report',
            'entity_id' => 1
        ],
        [
            'user_id' => NULL,
            'message' => 'New feature: You can now export reports to Excel and PDF formats.',
            'type' => 'info'
        ]
    ];
    
    // Insert sample notifications
    $query = "INSERT INTO notifications (user_id, message, type, entity_type, entity_id) VALUES (:user_id, :message, :type, :entity_type, :entity_id)";
    $stmt = $db->prepare($query);
    
    foreach ($sample_notifications as $notification) {
        $stmt->bindValue(':user_id', $notification['user_id']);
        $stmt->bindValue(':message', $notification['message']);
        $stmt->bindValue(':type', $notification['type']);
        $stmt->bindValue(':entity_type', $notification['entity_type'] ?? NULL);
        $stmt->bindValue(':entity_id', $notification['entity_id'] ?? NULL);
        $stmt->execute();
    }
    
    echo "\nSample notifications added successfully!";
    
} catch(PDOException $e) {
    echo "Error creating notifications table: " . $e->getMessage();
}
?>
