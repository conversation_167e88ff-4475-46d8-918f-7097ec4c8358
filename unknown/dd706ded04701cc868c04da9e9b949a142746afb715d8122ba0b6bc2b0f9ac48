<?php
/**
 * Verify Members and Loans Script
 * This script displays all members and their current book loans
 */

require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<h1>📊 Members and Loans Verification</h1>";

// Get database statistics
$stats_query = "
    SELECT 
        (SELECT COUNT(*) FROM members) as total_members,
        (SELECT COUNT(*) FROM books) as total_books,
        (SELECT COUNT(*) FROM book_loans) as total_loans,
        (SELECT COUNT(*) FROM book_loans WHERE status = 'borrowed') as active_loans,
        (SELECT COUNT(*) FROM book_loans WHERE status = 'returned') as returned_loans,
        (SELECT COUNT(*) FROM book_loans WHERE status = 'overdue') as overdue_loans
";
$stats_stmt = $db->prepare($stats_query);
$stats_stmt->execute();
$stats = $stats_stmt->fetch();

echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>📈 Database Statistics</h2>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>";
echo "<div><strong>Total Members:</strong> {$stats['total_members']}</div>";
echo "<div><strong>Total Books:</strong> {$stats['total_books']}</div>";
echo "<div><strong>Total Loans:</strong> {$stats['total_loans']}</div>";
echo "<div><strong>Active Loans:</strong> {$stats['active_loans']}</div>";
echo "<div><strong>Returned Loans:</strong> {$stats['returned_loans']}</div>";
echo "<div><strong>Overdue Loans:</strong> {$stats['overdue_loans']}</div>";
echo "</div>";
echo "</div>";

// Get all members with their loan information
echo "<h2>👥 All Members and Their Current Loans</h2>";

$members_query = "
    SELECT 
        m.id,
        m.first_name,
        m.last_name,
        m.email,
        m.phone,
        m.membership_date,
        m.membership_status,
        COUNT(bl.id) as total_loans,
        COUNT(CASE WHEN bl.status = 'borrowed' THEN 1 END) as active_loans
    FROM members m
    LEFT JOIN book_loans bl ON m.id = bl.member_id
    GROUP BY m.id
    ORDER BY m.last_name, m.first_name
";

$members_stmt = $db->prepare($members_query);
$members_stmt->execute();
$members = $members_stmt->fetchAll();

if (!empty($members)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>ID</th><th>Name</th><th>Email</th><th>Phone</th><th>Member Since</th><th>Status</th><th>Total Loans</th><th>Active Loans</th><th>Actions</th>";
    echo "</tr>";
    
    foreach ($members as $member) {
        $status_color = $member['membership_status'] === 'active' ? 'green' : 'red';
        echo "<tr>";
        echo "<td>{$member['id']}</td>";
        echo "<td>{$member['first_name']} {$member['last_name']}</td>";
        echo "<td>{$member['email']}</td>";
        echo "<td>{$member['phone']}</td>";
        echo "<td>{$member['membership_date']}</td>";
        echo "<td style='color: $status_color; font-weight: bold;'>{$member['membership_status']}</td>";
        echo "<td>{$member['total_loans']}</td>";
        echo "<td style='font-weight: bold;'>{$member['active_loans']}</td>";
        echo "<td><a href='#member-{$member['id']}'>View Loans</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No members found in the database.</p>";
}

// Show detailed loan information for each member
echo "<h2>📚 Detailed Loan Information</h2>";

foreach ($members as $member) {
    // Get loans for this member
    $loans_query = "
        SELECT 
            bl.id as loan_id,
            bl.issue_date,
            bl.due_date,
            bl.return_date,
            bl.status,
            bl.fine,
            b.title,
            b.author,
            b.isbn
        FROM book_loans bl
        JOIN books b ON bl.book_id = b.id
        WHERE bl.member_id = :member_id
        ORDER BY bl.issue_date DESC
    ";
    
    $loans_stmt = $db->prepare($loans_query);
    $loans_stmt->bindParam(':member_id', $member['id']);
    $loans_stmt->execute();
    $loans = $loans_stmt->fetchAll();
    
    if (!empty($loans)) {
        echo "<div id='member-{$member['id']}' style='margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;'>";
        echo "<h3>📖 {$member['first_name']} {$member['last_name']} - Loan History</h3>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>Loan ID</th><th>Book Title</th><th>Author</th><th>ISBN</th><th>Issue Date</th><th>Due Date</th><th>Return Date</th><th>Status</th><th>Fine</th>";
        echo "</tr>";
        
        foreach ($loans as $loan) {
            $status_color = 'black';
            if ($loan['status'] === 'borrowed') $status_color = 'green';
            elseif ($loan['status'] === 'overdue') $status_color = 'red';
            elseif ($loan['status'] === 'returned') $status_color = 'blue';
            
            $return_date = $loan['return_date'] ? $loan['return_date'] : 'Not returned';
            $fine = $loan['fine'] > 0 ? '$' . number_format($loan['fine'], 2) : 'None';
            
            echo "<tr>";
            echo "<td>{$loan['loan_id']}</td>";
            echo "<td>{$loan['title']}</td>";
            echo "<td>{$loan['author']}</td>";
            echo "<td>{$loan['isbn']}</td>";
            echo "<td>{$loan['issue_date']}</td>";
            echo "<td>{$loan['due_date']}</td>";
            echo "<td>{$return_date}</td>";
            echo "<td style='color: $status_color; font-weight: bold;'>{$loan['status']}</td>";
            echo "<td>{$fine}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    }
}

// Show books and their availability
echo "<h2>📖 Book Availability Status</h2>";

$books_query = "
    SELECT 
        b.id,
        b.title,
        b.author,
        b.isbn,
        b.quantity,
        b.available_quantity,
        (b.quantity - b.available_quantity) as borrowed_count
    FROM books b
    ORDER BY b.title
";

$books_stmt = $db->prepare($books_query);
$books_stmt->execute();
$books = $books_stmt->fetchAll();

if (!empty($books)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>ID</th><th>Title</th><th>Author</th><th>ISBN</th><th>Total Qty</th><th>Available</th><th>Borrowed</th><th>Status</th>";
    echo "</tr>";
    
    foreach ($books as $book) {
        $availability_status = $book['available_quantity'] > 0 ? 'Available' : 'All Borrowed';
        $status_color = $book['available_quantity'] > 0 ? 'green' : 'red';
        
        echo "<tr>";
        echo "<td>{$book['id']}</td>";
        echo "<td>{$book['title']}</td>";
        echo "<td>{$book['author']}</td>";
        echo "<td>{$book['isbn']}</td>";
        echo "<td>{$book['quantity']}</td>";
        echo "<td style='font-weight: bold;'>{$book['available_quantity']}</td>";
        echo "<td>{$book['borrowed_count']}</td>";
        echo "<td style='color: $status_color; font-weight: bold;'>{$availability_status}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No books found in the database.</p>";
}

echo "<div style='margin: 30px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;'>";
echo "<h3>🔗 Quick Links</h3>";
echo "<ul>";
echo "<li><a href='members/index.php'>Manage Members</a></li>";
echo "<li><a href='books/index.php'>Manage Books</a></li>";
echo "<li><a href='loans/index.php'>Manage Loans</a></li>";
echo "<li><a href='librarian/dashboard.php'>Librarian Dashboard</a></li>";
echo "<li><a href='generate_members_and_loans.php'>Generate More Members & Loans</a></li>";
echo "</ul>";
echo "</div>";

?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h1, h2, h3 { color: #333; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    a { color: #007bff; text-decoration: none; }
    a:hover { text-decoration: underline; }
    .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
</style>
