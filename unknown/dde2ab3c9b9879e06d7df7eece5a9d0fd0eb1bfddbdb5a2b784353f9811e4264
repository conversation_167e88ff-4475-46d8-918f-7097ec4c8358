/**
 * Dashboard Enhancements CSS
 * Styles for task management and other dashboard enhancements
 * Dashboard customization functionality has been completely removed
 */

/* Dashboard Widget Styles */
.dashboard-widget {
    margin-bottom: 1.5rem;
}

/* Task Management */
.task-list {
    max-height: 400px;
    overflow-y: auto;
}

.task-item {
    padding: 0.75rem;
    border-left: 3px solid transparent;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 0.25rem;
}

.task-item:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

.task-item.priority-high {
    border-left-color: #dc3545;
}

.task-item.priority-medium {
    border-left-color: #fd7e14;
}

.task-item.priority-low {
    border-left-color: #0dcaf0;
}

.task-item.completed {
    opacity: 0.6;
}

.task-item.completed .task-title {
    text-decoration: line-through;
}

.task-actions {
    visibility: hidden;
    opacity: 0;
    transition: all 0.2s ease;
}

.task-item:hover .task-actions {
    visibility: visible;
    opacity: 1;
}

.task-due-date {
    font-size: 0.8rem;
    color: #6c757d;
}

.task-due-date.overdue {
    color: #dc3545;
    font-weight: bold;
}

/* Saved Filters */
.saved-filter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.saved-filter-actions {
    visibility: hidden;
    opacity: 0;
    transition: all 0.2s ease;
}

.saved-filter-item:hover .saved-filter-actions {
    visibility: visible;
    opacity: 1;
}

/* Book Condition Tracking */
.condition-rating {
    display: inline-flex;
    align-items: center;
}

.condition-rating .bi-star-fill {
    color: #ffc107;
}

.condition-rating .bi-star {
    color: #dee2e6;
}

.condition-images {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.condition-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.condition-image:hover {
    transform: scale(1.05);
}

/* Bulk Operations */
.bulk-actions-bar {
    background-color: rgba(0, 123, 255, 0.1);
    padding: 0.75rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    display: none;
}

.bulk-actions-bar.active {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.bulk-select-all {
    margin-right: 1rem;
}

/* Member Communication Center */
.communication-templates {
    max-height: 300px;
    overflow-y: auto;
}

.template-item {
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s ease;
}

.template-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.template-item.selected {
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #0d6efd;
}

.member-note {
    background-color: #fff3cd;
    border-left: 3px solid #ffc107;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    border-radius: 0.25rem;
}

.member-note-date {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Quick Member Registration */
.quick-registration-form .form-group {
    margin-bottom: 0.75rem;
}

.quick-registration-form .required-field::after {
    content: "*";
    color: #dc3545;
    margin-left: 0.25rem;
}

/* Acquisition Suggestions */
.acquisition-item {
    border-left: 3px solid #0d6efd;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    background-color: rgba(13, 110, 253, 0.05);
    border-radius: 0.25rem;
}

.acquisition-reason {
    font-size: 0.85rem;
    color: #6c757d;
}

.acquisition-stats {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
}

.acquisition-stat {
    display: flex;
    align-items: center;
    font-size: 0.85rem;
}

.acquisition-stat i {
    margin-right: 0.25rem;
}

/* Dark mode adjustments */
.dark-mode .dashboard-widget {
    background-color: #2b3035;
    border-color: #495057;
}

.dark-mode .card-header {
    background-color: #343a40;
    border-color: #495057;
}

.dark-mode .task-item {
    background-color: rgba(255, 255, 255, 0.05);
}

.dark-mode .task-item:hover {
    background-color: rgba(255, 255, 255, 0.08);
}

.dark-mode .condition-rating .bi-star {
    color: #495057;
}

.dark-mode .member-note {
    background-color: rgba(255, 193, 7, 0.15);
    border-left-color: #ffc107;
}

.dark-mode .acquisition-item {
    background-color: rgba(13, 110, 253, 0.1);
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .dashboard-widget {
        margin-bottom: 1rem;
    }

    .bulk-actions-bar {
        flex-direction: column;
        gap: 0.5rem;
    }

    .bulk-actions-bar .btn-group {
        width: 100%;
        justify-content: center;
    }

    .condition-images {
        justify-content: center;
    }
}
