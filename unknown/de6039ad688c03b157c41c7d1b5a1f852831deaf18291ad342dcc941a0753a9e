<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../login.php');
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['notification_ids'])) {
    // Connect to database
    $database = new Database();
    $db = $database->getConnection();
    
    // Get notification IDs from POST data
    $notification_ids = json_decode($_POST['notification_ids'], true);
    
    if (is_array($notification_ids) && !empty($notification_ids)) {
        // Prepare placeholders for the IN clause
        $placeholders = implode(',', array_fill(0, count($notification_ids), '?'));
        
        // Update notifications to mark as read
        $query = "UPDATE notifications 
                  SET is_read = 1 
                  WHERE id IN ($placeholders) 
                  AND (user_id = ? OR user_id IS NULL)";
        
        $stmt = $db->prepare($query);
        
        // Bind notification IDs
        foreach ($notification_ids as $index => $id) {
            $stmt->bindValue($index + 1, $id);
        }
        
        // Bind user ID as the last parameter
        $stmt->bindValue(count($notification_ids) + 1, $_SESSION['user_id']);
        
        // Execute the query
        $stmt->execute();
        
        // Log activity
        $count = count($notification_ids);
        logActivity($db, 'mark_read', "Marked $count notifications as read");
        
        // Set success message
        setMessage("$count notifications marked as read", 'success');
    }
    
    // Redirect back to notifications page
    redirect('index.php');
} else {
    // Invalid request, redirect to notifications page
    redirect('index.php');
}
?>
