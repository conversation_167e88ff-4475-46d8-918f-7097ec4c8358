<?php
/**
 * Fix Dashboard Balance - Reduce to 1000+ Members and Balance All Loan Statistics
 * This script will properly reduce members and create realistic loan ratios
 */

require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Set execution time limit
set_time_limit(600); // 10 minutes

echo "<h1>🔧 Fixing Dashboard Balance</h1>";
echo "<p><strong>Target: ~1000 members with balanced loan statistics</strong></p>";

// Get current statistics
echo "<h2>📊 Current Statistics</h2>";
$stats_query = "
    SELECT
        (SELECT COUNT(*) FROM members) as total_members,
        (SELECT COUNT(*) FROM books) as total_books,
        (SELECT SUM(available_quantity) FROM books) as available_books,
        (SELECT COUNT(*) FROM book_loans) as total_loans,
        (SELECT COUNT(*) FROM book_loans WHERE status = 'borrowed') as active_loans,
        (SELECT COUNT(*) FROM book_loans WHERE status = 'overdue') as overdue_loans,
        (SELECT COUNT(*) FROM book_loans WHERE status = 'returned') as returned_loans
";
$stats_stmt = $db->prepare($stats_query);
$stats_stmt->execute();
$current_stats = $stats_stmt->fetch();

echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>❌ Current (Unbalanced):</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;'>";
echo "<div><strong>👥 Members:</strong> {$current_stats['total_members']}</div>";
echo "<div><strong>📚 Books:</strong> {$current_stats['total_books']}</div>";
echo "<div><strong>📖 Available:</strong> {$current_stats['available_books']}</div>";
echo "<div><strong>📋 Total Loans:</strong> {$current_stats['total_loans']}</div>";
echo "<div><strong>✅ Active:</strong> {$current_stats['active_loans']}</div>";
echo "<div><strong>⚠️ Overdue:</strong> {$current_stats['overdue_loans']}</div>";
echo "<div><strong>📚 Returned:</strong> {$current_stats['returned_loans']}</div>";
echo "</div>";
echo "</div>";

// Target numbers for balanced dashboard
$target_members = 1200; // Slightly above 1000
$target_active_loans = 350; // Reasonable for 1200 members
$target_overdue_loans = 80; // About 20% of active loans
$target_returned_loans = 800; // Historical loans

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🎯 Target (Balanced):</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;'>";
echo "<div><strong>👥 Members:</strong> ~{$target_members}</div>";
echo "<div><strong>✅ Active Loans:</strong> ~{$target_active_loans}</div>";
echo "<div><strong>⚠️ Overdue:</strong> ~{$target_overdue_loans}</div>";
echo "<div><strong>📚 Returned:</strong> ~{$target_returned_loans}</div>";
echo "</div>";
echo "</div>";

// Start transaction
$db->beginTransaction();

try {
    echo "<h2>🚀 Starting Balance Process...</h2>";
    
    // Step 1: Clear ALL existing loan data to start fresh
    echo "<h3>Step 1: Clearing all loan data...</h3>";
    $clear_loans = "DELETE FROM book_loans";
    $clear_stmt = $db->prepare($clear_loans);
    $clear_stmt->execute();
    $cleared_loans = $clear_stmt->rowCount();
    echo "<p>🗑️ Cleared {$cleared_loans} existing loans</p>";
    
    // Step 2: Reduce members to target count
    echo "<h3>Step 2: Reducing members to {$target_members}...</h3>";
    
    // Keep first N members by ID
    $keep_members_query = "SELECT id FROM members ORDER BY id LIMIT {$target_members}";
    $keep_stmt = $db->prepare($keep_members_query);
    $keep_stmt->execute();
    $members_to_keep = $keep_stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (count($members_to_keep) > 0) {
        $members_list = implode(',', $members_to_keep);
        $delete_members_query = "DELETE FROM members WHERE id NOT IN ({$members_list})";
        $delete_members_stmt = $db->prepare($delete_members_query);
        $delete_members_stmt->execute();
        $deleted_members = $delete_members_stmt->rowCount();
        echo "<p>🗑️ Removed {$deleted_members} excess members</p>";
    }
    
    // Step 3: Get available books for loan generation
    echo "<h3>Step 3: Preparing for loan generation...</h3>";
    $books_query = "SELECT id FROM books ORDER BY id";
    $books_stmt = $db->prepare($books_query);
    $books_stmt->execute();
    $available_books = $books_stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($available_books)) {
        throw new Exception("No books available for loan generation");
    }
    
    echo "<p>📚 Found " . count($available_books) . " books for loan generation</p>";
    
    // Step 4: Generate balanced loan data
    echo "<h3>Step 4: Generating balanced loan data...</h3>";
    
    $loans_created = 0;
    $batch_size = 50;
    
    // Get all remaining members
    $members_query = "SELECT id, membership_date FROM members ORDER BY id";
    $members_stmt = $db->prepare($members_query);
    $members_stmt->execute();
    $all_members = $members_stmt->fetchAll();
    
    echo "<p>👥 Working with " . count($all_members) . " members</p>";
    
    // Calculate loans per member to reach targets
    $total_target_loans = $target_active_loans + $target_overdue_loans + $target_returned_loans;
    $loans_per_member = ceil($total_target_loans / count($all_members));
    
    echo "<p>📊 Target: {$total_target_loans} total loans (~{$loans_per_member} per member)</p>";
    
    foreach ($all_members as $index => $member) {
        $member_id = $member['id'];
        $membership_date = $member['membership_date'];
        
        // Generate 1-4 loans per member
        $num_loans = rand(1, 4);
        
        for ($i = 0; $i < $num_loans; $i++) {
            $book_id = $available_books[array_rand($available_books)];
            
            // Determine loan status based on targets
            $rand = rand(1, 100);
            if ($loans_created < $target_returned_loans && $rand <= 65) {
                $status = 'returned';
            } elseif ($loans_created < ($target_returned_loans + $target_active_loans) && $rand <= 85) {
                $status = 'borrowed';
            } else {
                $status = 'overdue';
            }
            
            // Generate realistic dates
            $join_timestamp = strtotime($membership_date);
            $now_timestamp = strtotime('today');
            
            if ($status === 'returned') {
                // Returned books - issue date 1-6 months ago
                $issue_timestamp = $now_timestamp - rand(30, 180) * 24 * 60 * 60;
                $issue_date = date('Y-m-d H:i:s', $issue_timestamp);
                $due_date = date('Y-m-d H:i:s', $issue_timestamp + (14 * 24 * 60 * 60));
                
                // Return date - could be early, on time, or late
                $return_delay = rand(-2, 8); // -2 days early to 8 days late
                $return_timestamp = strtotime($due_date) + ($return_delay * 24 * 60 * 60);
                $return_date = date('Y-m-d H:i:s', $return_timestamp);
                
                $fine = ($return_delay > 0) ? $return_delay * 1.00 : 0;
                
            } elseif ($status === 'borrowed') {
                // Currently borrowed - issue date 1-13 days ago
                $issue_timestamp = $now_timestamp - rand(1, 13) * 24 * 60 * 60;
                $issue_date = date('Y-m-d H:i:s', $issue_timestamp);
                $due_date = date('Y-m-d H:i:s', $issue_timestamp + (14 * 24 * 60 * 60));
                $return_date = null;
                $fine = 0;
                
            } else { // overdue
                // Overdue books - issue date 15-60 days ago
                $issue_timestamp = $now_timestamp - rand(15, 60) * 24 * 60 * 60;
                $issue_date = date('Y-m-d H:i:s', $issue_timestamp);
                $due_date = date('Y-m-d H:i:s', $issue_timestamp + (14 * 24 * 60 * 60));
                $return_date = null;
                
                // Calculate fine
                $days_overdue = ($now_timestamp - strtotime($due_date)) / (24 * 60 * 60);
                $fine = max(1, floor($days_overdue)) * 1.00;
                $status = 'borrowed'; // Will be updated to overdue later
            }
            
            // Insert loan
            $loan_query = "INSERT INTO book_loans (book_id, member_id, issue_date, due_date, return_date, status, fine)
                          VALUES (:book_id, :member_id, :issue_date, :due_date, :return_date, :status, :fine)";
            
            $loan_stmt = $db->prepare($loan_query);
            $loan_stmt->bindParam(':book_id', $book_id);
            $loan_stmt->bindParam(':member_id', $member_id);
            $loan_stmt->bindParam(':issue_date', $issue_date);
            $loan_stmt->bindParam(':due_date', $due_date);
            $loan_stmt->bindParam(':return_date', $return_date);
            $loan_stmt->bindParam(':status', $status);
            $loan_stmt->bindParam(':fine', $fine);
            
            if ($loan_stmt->execute()) {
                $loans_created++;
            }
        }
        
        // Progress update every 100 members
        if (($index + 1) % 100 === 0) {
            echo "<p>📈 Progress: " . ($index + 1) . "/" . count($all_members) . " members processed, {$loans_created} loans created</p>";
            flush();
        }
        
        // Stop if we've reached our target
        if ($loans_created >= $total_target_loans) {
            break;
        }
    }
    
    // Step 5: Update overdue statuses
    echo "<h3>Step 5: Updating overdue statuses...</h3>";
    $overdue_update = "UPDATE book_loans SET status = 'overdue' WHERE status = 'borrowed' AND due_date < CURDATE()";
    $overdue_stmt = $db->prepare($overdue_update);
    $overdue_stmt->execute();
    $overdue_updated = $overdue_stmt->rowCount();
    echo "<p>📅 Updated {$overdue_updated} loans to overdue status</p>";
    
    // Commit all changes
    $db->commit();
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ Balance Complete!</h3>";
    echo "<p>Successfully balanced the dashboard with realistic data.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    $db->rollBack();
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error During Balance</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
    exit;
}

// Get final statistics
echo "<h2>📊 Final Balanced Statistics</h2>";
$final_stats_stmt = $db->prepare($stats_query);
$final_stats_stmt->execute();
$final_stats = $final_stats_stmt->fetch();

echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
echo "<h3>✅ After Balancing:</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;'>";
echo "<div><strong>👥 Members:</strong> {$final_stats['total_members']}</div>";
echo "<div><strong>📚 Books:</strong> {$final_stats['total_books']}</div>";
echo "<div><strong>📖 Available:</strong> {$final_stats['available_books']}</div>";
echo "<div><strong>📋 Total Loans:</strong> {$final_stats['total_loans']}</div>";
echo "<div><strong>✅ Active:</strong> {$final_stats['active_loans']}</div>";
echo "<div><strong>⚠️ Overdue:</strong> {$final_stats['overdue_loans']}</div>";
echo "<div><strong>📚 Returned:</strong> {$final_stats['returned_loans']}</div>";
echo "</div>";
echo "</div>";

echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🎉 Dashboard is Now Properly Balanced!</h3>";
echo "<p>Your dashboard should now show realistic numbers around 1000+ members with balanced loan statistics.</p>";
echo "<p><a href='admin/dashboard.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Updated Dashboard</a></p>";
echo "</div>";
?>
