<?php
/**
 * Fix Overdue Books Balance Script
 *
 * This script addresses issues caused by having too many overdue books (751)
 * by balancing the system to maintain realistic numbers while preserving
 * some overdue books for testing purposes.
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Fix Overdue Books Balance</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
        .info { color: blue; }
        .stats { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>";

echo "<h1>🔧 Fix Overdue Books Balance</h1>";
echo "<p>Fixing issues caused by having too many overdue books (751)...</p>";

try {
    $db->beginTransaction();

    // Step 1: Get current statistics
    echo "<h2>📊 Step 1: Current System Status</h2>";

    $stats_queries = [
        'total_loans' => "SELECT COUNT(*) as count FROM book_loans",
        'active_loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed'",
        'overdue_loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'overdue'",
        'returned_loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'returned'",
        'total_members' => "SELECT COUNT(*) as count FROM members",
        'total_books' => "SELECT COUNT(*) as count FROM books",
        'available_books' => "SELECT SUM(available_quantity) as count FROM books"
    ];

    $stats = [];
    foreach ($stats_queries as $key => $query) {
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats[$key] = $stmt->fetch()['count'] ?? 0;
    }

    echo "<div class='stats'>";
    echo "<strong>Current Statistics:</strong><br>";
    echo "📚 Total Loans: {$stats['total_loans']}<br>";
    echo "✅ Active Loans: {$stats['active_loans']}<br>";
    echo "⚠️ Overdue Loans: {$stats['overdue_loans']}<br>";
    echo "📖 Returned Loans: {$stats['returned_loans']}<br>";
    echo "👥 Total Members: {$stats['total_members']}<br>";
    echo "📚 Total Books: {$stats['total_books']}<br>";
    echo "📖 Available Books: {$stats['available_books']}<br>";
    echo "</div>";

    // Step 2: Calculate target numbers for balanced system
    echo "<h2>🎯 Step 2: Calculate Target Balance</h2>";

    $target_overdue = min(50, $stats['total_members'] * 0.05); // Max 50 or 5% of members
    $target_active = min(100, $stats['total_members'] * 0.10); // Max 100 or 10% of members
    $excess_overdue = $stats['overdue_loans'] - $target_overdue;

    echo "<div class='info'>";
    echo "<strong>Target Numbers:</strong><br>";
    echo "🎯 Target Overdue: {$target_overdue}<br>";
    echo "🎯 Target Active: {$target_active}<br>";
    echo "📉 Excess Overdue to Fix: {$excess_overdue}<br>";
    echo "</div>";

    // Step 3: Return very old overdue books (60+ days overdue)
    echo "<h2>📚 Step 3: Return Very Old Overdue Books</h2>";

    $very_old_query = "SELECT bl.id, bl.book_id, bl.member_id, bl.due_date,
                              DATEDIFF(CURDATE(), bl.due_date) as days_overdue
                       FROM book_loans bl
                       WHERE bl.status = 'overdue'
                       AND DATEDIFF(CURDATE(), bl.due_date) > 60
                       ORDER BY bl.due_date ASC";
    $very_old_stmt = $db->prepare($very_old_query);
    $very_old_stmt->execute();
    $very_old_loans = $very_old_stmt->fetchAll();

    $very_old_returned = 0;
    foreach ($very_old_loans as $loan) {
        // Return the book with a fine
        $fine = min(50.00, $loan['days_overdue'] * 1.00); // Cap at $50
        $return_date = date('Y-m-d', strtotime($loan['due_date'] . ' + ' . $loan['days_overdue'] . ' days'));

        $return_query = "UPDATE book_loans
                        SET status = 'returned',
                            return_date = :return_date,
                            fine = :fine
                        WHERE id = :id";
        $return_stmt = $db->prepare($return_query);
        $return_stmt->execute([
            ':return_date' => $return_date,
            ':fine' => $fine,
            ':id' => $loan['id']
        ]);

        // Update book availability
        $update_book_query = "UPDATE books
                             SET available_quantity = available_quantity + 1
                             WHERE id = :book_id
                             AND available_quantity < quantity";
        $update_book_stmt = $db->prepare($update_book_query);
        $update_book_stmt->execute([':book_id' => $loan['book_id']]);

        $very_old_returned++;
    }

    echo "<p class='success'>✅ Returned {$very_old_returned} very old overdue books (60+ days) with appropriate fines</p>";

    // Step 4: Return moderately overdue books (30-60 days)
    echo "<h2>📖 Step 4: Return Moderately Overdue Books</h2>";

    $moderate_query = "SELECT bl.id, bl.book_id, bl.member_id, bl.due_date,
                              DATEDIFF(CURDATE(), bl.due_date) as days_overdue
                       FROM book_loans bl
                       WHERE bl.status = 'overdue'
                       AND DATEDIFF(CURDATE(), bl.due_date) BETWEEN 30 AND 60
                       ORDER BY RAND()
                       LIMIT " . max(0, $excess_overdue - $very_old_returned);
    $moderate_stmt = $db->prepare($moderate_query);
    $moderate_stmt->execute();
    $moderate_loans = $moderate_stmt->fetchAll();

    $moderate_returned = 0;
    foreach ($moderate_loans as $loan) {
        // Return the book with a fine
        $fine = $loan['days_overdue'] * 1.00;
        $return_date = date('Y-m-d', strtotime($loan['due_date'] . ' + ' . $loan['days_overdue'] . ' days'));

        $return_query = "UPDATE book_loans
                        SET status = 'returned',
                            return_date = :return_date,
                            fine = :fine
                        WHERE id = :id";
        $return_stmt = $db->prepare($return_query);
        $return_stmt->execute([
            ':return_date' => $return_date,
            ':fine' => $fine,
            ':id' => $loan['id']
        ]);

        // Update book availability
        $update_book_query = "UPDATE books
                             SET available_quantity = available_quantity + 1
                             WHERE id = :book_id
                             AND available_quantity < quantity";
        $update_book_stmt = $db->prepare($update_book_query);
        $update_book_stmt->execute([':book_id' => $loan['book_id']]);

        $moderate_returned++;
    }

    echo "<p class='success'>✅ Returned {$moderate_returned} moderately overdue books (30-60 days) with fines</p>";

    // Step 5: Calculate reasonable fines for remaining overdue books
    echo "<h2>💰 Step 5: Calculate Reasonable Fines</h2>";

    $fine_query = "UPDATE book_loans
                   SET fine = LEAST(DATEDIFF(CURDATE(), due_date) * 1.00, 25.00)
                   WHERE status = 'overdue'
                   AND due_date < CURDATE()";
    $fine_stmt = $db->prepare($fine_query);
    $fine_stmt->execute();
    $fines_updated = $fine_stmt->rowCount();

    echo "<p class='success'>✅ Updated fines for {$fines_updated} remaining overdue loans (capped at $25)</p>";

    // Step 6: Convert some overdue to active loans (extend due dates)
    echo "<h2>🔄 Step 6: Convert Some Overdue to Active Loans</h2>";

    $extend_query = "SELECT bl.id, bl.due_date
                     FROM book_loans bl
                     WHERE bl.status = 'overdue'
                     AND DATEDIFF(CURDATE(), bl.due_date) <= 7
                     ORDER BY RAND()
                     LIMIT 20";
    $extend_stmt = $db->prepare($extend_query);
    $extend_stmt->execute();
    $extend_loans = $extend_stmt->fetchAll();

    $extended_count = 0;
    foreach ($extend_loans as $loan) {
        // Extend due date by 14 days and change status to borrowed
        $new_due_date = date('Y-m-d', strtotime('+14 days'));

        $extend_update_query = "UPDATE book_loans
                               SET status = 'borrowed',
                                   due_date = :new_due_date,
                                   fine = 0
                               WHERE id = :id";
        $extend_update_stmt = $db->prepare($extend_update_query);
        $extend_update_stmt->execute([
            ':new_due_date' => $new_due_date,
            ':id' => $loan['id']
        ]);

        $extended_count++;
    }

    echo "<p class='success'>✅ Extended due dates for {$extended_count} recent overdue loans (converted to active)</p>";

    // Step 7: Fix book availability calculations
    echo "<h2>📚 Step 7: Fix Book Availability</h2>";

    $book_fix_query = "UPDATE books b
                       SET available_quantity = GREATEST(0,
                           b.quantity - (
                               SELECT COUNT(*)
                               FROM book_loans bl
                               WHERE bl.book_id = b.id
                               AND bl.status IN ('borrowed', 'overdue')
                           )
                       )";
    $book_fix_stmt = $db->prepare($book_fix_query);
    $book_fix_stmt->execute();
    $books_fixed = $book_fix_stmt->rowCount();

    echo "<p class='success'>✅ Fixed availability for {$books_fixed} books</p>";

    // Step 8: Get final statistics
    echo "<h2>📊 Step 8: Final System Status</h2>";

    $final_stats = [];
    foreach ($stats_queries as $key => $query) {
        $stmt = $db->prepare($query);
        $stmt->execute();
        $final_stats[$key] = $stmt->fetch()['count'] ?? 0;
    }

    echo "<div class='stats'>";
    echo "<strong>Final Statistics:</strong><br>";
    echo "📚 Total Loans: {$final_stats['total_loans']}<br>";
    echo "✅ Active Loans: {$final_stats['active_loans']} (was {$stats['active_loans']})<br>";
    echo "⚠️ Overdue Loans: {$final_stats['overdue_loans']} (was {$stats['overdue_loans']})<br>";
    echo "📖 Returned Loans: {$final_stats['returned_loans']} (was {$stats['returned_loans']})<br>";
    echo "👥 Total Members: {$final_stats['total_members']}<br>";
    echo "📚 Total Books: {$final_stats['total_books']}<br>";
    echo "📖 Available Books: {$final_stats['available_books']} (was {$stats['available_books']})<br>";
    echo "</div>";

    // Step 9: Performance optimizations
    echo "<h2>⚡ Step 9: Performance Optimizations</h2>";

    // Add indexes for better performance
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_book_loans_status ON book_loans(status)",
        "CREATE INDEX IF NOT EXISTS idx_book_loans_due_date ON book_loans(due_date)",
        "CREATE INDEX IF NOT EXISTS idx_book_loans_member_status ON book_loans(member_id, status)",
        "CREATE INDEX IF NOT EXISTS idx_book_loans_book_status ON book_loans(book_id, status)"
    ];

    $indexes_created = 0;
    foreach ($indexes as $index_sql) {
        try {
            $db->exec($index_sql);
            $indexes_created++;
        } catch (Exception $e) {
            // Index might already exist, continue
        }
    }

    echo "<p class='success'>✅ Created/verified {$indexes_created} database indexes for better performance</p>";

    // Step 10: Summary and recommendations
    echo "<h2>📋 Step 10: Summary & Recommendations</h2>";

    $overdue_reduction = $stats['overdue_loans'] - $final_stats['overdue_loans'];
    $books_freed = $final_stats['available_books'] - $stats['available_books'];

    echo "<div class='info'>";
    echo "<strong>Changes Made:</strong><br>";
    echo "📉 Reduced overdue books by: {$overdue_reduction}<br>";
    echo "📚 Freed up books: {$books_freed}<br>";
    echo "💰 Applied reasonable fine caps ($25 max)<br>";
    echo "⚡ Added performance indexes<br>";
    echo "🔄 Extended some recent overdue loans<br>";
    echo "</div>";

    echo "<div class='warning'>";
    echo "<strong>Recommendations:</strong><br>";
    echo "• Run this script monthly to maintain balance<br>";
    echo "• Monitor overdue books - keep under 50-100<br>";
    echo "• Consider automated fine calculations<br>";
    echo "• Set up email reminders for due dates<br>";
    echo "• Regular database maintenance<br>";
    echo "</div>";

    $db->commit();
    echo "<h2 class='success'>✅ All fixes completed successfully!</h2>";
    echo "<p><a href='admin/dashboard.php'>← Back to Dashboard</a></p>";

} catch (Exception $e) {
    $db->rollback();
    echo "<h2 class='error'>❌ Error occurred: " . $e->getMessage() . "</h2>";
    echo "<p>All changes have been rolled back.</p>";
}

echo "</body></html>";
?>