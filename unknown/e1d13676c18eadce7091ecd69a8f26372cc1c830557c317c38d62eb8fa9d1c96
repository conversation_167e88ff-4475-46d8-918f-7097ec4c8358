<?php
/**
 * Google Welcome Component
 *
 * This file contains the welcome notification for first-time Google login users.
 */

// Check if this is a first-time Google login
$show_google_welcome = false;
$is_new_google_user = false;

if (isset($_SESSION['new_google_user']) && $_SESSION['new_google_user'] === true) {
    $show_google_welcome = true;
    $is_new_google_user = true;
    // Clear the flag so it only shows once
    unset($_SESSION['new_google_user']);
}

// Check if this is a linked Google account
if (isset($_SESSION['google_account_linked']) && $_SESSION['google_account_linked'] === true) {
    $show_google_welcome = true;
    $is_new_google_user = false;
    // Clear the flag so it only shows once
    unset($_SESSION['google_account_linked']);
}

// Only show if we have a flag set
if ($show_google_welcome):
?>
<div class="google-welcome-notification" id="googleWelcomeNotification">
    <div class="google-welcome-content">
        <div class="d-flex align-items-center mb-3">
            <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="24" height="24" class="me-2">
            <h5 class="mb-0"><?php echo $is_new_google_user ? 'Welcome to the Library!' : 'Google Account Linked'; ?></h5>
            <button type="button" class="btn-close ms-auto" aria-label="Close" id="closeGoogleWelcome"></button>
        </div>

        <?php if ($is_new_google_user): ?>
            <p>Your account has been created successfully using your Google account. You can now enjoy all the features of our library system!</p>
            <div class="d-flex justify-content-between align-items-center mt-3">
                <a href="<?php echo url('google_account.php'); ?>" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-gear me-1"></i> Manage Google Account
                </a>
                <button type="button" class="btn btn-sm btn-success" id="continueGoogleWelcome">
                    <i class="bi bi-check-circle me-1"></i> Got it!
                </button>
            </div>
        <?php else: ?>
            <p>Your Google account has been successfully linked to your library account. You can now sign in using Google authentication!</p>
            <div class="d-flex justify-content-between align-items-center mt-3">
                <a href="<?php echo url('google_account.php'); ?>" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-gear me-1"></i> Manage Google Account
                </a>
                <button type="button" class="btn btn-sm btn-success" id="continueGoogleWelcome">
                    <i class="bi bi-check-circle me-1"></i> Got it!
                </button>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.google-welcome-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    max-width: 400px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1050;
    overflow: hidden;
    animation: slideIn 0.5s ease-out;
}

.google-welcome-content {
    padding: 20px;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.google-welcome-notification.hiding {
    animation: slideOut 0.5s ease-in forwards;
}

@media (max-width: 576px) {
    .google-welcome-notification {
        bottom: 0;
        right: 0;
        left: 0;
        max-width: 100%;
        border-radius: 0;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const notification = document.getElementById('googleWelcomeNotification');
    const closeButton = document.getElementById('closeGoogleWelcome');
    const continueButton = document.getElementById('continueGoogleWelcome');

    if (notification && closeButton && continueButton) {
        // Close notification when close button is clicked
        closeButton.addEventListener('click', function() {
            hideNotification();
        });

        // Close notification when continue button is clicked
        continueButton.addEventListener('click', function() {
            hideNotification();
        });

        // Auto-hide after 10 seconds
        setTimeout(function() {
            hideNotification();
        }, 10000);

        function hideNotification() {
            notification.classList.add('hiding');
            setTimeout(function() {
                notification.remove();
            }, 500);
        }
    }
});
</script>
<?php endif; ?>
