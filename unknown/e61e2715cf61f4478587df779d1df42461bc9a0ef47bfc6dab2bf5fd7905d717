<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple LMS Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple LMS Test Page</h1>
        
        <div class="test-section">
            <h2>PHP Test</h2>
            <p class="success">✓ PHP is working! Version: <?php echo phpversion(); ?></p>
            <p class="success">✓ Current time: <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
        
        <div class="test-section">
            <h2>File System Test</h2>
            <?php
            $files = [
                'config/database.php',
                'config/config.php', 
                'includes/functions.php',
                'assets/css/home.css'
            ];
            
            foreach ($files as $file) {
                if (file_exists($file)) {
                    echo "<p class='success'>✓ $file exists</p>";
                } else {
                    echo "<p class='error'>✗ $file missing</p>";
                }
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>Database Test</h2>
            <?php
            try {
                require_once 'config/database.php';
                $database = new Database();
                $db = $database->getConnection();
                echo "<p class='success'>✓ Database connection successful</p>";
                
                // Test a simple query
                $query = "SELECT 1 as test";
                $stmt = $db->prepare($query);
                $stmt->execute();
                $result = $stmt->fetch();
                echo "<p class='success'>✓ Database query successful</p>";
                
            } catch (Exception $e) {
                echo "<p class='error'>✗ Database error: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>Session Test</h2>
            <?php
            session_start();
            echo "<p class='success'>✓ Session started</p>";
            echo "<p>Session ID: " . session_id() . "</p>";
            ?>
        </div>
        
        <div class="test-section">
            <h2>Configuration Test</h2>
            <?php
            try {
                require_once 'config/config.php';
                echo "<p class='success'>✓ Config loaded</p>";
                echo "<p>Base URL: " . BASE_URL . "</p>";
                echo "<p>URL function test: " . url('test/path') . "</p>";
            } catch (Exception $e) {
                echo "<p class='error'>✗ Config error: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>Functions Test</h2>
            <?php
            try {
                require_once 'includes/functions.php';
                echo "<p class='success'>✓ Functions loaded</p>";
                echo "<p>isLoggedIn(): " . (isLoggedIn() ? 'true' : 'false') . "</p>";
                echo "<p>isMemberLoggedIn(): " . (isMemberLoggedIn() ? 'true' : 'false') . "</p>";
            } catch (Exception $e) {
                echo "<p class='error'>✗ Functions error: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>Navigation Links</h2>
            <div class="btn-group" role="group">
                <a href="home.php" class="btn btn-primary">Home Page</a>
                <a href="index.php" class="btn btn-secondary">Index Page</a>
                <a href="login.php" class="btn btn-info">Login Page</a>
                <a href="catalog.php" class="btn btn-success">Catalog Page</a>
                <a href="diagnostic.php" class="btn btn-warning">Diagnostic</a>
                <a href="check_database.php" class="btn btn-danger">Database Check</a>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Server Information</h2>
            <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE']; ?></p>
            <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT']; ?></p>
            <p><strong>Script Name:</strong> <?php echo $_SERVER['SCRIPT_NAME']; ?></p>
            <p><strong>Request URI:</strong> <?php echo $_SERVER['REQUEST_URI']; ?></p>
            <p><strong>HTTP Host:</strong> <?php echo $_SERVER['HTTP_HOST']; ?></p>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
