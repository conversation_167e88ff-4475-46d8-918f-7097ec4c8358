<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if user is already logged in
if (isLoggedIn()) {
    // Redirect based on role
    if (isAdmin()) {
        redirect(url('admin/dashboard.php'));
    } elseif (isLibrarian()) {
        redirect(url('librarian/dashboard.php'));
    } else {
        redirect(url('index.php'));
    }
} elseif (isMemberLoggedIn()) {
    redirect(url('member_dashboard.php'));
}

$error = '';
$success = '';

// Process forgot password form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email']);

    // Validate input
    if (empty($email)) {
        $error = 'Please enter your email address';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address';
    } else {
        // Connect to database
        $database = new Database();
        $db = $database->getConnection();

        // Check if email exists in users table
        $query = "SELECT id, username FROM users WHERE email = :email";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();

        $user_exists = false;
        $is_member = false;
        $user_id = null;
        $username = null;

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch();
            $user_id = $row['id'];
            $username = $row['username'];
            $user_exists = true;
        } else {
            // Check if email exists in members table
            $query = "SELECT id, first_name FROM members WHERE email = :email";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':email', $email);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $row = $stmt->fetch();
                $user_id = $row['id'];
                $username = $row['first_name'];
                $user_exists = true;
                $is_member = true;
            }
        }

        if ($user_exists) {
            // Generate a unique reset token
            $reset_token = bin2hex(random_bytes(32));
            $expires = date('Y-m-d H:i:s', time() + 3600); // Token expires in 1 hour

            if ($is_member) {
                // Update members table with reset token
                $query = "UPDATE members SET reset_token = :token, reset_expires = :expires WHERE id = :id";
            } else {
                // Update users table with reset token
                $query = "UPDATE users SET reset_token = :token, reset_expires = :expires WHERE id = :id";
            }

            $stmt = $db->prepare($query);
            $stmt->bindParam(':token', $reset_token);
            $stmt->bindParam(':expires', $expires);
            $stmt->bindParam(':id', $user_id);
            $stmt->execute();

            // In a real application, you would send an email with the reset link
            // For this demo, we'll just show the reset link on the page
            $reset_link = url('reset_password.php') . '?token=' . $reset_token;

            $success = 'Password reset instructions have been sent to your email address.';

            // For demo purposes only - in a real application, this would be sent via email
            $success .= '<br><small class="text-muted">Demo: <a href="' . $reset_link . '">Click here to reset your password</a></small>';
        } else {
            // Don't reveal that the email doesn't exist for security reasons
            $success = 'If your email address exists in our database, you will receive a password recovery link at your email address.';
        }
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-image: url('assets/images/library-background.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .forgot-password-container {
            max-width: 500px;
            width: 100%;
            margin: 0 auto;
            animation: fadeIn 0.8s ease-in-out;
        }
        .card {
            border: none;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.8rem 2rem rgba(0, 0, 0, 0.25);
        }
        .card-header {
            background-color: #343a40;
            color: white;
            font-weight: bold;
            padding: 1.2rem;
        }
        .btn-primary {
            background-color: #343a40;
            border-color: #343a40;
            padding: 0.6rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #23272b;
            border-color: #23272b;
            transform: translateY(-2px);
        }
        .form-control {
            padding: 0.6rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #343a40;
            box-shadow: 0 0 0 0.25rem rgba(52, 58, 64, 0.25);
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @media (max-width: 576px) {
            .forgot-password-container {
                max-width: 100%;
                padding: 0 15px;
            }
            .card-body {
                padding: 1.5rem !important;
            }
        }
    </style>
</head>
<body>
    <div class="forgot-password-container">
        <div class="card">
            <div class="card-header text-center py-3">
                <h4 class="mb-0"><i class="bi bi-book me-2"></i>Library Management System</h4>
            </div>
            <div class="card-body p-4">
                <h5 class="card-title text-center mb-4">Forgot Password</h5>

                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <?php echo h($error); ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                    <div class="alert alert-success">
                        <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <?php if (empty($success)): ?>
                    <p class="text-muted mb-4">Enter your email address and we'll send you a link to reset your password.</p>
                    <form method="post" action="<?php echo h($_SERVER['PHP_SELF']); ?>">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                <input type="email" class="form-control" id="email" name="email" required autofocus>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-envelope-paper me-2"></i>Send Reset Link
                            </button>
                        </div>
                    </form>
                <?php endif; ?>

                <div class="mt-4 text-center">
                    <p><a href="<?php echo url('login.php'); ?>" class="text-decoration-none"><i class="bi bi-arrow-left me-1"></i> Back to Login</a></p>
                </div>
            </div>
            <div class="card-footer text-center py-3">
                <p class="mb-0">Library Management System &copy; <?php echo date('Y'); ?></p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
