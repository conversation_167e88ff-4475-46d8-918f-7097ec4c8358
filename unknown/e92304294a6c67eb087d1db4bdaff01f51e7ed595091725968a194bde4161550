<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect(url('../auth/login.php'));
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    setMessage('Invalid book ID', 'danger');
    redirect(url('books/index.php'));
}

$book_id = (int)$_GET['id'];

// Get book details
$query = "SELECT * FROM books WHERE id = :id";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $book_id);
$stmt->execute();

if ($stmt->rowCount() == 0) {
    setMessage('Book not found', 'danger');
    redirect(url('books/index.php'));
}

$book = $stmt->fetch();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $isbn = sanitize($_POST['isbn']);
    $title = sanitize($_POST['title']);
    $author = sanitize($_POST['author']);
    $category = sanitize($_POST['category']);
    $publication_year = (int)$_POST['publication_year'];
    $publisher = sanitize($_POST['publisher']);
    $quantity = (int)$_POST['quantity'];
    $available_quantity = (int)$_POST['available_quantity'];
    $shelf_location = sanitize($_POST['shelf_location']);
    $description = sanitize($_POST['description']);
    
    // Validate input
    $errors = [];
    
    if (empty($title)) {
        $errors[] = 'Title is required';
    }
    
    if (empty($author)) {
        $errors[] = 'Author is required';
    }
    
    if ($quantity < 0) {
        $errors[] = 'Quantity cannot be negative';
    }
    
    if ($available_quantity < 0) {
        $errors[] = 'Available quantity cannot be negative';
    }
    
    if ($available_quantity > $quantity) {
        $errors[] = 'Available quantity cannot be greater than total quantity';
    }
    
    // If no errors, update book
    if (empty($errors)) {
        // Handle cover image upload
        $cover_image = $book['cover_image']; // Keep existing image by default
        
        if (isset($_FILES['cover_image']) && $_FILES['cover_image']['error'] == 0) {
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
            $max_size = 2 * 1024 * 1024; // 2MB
            
            if (in_array($_FILES['cover_image']['type'], $allowed_types) && $_FILES['cover_image']['size'] <= $max_size) {
                $upload_dir = COVERS_PATH;
                $filename = time() . '_' . basename($_FILES['cover_image']['name']);
                $target_file = $upload_dir . $filename;
                
                if (move_uploaded_file($_FILES['cover_image']['tmp_name'], $target_file)) {
                    // Delete old image if exists
                    if (!empty($book['cover_image']) && file_exists(COVERS_PATH . $book['cover_image'])) {
                        unlink(COVERS_PATH . $book['cover_image']);
                    }
                    
                    $cover_image = $filename;
                } else {
                    $errors[] = 'Failed to upload image';
                }
            } else {
                $errors[] = 'Invalid image file. Only JPG, PNG, and GIF files under 2MB are allowed';
            }
        }
        
        if (empty($errors)) {
            // Update book in database
            $query = "UPDATE books SET 
                      isbn = :isbn,
                      title = :title,
                      author = :author,
                      category = :category,
                      publication_year = :publication_year,
                      publisher = :publisher,
                      quantity = :quantity,
                      available_quantity = :available_quantity,
                      shelf_location = :shelf_location,
                      description = :description,
                      cover_image = :cover_image
                      WHERE id = :id";
                      
            $stmt = $db->prepare($query);
            $stmt->bindParam(':isbn', $isbn);
            $stmt->bindParam(':title', $title);
            $stmt->bindParam(':author', $author);
            $stmt->bindParam(':category', $category);
            $stmt->bindParam(':publication_year', $publication_year);
            $stmt->bindParam(':publisher', $publisher);
            $stmt->bindParam(':quantity', $quantity);
            $stmt->bindParam(':available_quantity', $available_quantity);
            $stmt->bindParam(':shelf_location', $shelf_location);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':cover_image', $cover_image);
            $stmt->bindParam(':id', $book_id);
            
            if ($stmt->execute()) {
                setMessage('Book updated successfully', 'success');
                redirect(url('books/index.php'));
            } else {
                $errors[] = 'Failed to update book';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Book - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Edit Book</h1>
                    <a href="<?php echo url('books/index.php'); ?>" class="btn btn-sm btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Books
                    </a>
                </div>
                
                <?php if (isset($errors) && !empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-body">
                                <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF'] . '?id=' . $book_id); ?>" method="post" enctype="multipart/form-data">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="isbn" class="form-label">ISBN</label>
                                            <input type="text" class="form-control" id="isbn" name="isbn" value="<?php echo $book['isbn']; ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="title" name="title" value="<?php echo $book['title']; ?>" required>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="author" class="form-label">Author <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="author" name="author" value="<?php echo $book['author']; ?>" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="category" class="form-label">Category</label>
                                            <input type="text" class="form-control" id="category" name="category" value="<?php echo $book['category']; ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="publication_year" class="form-label">Publication Year</label>
                                            <input type="number" class="form-control" id="publication_year" name="publication_year" value="<?php echo $book['publication_year']; ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="publisher" class="form-label">Publisher</label>
                                            <input type="text" class="form-control" id="publisher" name="publisher" value="<?php echo $book['publisher']; ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <label for="quantity" class="form-label">Total Quantity <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="quantity" name="quantity" value="<?php echo $book['quantity']; ?>" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="available_quantity" class="form-label">Available Quantity <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="available_quantity" name="available_quantity" value="<?php echo $book['available_quantity']; ?>" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="shelf_location" class="form-label">Shelf Location</label>
                                            <input type="text" class="form-control" id="shelf_location" name="shelf_location" value="<?php echo $book['shelf_location']; ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea class="form-control" id="description" name="description" rows="4"><?php echo $book['description']; ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="cover_image" class="form-label">Cover Image</label>
                                        <input type="file" class="form-control" id="cover_image" name="cover_image">
                                        <div class="form-text">Leave empty to keep current image. Only JPG, PNG, and GIF files under 2MB are allowed.</div>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">Update Book</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Current Cover Image</h5>
                            </div>
                            <div class="card-body text-center">
                                <?php if (!empty($book['cover_image']) && file_exists(COVERS_PATH . $book['cover_image'])): ?>
                                    <img src="<?php echo url('uploads/covers/' . $book['cover_image']); ?>" alt="<?php echo $book['title']; ?>" class="img-fluid mb-3" style="max-height: 300px;">
                                <?php else: ?>
                                    <div class="bg-light p-5 mb-3 d-flex align-items-center justify-content-center">
                                        <i class="bi bi-book fs-1 text-secondary"></i>
                                    </div>
                                    <p class="text-muted">No cover image available</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
</body>
</html>
