<?php
/**
 * System Health Check
 * 
 * Quick monitoring script to check if the LMS system is balanced
 * and performing well.
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>LMS System Health Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .healthy { color: green; background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .critical { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .stats { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; min-width: 150px; text-align: center; }
    </style>
</head>
<body>";

echo "<h1>🏥 LMS System Health Check</h1>";
echo "<p>Checking system balance and performance...</p>";

try {
    // Get current statistics
    $stats_queries = [
        'total_loans' => "SELECT COUNT(*) as count FROM book_loans",
        'active_loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed'",
        'overdue_loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'overdue'",
        'returned_loans' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'returned'",
        'total_members' => "SELECT COUNT(*) as count FROM members",
        'active_members' => "SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status IN ('borrowed', 'overdue')",
        'total_books' => "SELECT COUNT(*) as count FROM books",
        'available_books' => "SELECT SUM(available_quantity) as count FROM books",
        'total_fines' => "SELECT COALESCE(SUM(fine), 0) as count FROM book_loans WHERE fine > 0"
    ];

    $stats = [];
    foreach ($stats_queries as $key => $query) {
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats[$key] = $stmt->fetch()['count'] ?? 0;
    }

    // Display current metrics
    echo "<h2>📊 Current System Metrics</h2>";
    echo "<div class='stats'>";
    echo "<div class='metric'><strong>📚 Total Loans</strong><br>{$stats['total_loans']}</div>";
    echo "<div class='metric'><strong>✅ Active Loans</strong><br>{$stats['active_loans']}</div>";
    echo "<div class='metric'><strong>⚠️ Overdue Loans</strong><br>{$stats['overdue_loans']}</div>";
    echo "<div class='metric'><strong>📖 Returned Loans</strong><br>{$stats['returned_loans']}</div>";
    echo "<div class='metric'><strong>👥 Total Members</strong><br>{$stats['total_members']}</div>";
    echo "<div class='metric'><strong>🔄 Active Members</strong><br>{$stats['active_members']}</div>";
    echo "<div class='metric'><strong>📚 Total Books</strong><br>{$stats['total_books']}</div>";
    echo "<div class='metric'><strong>📖 Available Books</strong><br>{$stats['available_books']}</div>";
    echo "<div class='metric'><strong>💰 Total Fines</strong><br>$" . number_format($stats['total_fines'], 2) . "</div>";
    echo "</div>";

    // Health checks
    echo "<h2>🔍 Health Check Results</h2>";

    // Check 1: Overdue books ratio
    $overdue_ratio = $stats['total_members'] > 0 ? ($stats['overdue_loans'] / $stats['total_members']) * 100 : 0;
    if ($overdue_ratio <= 5) {
        echo "<div class='healthy'>✅ Overdue Books Ratio: {$overdue_ratio}% (Healthy - under 5%)</div>";
    } elseif ($overdue_ratio <= 10) {
        echo "<div class='warning'>⚠️ Overdue Books Ratio: {$overdue_ratio}% (Warning - consider running balance script)</div>";
    } else {
        echo "<div class='critical'>❌ Overdue Books Ratio: {$overdue_ratio}% (Critical - run fix_overdue_balance.php)</div>";
    }

    // Check 2: Book availability
    $availability_ratio = $stats['total_books'] > 0 ? ($stats['available_books'] / ($stats['total_books'] * 50)) * 100 : 0; // Assuming 50 copies per book
    if ($availability_ratio >= 70) {
        echo "<div class='healthy'>✅ Book Availability: {$availability_ratio}% (Healthy)</div>";
    } elseif ($availability_ratio >= 50) {
        echo "<div class='warning'>⚠️ Book Availability: {$availability_ratio}% (Warning - many books are borrowed)</div>";
    } else {
        echo "<div class='critical'>❌ Book Availability: {$availability_ratio}% (Critical - most books are unavailable)</div>";
    }

    // Check 3: Active member engagement
    $engagement_ratio = $stats['total_members'] > 0 ? ($stats['active_members'] / $stats['total_members']) * 100 : 0;
    if ($engagement_ratio >= 10 && $engagement_ratio <= 30) {
        echo "<div class='healthy'>✅ Member Engagement: {$engagement_ratio}% (Healthy - good activity level)</div>";
    } elseif ($engagement_ratio < 10) {
        echo "<div class='warning'>⚠️ Member Engagement: {$engagement_ratio}% (Low - consider member outreach)</div>";
    } else {
        echo "<div class='warning'>⚠️ Member Engagement: {$engagement_ratio}% (High - monitor for capacity issues)</div>";
    }

    // Check 4: Fine amounts
    $avg_fine = $stats['overdue_loans'] > 0 ? $stats['total_fines'] / $stats['overdue_loans'] : 0;
    if ($avg_fine <= 25) {
        echo "<div class='healthy'>✅ Average Fine: $" . number_format($avg_fine, 2) . " (Reasonable)</div>";
    } elseif ($avg_fine <= 50) {
        echo "<div class='warning'>⚠️ Average Fine: $" . number_format($avg_fine, 2) . " (High - consider fine caps)</div>";
    } else {
        echo "<div class='critical'>❌ Average Fine: $" . number_format($avg_fine, 2) . " (Excessive - run balance script)</div>";
    }

    // Check 5: Database performance (simple query timing)
    $start_time = microtime(true);
    $db->query("SELECT COUNT(*) FROM book_loans bl JOIN books b ON bl.book_id = b.id JOIN members m ON bl.member_id = m.id WHERE bl.status = 'overdue'");
    $query_time = (microtime(true) - $start_time) * 1000; // Convert to milliseconds

    if ($query_time <= 100) {
        echo "<div class='healthy'>✅ Database Performance: {$query_time}ms (Fast)</div>";
    } elseif ($query_time <= 500) {
        echo "<div class='warning'>⚠️ Database Performance: {$query_time}ms (Slow - consider optimization)</div>";
    } else {
        echo "<div class='critical'>❌ Database Performance: {$query_time}ms (Very slow - needs optimization)</div>";
    }

    // Recommendations
    echo "<h2>💡 Recommendations</h2>";
    
    if ($overdue_ratio > 10) {
        echo "<div class='info'>🔧 Run the balance script: <a href='fix_overdue_balance.php'>fix_overdue_balance.php</a></div>";
    }
    
    if ($availability_ratio < 50) {
        echo "<div class='info'>📚 Consider adding more book copies or returning overdue books</div>";
    }
    
    if ($engagement_ratio < 10) {
        echo "<div class='info'>📢 Consider member outreach programs to increase library usage</div>";
    }
    
    if ($query_time > 500) {
        echo "<div class='info'>⚡ Database needs optimization - consider adding indexes or cleaning old data</div>";
    }

    // Quick actions
    echo "<h2>🚀 Quick Actions</h2>";
    echo "<div class='info'>";
    echo "<a href='fix_overdue_balance.php' style='margin: 5px; padding: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>🔧 Balance System</a> ";
    echo "<a href='admin/dashboard.php' style='margin: 5px; padding: 10px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>📊 View Dashboard</a> ";
    echo "<a href='reports/index.php' style='margin: 5px; padding: 10px; background: #17a2b8; color: white; text-decoration: none; border-radius: 5px;'>📋 Generate Reports</a>";
    echo "</div>";

    echo "<hr>";
    echo "<p><small>Last checked: " . date('Y-m-d H:i:s') . " | Run this check weekly for optimal performance</small></p>";

} catch (Exception $e) {
    echo "<div class='critical'>❌ Error checking system health: " . $e->getMessage() . "</div>";
}

echo "</body></html>";
?>
