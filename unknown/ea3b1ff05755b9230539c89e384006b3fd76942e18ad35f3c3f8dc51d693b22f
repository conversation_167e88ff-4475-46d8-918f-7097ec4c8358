<?php
/**
 * Fix Overdue Books Issue
 * This script will rebalance the loan statuses to create more realistic data
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>🔧 Fixing Overdue Books Issue</h2>";
    
    // First, let's see the current situation
    echo "<h3>📊 Current Status Before Fix</h3>";
    
    $stmt = $db->prepare('SELECT status, COUNT(*) as count FROM book_loans GROUP BY status');
    $stmt->execute();
    $current_status = $stmt->fetchAll();
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    foreach($current_status as $status) {
        echo "<p><strong>{$status['status']}:</strong> {$status['count']} loans</p>";
    }
    echo "</div>";
    
    // Get total loans
    $stmt = $db->prepare('SELECT COUNT(*) as total FROM book_loans');
    $stmt->execute();
    $total_loans = $stmt->fetch()['total'];
    
    echo "<p><strong>Total Loans:</strong> $total_loans</p>";
    
    // Start transaction
    $db->beginTransaction();
    
    echo "<h3>🔄 Rebalancing Loan Statuses</h3>";
    
    // Target distribution (more realistic):
    // 70% returned
    // 15% active (borrowed, not overdue)
    // 10% overdue
    // 5% recently borrowed (within last week)
    
    $target_returned = round($total_loans * 0.70);
    $target_active = round($total_loans * 0.15);
    $target_overdue = round($total_loans * 0.10);
    $target_recent = $total_loans - ($target_returned + $target_active + $target_overdue);
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h5>Target Distribution:</h5>";
    echo "<p><strong>Returned:</strong> $target_returned (70%)</p>";
    echo "<p><strong>Active (not overdue):</strong> $target_active (15%)</p>";
    echo "<p><strong>Overdue:</strong> $target_overdue (10%)</p>";
    echo "<p><strong>Recent:</strong> $target_recent (5%)</p>";
    echo "</div>";
    
    // 1. Set most loans to returned status
    $stmt = $db->prepare("
        UPDATE book_loans 
        SET status = 'returned', 
            return_date = DATE_SUB(CURDATE(), INTERVAL FLOOR(RAND() * 180) DAY),
            fine = 0
        ORDER BY RAND() 
        LIMIT ?
    ");
    $stmt->execute([$target_returned]);
    echo "<p>✅ Set $target_returned loans to 'returned' status</p>";
    
    // 2. Set some loans to active (borrowed, not overdue)
    $stmt = $db->prepare("
        UPDATE book_loans 
        SET status = 'borrowed',
            issue_date = DATE_SUB(CURDATE(), INTERVAL FLOOR(RAND() * 10) DAY),
            due_date = DATE_ADD(CURDATE(), INTERVAL (7 + FLOOR(RAND() * 7)) DAY),
            return_date = NULL,
            fine = 0
        WHERE status != 'returned'
        ORDER BY RAND() 
        LIMIT ?
    ");
    $stmt->execute([$target_active]);
    echo "<p>✅ Set $target_active loans to 'borrowed' (active) status</p>";
    
    // 3. Set some loans to overdue
    $stmt = $db->prepare("
        UPDATE book_loans 
        SET status = 'overdue',
            issue_date = DATE_SUB(CURDATE(), INTERVAL (20 + FLOOR(RAND() * 40)) DAY),
            due_date = DATE_SUB(CURDATE(), INTERVAL (1 + FLOOR(RAND() * 30)) DAY),
            return_date = NULL,
            fine = (1 + FLOOR(RAND() * 30)) * 1.00
        WHERE status NOT IN ('returned', 'borrowed')
        ORDER BY RAND() 
        LIMIT ?
    ");
    $stmt->execute([$target_overdue]);
    echo "<p>✅ Set $target_overdue loans to 'overdue' status</p>";
    
    // 4. Set remaining loans to recent borrowed
    $stmt = $db->prepare("
        UPDATE book_loans 
        SET status = 'borrowed',
            issue_date = DATE_SUB(CURDATE(), INTERVAL FLOOR(RAND() * 7) DAY),
            due_date = DATE_ADD(CURDATE(), INTERVAL (10 + FLOOR(RAND() * 7)) DAY),
            return_date = NULL,
            fine = 0
        WHERE status NOT IN ('returned', 'borrowed', 'overdue')
    ");
    $stmt->execute();
    echo "<p>✅ Set remaining loans to 'borrowed' (recent) status</p>";
    
    // Update book availability based on current loans
    echo "<h3>📚 Updating Book Availability</h3>";
    
    // Reset all books to full availability first
    $stmt = $db->prepare("UPDATE books SET available_quantity = quantity");
    $stmt->execute();
    echo "<p>✅ Reset all books to full availability</p>";
    
    // Reduce availability for currently borrowed books
    $stmt = $db->prepare("
        UPDATE books b 
        SET available_quantity = GREATEST(0, quantity - (
            SELECT COUNT(*) 
            FROM book_loans bl 
            WHERE bl.book_id = b.id 
            AND bl.status IN ('borrowed', 'overdue')
        ))
    ");
    $stmt->execute();
    echo "<p>✅ Updated book availability based on current loans</p>";
    
    // Commit transaction
    $db->commit();
    
    echo "<h3>✅ Fix Complete - New Status Distribution</h3>";
    
    // Show new status distribution
    $stmt = $db->prepare('SELECT status, COUNT(*) as count FROM book_loans GROUP BY status');
    $stmt->execute();
    $new_status = $stmt->fetchAll();
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    foreach($new_status as $status) {
        $percentage = round(($status['count'] / $total_loans) * 100, 1);
        echo "<p><strong>{$status['status']}:</strong> {$status['count']} loans ({$percentage}%)</p>";
    }
    echo "</div>";
    
    // Show overdue calculation
    $stmt = $db->prepare('SELECT COUNT(*) as count FROM book_loans WHERE status = "overdue" OR (status = "borrowed" AND due_date < CURDATE())');
    $stmt->execute();
    $total_overdue_new = $stmt->fetch()['count'];
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h5>📊 New Overdue Calculation:</h5>";
    echo "<p><strong>Total Overdue Books:</strong> $total_overdue_new</p>";
    echo "<p>This should now show a much more reasonable number in your dashboard!</p>";
    echo "</div>";
    
    echo "<h3>🎉 Success!</h3>";
    echo "<p>The overdue books issue has been fixed. Your dashboard should now show realistic numbers.</p>";
    echo "<p><a href='admin/dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Dashboard</a></p>";
    
} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollback();
    }
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h4>❌ Error</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
