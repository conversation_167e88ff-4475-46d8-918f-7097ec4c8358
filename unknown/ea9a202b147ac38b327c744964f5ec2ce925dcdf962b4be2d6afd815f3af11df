<?php
/**
 * Debug Settings Access
 */

echo "<h2>🔍 Settings Debug</h2>";
echo "<style>
    .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .btn { display: inline-block; padding: 10px 15px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }
    .btn-primary { background: #007bff; }
    .btn-success { background: #28a745; }
    .btn-danger { background: #dc3545; }
</style>";

// Check file existence
$settings_file = __DIR__ . '/admin/settings.php';
$simple_settings_file = __DIR__ . '/admin/simple_settings.php';
$test_file = __DIR__ . '/admin/test.php';

echo "<div class='info'><strong>File System Check:</strong></div>";
echo "<ul>";
echo "<li><strong>settings.php exists:</strong> " . (file_exists($settings_file) ? 'Yes' : 'No') . "</li>";
echo "<li><strong>simple_settings.php exists:</strong> " . (file_exists($simple_settings_file) ? 'Yes' : 'No') . "</li>";
echo "<li><strong>test.php exists:</strong> " . (file_exists($test_file) ? 'Yes' : 'No') . "</li>";
echo "</ul>";

// Test URLs
echo "<div class='info'><strong>Test Links:</strong></div>";
$test_urls = [
    'Test File' => 'admin/test.php',
    'Simple Settings' => 'admin/simple_settings.php',
    'Original Settings' => 'admin/settings.php'
];

foreach ($test_urls as $name => $url) {
    echo "<a href='$url' class='btn btn-primary' target='_blank'>$name</a>";
}

// Check session and authentication
session_start();
echo "<div class='info'><strong>Authentication Check:</strong></div>";
echo "<ul>";
echo "<li><strong>Session Active:</strong> " . (session_status() === PHP_SESSION_ACTIVE ? 'Yes' : 'No') . "</li>";
echo "<li><strong>User ID:</strong> " . ($_SESSION['user_id'] ?? 'Not set') . "</li>";
echo "<li><strong>Username:</strong> " . ($_SESSION['username'] ?? 'Not set') . "</li>";
echo "<li><strong>Role:</strong> " . ($_SESSION['role'] ?? 'Not set') . "</li>";
echo "</ul>";

// Quick login function
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['quick_login'])) {
    try {
        require_once 'config/database.php';
        $database = new Database();
        $db = $database->getConnection();
        
        // Get admin user
        $query = "SELECT * FROM users WHERE role = 'admin' LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $user = $stmt->fetch();
            
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['role'] = $user['role'];
            
            echo "<div class='success'>✅ Successfully logged in as admin!</div>";
            echo "<script>setTimeout(function(){ window.location.reload(); }, 1000);</script>";
        } else {
            echo "<div class='error'>❌ No admin user found</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Login failed: " . $e->getMessage() . "</div>";
    }
}

// Show login option if not logged in
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    echo "<div class='error'>❌ You need to be logged in as admin</div>";
    echo "<form method='POST' style='display: inline;'>";
    echo "<button type='submit' name='quick_login' class='btn btn-success'>🚀 Quick Admin Login</button>";
    echo "</form>";
} else {
    echo "<div class='success'>✅ You are logged in as admin!</div>";
    echo "<a href='admin/settings.php' class='btn btn-success'>🔧 Try Settings Now</a>";
}

// Test HTTP access to settings
echo "<div class='info'><strong>HTTP Test:</strong></div>";
$test_url = 'http://localhost/Library/lms/admin/settings.php';
$context = stream_context_create([
    'http' => [
        'timeout' => 5,
        'ignore_errors' => true
    ]
]);

$response = @file_get_contents($test_url, false, $context);
if ($response !== false) {
    if (strpos($response, 'Not Found') !== false) {
        echo "<div class='error'>❌ HTTP returns 'Not Found'</div>";
    } elseif (strpos($response, 'System Settings') !== false) {
        echo "<div class='success'>✅ Settings page loads via HTTP</div>";
    } elseif (strpos($response, 'login') !== false || strpos($response, 'redirect') !== false) {
        echo "<div class='info'>⚠️ Settings redirects (needs login)</div>";
    } else {
        echo "<div class='info'>ℹ️ Settings returns unknown content</div>";
    }
} else {
    echo "<div class='error'>❌ Cannot access via HTTP</div>";
}

echo "<div class='success'>🎉 Debug completed!</div>";
?>
