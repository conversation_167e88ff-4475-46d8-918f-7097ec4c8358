<?php
session_start();

// Include required files
require_once "../config/database.php";
require_once "../config/config.php";
require_once "../includes/functions.php";

// Check if user is admin (with development bypass)
if (!isLoggedIn() || !isAdmin()) {
    header("Location: ../auth/login.php");
    exit();
}

$database = new Database();
$db = $database->getConnection();

$success_message = "";
$error_message = "";

// Helper function to get settings with table creation if needed
function getSetting($db, $group, $key, $default = "") {
    try {
        // Check if settings table exists, create if not
        $checkTable = "SHOW TABLES LIKE 'settings'";
        $stmt = $db->prepare($checkTable);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            // Create settings table
            $createTable = "CREATE TABLE settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_group VARCHAR(50) NOT NULL,
                setting_key VARCHAR(100) NOT NULL,
                setting_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_setting (setting_group, setting_key)
            )";
            $db->exec($createTable);
        }

        $query = "SELECT setting_value FROM settings WHERE setting_group = ? AND setting_key = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$group, $key]);
        $result = $stmt->fetch();
        return $result ? $result["setting_value"] : $default;
    } catch (Exception $e) {
        error_log("Settings error: " . $e->getMessage());
        return $default;
    }
}

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    try {
        if (isset($_POST["save_email_settings"])) {
            $settings = [
                "from_email" => $_POST["from_email"] ?? "<EMAIL>",
                "from_name" => $_POST["from_name"] ?? "Library Management System",
                "reply_to" => $_POST["reply_to"] ?? "<EMAIL>",
                "smtp_enabled" => isset($_POST["smtp_enabled"]) ? "true" : "false",
                "smtp_host" => $_POST["smtp_host"] ?? "",
                "smtp_port" => $_POST["smtp_port"] ?? "587",
                "smtp_username" => $_POST["smtp_username"] ?? "",
                "smtp_password" => $_POST["smtp_password"] ?? "",
                "smtp_secure" => $_POST["smtp_secure"] ?? "tls"
            ];

            foreach ($settings as $key => $value) {
                $query = "INSERT INTO settings (setting_group, setting_key, setting_value)
                          VALUES ('email', ?, ?)
                          ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
                $stmt = $db->prepare($query);
                $stmt->execute([$key, $value]);
            }

            $success_message = "Email settings saved successfully!";
        }

        if (isset($_POST["send_test_email"])) {
            $to_email = $_POST["test_email"] ?? "<EMAIL>";

            // Validate email address
            if (!filter_var($to_email, FILTER_VALIDATE_EMAIL)) {
                $error_message = "Please enter a valid email address for testing.";
            } else {
                $subject = "Test Email from LMS";
                $body = "<h3>Test Email</h3><p>This is a test email from your Library Management System.</p><p>Sent at: " . date('Y-m-d H:i:s') . "</p>";

                $from_name = getSetting($db, "email", "from_name", "Library System");
                $from_email = getSetting($db, "email", "from_email", "<EMAIL>");

                $headers = "MIME-Version: 1.0\r\n";
                $headers .= "Content-type:text/html;charset=UTF-8\r\n";
                $headers .= "From: {$from_name} <{$from_email}>\r\n";
                $headers .= "Reply-To: " . getSetting($db, "email", "reply_to", $from_email) . "\r\n";

                if (mail($to_email, $subject, $body, $headers)) {
                    $success_message = "Test email sent successfully to {$to_email}";
                } else {
                    $error_message = "Failed to send test email. Please check your email settings and server configuration.";
                }
            }
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Settings - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
</head>
<body>
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>📧 Email Settings</h1>
            <div>
                <a href="dashboard.php" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>

        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <form method="POST">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Basic Email Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="from_email" class="form-label">From Email</label>
                        <input type="email" class="form-control" id="from_email" name="from_email"
                               value="<?php echo htmlspecialchars(getSetting($db, 'email', 'from_email', '<EMAIL>')); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="from_name" class="form-label">From Name</label>
                        <input type="text" class="form-control" id="from_name" name="from_name"
                               value="<?php echo htmlspecialchars(getSetting($db, 'email', 'from_name', 'Library Management System')); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="reply_to" class="form-label">Reply To Email</label>
                        <input type="email" class="form-control" id="reply_to" name="reply_to"
                               value="<?php echo htmlspecialchars(getSetting($db, 'email', 'reply_to', '<EMAIL>')); ?>">
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5>Test Email</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="test_email" class="form-label">Test Email Address</label>
                        <input type="email" class="form-control" id="test_email" name="test_email"
                               value="<EMAIL>">
                    </div>
                    <button type="submit" name="send_test_email" class="btn btn-info">Send Test Email</button>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" name="save_email_settings" class="btn btn-primary">
                    <i class="bi bi-save"></i> Save Email Settings
                </button>
                <a href="dashboard.php" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>