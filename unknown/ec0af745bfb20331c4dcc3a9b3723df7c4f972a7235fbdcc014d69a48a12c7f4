<?php
/**
 * Generate 1000+ Users with Comprehensive Loan Data
 * This script creates a large dataset of members with realistic borrowing patterns
 */

require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Set execution time limit for large data generation
set_time_limit(300); // 5 minutes

echo "<h1>🚀 Generating 1000+ Users with Loan Data</h1>";
echo "<p><strong>⏱️ This may take a few minutes...</strong></p>";

// Arrays for generating realistic names
$first_names = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'Alexander', 'Karen', 'Patrick', 'Betty',
    'Frank', 'Helen', 'Raymond', 'Sandra', 'Jack', 'Donna', 'Dennis', 'Carol', 'Jerry', 'Ruth',
    'Tyler', 'Sharon', 'Aaron', 'Michelle', 'Jose', 'Laura', 'Henry', 'Sarah', 'Adam', 'Kimberly',
    'Douglas', 'Deborah', 'Nathan', 'Dorothy', 'Peter', 'Lisa', 'Zachary', 'Nancy', 'Kyle', 'Karen',
    'Noah', 'Betty', 'Alan', 'Helen', 'Ethan', 'Sandra', 'Jeremy', 'Donna', 'Liam', 'Carol',
    'Mason', 'Ruth', 'Lucas', 'Sharon', 'Logan', 'Michelle', 'Oliver', 'Laura', 'Elijah', 'Sarah'
];

$last_names = [
    'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez',
    'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin',
    'Lee', 'Perez', 'Thompson', 'White', 'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson',
    'Walker', 'Young', 'Allen', 'King', 'Wright', 'Scott', 'Torres', 'Nguyen', 'Hill', 'Flores',
    'Green', 'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell', 'Carter', 'Roberts',
    'Gomez', 'Phillips', 'Evans', 'Turner', 'Diaz', 'Parker', 'Cruz', 'Edwards', 'Collins', 'Reyes',
    'Stewart', 'Morris', 'Morales', 'Murphy', 'Cook', 'Rogers', 'Gutierrez', 'Ortiz', 'Morgan', 'Cooper',
    'Peterson', 'Bailey', 'Reed', 'Kelly', 'Howard', 'Ramos', 'Kim', 'Cox', 'Ward', 'Richardson',
    'Watson', 'Brooks', 'Chavez', 'Wood', 'James', 'Bennett', 'Gray', 'Mendoza', 'Ruiz', 'Hughes',
    'Price', 'Alvarez', 'Castillo', 'Sanders', 'Patel', 'Myers', 'Long', 'Ross', 'Foster', 'Jimenez'
];

$cities = [
    'Springfield', 'Franklin', 'Georgetown', 'Clinton', 'Greenville', 'Madison', 'Marion', 'Salem',
    'Fairview', 'Bristol', 'Riverside', 'Auburn', 'Manchester', 'Milton', 'Newport', 'Centerville',
    'Kingston', 'Ashland', 'Burlington', 'Oxford', 'Clayton', 'Lexington', 'Milford', 'Dayton'
];

$streets = [
    'Main St', 'Oak Ave', 'Pine Rd', 'Maple Dr', 'Cedar Ln', 'Elm St', 'Park Ave', 'First St',
    'Second St', 'Third St', 'Church St', 'School St', 'Washington St', 'Lincoln Ave', 'Jefferson Rd',
    'Madison Dr', 'Franklin St', 'Jackson Ave', 'Wilson Rd', 'Davis Ln', 'Miller St', 'Johnson Ave'
];

// Function to generate a random member
function generateRandomMember($first_names, $last_names, $cities, $streets) {
    $first_name = $first_names[array_rand($first_names)];
    $last_name = $last_names[array_rand($last_names)];
    $city = $cities[array_rand($cities)];
    $street = $streets[array_rand($streets)];

    // Generate unique email
    $email_base = strtolower($first_name . '.' . $last_name);
    $email = $email_base . rand(1, 9999) . '@email.com';

    // Generate phone number
    $phone = '555-' . str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);

    // Generate address
    $address = rand(100, 9999) . ' ' . $street . ', ' . $city . ', IL ' . rand(60000, 65000);

    // Generate membership date (last 2 years) with random time
    $start_date = strtotime('-2 years');
    $end_date = strtotime('-1 month');
    $membership_timestamp = rand($start_date, $end_date);

    // Add random registration time (office hours: 9 AM to 5 PM)
    $reg_hour = rand(9, 17);
    $reg_minute = rand(0, 59);
    $reg_second = rand(0, 59);
    $membership_timestamp += ($reg_hour * 3600) + ($reg_minute * 60) + $reg_second;

    $membership_date = date('Y-m-d H:i:s', $membership_timestamp);

    return [
        'first_name' => $first_name,
        'last_name' => $last_name,
        'email' => $email,
        'phone' => $phone,
        'address' => $address,
        'membership_date' => $membership_date
    ];
}

// Function to add member with password
function addMemberWithPassword($db, $member_data) {
    // Generate password
    $default_password = strtolower($member_data['first_name']) . '123';
    $hashed_password = password_hash($default_password, PASSWORD_DEFAULT);

    // Insert member
    $query = "INSERT INTO members (first_name, last_name, email, phone, address, membership_date, membership_status, password)
              VALUES (:first_name, :last_name, :email, :phone, :address, :membership_date, 'active', :password)";

    $stmt = $db->prepare($query);
    $stmt->bindParam(':first_name', $member_data['first_name']);
    $stmt->bindParam(':last_name', $member_data['last_name']);
    $stmt->bindParam(':email', $member_data['email']);
    $stmt->bindParam(':phone', $member_data['phone']);
    $stmt->bindParam(':address', $member_data['address']);
    $stmt->bindParam(':membership_date', $member_data['membership_date']);
    $stmt->bindParam(':password', $hashed_password);

    if ($stmt->execute()) {
        return $db->lastInsertId();
    }
    return false;
}

// Function to create loan with realistic random dates
function createLoanWithStatus($db, $book_id, $member_id, $status, $member_join_date) {
    // Generate random issue date between member join date and now
    $join_timestamp = strtotime($member_join_date);
    $now_timestamp = strtotime('today');

    // Make sure there's at least 30 days between join date and now for realistic borrowing
    $min_borrow_date = max($join_timestamp, strtotime('-2 years'));
    $max_borrow_date = $now_timestamp - (30 * 24 * 60 * 60); // At least 30 days ago

    if ($max_borrow_date <= $min_borrow_date) {
        $max_borrow_date = $now_timestamp - (7 * 24 * 60 * 60); // At least 1 week ago
    }

    // Add random time (library hours: 8 AM to 8 PM)
    $random_hour = rand(8, 20); // 8 AM to 8 PM
    $random_minute = rand(0, 59);
    $random_second = rand(0, 59);

    $issue_timestamp = rand($min_borrow_date, $max_borrow_date);
    $issue_timestamp += ($random_hour * 3600) + ($random_minute * 60) + $random_second;

    $issue_date = date('Y-m-d H:i:s', $issue_timestamp);
    $due_date = date('Y-m-d H:i:s', $issue_timestamp + (14 * 24 * 60 * 60)); // 14 days later

    $return_date = null;
    $fine = 0;

    if ($status === 'returned') {
        // Random return date - could be early, on time, or late
        $return_delay = rand(-3, 12); // -3 days early to 12 days late
        $return_timestamp = strtotime($due_date) + ($return_delay * 24 * 60 * 60);

        // Make sure return date is not in the future
        if ($return_timestamp > $now_timestamp) {
            $return_timestamp = $now_timestamp - rand(1, 7) * 24 * 60 * 60; // 1-7 days ago
        }

        // Add random return time (library hours: 8 AM to 8 PM)
        $return_hour = rand(8, 20);
        $return_minute = rand(0, 59);
        $return_second = rand(0, 59);
        $return_timestamp += ($return_hour * 3600) + ($return_minute * 60) + $return_second;

        $return_date = date('Y-m-d H:i:s', $return_timestamp);

        // Calculate fine if late
        if ($return_delay > 0) {
            $fine = $return_delay * 1.00; // $1 per day
        }
    } elseif ($status === 'overdue') {
        // For overdue books, make sure due date is in the past
        $max_overdue_issue = $now_timestamp - (20 * 24 * 60 * 60); // At least 20 days ago
        $min_overdue_issue = $now_timestamp - (90 * 24 * 60 * 60); // At most 90 days ago

        $issue_timestamp = rand($min_overdue_issue, $max_overdue_issue);

        // Add random time for overdue books
        $overdue_hour = rand(8, 20);
        $overdue_minute = rand(0, 59);
        $overdue_second = rand(0, 59);
        $issue_timestamp += ($overdue_hour * 3600) + ($overdue_minute * 60) + $overdue_second;

        $issue_date = date('Y-m-d H:i:s', $issue_timestamp);
        $due_date = date('Y-m-d H:i:s', $issue_timestamp + (14 * 24 * 60 * 60));

        $status = 'borrowed'; // Will be updated to overdue later

        // Calculate fine based on days overdue
        $days_overdue = ($now_timestamp - strtotime($due_date)) / (24 * 60 * 60);
        $fine = max(1, floor($days_overdue)) * 1.00; // $1 per day overdue
    } else {
        // For currently borrowed books, issue date should be recent
        $recent_issue_start = $now_timestamp - (13 * 24 * 60 * 60); // Up to 13 days ago
        $recent_issue_end = $now_timestamp - (1 * 24 * 60 * 60); // At least 1 day ago

        $issue_timestamp = rand($recent_issue_start, $recent_issue_end);

        // Add random time for current loans
        $current_hour = rand(8, 20);
        $current_minute = rand(0, 59);
        $current_second = rand(0, 59);
        $issue_timestamp += ($current_hour * 3600) + ($current_minute * 60) + $current_second;

        $issue_date = date('Y-m-d H:i:s', $issue_timestamp);
        $due_date = date('Y-m-d H:i:s', $issue_timestamp + (14 * 24 * 60 * 60));
    }

    $query = "INSERT INTO book_loans (book_id, member_id, issue_date, due_date, return_date, status, fine)
              VALUES (:book_id, :member_id, :issue_date, :due_date, :return_date, :status, :fine)";

    $stmt = $db->prepare($query);
    $stmt->bindParam(':book_id', $book_id);
    $stmt->bindParam(':member_id', $member_id);
    $stmt->bindParam(':issue_date', $issue_date);
    $stmt->bindParam(':due_date', $due_date);
    $stmt->bindParam(':return_date', $return_date);
    $stmt->bindParam(':status', $status);
    $stmt->bindParam(':fine', $fine);

    return $stmt->execute();
}

// Get available books
$books_query = "SELECT id FROM books ORDER BY id";
$books_stmt = $db->prepare($books_query);
$books_stmt->execute();
$available_books = $books_stmt->fetchAll(PDO::FETCH_COLUMN);

if (empty($available_books)) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ No Books Available</h3>";
    echo "<p>Please add books to the library first before generating users.</p>";
    echo "</div>";
    exit;
}

echo "<h2>📊 Starting Generation Process</h2>";
echo "<p>Available books: " . count($available_books) . "</p>";

// Start generation
$target_users = 1000;
$users_created = 0;
$loans_created = 0;
$batch_size = 50; // Process in batches for better performance

echo "<div id='progress' style='background: #e9ecef; border-radius: 5px; padding: 10px; margin: 20px 0;'>";
echo "<div id='progress-bar' style='background: #007bff; height: 20px; border-radius: 3px; width: 0%; transition: width 0.3s;'></div>";
echo "<p id='progress-text'>Starting...</p>";
echo "</div>";

// Disable autocommit for better performance
$db->beginTransaction();

try {
    for ($i = 0; $i < $target_users; $i++) {
        // Generate member
        $member_data = generateRandomMember($first_names, $last_names, $cities, $streets);
        $member_id = addMemberWithPassword($db, $member_data);

        if ($member_id) {
            $users_created++;

            // Generate 1-5 loans per member with realistic timing spread over their membership
            $num_loans = rand(1, 5);

            // Create loans at different times during their membership
            for ($j = 0; $j < $num_loans; $j++) {
                $book_id = $available_books[array_rand($available_books)];

                // Vary loan status based on loan number (older loans more likely to be returned)
                if ($j == 0) {
                    // First loan - more likely to be returned (older)
                    $rand = rand(1, 100);
                    if ($rand <= 60) $status = 'returned';
                    elseif ($rand <= 85) $status = 'overdue';
                    else $status = 'borrowed';
                } elseif ($j == $num_loans - 1) {
                    // Last loan - more likely to be current
                    $rand = rand(1, 100);
                    if ($rand <= 50) $status = 'borrowed';
                    elseif ($rand <= 75) $status = 'returned';
                    else $status = 'overdue';
                } else {
                    // Middle loans - balanced distribution
                    $rand = rand(1, 100);
                    if ($rand <= 35) $status = 'returned';
                    elseif ($rand <= 70) $status = 'borrowed';
                    else $status = 'overdue';
                }

                // Pass member's join date for realistic date generation
                if (createLoanWithStatus($db, $book_id, $member_id, $status, $member_data['membership_date'])) {
                    $loans_created++;
                }
            }
        }

        // Commit in batches
        if (($i + 1) % $batch_size === 0) {
            $db->commit();
            $db->beginTransaction();

            // Update progress
            $progress = (($i + 1) / $target_users) * 100;
            echo "<script>
                document.getElementById('progress-bar').style.width = '{$progress}%';
                document.getElementById('progress-text').innerHTML = 'Created " . ($i + 1) . " / {$target_users} users ({$progress}%)';
            </script>";
            flush();
        }
    }

    // Final commit
    $db->commit();

} catch (Exception $e) {
    $db->rollBack();
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error During Generation</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
    exit;
}

// Update overdue statuses and calculate fines
echo "<h2>📅 Updating Overdue Loan Statuses and Calculating Fines</h2>";

// First, update status to overdue for books past due date
$overdue_query = "UPDATE book_loans SET status = 'overdue' WHERE status = 'borrowed' AND due_date < CURDATE()";
$overdue_stmt = $db->prepare($overdue_query);
$overdue_stmt->execute();
$overdue_count = $overdue_stmt->rowCount();

echo "<p>✅ Updated {$overdue_count} loans to overdue status.</p>";

// Calculate and update fines for overdue books
$fine_query = "
    UPDATE book_loans
    SET fine = DATEDIFF(CURDATE(), due_date) * 1.00
    WHERE status = 'overdue' AND due_date < CURDATE() AND fine = 0
";
$fine_stmt = $db->prepare($fine_query);
$fine_stmt->execute();
$fine_count = $fine_stmt->rowCount();

echo "<p>💰 Calculated fines for {$fine_count} overdue books.</p>";

// Also update fines for returned books that were returned late
$late_return_query = "
    UPDATE book_loans
    SET fine = DATEDIFF(return_date, due_date) * 1.00
    WHERE status = 'returned' AND return_date > due_date AND fine = 0
";
$late_return_stmt = $db->prepare($late_return_query);
$late_return_stmt->execute();
$late_return_count = $late_return_stmt->rowCount();

echo "<p>📚 Updated fines for {$late_return_count} late returned books.</p>";

// Get final statistics
$stats_query = "
    SELECT
        (SELECT COUNT(*) FROM members) as total_members,
        (SELECT COUNT(*) FROM book_loans) as total_loans,
        (SELECT COUNT(*) FROM book_loans WHERE status = 'borrowed') as active_loans,
        (SELECT COUNT(*) FROM book_loans WHERE status = 'returned') as returned_loans,
        (SELECT COUNT(*) FROM book_loans WHERE status = 'overdue') as overdue_loans,
        (SELECT SUM(fine) FROM book_loans WHERE fine > 0) as total_fines
";
$stats_stmt = $db->prepare($stats_query);
$stats_stmt->execute();
$stats = $stats_stmt->fetch();

echo "<script>
    document.getElementById('progress-bar').style.width = '100%';
    document.getElementById('progress-text').innerHTML = 'Generation Complete! ✅';
</script>";

echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>🎉 SUCCESS! 1000+ Users Generated!</h2>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
echo "<div><strong>👥 Total Members:</strong> {$stats['total_members']}</div>";
echo "<div><strong>📚 Total Loans:</strong> {$stats['total_loans']}</div>";
echo "<div><strong>✅ Active Loans:</strong> {$stats['active_loans']}</div>";
echo "<div><strong>📖 Returned Loans:</strong> {$stats['returned_loans']}</div>";
echo "<div><strong>⚠️ Overdue Loans:</strong> {$stats['overdue_loans']}</div>";
echo "<div><strong>💰 Total Fines:</strong> $" . number_format($stats['total_fines'], 2) . "</div>";
echo "</div>";
echo "</div>";

// Show detailed fine statistics
echo "<h2>💰 Fine Statistics</h2>";
$fine_stats_query = "
    SELECT
        COUNT(CASE WHEN fine > 0 THEN 1 END) as loans_with_fines,
        COUNT(CASE WHEN status = 'overdue' AND fine > 0 THEN 1 END) as overdue_with_fines,
        COUNT(CASE WHEN status = 'returned' AND fine > 0 THEN 1 END) as returned_with_fines,
        AVG(CASE WHEN fine > 0 THEN fine END) as avg_fine,
        MAX(fine) as max_fine,
        MIN(CASE WHEN fine > 0 THEN fine END) as min_fine
    FROM book_loans
";
$fine_stats_stmt = $db->prepare($fine_stats_query);
$fine_stats_stmt->execute();
$fine_stats = $fine_stats_stmt->fetch();

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>📊 Fine Breakdown</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>";
echo "<div><strong>📋 Loans with Fines:</strong> {$fine_stats['loans_with_fines']}</div>";
echo "<div><strong>⚠️ Overdue with Fines:</strong> {$fine_stats['overdue_with_fines']}</div>";
echo "<div><strong>📚 Returned with Fines:</strong> {$fine_stats['returned_with_fines']}</div>";
echo "<div><strong>📈 Average Fine:</strong> $" . number_format($fine_stats['avg_fine'], 2) . "</div>";
echo "<div><strong>🔺 Highest Fine:</strong> $" . number_format($fine_stats['max_fine'], 2) . "</div>";
echo "<div><strong>🔻 Lowest Fine:</strong> $" . number_format($fine_stats['min_fine'], 2) . "</div>";
echo "</div>";
echo "</div>";

// Show breakdown by loan status
echo "<h2>📊 Loan Status Breakdown</h2>";
$breakdown_query = "
    SELECT
        status,
        COUNT(*) as count,
        ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM book_loans)), 2) as percentage
    FROM book_loans
    GROUP BY status
    ORDER BY count DESC
";
$breakdown_stmt = $db->prepare($breakdown_query);
$breakdown_stmt->execute();
$breakdown = $breakdown_stmt->fetchAll();

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f0f0f0;'>";
echo "<th>Status</th><th>Count</th><th>Percentage</th><th>Description</th>";
echo "</tr>";

foreach ($breakdown as $row) {
    $status_color = 'black';
    $description = '';

    switch ($row['status']) {
        case 'borrowed':
            $status_color = 'green';
            $description = 'Currently borrowed books';
            break;
        case 'returned':
            $status_color = 'blue';
            $description = 'Books that have been returned';
            break;
        case 'overdue':
            $status_color = 'red';
            $description = 'Books past their due date';
            break;
    }

    echo "<tr>";
    echo "<td style='color: {$status_color}; font-weight: bold;'>" . ucfirst($row['status']) . "</td>";
    echo "<td>{$row['count']}</td>";
    echo "<td>{$row['percentage']}%</td>";
    echo "<td>{$description}</td>";
    echo "</tr>";
}
echo "</table>";

// Show top members with highest fines
echo "<h2>💸 Top Members with Highest Fines</h2>";
$top_fines_query = "
    SELECT
        m.id,
        m.first_name,
        m.last_name,
        m.email,
        SUM(bl.fine) as total_fines,
        COUNT(CASE WHEN bl.status = 'overdue' THEN 1 END) as overdue_books,
        COUNT(CASE WHEN bl.fine > 0 THEN 1 END) as books_with_fines
    FROM members m
    JOIN book_loans bl ON m.id = bl.member_id
    WHERE bl.fine > 0
    GROUP BY m.id
    ORDER BY total_fines DESC
    LIMIT 10
";
$top_fines_stmt = $db->prepare($top_fines_query);
$top_fines_stmt->execute();
$top_fines = $top_fines_stmt->fetchAll();

if (!empty($top_fines)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Rank</th><th>Member</th><th>Email</th><th>Total Fines</th><th>Overdue Books</th><th>Books with Fines</th>";
    echo "</tr>";

    $rank = 1;
    foreach ($top_fines as $member) {
        echo "<tr>";
        echo "<td><strong>#{$rank}</strong></td>";
        echo "<td>{$member['first_name']} {$member['last_name']}</td>";
        echo "<td>{$member['email']}</td>";
        echo "<td style='color: red; font-weight: bold;'>$" . number_format($member['total_fines'], 2) . "</td>";
        echo "<td style='color: orange;'>{$member['overdue_books']}</td>";
        echo "<td>{$member['books_with_fines']}</td>";
        echo "</tr>";
        $rank++;
    }
    echo "</table>";
} else {
    echo "<p>No members with fines found.</p>";
}

// Show sample of generated data
echo "<h2>👥 Sample Generated Members</h2>";
$sample_query = "
    SELECT
        m.id,
        m.first_name,
        m.last_name,
        m.email,
        m.membership_date,
        COUNT(bl.id) as total_loans,
        COUNT(CASE WHEN bl.status = 'borrowed' THEN 1 END) as active_loans,
        COUNT(CASE WHEN bl.status = 'returned' THEN 1 END) as returned_loans,
        COUNT(CASE WHEN bl.status = 'overdue' THEN 1 END) as overdue_loans
    FROM members m
    LEFT JOIN book_loans bl ON m.id = bl.member_id
    GROUP BY m.id
    ORDER BY m.id DESC
    LIMIT 10
";
$sample_stmt = $db->prepare($sample_query);
$sample_stmt->execute();
$sample_members = $sample_stmt->fetchAll();

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f0f0f0;'>";
echo "<th>ID</th><th>Name</th><th>Email</th><th>Member Since</th><th>Total Loans</th><th>Active</th><th>Returned</th><th>Overdue</th>";
echo "</tr>";

foreach ($sample_members as $member) {
    echo "<tr>";
    echo "<td>{$member['id']}</td>";
    echo "<td>{$member['first_name']} {$member['last_name']}</td>";
    echo "<td>{$member['email']}</td>";
    echo "<td>{$member['membership_date']}</td>";
    echo "<td>{$member['total_loans']}</td>";
    echo "<td style='color: green;'>{$member['active_loans']}</td>";
    echo "<td style='color: blue;'>{$member['returned_loans']}</td>";
    echo "<td style='color: red;'>{$member['overdue_loans']}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<div style='margin: 30px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;'>";
echo "<h3>🔗 What You Can Do Now</h3>";
echo "<ul>";
echo "<li><a href='verify_members_and_loans.php'>📊 View Complete Database Statistics</a></li>";
echo "<li><a href='members/index.php'>👥 Manage All Members</a></li>";
echo "<li><a href='loans/index.php'>📚 Manage All Loans</a></li>";
echo "<li><a href='loans/overdue.php'>⚠️ View Overdue Books</a></li>";
echo "<li><a href='reports/index.php'>📈 Generate Reports</a></li>";
echo "<li><a href='librarian/dashboard.php'>🏠 Librarian Dashboard</a></li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🔐 Login Information</h4>";
echo "<p><strong>All generated members use the same password pattern:</strong></p>";
echo "<p><code>[firstname]123</code></p>";
echo "<p><strong>Examples:</strong></p>";
echo "<ul>";
echo "<li>James Smith → Password: <code>james123</code></li>";
echo "<li>Mary Johnson → Password: <code>mary123</code></li>";
echo "<li>John Williams → Password: <code>john123</code></li>";
echo "</ul>";
echo "<p><a href='member_login.php' target='_blank'>🔗 Test Member Login</a></p>";
echo "</div>";

?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h1, h2, h3 { color: #333; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    a { color: #007bff; text-decoration: none; }
    a:hover { text-decoration: underline; }
    .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
</style>

<script>
// Auto-refresh progress (if needed)
function updateProgress() {
    // This function can be used for real-time updates if needed
}
</script>
