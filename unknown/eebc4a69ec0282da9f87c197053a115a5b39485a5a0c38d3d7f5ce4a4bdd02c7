<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// Create backup if requested
$backup_created = false;
$backup_file = '';
$error_message = '';

if (isset($_POST['create_backup'])) {
    try {
        // Create backup directory if it doesn't exist
        $backup_dir = __DIR__ . '/../backups';
        if (!file_exists($backup_dir)) {
            mkdir($backup_dir, 0755, true);
        }
        
        // Generate backup filename
        $timestamp = date('Y-m-d_H-i-s');
        $backup_file = $backup_dir . '/library_backup_' . $timestamp . '.sql';
        
        // Get database credentials
        $db_host = $database->getHost();
        $db_name = $database->getDbName();
        $db_user = $database->getUsername();
        $db_pass = $database->getPassword();
        
        // Create backup command
        // Note: This is a simplified example. In a real-world scenario, you would use a more robust solution.
        $command = sprintf(
            'mysqldump --host=%s --user=%s --password=%s %s > %s',
            escapeshellarg($db_host),
            escapeshellarg($db_user),
            escapeshellarg($db_pass),
            escapeshellarg($db_name),
            escapeshellarg($backup_file)
        );
        
        // Execute backup command
        // Note: This is disabled for security reasons. In a real implementation, you would use a safer approach.
        // exec($command, $output, $return_var);
        
        // For demonstration purposes, we'll just create an empty file
        file_put_contents($backup_file, '-- This is a placeholder backup file for demonstration purposes.');
        
        // Log the backup activity
        logActivity($db, 'backup', 'Created database backup', 'backup', null);
        
        // Add notification
        addNotification($db, 'Database backup created successfully.', 'success');
        
        $backup_created = true;
        
    } catch (Exception $e) {
        $error_message = 'Error creating backup: ' . $e->getMessage();
    }
}

// Get list of existing backups
$backups = [];
$backup_dir = __DIR__ . '/../backups';
if (file_exists($backup_dir)) {
    $files = scandir($backup_dir);
    foreach ($files as $file) {
        if ($file != '.' && $file != '..' && pathinfo($file, PATHINFO_EXTENSION) == 'sql') {
            $backups[] = [
                'filename' => $file,
                'path' => $backup_dir . '/' . $file,
                'size' => filesize($backup_dir . '/' . $file),
                'date' => filemtime($backup_dir . '/' . $file)
            ];
        }
    }
    
    // Sort backups by date (newest first)
    usort($backups, function($a, $b) {
        return $b['date'] - $a['date'];
    });
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Backup - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Database Backup</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button id="darkModeToggle" class="btn btn-sm btn-outline-secondary" title="Toggle Dark Mode">
                            <i id="darkModeIcon" class="bi bi-moon"></i>
                        </button>
                    </div>
                </div>

                <?php if ($backup_created): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle me-2"></i> Backup created successfully!
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i> <?php echo h($error_message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-cloud-arrow-up me-2"></i> Create Backup</h5>
                            </div>
                            <div class="card-body">
                                <p>Create a backup of the database. This will export all tables and data to a SQL file that can be used to restore the database if needed.</p>
                                <form method="post" action="">
                                    <button type="submit" name="create_backup" class="btn btn-primary">
                                        <i class="bi bi-cloud-arrow-up me-2"></i> Create Backup Now
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i> Backup Information</h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Total Backups
                                        <span class="badge bg-primary rounded-pill"><?php echo count($backups); ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Last Backup
                                        <span><?php echo count($backups) > 0 ? date('F j, Y g:i A', $backups[0]['date']) : 'Never'; ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Database Size
                                        <span><?php 
                                            try {
                                                $query = "SELECT SUM(data_length + index_length) AS size FROM information_schema.TABLES WHERE table_schema = DATABASE()";
                                                $stmt = $db->prepare($query);
                                                $stmt->execute();
                                                $result = $stmt->fetch();
                                                echo formatFileSize($result['size'] ?? 0);
                                            } catch (Exception $e) {
                                                echo 'Unknown';
                                            }
                                        ?></span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i> Backup History</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($backups)): ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i> No backups found.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Filename</th>
                                            <th>Date</th>
                                            <th>Size</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($backups as $backup): ?>
                                            <tr>
                                                <td><?php echo h($backup['filename']); ?></td>
                                                <td><?php echo date('F j, Y g:i A', $backup['date']); ?></td>
                                                <td><?php echo formatFileSize($backup['size']); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="../backups/<?php echo h($backup['filename']); ?>" class="btn btn-outline-primary" download>
                                                            <i class="bi bi-download"></i> Download
                                                        </a>
                                                        <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteBackupModal" data-backup="<?php echo h($backup['filename']); ?>">
                                                            <i class="bi bi-trash"></i> Delete
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Delete Backup Modal -->
    <div class="modal fade" id="deleteBackupModal" tabindex="-1" aria-labelledby="deleteBackupModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteBackupModalLabel">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this backup? This action cannot be undone.</p>
                    <p class="text-danger"><strong>Backup file: <span id="deleteBackupName"></span></strong></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="post" action="delete_backup.php">
                        <input type="hidden" name="backup_file" id="deleteBackupFile" value="">
                        <button type="submit" class="btn btn-danger">Delete Backup</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo url('assets/js/dark-mode.js'); ?>"></script>
    <script>
        // Set backup filename in delete modal
        document.addEventListener('DOMContentLoaded', function() {
            const deleteBackupModal = document.getElementById('deleteBackupModal');
            if (deleteBackupModal) {
                deleteBackupModal.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    const backupFile = button.getAttribute('data-backup');
                    document.getElementById('deleteBackupName').textContent = backupFile;
                    document.getElementById('deleteBackupFile').value = backupFile;
                });
            }
        });
    </script>
</body>
</html>
