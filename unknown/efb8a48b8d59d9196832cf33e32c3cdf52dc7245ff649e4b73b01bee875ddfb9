<?php
/**
 * Test script to verify all diagnostic files are working
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🔧 Diagnostics Test Suite</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .test-link { display: inline-block; margin: 5px; padding: 8px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
    .test-link:hover { background: #0056b3; color: white; text-decoration: none; }
</style>";

echo "<div class='test-section'>";
echo "<h2>🔍 File Existence Check</h2>";

$diagnostic_files = [
    'diagnostic.php' => 'Main Diagnostic Page',
    'database_status.php' => 'Database Status Check',
    'troubleshoot.php' => 'Troubleshooting Guide',
    'admin/ajax/test_database.php' => 'Advanced Database Test'
];

foreach ($diagnostic_files as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ $description ($file) - File exists</div>";
    } else {
        echo "<div class='error'>❌ $description ($file) - File missing</div>";
    }
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>🔗 Test Links</h2>";
echo "<p>Click these links to test each diagnostic tool:</p>";

foreach ($diagnostic_files as $file => $description) {
    if (file_exists($file)) {
        echo "<a href='$file' target='_blank' class='test-link'>🔧 $description</a>";
    }
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>📊 Quick Database Test</h2>";
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<div class='success'>✅ Database connection successful</div>";
        
        // Test basic query
        $stmt = $db->query("SELECT 1 as test");
        $result = $stmt->fetch();
        echo "<div class='success'>✅ Database query test successful</div>";
        
        // Check if testConnection method exists
        if (method_exists($database, 'testConnection')) {
            $test_result = $database->testConnection();
            if ($test_result['success']) {
                echo "<div class='success'>✅ testConnection method working</div>";
            } else {
                echo "<div class='error'>❌ testConnection method failed: " . $test_result['message'] . "</div>";
            }
        } else {
            echo "<div class='warning'>⚠️ testConnection method not found</div>";
        }
        
    } else {
        echo "<div class='error'>❌ Database connection failed</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>🎯 Quick Actions</h2>";
echo "<p><a href='admin/dashboard.php' class='test-link'>📊 Go to Admin Dashboard</a></p>";
echo "<p><a href='index.php' class='test-link'>🏠 Go to Home Page</a></p>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>📝 Test Summary</h2>";
echo "<div class='info'>";
echo "<strong>If all tests pass:</strong><br>";
echo "• All diagnostic files exist and should be accessible<br>";
echo "• Database connection is working<br>";
echo "• The diagnostics menu should work without errors<br><br>";
echo "<strong>If you see errors:</strong><br>";
echo "• Check that XAMPP/WAMP MySQL service is running<br>";
echo "• Verify database credentials in config/database.php<br>";
echo "• Run setup.php to create missing database tables<br>";
echo "</div>";
echo "</div>";
