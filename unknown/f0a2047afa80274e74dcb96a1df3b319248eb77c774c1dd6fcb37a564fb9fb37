<?php
/**
 * Update Loan Statuses
 * This script automatically updates loan statuses based on due dates
 * Should be run periodically (daily) to maintain accurate overdue status
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>🔄 Updating Loan Statuses</h2>";
    
    // Start transaction
    $db->beginTransaction();
    
    // 1. Update borrowed books that are now overdue
    $stmt = $db->prepare("
        UPDATE book_loans 
        SET status = 'overdue',
            fine = DATEDIFF(CURDATE(), due_date) * 1.00
        WHERE status = 'borrowed' 
        AND due_date < CURDATE()
    ");
    $stmt->execute();
    $overdue_updated = $stmt->rowCount();
    
    echo "<p>✅ Updated $overdue_updated borrowed books to overdue status</p>";
    
    // 2. Update fines for existing overdue books
    $stmt = $db->prepare("
        UPDATE book_loans 
        SET fine = DATEDIFF(CURDATE(), due_date) * 1.00
        WHERE status = 'overdue'
        AND due_date < CURDATE()
    ");
    $stmt->execute();
    $fines_updated = $stmt->rowCount();
    
    echo "<p>✅ Updated fines for $fines_updated overdue books</p>";
    
    // 3. Show current status
    echo "<h3>📊 Current Status Distribution</h3>";
    
    $stmt = $db->prepare('SELECT status, COUNT(*) as count FROM book_loans GROUP BY status');
    $stmt->execute();
    $statuses = $stmt->fetchAll();
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    foreach($statuses as $status) {
        echo "<p><strong>{$status['status']}:</strong> {$status['count']} loans</p>";
    }
    echo "</div>";
    
    // 4. Show overdue calculation
    $stmt = $db->prepare('SELECT COUNT(*) as count FROM book_loans WHERE status = "overdue" OR (status = "borrowed" AND due_date < CURDATE())');
    $stmt->execute();
    $total_overdue = $stmt->fetch()['count'];
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h5>📊 Overdue Books Count:</h5>";
    echo "<p><strong>Total Overdue Books:</strong> $total_overdue</p>";
    echo "</div>";
    
    // Commit transaction
    $db->commit();
    
    echo "<h3>✅ Status Update Complete</h3>";
    echo "<p>Loan statuses have been updated based on current due dates.</p>";
    echo "<p><strong>Note:</strong> This script should be run daily to maintain accurate overdue status.</p>";
    
} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollback();
    }
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h4>❌ Error</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
