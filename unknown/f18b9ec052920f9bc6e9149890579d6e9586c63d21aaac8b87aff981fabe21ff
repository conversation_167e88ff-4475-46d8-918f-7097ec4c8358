<?php
/**
 * Dashboard Statistics API
 * Provides real-time statistics for the admin dashboard
 * Improved version with standardized calculations and better terminology
 */

header('Content-Type: application/json');
session_start();

require_once '../config/database.php';
require_once '../includes/functions.php';

// For development - bypass authentication (remove in production)
$bypass_auth = true;

// Check if user is logged in and is admin
if (!$bypass_auth && (!isLoggedIn() || !isAdmin())) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();

    $stats = [];

    // Check if tables exist first
    $required_tables = ['books', 'members', 'book_loans'];
    $tables_exist = true;

    foreach ($required_tables as $table) {
        try {
            $query = "SHOW TABLES LIKE '$table'";
            $stmt = $db->prepare($query);
            $stmt->execute();
            if ($stmt->rowCount() == 0) {
                $tables_exist = false;
                break;
            }
        } catch (Exception $e) {
            $tables_exist = false;
            break;
        }
    }

    if ($tables_exist) {
        // 1. BOOK STATISTICS
        // Total book titles (unique books)
        $query = "SELECT COUNT(*) as total FROM books";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats['total_books'] = $stmt->fetch()['total'] ?? 0;

        // Total copies (sum of all quantities)
        $query = "SELECT SUM(COALESCE(quantity, 0)) as total FROM books";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats['total_copies'] = $stmt->fetch()['total'] ?? 0;

        // Available copies (sum of available quantities)
        $query = "SELECT SUM(COALESCE(available_quantity, 0)) as available FROM books";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats['available_books'] = $stmt->fetch()['available'] ?? 0;

        // 2. MEMBER STATISTICS
        // Total members
        $query = "SELECT COUNT(*) as total FROM members";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats['total_members'] = $stmt->fetch()['total'] ?? 0;

        // 3. LOAN STATISTICS (Standardized calculations)
        // Active loans (currently borrowed - not overdue yet)
        $query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'borrowed' AND due_date >= CURDATE()";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats['active_loans'] = $stmt->fetch()['total'] ?? 0;

        // Overdue loans (explicitly overdue OR borrowed past due date)
        $query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'overdue' OR (status = 'borrowed' AND due_date < CURDATE())";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats['overdue_books'] = $stmt->fetch()['total'] ?? 0;
    } else {
        // Tables don't exist - return default values
        $stats = [
            'total_books' => 0,
            'total_copies' => 0,
            'available_books' => 0,
            'total_members' => 0,
            'active_loans' => 0,
            'overdue_books' => 0
        ];
    }

    // Total current loans (active + overdue)
    $stats['total_current_loans'] = $stats['active_loans'] + $stats['overdue_books'];

    // 4. ADDITIONAL METRICS
    // Books utilization rate
    $stats['utilization_rate'] = $stats['total_copies'] > 0 ?
        round(($stats['total_current_loans'] / $stats['total_copies']) * 100, 1) : 0;

    // Book availability rate
    $stats['availability_rate'] = $stats['total_copies'] > 0 ?
        round(($stats['available_books'] / $stats['total_copies']) * 100, 1) : 0;

    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'timestamp' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
