<?php
/**
 * Test Relative Paths Fix
 * This file tests if the relative path routing is working correctly
 */

require_once 'config/config.php';

echo "<h2>Relative Path Routing Test</h2>";

echo "<h3>Configuration Status:</h3>";
echo "<p><strong>BASE_URL:</strong> '" . BASE_URL . "' (should be empty)</p>";

// Test URL function
echo "<h3>URL Function Test:</h3>";
echo "<p><strong>url('admin/dashboard.php'):</strong> '" . url('admin/dashboard.php') . "'</p>";
echo "<p><strong>url('login.php'):</strong> '" . url('login.php') . "'</p>";
echo "<p><strong>url('index.php'):</strong> '" . url('index.php') . "'</p>";

// Check file existence
echo "<h3>File Existence Check:</h3>";
$test_paths = [
    'admin/dashboard.php',
    'login.php',
    'index.php'
];

foreach ($test_paths as $path) {
    $exists = file_exists($path);
    echo "<p><strong>$path:</strong> " . ($exists ? '✅ EXISTS' : '❌ NOT FOUND') . "</p>";
}

echo "<h3>Test Login Redirect:</h3>";
echo "<p>When you login as admin, you should be redirected to: <strong>admin/dashboard.php</strong> (relative path)</p>";
echo "<p><a href='login.php' class='btn btn-primary'>Test Login</a></p>";

echo "<h3>Direct Dashboard Access:</h3>";
echo "<p><a href='admin/dashboard.php' class='btn btn-success'>Go to Admin Dashboard</a></p>";

echo "<h3>Fix Summary:</h3>";
echo "<p>✅ Removed absolute path: /Library-PLISCO_SYSTEM/lms</p>";
echo "<p>✅ Using relative paths: admin/dashboard.php</p>";
echo "<p>✅ No more URL generation issues</p>";
echo "<p>✅ All redirects use header('Location: relative/path')</p>";

echo "<h3>Expected Behavior:</h3>";
echo "<p>• Login as admin → redirects to admin/dashboard.php</p>";
echo "<p>• Login as librarian → redirects to librarian/dashboard.php</p>";
echo "<p>• Login as member → redirects to member_dashboard.php</p>";
echo "<p>• All paths are relative to the current directory</p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px; }
.btn-success { background: #28a745; }
h3 { color: #333; margin-top: 20px; }
p { margin: 5px 0; }
</style>
