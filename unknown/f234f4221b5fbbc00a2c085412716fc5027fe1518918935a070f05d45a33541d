# Library Management System - Installation Guide

This guide will help you install and set up the Library Management System on your server.

## Prerequisites

Before you begin, make sure you have the following:

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache, Nginx, etc.)
- XAMPP, WAMP, MAMP, or similar local development environment

## Installation Steps

### 1. Set Up the Web Server

If you're using XAMPP:
1. Download and install XAMPP from [https://www.apachefriends.org/](https://www.apachefriends.org/)
2. Start the Apache and MySQL services from the XAMPP Control Panel

### 2. Get the Library Management System Files

1. Download or clone the Library Management System files
2. Extract the files to your web server's document root:
   - For XAMPP: `C:\xampp\htdocs\lms`
   - For WAMP: `C:\wamp\www\lms`
   - For MAMP: `/Applications/MAMP/htdocs/lms`

### 3. Set Up the Database

#### Automatic Setup (Recommended)

1. Open your web browser and navigate to `http://localhost/lms/setup.php`
2. Fill in your database details:
   - Database Host: `localhost` (default)
   - Database Name: `lms_db` (default)
   - Database Username: `root` (default)
   - Database Password: (leave empty if no password is set)
3. Click "Set Up Database" to automatically create the database and tables

#### Manual Setup

If the automatic setup doesn't work, you can set up the database manually:

1. Create a new database named `lms_db` in phpMyAdmin or your preferred MySQL client
2. Import the SQL file from `database/lms_db.sql`
3. Update the database connection details in `config/database.php`

### 4. Configure File Permissions

Make sure the following directories are writable by the web server:
- `uploads/`
- `uploads/covers/`
- `uploads/members/`

On Linux/Mac:
```
chmod -R 755 uploads
```

### 5. Access the System

1. Open your web browser and navigate to `http://localhost/lms/`
2. Log in with the default admin credentials:
   - Username: `admin`
   - Password: `admin123`

### 6. Post-Installation Steps

1. Change the default admin password immediately after your first login
2. Configure your library settings in the admin panel
3. Add books, members, and other data to your system

## Troubleshooting

### Database Connection Issues

If you encounter database connection issues:
1. Verify your database credentials in `config/database.php`
2. Make sure MySQL service is running
3. Check if the database `lms_db` exists

### File Upload Issues

If you encounter issues with file uploads:
1. Check that the `uploads` directory and its subdirectories exist and are writable
2. Verify PHP file upload settings in your `php.ini` file:
   - `file_uploads = On`
   - `upload_max_filesize = 10M` (or higher)
   - `post_max_size = 10M` (or higher)

### Page Not Found Errors

If you get "Page Not Found" errors:
1. Make sure your web server is running
2. Verify that the files are in the correct directory
3. Check if your server has URL rewriting enabled if needed

## Support

If you need help with installation or have any questions, please:
1. Check the documentation in the `docs` directory
2. Review the README.md file for additional information
3. Contact the system administrator or developer

---

Thank you for installing the Library Management System!
