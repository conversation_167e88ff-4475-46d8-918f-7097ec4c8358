<?php
/**
 * Comprehensive System Enhancement Script
 * This script improves accessibility, functionality, and user experience
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

$enhancements_applied = [];
$errors = [];

// Enhancement 1: Create missing database indexes for performance
function createDatabaseIndexes($db) {
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_book_loans_status ON book_loans(status)",
        "CREATE INDEX IF NOT EXISTS idx_book_loans_due_date ON book_loans(due_date)",
        "CREATE INDEX IF NOT EXISTS idx_book_loans_member_id ON book_loans(member_id)",
        "CREATE INDEX IF NOT EXISTS idx_book_loans_book_id ON book_loans(book_id)",
        "CREATE INDEX IF NOT EXISTS idx_books_title ON books(title)",
        "CREATE INDEX IF NOT EXISTS idx_books_author ON books(author)",
        "CREATE INDEX IF NOT EXISTS idx_books_category ON books(category)",
        "CREATE INDEX IF NOT EXISTS idx_members_email ON members(email)",
        "CREATE INDEX IF NOT EXISTS idx_members_status ON members(membership_status)",
        "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
        "CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)"
    ];
    
    $created = 0;
    foreach ($indexes as $index) {
        try {
            $db->exec($index);
            $created++;
        } catch (Exception $e) {
            // Index might already exist, continue
        }
    }
    return $created;
}

// Enhancement 2: Create system settings table
function createSystemSettingsTable($db) {
    $sql = "CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    try {
        $db->exec($sql);
        
        // Insert default settings
        $default_settings = [
            ['library_name', 'Central Library', 'string', 'Name of the library'],
            ['fine_per_day', '1.00', 'number', 'Fine amount per day for overdue books'],
            ['max_loan_days', '14', 'number', 'Maximum loan period in days'],
            ['max_renewals', '2', 'number', 'Maximum number of renewals allowed'],
            ['email_notifications', '1', 'boolean', 'Enable email notifications'],
            ['auto_calculate_fines', '1', 'boolean', 'Automatically calculate fines'],
            ['library_hours', '{"monday":"9:00-17:00","tuesday":"9:00-17:00","wednesday":"9:00-17:00","thursday":"9:00-17:00","friday":"9:00-17:00","saturday":"9:00-13:00","sunday":"closed"}', 'json', 'Library operating hours']
        ];
        
        foreach ($default_settings as $setting) {
            $check = $db->prepare("SELECT id FROM system_settings WHERE setting_key = ?");
            $check->execute([$setting[0]]);
            if ($check->rowCount() == 0) {
                $insert = $db->prepare("INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)");
                $insert->execute($setting);
            }
        }
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// Enhancement 3: Create activity log table for better tracking
function createActivityLogTable($db) {
    $sql = "CREATE TABLE IF NOT EXISTS activity_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        member_id INT,
        action VARCHAR(100) NOT NULL,
        entity_type VARCHAR(50),
        entity_id INT,
        details TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_activity_user_id (user_id),
        INDEX idx_activity_member_id (member_id),
        INDEX idx_activity_timestamp (timestamp),
        INDEX idx_activity_action (action)
    )";
    
    try {
        $db->exec($sql);
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// Enhancement 4: Create notifications table
function createNotificationsTable($db) {
    $sql = "CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        member_id INT,
        type ENUM('info', 'success', 'warning', 'danger') DEFAULT 'info',
        title VARCHAR(255),
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        read_at TIMESTAMP NULL,
        INDEX idx_notifications_user_id (user_id),
        INDEX idx_notifications_member_id (member_id),
        INDEX idx_notifications_is_read (is_read),
        INDEX idx_notifications_created_at (created_at)
    )";
    
    try {
        $db->exec($sql);
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// Enhancement 5: Create book reservations table
function createBookReservationsTable($db) {
    $sql = "CREATE TABLE IF NOT EXISTS book_reservations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        book_id INT NOT NULL,
        member_id INT NOT NULL,
        status ENUM('pending', 'ready', 'fulfilled', 'cancelled') DEFAULT 'pending',
        reserved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NULL,
        fulfilled_at TIMESTAMP NULL,
        notes TEXT,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
        INDEX idx_reservations_book_id (book_id),
        INDEX idx_reservations_member_id (member_id),
        INDEX idx_reservations_status (status)
    )";
    
    try {
        $db->exec($sql);
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// Enhancement 6: Add missing columns to existing tables
function addMissingColumns($db) {
    $columns_to_add = [
        "ALTER TABLE books ADD COLUMN IF NOT EXISTS isbn VARCHAR(20)",
        "ALTER TABLE books ADD COLUMN IF NOT EXISTS publisher VARCHAR(255)",
        "ALTER TABLE books ADD COLUMN IF NOT EXISTS publication_year YEAR",
        "ALTER TABLE books ADD COLUMN IF NOT EXISTS language VARCHAR(50) DEFAULT 'English'",
        "ALTER TABLE books ADD COLUMN IF NOT EXISTS pages INT",
        "ALTER TABLE books ADD COLUMN IF NOT EXISTS cover_image VARCHAR(255)",
        "ALTER TABLE books ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
        "ALTER TABLE books ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
        
        "ALTER TABLE members ADD COLUMN IF NOT EXISTS phone VARCHAR(20)",
        "ALTER TABLE members ADD COLUMN IF NOT EXISTS address TEXT",
        "ALTER TABLE members ADD COLUMN IF NOT EXISTS date_of_birth DATE",
        "ALTER TABLE members ADD COLUMN IF NOT EXISTS membership_type ENUM('standard', 'premium', 'student', 'senior') DEFAULT 'standard'",
        "ALTER TABLE members ADD COLUMN IF NOT EXISTS profile_image VARCHAR(255)",
        "ALTER TABLE members ADD COLUMN IF NOT EXISTS last_activity TIMESTAMP",
        
        "ALTER TABLE book_loans ADD COLUMN IF NOT EXISTS renewal_count INT DEFAULT 0",
        "ALTER TABLE book_loans ADD COLUMN IF NOT EXISTS notes TEXT",
        "ALTER TABLE book_loans ADD COLUMN IF NOT EXISTS fine_paid DECIMAL(10,2) DEFAULT 0.00",
        
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS login_attempts INT DEFAULT 0",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP NULL"
    ];
    
    $added = 0;
    foreach ($columns_to_add as $column) {
        try {
            $db->exec($column);
            $added++;
        } catch (Exception $e) {
            // Column might already exist, continue
        }
    }
    return $added;
}

// Run all enhancements
try {
    // Database performance improvements
    $indexes_created = createDatabaseIndexes($db);
    if ($indexes_created > 0) {
        $enhancements_applied[] = "Created $indexes_created database indexes for better performance";
    }
    
    // System settings
    if (createSystemSettingsTable($db)) {
        $enhancements_applied[] = "Created system settings table with default configuration";
    }
    
    // Activity logging
    if (createActivityLogTable($db)) {
        $enhancements_applied[] = "Created activity log table for better tracking";
    }
    
    // Notifications system
    if (createNotificationsTable($db)) {
        $enhancements_applied[] = "Created notifications table for user alerts";
    }
    
    // Book reservations
    if (createBookReservationsTable($db)) {
        $enhancements_applied[] = "Created book reservations table for hold requests";
    }
    
    // Missing columns
    $columns_added = addMissingColumns($db);
    if ($columns_added > 0) {
        $enhancements_applied[] = "Added $columns_added missing columns to existing tables";
    }
    
} catch (Exception $e) {
    $errors[] = "Database enhancement error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Enhancement - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .enhancement-card {
            border-left: 4px solid #28a745;
            background: #f8f9fa;
        }
        .error-card {
            border-left: 4px solid #dc3545;
            background: #f8f9fa;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="bi bi-gear-fill me-2"></i>System Enhancement Results</h4>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($enhancements_applied)): ?>
                            <div class="alert alert-success">
                                <h5><i class="bi bi-check-circle me-2"></i>Enhancements Applied Successfully!</h5>
                                <ul class="mb-0">
                                    <?php foreach ($enhancements_applied as $enhancement): ?>
                                        <li><?php echo htmlspecialchars($enhancement); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <h5><i class="bi bi-exclamation-triangle me-2"></i>Errors Encountered</h5>
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <div class="mt-4">
                            <h5>Next Steps:</h5>
                            <div class="btn-group-vertical w-100" role="group">
                                <a href="accessibility_improvements.php" class="btn btn-outline-primary">
                                    <i class="bi bi-universal-access me-2"></i>Apply Accessibility Improvements
                                </a>
                                <a href="ui_enhancements.php" class="btn btn-outline-success">
                                    <i class="bi bi-palette me-2"></i>Apply UI/UX Enhancements
                                </a>
                                <a href="security_hardening.php" class="btn btn-outline-warning">
                                    <i class="bi bi-shield-check me-2"></i>Apply Security Hardening
                                </a>
                                <a href="system_health_check.php" class="btn btn-outline-info">
                                    <i class="bi bi-heart-pulse me-2"></i>Run System Health Check
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
