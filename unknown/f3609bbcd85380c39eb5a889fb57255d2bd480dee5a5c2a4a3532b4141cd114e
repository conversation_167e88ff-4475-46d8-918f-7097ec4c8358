<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Auto-login as admin for testing
try {
    $database = new Database();
    $db = $database->getConnection();
    
    $query = "SELECT * FROM users WHERE role = 'admin' LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        $_SESSION['user_id'] = $admin['id'];
        $_SESSION['username'] = $admin['username'];
        $_SESSION['role'] = $admin['role'];
        $_SESSION['logged_in'] = true;
    }
} catch (Exception $e) {
    echo "Setup error: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix View Details Links</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .stats-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 10px;
            overflow: hidden;
        }
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .view-details-link {
            z-index: 1050 !important;
            position: relative !important;
            pointer-events: auto !important;
            cursor: pointer !important;
            text-decoration: none !important;
        }
        .view-details-link:hover {
            opacity: 0.8 !important;
            text-decoration: underline !important;
        }
        .card-footer a {
            z-index: 1050 !important;
            position: relative !important;
            pointer-events: auto !important;
            cursor: pointer !important;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <h2><i class="bi bi-tools me-2"></i>Fix View Details Links</h2>
        
        <div class="alert alert-success">
            <strong>✅ Links Fixed!</strong> The CSS has been updated to ensure all "View Details" links are clickable.
        </div>
        
        <!-- Test the same structure as dashboard -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-white bg-primary stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-white-50">Total Books</h6>
                                <h3 class="mb-0">150</h3>
                            </div>
                            <div>
                                <i class="bi bi-book fs-1"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a href="<?php echo url('books/index.php'); ?>" class="text-white-50 text-decoration-none view-details-link">
                            <i class="bi bi-eye me-1"></i>View Details
                        </a>
                        <i class="bi bi-chevron-right text-white-50"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card text-white bg-success stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-white-50">Total Members</h6>
                                <h3 class="mb-0">1000</h3>
                            </div>
                            <div>
                                <i class="bi bi-people fs-1"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a href="<?php echo url('members/index.php'); ?>" class="text-white-50 text-decoration-none view-details-link">
                            <i class="bi bi-eye me-1"></i>View Details
                        </a>
                        <i class="bi bi-chevron-right text-white-50"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card text-white bg-warning stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-white-50">Active Loans</h6>
                                <h3 class="mb-0">75</h3>
                            </div>
                            <div>
                                <i class="bi bi-journal-arrow-up fs-1"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a href="<?php echo url('loans/index.php'); ?>" class="text-white-50 text-decoration-none view-details-link">
                            <i class="bi bi-eye me-1"></i>View Details
                        </a>
                        <i class="bi bi-chevron-right text-white-50"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card text-white bg-danger stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-white-50">Overdue Books</h6>
                                <h3 class="mb-0">12</h3>
                            </div>
                            <div>
                                <i class="bi bi-exclamation-triangle fs-1"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a href="<?php echo url('loans/overdue.php'); ?>" class="text-white-50 text-decoration-none view-details-link">
                            <i class="bi bi-eye me-1"></i>View Details
                        </a>
                        <i class="bi bi-chevron-right text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-check-circle me-2"></i>What Was Fixed</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item">✅ Added proper z-index to view-details-link class</li>
                    <li class="list-group-item">✅ Ensured pointer-events are set to auto</li>
                    <li class="list-group-item">✅ Fixed card footer link positioning</li>
                    <li class="list-group-item">✅ Added hover effects for better user experience</li>
                    <li class="list-group-item">✅ Ensured all dashboard links are clickable</li>
                </ul>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="admin/dashboard.php" class="btn btn-primary btn-lg">
                <i class="bi bi-speedometer2 me-2"></i>Go to Admin Dashboard
            </a>
            <a href="quick_admin_login.php" class="btn btn-success btn-lg ms-2">
                <i class="bi bi-box-arrow-in-right me-2"></i>Quick Admin Login
            </a>
        </div>
        
        <div class="alert alert-info mt-4">
            <h6><i class="bi bi-info-circle me-2"></i>Testing Instructions:</h6>
            <ol>
                <li>Click the "Go to Admin Dashboard" button above</li>
                <li>Try clicking the "View Details" links in the statistics cards</li>
                <li>All links should now be clickable and working properly</li>
            </ol>
        </div>
    </div>
    
    <script>
        // Add click event listeners to test functionality
        document.addEventListener('DOMContentLoaded', function() {
            const viewDetailsLinks = document.querySelectorAll('.view-details-link');
            
            viewDetailsLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    console.log('View Details link clicked:', this.href);
                    // Show a brief visual feedback
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 100);
                });
            });
        });
    </script>
</body>
</html>
