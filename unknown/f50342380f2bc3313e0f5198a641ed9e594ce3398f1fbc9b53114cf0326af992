<?php
/**
 * Google Authentication Fallback
 * 
 * This page provides a fallback for when JavaScript is disabled.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/google_auth.php';

// Get Google login URL
$google_login_url = getGoogleLoginUrl();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Authentication - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .fallback-container {
            max-width: 500px;
            width: 100%;
            margin: 0 auto;
        }
        .card {
            border: none;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            overflow: hidden;
        }
        .card-header {
            background-color: #343a40;
            color: white;
            font-weight: bold;
            padding: 1.2rem;
        }
        .btn-google {
            background-color: #ffffff;
            color: #757575;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-family: 'Roboto', sans-serif;
            font-weight: 500;
            font-size: 14px;
            padding: 10px 12px;
            position: relative;
            text-align: center;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        }
        .btn-google:hover {
            background-color: #f8f9fa;
            box-shadow: 0 2px 4px rgba(0,0,0,0.12);
            border-color: #c6c6c6;
            color: #3c4043;
        }
        .btn-google img {
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <div class="fallback-container">
        <div class="card">
            <div class="card-header text-center">
                <h4 class="mb-0"><i class="bi bi-google me-2"></i>Google Authentication</h4>
            </div>
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="48" height="48">
                    <h5 class="mt-3">JavaScript is Disabled</h5>
                    <p class="text-muted">We've detected that JavaScript is disabled in your browser.</p>
                </div>
                
                <div class="alert alert-info">
                    <p><strong>For the best experience:</strong></p>
                    <ul>
                        <li>Enable JavaScript in your browser settings</li>
                        <li>Refresh this page after enabling JavaScript</li>
                    </ul>
                </div>
                
                <p class="text-center">You can still proceed with Google authentication:</p>
                
                <div class="d-grid gap-2 mb-3">
                    <a href="<?php echo h($google_login_url); ?>" class="btn btn-google">
                        <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="20" height="20" class="me-2">
                        <span>Continue with Google</span>
                    </a>
                </div>
                
                <div class="text-center mt-3">
                    <a href="login.php" class="btn btn-link">Return to Login Page</a>
                </div>
            </div>
            <div class="card-footer text-center py-3">
                <p class="mb-0">Library Management System &copy; <?php echo date('Y'); ?></p>
            </div>
        </div>
    </div>
</body>
</html>
