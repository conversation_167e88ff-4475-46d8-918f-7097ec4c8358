<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Complete Book Cover Fix</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h2>Complete Book Cover Fix</h2>";

// Step 1: Check current state
echo "<h3>Step 1: Current State Analysis</h3>";

$query = "SELECT COUNT(*) as total FROM books";
$stmt = $db->prepare($query);
$stmt->execute();
$total_books = $stmt->fetch()['total'];

$query = "SELECT COUNT(*) as with_covers FROM books WHERE cover_image IS NOT NULL AND cover_image != ''";
$stmt = $db->prepare($query);
$stmt->execute();
$books_with_covers = $stmt->fetch()['with_covers'];

echo "<p>Total books: {$total_books}</p>";
echo "<p>Books with cover_image set: {$books_with_covers}</p>";
echo "<p>Books without covers: " . ($total_books - $books_with_covers) . "</p>";

// Step 2: Check available cover files
echo "<h3>Step 2: Available Cover Files</h3>";
$covers_dir = 'uploads/covers/';
$available_covers = [];
if (is_dir($covers_dir)) {
    $files = scandir($covers_dir);
    foreach ($files as $file) {
        if ($file != '.' && $file != '..' && in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif'])) {
            $available_covers[] = $file;
        }
    }
    echo "<p>Available cover files: " . count($available_covers) . "</p>";
    echo "<ul>";
    foreach ($available_covers as $cover) {
        echo "<li>" . htmlspecialchars($cover) . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p class='text-danger'>Covers directory not found!</p>";
}

// Step 3: Fix covers if requested
if (isset($_POST['fix_all_covers'])) {
    echo "<h3>Step 3: Fixing Book Covers</h3>";
    
    // Comprehensive book-to-cover mapping
    $book_cover_mapping = [
        '1984' => '1984_cover.jpg',
        'A Game of Thrones' => '1747724281_AGameOfThrones.jpg',
        'Good to Great' => '1747724303_Good to Great.jpg',
        'Pride and Prejudice' => '1747724335_Pride and Prejudice.jpg',
        'Steve Jobs' => '1747725415_steve-jobs-9781451648553_hr.jpg',
        'The Alchemist' => '1747725431_the-alchemist-a-graphic-novel.jpg',
        'The Catcher in the Rye' => '1747725459_Book-Review-The-Catcher-in-the-Rye-by-J-D-Salinger.jpg',
        'The Great Gatsby' => '1747725503_the-great-gatsby-and-other-works-9781645173519_hr.jpg',
        'The Martian' => '1747725673_The_Martian_(Weir_novel).jpg',
        'To Kill a Mockingbird' => '1747725698_fuck-640x996.jpg',
        'Harry Potter' => '1747725734_71qFBdNS+dL.jpg',
        'Sapiens' => '1747725751_9781784870799.jpg'
    ];
    
    // Get all books
    $query = "SELECT * FROM books ORDER BY id";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $all_books = $stmt->fetchAll();
    
    $updated_count = 0;
    $cover_index = 0;
    
    foreach ($all_books as $book) {
        $cover_file = null;
        
        // First, check if book already has a valid cover
        if (!empty($book['cover_image']) && file_exists($covers_dir . $book['cover_image'])) {
            continue; // Skip books that already have valid covers
        }
        
        // Check specific mappings first
        foreach ($book_cover_mapping as $title_pattern => $cover) {
            if (stripos($book['title'], $title_pattern) !== false && in_array($cover, $available_covers)) {
                $cover_file = $cover;
                break;
            }
        }
        
        // If no specific mapping, try to match by title similarity
        if (!$cover_file) {
            foreach ($available_covers as $cover) {
                $cover_name = pathinfo($cover, PATHINFO_FILENAME);
                $cover_clean = preg_replace('/[^a-z0-9]/', '', strtolower($cover_name));
                $title_clean = preg_replace('/[^a-z0-9]/', '', strtolower($book['title']));
                
                // Check for partial matches
                if (strlen($title_clean) > 3 && (strpos($cover_clean, $title_clean) !== false || strpos($title_clean, $cover_clean) !== false)) {
                    $cover_file = $cover;
                    break;
                }
            }
        }
        
        // If still no match, assign covers in round-robin fashion
        if (!$cover_file && count($available_covers) > 0) {
            $cover_file = $available_covers[$cover_index % count($available_covers)];
            $cover_index++;
        }
        
        // Update the book if we found a cover
        if ($cover_file) {
            $update_query = "UPDATE books SET cover_image = :cover_image WHERE id = :id";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->bindParam(':cover_image', $cover_file);
            $update_stmt->bindParam(':id', $book['id']);
            
            if ($update_stmt->execute()) {
                echo "<div class='alert alert-success'>✅ Updated '{$book['title']}' with cover: {$cover_file}</div>";
                $updated_count++;
            } else {
                echo "<div class='alert alert-danger'>❌ Failed to update '{$book['title']}'</div>";
            }
        }
    }
    
    echo "<div class='alert alert-info'><strong>Summary:</strong> Updated {$updated_count} books with cover images.</div>";
    
    // Refresh the page to show updated stats
    echo "<script>setTimeout(function(){ window.location.href = window.location.pathname; }, 3000);</script>";
}

// Step 4: Test image display
echo "<h3>Step 4: Test Image Display</h3>";
$query = "SELECT * FROM books WHERE cover_image IS NOT NULL AND cover_image != '' LIMIT 6";
$stmt = $db->prepare($query);
$stmt->execute();
$test_books = $stmt->fetchAll();

echo "<div class='row'>";
foreach ($test_books as $book) {
    $image_src = url('uploads/covers/' . $book['cover_image']);
    $file_exists = file_exists('uploads/covers/' . $book['cover_image']);
    
    echo "<div class='col-md-4 mb-3'>";
    echo "<div class='card'>";
    echo "<div class='card-header'>";
    echo "<small>File: " . htmlspecialchars($book['cover_image']) . "</small><br>";
    echo "<small>Exists: " . ($file_exists ? 'Yes' : 'No') . "</small><br>";
    echo "<small>URL: " . htmlspecialchars($image_src) . "</small>";
    echo "</div>";
    
    if ($file_exists) {
        echo "<img src='" . htmlspecialchars($image_src) . "' class='card-img-top' style='height: 200px; object-fit: cover;' alt='" . htmlspecialchars($book['title']) . "'>";
    } else {
        echo "<div class='card-img-top d-flex align-items-center justify-content-center bg-light' style='height: 200px;'>";
        echo "<span class='text-muted'>Image not found</span>";
        echo "</div>";
    }
    
    echo "<div class='card-body'>";
    echo "<h6 class='card-title'>" . htmlspecialchars($book['title']) . "</h6>";
    echo "<p class='card-text'><small>" . htmlspecialchars($book['author']) . "</small></p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}
echo "</div>";

// Action buttons
echo "<h3>Actions</h3>";
echo "<form method='post' class='mb-3'>";
echo "<button type='submit' name='fix_all_covers' class='btn btn-primary btn-lg'>Fix All Book Covers</button>";
echo "</form>";

echo "<div class='mt-4'>";
echo "<a href='catalog.php' class='btn btn-success'>View Catalog</a> ";
echo "<a href='books/index.php' class='btn btn-info'>Manage Books</a>";
echo "</div>";

echo "</div></body></html>";
?>
