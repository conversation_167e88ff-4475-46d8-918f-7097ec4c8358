<?php
/**
 * Test Logout Routing Fix
 * This file tests if the logout routing is working correctly
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h2>Logout Routing Test</h2>";

// Show current session status
echo "<h3>Current Session Status:</h3>";
session_start();
if (isset($_SESSION['user_id'])) {
    echo "<p class='success'>✅ Admin/Staff logged in: " . ($_SESSION['username'] ?? 'Unknown') . "</p>";
} elseif (isset($_SESSION['member_id'])) {
    echo "<p class='success'>✅ Member logged in: " . ($_SESSION['member_name'] ?? 'Unknown') . "</p>";
} else {
    echo "<p class='info'>ℹ️ No active session</p>";
}

// Test logout URLs from different directories
echo "<h3>Logout URL Generation Test:</h3>";
echo "<p><strong>From root directory:</strong></p>";
echo "<ul>";
echo "<li>logout.php → <code>" . url('logout.php') . "</code></li>";
echo "<li>logout_success.php → <code>" . url('logout_success.php') . "</code></li>";
echo "<li>home.php → <code>" . url('home.php') . "</code></li>";
echo "<li>login.php → <code>" . url('login.php') . "</code></li>";
echo "</ul>";

// Check logout files exist
echo "<h3>Logout Files Check:</h3>";
$logout_files = [
    'logout.php' => 'Main logout file',
    'logout_success.php' => 'Logout success page',
    'logout_fix.php' => 'Alternative logout file',
    'member_logout.php' => 'Member logout file'
];

foreach ($logout_files as $file => $description) {
    $exists = file_exists($file);
    echo "<p>" . ($exists ? '✅' : '❌') . " <strong>$description:</strong> $file</p>";
}

// Test logout links
echo "<h3>Test Logout Process:</h3>";
echo "<div class='alert alert-warning'>";
echo "<strong>⚠️ Warning:</strong> These links will log you out if you're currently logged in!";
echo "</div>";

echo "<p><a href='" . url('logout.php') . "' class='btn btn-danger'>Test Main Logout</a></p>";
echo "<p><a href='" . url('logout_success.php') . "' class='btn btn-info'>View Logout Success Page</a></p>";

// Navigation test
echo "<h3>Navigation After Logout:</h3>";
echo "<p>After logout, you should be redirected to:</p>";
echo "<ol>";
echo "<li><strong>logout_success.php</strong> - Shows logout confirmation</li>";
echo "<li><strong>home.php</strong> - Automatically redirected after 5 seconds</li>";
echo "</ol>";

echo "<h3>Expected Behavior:</h3>";
echo "<ul>";
echo "<li>✅ Logout from any page should work</li>";
echo "<li>✅ Should redirect to logout success page</li>";
echo "<li>✅ Should automatically redirect to home page</li>";
echo "<li>✅ All session data should be cleared</li>";
echo "<li>✅ Remember me cookies should be cleared</li>";
echo "</ul>";

echo "<h3>Quick Login for Testing:</h3>";
if (!isset($_SESSION['user_id']) && !isset($_SESSION['member_id'])) {
    echo "<p><a href='" . url('login.php') . "' class='btn btn-success'>Login to Test Logout</a></p>";
    echo "<p><a href='" . url('quick_login.php') . "' class='btn btn-primary'>Quick Admin Login</a></p>";
} else {
    echo "<p class='success'>You are logged in and can test logout functionality above.</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px; }
.btn-danger { background: #dc3545; }
.btn-success { background: #28a745; }
.btn-info { background: #17a2b8; }
.btn-primary { background: #007bff; }
.alert { padding: 15px; margin: 15px 0; border-radius: 4px; }
.alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
.success { color: #28a745; }
.info { color: #17a2b8; }
h3 { color: #333; margin-top: 20px; }
p { margin: 5px 0; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
ul, ol { margin: 10px 0; }
li { margin: 5px 0; }
</style>
