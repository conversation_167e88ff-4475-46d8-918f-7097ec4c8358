<?php
/**
 * Member Fines and Payment History
 * Allow members to view their fines and payment history
 */

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in as member
if (!isset($_SESSION['member_id'])) {
    redirect('../login.php');
    exit;
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

$member_id = $_SESSION['member_id'];

// Get member information
$query = "SELECT * FROM members WHERE id = :member_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$member = $stmt->fetch();

if (!$member) {
    redirect('../login.php');
    exit;
}

// Get unpaid fines
$unpaid_query = "SELECT f.*, 
                        b.title as book_title,
                        b.author as book_author,
                        bl.due_date,
                        bl.issue_date,
                        DATEDIFF(CURDATE(), bl.due_date) as days_overdue
                 FROM fines f
                 LEFT JOIN book_loans bl ON f.loan_id = bl.id
                 LEFT JOIN books b ON bl.book_id = b.id
                 WHERE f.member_id = :member_id AND f.status = 'unpaid'
                 ORDER BY f.created_date DESC";

$stmt = $db->prepare($unpaid_query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$unpaid_fines = $stmt->fetchAll();

// Get payment history
$payment_query = "SELECT pt.*, f.amount as fine_amount,
                         b.title as book_title,
                         b.author as book_author,
                         bl.due_date,
                         u.username as processed_by
                  FROM payment_transactions pt
                  JOIN fines f ON pt.fine_id = f.id
                  LEFT JOIN book_loans bl ON f.loan_id = bl.id
                  LEFT JOIN books b ON bl.book_id = b.id
                  LEFT JOIN users u ON pt.processed_by = u.id
                  WHERE pt.member_id = :member_id
                  ORDER BY pt.transaction_date DESC";

$stmt = $db->prepare($payment_query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$payment_history = $stmt->fetchAll();

// Calculate totals
$total_unpaid = array_sum(array_column($unpaid_fines, 'amount'));
$total_paid = array_sum(array_column($payment_history, 'amount'));

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function formatCurrency($amount) {
    return '₱' . number_format($amount, 2);
}

function getPaymentMethodDisplay($method) {
    $methods = [
        'cash' => 'Cash',
        'gcash' => 'GCash',
        'paymaya' => 'PayMaya',
        'bank_transfer' => 'Bank Transfer',
        'credit_card' => 'Credit Card',
        'debit_card' => 'Debit Card'
    ];
    return $methods[$method] ?? ucfirst(str_replace('_', ' ', $method));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Fines & Payments - Library System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .stats-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.2s ease-in-out;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .fine-row {
            transition: background-color 0.2s ease;
        }
        .fine-row:hover {
            background-color: #f8f9fa;
        }
        .member-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .overdue-badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-4">
        <!-- Member Header -->
        <div class="member-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-person-circle me-2"></i>Welcome, <?php echo h($member['first_name'] . ' ' . $member['last_name']); ?>
                    </h1>
                    <p class="mb-0">View your library fines and payment history</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="../member_dashboard.php" class="btn btn-light">
                        <i class="bi bi-arrow-left me-1"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card stats-card text-white bg-danger">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="text-white-50">Unpaid Fines</h6>
                                <h3 class="mb-0"><?php echo formatCurrency($total_unpaid); ?></h3>
                                <small class="text-white-50"><?php echo count($unpaid_fines); ?> outstanding</small>
                            </div>
                            <div>
                                <i class="bi bi-exclamation-triangle fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card text-white bg-success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="text-white-50">Total Paid</h6>
                                <h3 class="mb-0"><?php echo formatCurrency($total_paid); ?></h3>
                                <small class="text-white-50"><?php echo count($payment_history); ?> payments</small>
                            </div>
                            <div>
                                <i class="bi bi-check-circle fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card text-white bg-info">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="text-white-50">Member Status</h6>
                                <h3 class="mb-0">
                                    <?php if ($total_unpaid > 0): ?>
                                        <span class="badge bg-warning">Has Fines</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">Good Standing</span>
                                    <?php endif; ?>
                                </h3>
                                <small class="text-white-50">Current status</small>
                            </div>
                            <div>
                                <i class="bi bi-person-check fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Unpaid Fines -->
        <?php if (!empty($unpaid_fines)): ?>
            <div class="card mb-4">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>Outstanding Fines
                        <span class="badge bg-light text-danger"><?php echo count($unpaid_fines); ?></span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Book</th>
                                    <th>Due Date</th>
                                    <th>Days Overdue</th>
                                    <th>Fine Amount</th>
                                    <th>Fine Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($unpaid_fines as $fine): ?>
                                    <tr class="fine-row">
                                        <td>
                                            <strong><?php echo h($fine['book_title'] ?? 'N/A'); ?></strong>
                                            <?php if ($fine['book_author']): ?>
                                                <br><small class="text-muted">by <?php echo h($fine['book_author']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($fine['due_date']): ?>
                                                <?php echo date('M j, Y', strtotime($fine['due_date'])); ?>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($fine['days_overdue'] > 0): ?>
                                                <span class="badge bg-danger overdue-badge">
                                                    <?php echo $fine['days_overdue']; ?> days
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong class="text-danger"><?php echo formatCurrency($fine['amount']); ?></strong>
                                        </td>
                                        <td><?php echo date('M j, Y', strtotime($fine['created_date'])); ?></td>
                                        <td>
                                            <span class="badge bg-danger">Unpaid</span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <p class="mb-0">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>Please visit the library to pay your outstanding fines.</strong>
                                Contact the librarian for payment options and assistance.
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <strong>Total Outstanding: <?php echo formatCurrency($total_unpaid); ?></strong>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="card mb-4">
                <div class="card-body text-center py-5">
                    <i class="bi bi-check-circle fs-1 text-success"></i>
                    <h4 class="mt-3 text-success">No Outstanding Fines!</h4>
                    <p class="text-muted">You have no unpaid fines. Keep up the good work!</p>
                </div>
            </div>
        <?php endif; ?>

        <!-- Payment History -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history me-2"></i>Payment History
                    <?php if (!empty($payment_history)): ?>
                        <span class="badge bg-primary"><?php echo count($payment_history); ?> payments</span>
                    <?php endif; ?>
                </h5>
            </div>
            <div class="card-body p-0">
                <?php if (empty($payment_history)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-receipt fs-1 text-muted"></i>
                        <p class="text-muted mt-3">No payment history found.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Receipt #</th>
                                    <th>Payment Date</th>
                                    <th>Book</th>
                                    <th>Amount</th>
                                    <th>Payment Method</th>
                                    <th>Reference</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($payment_history as $payment): ?>
                                    <tr class="fine-row">
                                        <td>
                                            <strong class="text-primary"><?php echo h($payment['receipt_number']); ?></strong>
                                        </td>
                                        <td>
                                            <?php echo date('M j, Y', strtotime($payment['transaction_date'])); ?><br>
                                            <small class="text-muted"><?php echo date('g:i A', strtotime($payment['transaction_date'])); ?></small>
                                        </td>
                                        <td>
                                            <strong><?php echo h($payment['book_title'] ?? 'N/A'); ?></strong>
                                            <?php if ($payment['book_author']): ?>
                                                <br><small class="text-muted">by <?php echo h($payment['book_author']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong class="text-success"><?php echo formatCurrency($payment['amount']); ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                <?php echo getPaymentMethodDisplay($payment['payment_method']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($payment['payment_reference']): ?>
                                                <code><?php echo h($payment['payment_reference']); ?></code>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">Paid</span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
