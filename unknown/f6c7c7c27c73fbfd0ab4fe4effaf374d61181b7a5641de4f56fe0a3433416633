<?php
// Simple Admin Dashboard - Working Version
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// For development - bypass authentication
$bypass_auth = true;

// Connect to database
try {
    $database = new Database();
    $db = $database->getConnection();
    if (!$db) {
        throw new Exception("Database connection failed");
    }
} catch (Exception $e) {
    $db_error = $e->getMessage();
}

// Get basic statistics
$stats = [
    'total_books' => 0,
    'available_books' => 0,
    'total_members' => 0,
    'active_loans' => 0,
    'overdue_books' => 0
];

if (isset($db)) {
    try {
        // Check if tables exist and get stats
        $tables = ['books', 'members', 'book_loans'];
        $tables_status = [];
        
        foreach ($tables as $table) {
            try {
                $query = "SELECT COUNT(*) as count FROM $table";
                $stmt = $db->prepare($query);
                $stmt->execute();
                $result = $stmt->fetch();
                $tables_status[$table] = $result['count'];
            } catch (Exception $e) {
                $tables_status[$table] = 'Error: ' . $e->getMessage();
            }
        }
        
        // Get actual stats if tables exist
        if (is_numeric($tables_status['books'])) {
            $stats['total_books'] = $tables_status['books'];
        }
        if (is_numeric($tables_status['members'])) {
            $stats['total_members'] = $tables_status['members'];
        }
        if (is_numeric($tables_status['book_loans'])) {
            $stats['active_loans'] = $tables_status['book_loans'];
        }
        
        // Try to get available books
        try {
            $query = "SELECT SUM(COALESCE(available_quantity, 0)) as available FROM books";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch();
            $stats['available_books'] = $result['available'] ?? 0;
        } catch (Exception $e) {
            $stats['available_books'] = 0;
        }
        
    } catch (Exception $e) {
        $stats_error = $e->getMessage();
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body { background-color: #f8f9fa; }
        .stats-card { transition: transform 0.2s; }
        .stats-card:hover { transform: translateY(-5px); }
        .sidebar { min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .sidebar .nav-link { color: rgba(255,255,255,0.8); }
        .sidebar .nav-link:hover { color: white; background: rgba(255,255,255,0.1); }
        .sidebar .nav-link.active { color: white; background: rgba(255,255,255,0.2); }
        .main-content { margin-left: 0; }
        @media (min-width: 768px) {
            .main-content { margin-left: 250px; }
        }
        .status-indicator { width: 10px; height: 10px; border-radius: 50%; display: inline-block; margin-right: 5px; }
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-danger { background-color: #dc3545; }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar position-fixed d-none d-md-block" style="width: 250px; z-index: 1000;">
        <div class="p-3">
            <h4 class="text-white mb-4">
                <i class="bi bi-book me-2"></i>LMS Admin
            </h4>
            <nav class="nav flex-column">
                <a class="nav-link active" href="dashboard.php">
                    <i class="bi bi-speedometer2 me-2"></i>Dashboard
                </a>
                <a class="nav-link" href="../books/index.php">
                    <i class="bi bi-book me-2"></i>Manage Books
                </a>
                <a class="nav-link" href="../members/index.php">
                    <i class="bi bi-people me-2"></i>Manage Members
                </a>
                <a class="nav-link" href="../loans/index.php">
                    <i class="bi bi-arrow-left-right me-2"></i>Manage Loans
                </a>
                <a class="nav-link" href="../reports/index.php">
                    <i class="bi bi-graph-up me-2"></i>Reports
                </a>
                <hr class="text-white-50">
                <a class="nav-link" href="../home.php">
                    <i class="bi bi-house me-2"></i>Back to Site
                </a>
                <a class="nav-link" href="../logout.php">
                    <i class="bi bi-box-arrow-right me-2"></i>Logout
                </a>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container-fluid">
                <button class="navbar-toggler d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <h5 class="mb-0">Admin Dashboard</h5>
                <div class="ms-auto">
                    <span class="badge bg-primary">
                        <i class="bi bi-person-circle me-1"></i>Administrator
                    </span>
                </div>
            </div>
        </nav>

        <!-- Dashboard Content -->
        <div class="container-fluid p-4">
            <!-- System Status -->
            <?php if (isset($db_error)): ?>
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Database Error:</strong> <?php echo h($db_error); ?>
                </div>
            <?php endif; ?>

            <?php if (isset($stats_error)): ?>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Statistics Error:</strong> <?php echo h($stats_error); ?>
                </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card stats-card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Books</h6>
                                    <h3 class="mb-0"><?php echo number_format($stats['total_books']); ?></h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-book fs-1"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <a href="../books/index.php" class="text-white text-decoration-none">
                                <i class="bi bi-arrow-right me-1"></i>Manage Books
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-3">
                    <div class="card stats-card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Available Books</h6>
                                    <h3 class="mb-0"><?php echo number_format($stats['available_books']); ?></h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-check-circle fs-1"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <a href="../catalog.php" class="text-white text-decoration-none">
                                <i class="bi bi-arrow-right me-1"></i>View Catalog
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-3">
                    <div class="card stats-card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Members</h6>
                                    <h3 class="mb-0"><?php echo number_format($stats['total_members']); ?></h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-people fs-1"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <a href="../members/index.php" class="text-white text-decoration-none">
                                <i class="bi bi-arrow-right me-1"></i>Manage Members
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-3">
                    <div class="card stats-card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Active Loans</h6>
                                    <h3 class="mb-0"><?php echo number_format($stats['active_loans']); ?></h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-arrow-left-right fs-1"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <a href="../loans/index.php" class="text-white text-decoration-none">
                                <i class="bi bi-arrow-right me-1"></i>Manage Loans
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>System Status</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <span class="status-indicator <?php echo isset($db) ? 'status-success' : 'status-danger'; ?>"></span>
                                Database Connection: <?php echo isset($db) ? 'Connected' : 'Failed'; ?>
                            </div>
                            <div class="mb-2">
                                <span class="status-indicator status-success"></span>
                                PHP Version: <?php echo phpversion(); ?>
                            </div>
                            <div class="mb-2">
                                <span class="status-indicator status-success"></span>
                                Server: <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-table me-2"></i>Database Tables</h5>
                        </div>
                        <div class="card-body">
                            <?php if (isset($tables_status)): ?>
                                <?php foreach ($tables_status as $table => $status): ?>
                                    <div class="mb-2">
                                        <span class="status-indicator <?php echo is_numeric($status) ? 'status-success' : 'status-danger'; ?>"></span>
                                        <?php echo ucfirst($table); ?>: 
                                        <?php echo is_numeric($status) ? $status . ' records' : 'Error'; ?>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted">Unable to check table status</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-lightning me-2"></i>Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <a href="../books/add.php" class="btn btn-primary w-100">
                                        <i class="bi bi-plus-circle me-2"></i>Add New Book
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="../members/add.php" class="btn btn-success w-100">
                                        <i class="bi bi-person-plus me-2"></i>Add New Member
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="../loans/issue.php" class="btn btn-info w-100">
                                        <i class="bi bi-book-half me-2"></i>Issue Book
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="../reports/index.php" class="btn btn-warning w-100">
                                        <i class="bi bi-graph-up me-2"></i>View Reports
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh stats every 30 seconds
        setInterval(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
