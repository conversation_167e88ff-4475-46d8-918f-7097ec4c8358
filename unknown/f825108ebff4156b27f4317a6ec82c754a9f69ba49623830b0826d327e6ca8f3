<?php
/**
 * System Access Test
 * This page tests all major access points of the LMS system
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session safely
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>LMS System Access Test</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css'>
    <style>
        .test-pass { color: #28a745; }
        .test-fail { color: #dc3545; }
        .test-warning { color: #ffc107; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; }
        .access-link { margin: 5px; }
    </style>
</head>
<body>
<div class='container mt-4'>
    <h1 class='mb-4'><i class='bi bi-shield-check me-2'></i>LMS System Access Test</h1>";

// Test 1: Core Page Access
echo "<div class='test-section'>
<h3><i class='bi bi-globe me-2'></i>Core Page Access Test</h3>";

$core_pages = [
    'index.php' => 'Main Entry Point',
    'home.php' => 'Public Homepage',
    'login.php' => 'Login Page',
    'register.php' => 'Registration Page',
    'catalog.php' => 'Book Catalog',
    'about.php' => 'About Page',
    'contact.php' => 'Contact Page'
];

foreach ($core_pages as $page => $description) {
    if (file_exists($page)) {
        echo "<div class='access-link'>
            <a href='$page' class='btn btn-sm btn-outline-primary' target='_blank'>
                <i class='bi bi-box-arrow-up-right me-1'></i>$page
            </a>
            <span class='test-pass ms-2'>✓ $description</span>
        </div>";
    } else {
        echo "<div class='access-link'>
            <span class='btn btn-sm btn-secondary disabled'>$page</span>
            <span class='test-fail ms-2'>✗ $description (Missing)</span>
        </div>";
    }
}
echo "</div>";

// Test 2: Admin Access
echo "<div class='test-section'>
<h3><i class='bi bi-shield-lock me-2'></i>Admin Access Test</h3>";

$admin_pages = [
    'admin/dashboard.php' => 'Admin Dashboard',
    'admin/index.php' => 'Admin Index'
];

foreach ($admin_pages as $page => $description) {
    if (file_exists($page)) {
        echo "<div class='access-link'>
            <a href='$page' class='btn btn-sm btn-outline-warning' target='_blank'>
                <i class='bi bi-box-arrow-up-right me-1'></i>$page
            </a>
            <span class='test-pass ms-2'>✓ $description</span>
        </div>";
    } else {
        echo "<div class='access-link'>
            <span class='btn btn-sm btn-secondary disabled'>$page</span>
            <span class='test-fail ms-2'>✗ $description (Missing)</span>
        </div>";
    }
}
echo "</div>";

// Test 3: Database Connection Test
echo "<div class='test-section'>
<h3><i class='bi bi-database me-2'></i>Database Connection Test</h3>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<p class='test-pass'><i class='bi bi-check-circle-fill me-2'></i>Database connection successful</p>";
    
    // Test sample queries
    $test_queries = [
        'SELECT COUNT(*) as count FROM users' => 'Users table',
        'SELECT COUNT(*) as count FROM members' => 'Members table',
        'SELECT COUNT(*) as count FROM books' => 'Books table',
        'SELECT COUNT(*) as count FROM book_loans' => 'Book loans table'
    ];
    
    foreach ($test_queries as $query => $description) {
        try {
            $stmt = $db->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch();
            echo "<p class='test-pass'><i class='bi bi-check-circle-fill me-2'></i>$description: {$result['count']} records</p>";
        } catch (Exception $e) {
            echo "<p class='test-fail'><i class='bi bi-x-circle-fill me-2'></i>$description: Error - " . $e->getMessage() . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p class='test-fail'><i class='bi bi-x-circle-fill me-2'></i>Database connection failed: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 4: Authentication Test
echo "<div class='test-section'>
<h3><i class='bi bi-person-check me-2'></i>Authentication Test</h3>";

try {
    require_once 'includes/functions.php';
    
    echo "<p class='test-pass'><i class='bi bi-check-circle-fill me-2'></i>Functions loaded successfully</p>";
    echo "<p><strong>Current Session Status:</strong></p>";
    echo "<ul>";
    echo "<li>User logged in: " . (isLoggedIn() ? '<span class="test-pass">Yes</span>' : '<span class="test-warning">No</span>') . "</li>";
    echo "<li>Admin role: " . (isAdmin() ? '<span class="test-pass">Yes</span>' : '<span class="test-warning">No</span>') . "</li>";
    echo "<li>Librarian role: " . (isLibrarian() ? '<span class="test-pass">Yes</span>' : '<span class="test-warning">No</span>') . "</li>";
    echo "<li>Member logged in: " . (isMemberLoggedIn() ? '<span class="test-pass">Yes</span>' : '<span class="test-warning">No</span>') . "</li>";
    echo "</ul>";
    
    if (isset($_SESSION['username'])) {
        echo "<p><strong>Logged in as:</strong> " . htmlspecialchars($_SESSION['username']) . " (" . ($_SESSION['role'] ?? 'Unknown role') . ")</p>";
    }
    
    if (isset($_SESSION['member_name'])) {
        echo "<p><strong>Member logged in as:</strong> " . htmlspecialchars($_SESSION['member_name']) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='test-fail'><i class='bi bi-x-circle-fill me-2'></i>Authentication test failed: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 5: Quick Actions
echo "<div class='test-section'>
<h3><i class='bi bi-lightning me-2'></i>Quick Actions</h3>";

echo "<div class='row'>
    <div class='col-md-6'>
        <h5>Public Access</h5>
        <div class='d-grid gap-2'>
            <a href='home.php' class='btn btn-primary'><i class='bi bi-house me-2'></i>Go to Homepage</a>
            <a href='catalog.php' class='btn btn-info'><i class='bi bi-book me-2'></i>Browse Books</a>
            <a href='register.php' class='btn btn-success'><i class='bi bi-person-plus me-2'></i>Register as Member</a>
        </div>
    </div>
    <div class='col-md-6'>
        <h5>System Access</h5>
        <div class='d-grid gap-2'>
            <a href='login.php' class='btn btn-warning'><i class='bi bi-box-arrow-in-right me-2'></i>Login</a>
            <a href='admin/dashboard.php' class='btn btn-danger'><i class='bi bi-shield-lock me-2'></i>Admin Dashboard</a>
            <a href='system_status_check.php' class='btn btn-secondary'><i class='bi bi-gear me-2'></i>System Status</a>
        </div>
    </div>
</div>";

echo "</div>";

// Test 6: System Information
echo "<div class='test-section'>
<h3><i class='bi bi-info-circle me-2'></i>System Information</h3>";

echo "<div class='row'>
    <div class='col-md-6'>
        <h6>Server Information</h6>
        <ul class='list-unstyled'>
            <li><strong>PHP Version:</strong> " . phpversion() . "</li>
            <li><strong>Server:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</li>
            <li><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</li>
            <li><strong>Current Directory:</strong> " . __DIR__ . "</li>
        </ul>
    </div>
    <div class='col-md-6'>
        <h6>System Status</h6>
        <ul class='list-unstyled'>
            <li><strong>Session Status:</strong> " . (session_status() === PHP_SESSION_ACTIVE ? '<span class="test-pass">Active</span>' : '<span class="test-warning">Inactive</span>') . "</li>
            <li><strong>Error Reporting:</strong> " . (error_reporting() ? '<span class="test-pass">Enabled</span>' : '<span class="test-warning">Disabled</span>') . "</li>
            <li><strong>Upload Directory:</strong> " . (is_dir('uploads') ? '<span class="test-pass">Exists</span>' : '<span class="test-fail">Missing</span>') . "</li>
            <li><strong>Config Directory:</strong> " . (is_dir('config') ? '<span class="test-pass">Exists</span>' : '<span class="test-fail">Missing</span>') . "</li>
        </ul>
    </div>
</div>";

echo "</div>";

// Summary
echo "<div class='test-section bg-light'>
<h3><i class='bi bi-clipboard-check me-2'></i>Test Summary</h3>
<div class='alert alert-info'>
    <h5><i class='bi bi-info-circle-fill me-2'></i>System Access Test Complete</h5>
    <p>This test verifies that all major components of your LMS system are accessible and functional.</p>
    <p><strong>Next Steps:</strong></p>
    <ul>
        <li>Test user registration and login functionality</li>
        <li>Verify book catalog browsing and search</li>
        <li>Test admin dashboard access and features</li>
        <li>Check member dashboard functionality</li>
    </ul>
</div>
</div>";

echo "</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
