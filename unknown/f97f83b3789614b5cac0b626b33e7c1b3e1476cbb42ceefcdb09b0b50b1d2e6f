<?php
/**
 * Test Logout Functionality
 */
session_start();

// Include functions if available
if (file_exists('includes/functions.php')) {
    require_once 'includes/functions.php';
}

echo "<h1>🔧 Logout Test Page</h1>";
echo "<p>This page helps test the logout functionality.</p>";

// Display current session status
echo "<h3>📊 Current Session Status:</h3>";
echo "<ul>";
echo "<li><strong>Session ID:</strong> " . session_id() . "</li>";
echo "<li><strong>User ID:</strong> " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'Not set') . "</li>";
echo "<li><strong>Username:</strong> " . (isset($_SESSION['username']) ? $_SESSION['username'] : 'Not set') . "</li>";
echo "<li><strong>Role:</strong> " . (isset($_SESSION['role']) ? $_SESSION['role'] : 'Not set') . "</li>";
echo "<li><strong>Member ID:</strong> " . (isset($_SESSION['member_id']) ? $_SESSION['member_id'] : 'Not set') . "</li>";
echo "<li><strong>Member Name:</strong> " . (isset($_SESSION['member_name']) ? $_SESSION['member_name'] : 'Not set') . "</li>";
echo "</ul>";

// Display available logout options
echo "<h3>🚪 Logout Options:</h3>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='logout.php' class='btn btn-danger' style='margin: 5px; padding: 10px 20px; background: #dc3545; color: white; text-decoration: none; border-radius: 5px;'>Main Logout (logout.php)</a><br>";
echo "<a href='auth/logout.php' class='btn btn-warning' style='margin: 5px; padding: 10px 20px; background: #ffc107; color: black; text-decoration: none; border-radius: 5px;'>Auth Logout (auth/logout.php)</a><br>";
echo "<a href='member_logout.php' class='btn btn-info' style='margin: 5px; padding: 10px 20px; background: #17a2b8; color: white; text-decoration: none; border-radius: 5px;'>Member Logout (member_logout.php)</a><br>";
echo "</div>";

// Quick login for testing
echo "<h3>🔑 Quick Login for Testing:</h3>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='quick_login.php' class='btn btn-success' style='margin: 5px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>Quick Admin Login</a><br>";
echo "</div>";

// Navigation
echo "<h3>🔗 Navigation:</h3>";
echo "<ul>";
echo "<li><a href='home.php'>Home Page</a></li>";
echo "<li><a href='admin/dashboard.php'>Admin Dashboard</a></li>";
echo "<li><a href='member_dashboard.php'>Member Dashboard</a></li>";
echo "<li><a href='login.php'>Login Page</a></li>";
echo "</ul>";

echo "<style>
.btn {
    display: inline-block;
    margin: 5px;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
}
.btn-danger { background: #dc3545; color: white; }
.btn-warning { background: #ffc107; color: black; }
.btn-info { background: #17a2b8; color: white; }
.btn-success { background: #28a745; color: white; }
</style>";
?>
