<?php
/**
 * Add Books Script - Part 2
 * This script adds a collection of books to the library database
 */

// Database connection
require_once 'config/database.php';
$database = new Database();
$db = $database->getConnection();

// Function to add a book
function addBook($db, $isbn, $title, $author, $category, $publication_year, $publisher, 
                $quantity, $available_quantity, $shelf_location, $description) {
    
    // Check if book already exists
    $check_query = "SELECT id FROM books WHERE isbn = :isbn";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':isbn', $isbn);
    $check_stmt->execute();
    
    if ($check_stmt->rowCount() > 0) {
        return "Book with ISBN $isbn already exists in the database.";
    }
    
    // Current date for created_at field
    $created_at = date('Y-m-d H:i:s');
    
    // Insert the book
    $query = "INSERT INTO books (isbn, title, author, category, publication_year, publisher, 
              quantity, available_quantity, shelf_location, description, created_at) 
              VALUES (:isbn, :title, :author, :category, :publication_year, :publisher, 
              :quantity, :available_quantity, :shelf_location, :description, :created_at)";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':isbn', $isbn);
    $stmt->bindParam(':title', $title);
    $stmt->bindParam(':author', $author);
    $stmt->bindParam(':category', $category);
    $stmt->bindParam(':publication_year', $publication_year);
    $stmt->bindParam(':publisher', $publisher);
    $stmt->bindParam(':quantity', $quantity);
    $stmt->bindParam(':available_quantity', $available_quantity);
    $stmt->bindParam(':shelf_location', $shelf_location);
    $stmt->bindParam(':description', $description);
    $stmt->bindParam(':created_at', $created_at);
    
    if ($stmt->execute()) {
        return "Book '$title' added successfully.";
    } else {
        return "Error adding book '$title': " . print_r($stmt->errorInfo(), true);
    }
}

// Books to add - Part 2
$books = [
    // Mystery & Thriller
    [
        'isbn' => '978-0062073495',
        'title' => 'Gone Girl',
        'author' => 'Gillian Flynn',
        'category' => 'Mystery, Thriller',
        'publication_year' => 2012,
        'publisher' => 'Crown Publishing Group',
        'quantity' => 5,
        'available_quantity' => 5,
        'shelf_location' => 'MYST-001',
        'description' => 'A psychological thriller about a missing wife.'
    ],
    [
        'isbn' => '978-**********',
        'title' => 'The Girl with the Dragon Tattoo',
        'author' => 'Stieg Larsson',
        'category' => 'Mystery, Thriller',
        'publication_year' => 2005,
        'publisher' => 'Norstedts Förlag',
        'quantity' => 4,
        'available_quantity' => 4,
        'shelf_location' => 'MYST-002',
        'description' => 'A journalist and a hacker investigate a decades-old disappearance.'
    ],
    [
        'isbn' => '978-**********',
        'title' => 'The Silent Patient',
        'author' => 'Alex Michaelides',
        'category' => 'Psychological Thriller',
        'publication_year' => 2019,
        'publisher' => 'Celadon Books',
        'quantity' => 3,
        'available_quantity' => 3,
        'shelf_location' => 'MYST-003',
        'description' => 'A woman shoots her husband and then stops speaking.'
    ],
    [
        'isbn' => '978-**********',
        'title' => 'The Woman in the Window',
        'author' => 'A.J. Finn',
        'category' => 'Thriller',
        'publication_year' => 2018,
        'publisher' => 'William Morrow',
        'quantity' => 4,
        'available_quantity' => 4,
        'shelf_location' => 'MYST-004',
        'description' => 'An agoraphobic woman witnesses a crime.'
    ],
    [
        'isbn' => '978-**********',
        'title' => 'The Girl on the Train',
        'author' => 'Paula Hawkins',
        'category' => 'Thriller',
        'publication_year' => 2015,
        'publisher' => 'Riverhead Books',
        'quantity' => 5,
        'available_quantity' => 5,
        'shelf_location' => 'MYST-005',
        'description' => 'A woman becomes entangled in a missing person investigation.'
    ],
    
    // Self-Help & Personal Development
    [
        'isbn' => '978-0671027032',
        'title' => 'How to Win Friends and Influence People',
        'author' => 'Dale Carnegie',
        'category' => 'Self-Help',
        'publication_year' => 1936,
        'publisher' => 'Simon & Schuster',
        'quantity' => 6,
        'available_quantity' => 6,
        'shelf_location' => 'SH-001',
        'description' => 'A classic guide to interpersonal skills.'
    ],
    [
        'isbn' => '978-0735211292',
        'title' => 'Atomic Habits',
        'author' => 'James Clear',
        'category' => 'Self-Help',
        'publication_year' => 2018,
        'publisher' => 'Avery',
        'quantity' => 5,
        'available_quantity' => 5,
        'shelf_location' => 'SH-002',
        'description' => 'A guide to building good habits and breaking bad ones.'
    ],
    [
        'isbn' => '978-0743269513',
        'title' => 'The 7 Habits of Highly Effective People',
        'author' => 'Stephen R. Covey',
        'category' => 'Self-Help',
        'publication_year' => 1989,
        'publisher' => 'Free Press',
        'quantity' => 4,
        'available_quantity' => 4,
        'shelf_location' => 'SH-003',
        'description' => 'A framework for personal and professional effectiveness.'
    ],
    [
        'isbn' => '978-1591846357',
        'title' => 'The 4-Hour Workweek',
        'author' => 'Timothy Ferriss',
        'category' => 'Self-Help, Business',
        'publication_year' => 2007,
        'publisher' => 'Crown Publishing',
        'quantity' => 3,
        'available_quantity' => 3,
        'shelf_location' => 'SH-004',
        'description' => 'A guide to lifestyle design and escaping the 9-5 grind.'
    ],
    [
        'isbn' => '978-0062801975',
        'title' => 'Think and Grow Rich',
        'author' => 'Napoleon Hill',
        'category' => 'Self-Help, Finance',
        'publication_year' => 1937,
        'publisher' => 'The Ralston Society',
        'quantity' => 5,
        'available_quantity' => 5,
        'shelf_location' => 'SH-005',
        'description' => 'A personal development and wealth-building classic.'
    ]
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Books (Part 2) - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Add Books to Library (Part 2)</h1>
        
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Adding Books...</h5>
                <div class="mt-3">
                    <?php
                    $success_count = 0;
                    $error_count = 0;
                    
                    foreach ($books as $book) {
                        $result = addBook(
                            $db, 
                            $book['isbn'],
                            $book['title'],
                            $book['author'],
                            $book['category'],
                            $book['publication_year'],
                            $book['publisher'],
                            $book['quantity'],
                            $book['available_quantity'],
                            $book['shelf_location'],
                            $book['description']
                        );
                        
                        echo "<p>";
                        if (strpos($result, 'successfully') !== false) {
                            echo "<span class='text-success'>✓ </span>";
                            $success_count++;
                        } else {
                            echo "<span class='text-warning'>⚠ </span>";
                            $error_count++;
                        }
                        echo $result . "</p>";
                    }
                    ?>
                    
                    <div class="alert <?php echo $error_count > 0 ? 'alert-warning' : 'alert-success'; ?> mt-3">
                        <strong>Summary:</strong> Added <?php echo $success_count; ?> books successfully.
                        <?php if ($error_count > 0): ?>
                            <?php echo $error_count; ?> books could not be added (likely already exist).
                        <?php endif; ?>
                    </div>
                    
                    <div class="mt-3">
                        <a href="add_books_part3.php" class="btn btn-primary">Add More Books (Part 3)</a>
                        <a href="books/index.php" class="btn btn-secondary">View All Books</a>
                        <a href="admin/dashboard.php" class="btn btn-outline-secondary">Back to Dashboard</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
