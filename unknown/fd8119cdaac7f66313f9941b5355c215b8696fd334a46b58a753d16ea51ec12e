<?php
session_start();

// Include required files with error handling
try {
    require_once '../config/database.php';
    require_once '../config/config.php';
    require_once '../includes/functions.php';
} catch (Exception $e) {
    die('Error loading required files: ' . $e->getMessage());
}

// Check if user is logged in and has admin/librarian privileges
if (!isLoggedIn()) {
    redirect('../login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get filter parameters
$category = isset($_GET['category']) ? sanitize($_GET['category']) : '';
$status = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Build query
$query = "SELECT b.*, 
          (b.quantity - b.available_quantity) as borrowed_count,
          COUNT(bl.id) as total_loans
          FROM books b
          LEFT JOIN book_loans bl ON b.id = bl.book_id
          WHERE 1=1";

$params = [];

if (!empty($category)) {
    $query .= " AND b.category = :category";
    $params[':category'] = $category;
}

if (!empty($status)) {
    if ($status === 'available') {
        $query .= " AND b.available_quantity > 0";
    } elseif ($status === 'unavailable') {
        $query .= " AND b.available_quantity = 0";
    }
}

if (!empty($search)) {
    $query .= " AND (b.title LIKE :search OR b.author LIKE :search OR b.isbn LIKE :search)";
    $params[':search'] = '%' . $search . '%';
}

$query .= " GROUP BY b.id ORDER BY b.title ASC";

$stmt = $db->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$books = $stmt->fetchAll();

// Get categories for filter
$cat_query = "SELECT DISTINCT category FROM books WHERE category IS NOT NULL AND category != '' ORDER BY category";
$cat_stmt = $db->prepare($cat_query);
$cat_stmt->execute();
$categories = $cat_stmt->fetchAll();

// Calculate statistics
$total_books = count($books);
$total_available = 0;
$total_borrowed = 0;
$total_quantity = 0;

foreach ($books as $book) {
    $total_available += $book['available_quantity'];
    $total_borrowed += $book['borrowed_count'];
    $total_quantity += $book['quantity'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Books Report - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .stats-card {
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .print-hide {
            display: block;
        }
        @media print {
            .print-hide {
                display: none !important;
            }
            .container-fluid {
                margin: 0;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-book me-2"></i>Books Report</h1>
                    <div class="btn-toolbar mb-2 mb-md-0 print-hide">
                        <button onclick="window.print()" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="bi bi-printer"></i> Print Report
                        </button>
                        <a href="index.php" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> Back to Reports
                        </a>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-book-fill fs-1 text-primary mb-2"></i>
                                <h5 class="card-title">Total Books</h5>
                                <h3 class="text-primary"><?php echo $total_books; ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-stack fs-1 text-info mb-2"></i>
                                <h5 class="card-title">Total Copies</h5>
                                <h3 class="text-info"><?php echo $total_quantity; ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-check-circle-fill fs-1 text-success mb-2"></i>
                                <h5 class="card-title">Available</h5>
                                <h3 class="text-success"><?php echo $total_available; ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-arrow-up-circle-fill fs-1 text-warning mb-2"></i>
                                <h5 class="card-title">Borrowed</h5>
                                <h3 class="text-warning"><?php echo $total_borrowed; ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4 print-hide">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="Title, Author, or ISBN">
                            </div>
                            <div class="col-md-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">All Categories</option>
                                    <?php foreach ($categories as $cat): ?>
                                        <option value="<?php echo htmlspecialchars($cat['category']); ?>" 
                                                <?php echo $category === $cat['category'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($cat['category']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">Availability</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Books</option>
                                    <option value="available" <?php echo $status === 'available' ? 'selected' : ''; ?>>Available</option>
                                    <option value="unavailable" <?php echo $status === 'unavailable' ? 'selected' : ''; ?>>Unavailable</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="bi bi-search"></i> Filter
                                </button>
                                <a href="books.php" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise"></i> Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Books Table -->
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>Books Inventory
                            <?php if (!empty($search) || !empty($category) || !empty($status)): ?>
                                <small class="text-muted">(Filtered Results)</small>
                            <?php endif; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($books)): ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>No books found matching your criteria.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Title</th>
                                            <th>Author</th>
                                            <th>ISBN</th>
                                            <th>Category</th>
                                            <th>Total Copies</th>
                                            <th>Available</th>
                                            <th>Borrowed</th>
                                            <th>Total Loans</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($books as $book): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($book['title']); ?></strong>
                                                    <?php if (!empty($book['publication_year'])): ?>
                                                        <br><small class="text-muted">(<?php echo $book['publication_year']; ?>)</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($book['author']); ?></td>
                                                <td><?php echo htmlspecialchars($book['isbn'] ?: 'N/A'); ?></td>
                                                <td>
                                                    <?php if (!empty($book['category'])): ?>
                                                        <span class="badge bg-secondary"><?php echo htmlspecialchars($book['category']); ?></span>
                                                    <?php else: ?>
                                                        <span class="text-muted">Uncategorized</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo $book['quantity']; ?></td>
                                                <td><?php echo $book['available_quantity']; ?></td>
                                                <td><?php echo $book['borrowed_count']; ?></td>
                                                <td><?php echo $book['total_loans']; ?></td>
                                                <td>
                                                    <?php if ($book['available_quantity'] == 0): ?>
                                                        <span class="badge bg-danger">Not Available</span>
                                                    <?php elseif ($book['available_quantity'] < $book['quantity']): ?>
                                                        <span class="badge bg-warning text-dark">Partially Available</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">Fully Available</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Report Footer -->
                <div class="mt-4 text-center text-muted">
                    <small>
                        Report generated on <?php echo date('F j, Y \a\t g:i A'); ?> | 
                        Total records: <?php echo count($books); ?>
                    </small>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
