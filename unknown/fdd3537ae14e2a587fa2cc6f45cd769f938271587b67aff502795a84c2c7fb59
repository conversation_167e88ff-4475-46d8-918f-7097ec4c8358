<?php
/**
 * Test URL Duplication Fix
 * This file tests if the URL duplication issue is resolved
 */

require_once 'config/config.php';

echo "<h2>URL Duplication Fix Test</h2>";

// Show current script info
echo "<h3>Current Script Info:</h3>";
echo "<p><strong>SCRIPT_NAME:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";
echo "<p><strong>Current Directory:</strong> " . dirname($_SERVER['SCRIPT_NAME'] ?? '') . "</p>";

// Test URL function from root directory
echo "<h3>URL Generation from Root Directory:</h3>";
$test_paths = [
    'admin/dashboard.php',
    'admin/settings.php',
    'admin/users.php',
    'admin/email_settings.php',
    'members/index.php',
    'books/index.php',
    'loans/index.php'
];

foreach ($test_paths as $path) {
    $generated_url = url($path);
    echo "<p><strong>$path</strong> → <code>$generated_url</code></p>";
}

echo "<h3>Expected Behavior:</h3>";
echo "<p>✅ From root directory: <code>admin/settings.php</code> should generate <code>admin/settings.php</code></p>";
echo "<p>✅ From admin directory: <code>admin/settings.php</code> should generate <code>../admin/settings.php</code></p>";
echo "<p>❌ Should NOT generate: <code>admin/admin/settings.php</code></p>";

echo "<h3>Test Links:</h3>";
echo "<p><a href='admin/settings.php' class='btn btn-primary'>Test Settings (Direct)</a></p>";
echo "<p><a href='" . url('admin/settings.php') . "' class='btn btn-success'>Test Settings (URL Function)</a></p>";
echo "<p><a href='admin/dashboard.php' class='btn btn-info'>Test Dashboard</a></p>";

echo "<h3>Admin Directory Test:</h3>";
echo "<p><a href='admin/test_url_from_admin.php' class='btn btn-warning'>Test URL Generation from Admin Directory</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px; }
.btn-success { background: #28a745; }
.btn-info { background: #17a2b8; }
.btn-warning { background: #ffc107; color: #212529; }
h3 { color: #333; margin-top: 20px; }
p { margin: 5px 0; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
</style>
