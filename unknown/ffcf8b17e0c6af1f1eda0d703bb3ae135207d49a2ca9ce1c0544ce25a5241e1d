<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is librarian
if (!isLoggedIn() || !isLibrarian()) {
    redirect('../login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Initialize variables
$book_id = $member_id = $issue_date = $due_date = '';
$errors = [];
$success = false;

// Get all books
$query = "SELECT id, title, author, isbn, available_quantity FROM books WHERE available_quantity > 0 ORDER BY title";
$stmt = $db->prepare($query);
$stmt->execute();
$books = $stmt->fetchAll();

// Get all members
$query = "SELECT id, first_name, last_name, email FROM members WHERE membership_status = 'active' ORDER BY last_name, first_name";
$stmt = $db->prepare($query);
$stmt->execute();
$members = $stmt->fetchAll();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $book_id = trim($_POST['book_id'] ?? '');
    $member_id = trim($_POST['member_id'] ?? '');
    $issue_date = trim($_POST['issue_date'] ?? '');
    $due_date = trim($_POST['due_date'] ?? '');
    
    // Validate input
    if (empty($book_id)) {
        $errors[] = 'Book is required';
    }
    
    if (empty($member_id)) {
        $errors[] = 'Member is required';
    }
    
    if (empty($issue_date)) {
        $errors[] = 'Issue date is required';
    }
    
    if (empty($due_date)) {
        $errors[] = 'Due date is required';
    } elseif (strtotime($due_date) < strtotime($issue_date)) {
        $errors[] = 'Due date cannot be earlier than issue date';
    }
    
    // Check if book is available
    if (!empty($book_id)) {
        $query = "SELECT available_quantity FROM books WHERE id = :book_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':book_id', $book_id);
        $stmt->execute();
        $book = $stmt->fetch();
        
        if ($book['available_quantity'] <= 0) {
            $errors[] = 'This book is not available for loan';
        }
    }
    
    // Check if member has reached maximum loans
    if (!empty($member_id)) {
        $query = "SELECT COUNT(*) as loan_count FROM book_loans 
                  WHERE member_id = :member_id AND status = 'borrowed'";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->execute();
        $loan_count = $stmt->fetch()['loan_count'];
        
        $max_loans = 3; // Maximum number of books a member can borrow
        
        if ($loan_count >= $max_loans) {
            $errors[] = 'Member has reached the maximum number of loans (' . $max_loans . ')';
        }
    }
    
    // If no errors, issue the book
    if (empty($errors)) {
        try {
            // Start transaction
            $db->beginTransaction();
            
            // Insert loan record
            $query = "INSERT INTO book_loans (book_id, member_id, issue_date, due_date, status) 
                      VALUES (:book_id, :member_id, :issue_date, :due_date, 'borrowed')";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(':book_id', $book_id);
            $stmt->bindParam(':member_id', $member_id);
            $stmt->bindParam(':issue_date', $issue_date);
            $stmt->bindParam(':due_date', $due_date);
            $stmt->execute();
            
            // Update book available quantity
            $query = "UPDATE books SET available_quantity = available_quantity - 1 
                      WHERE id = :book_id";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(':book_id', $book_id);
            $stmt->execute();
            
            // Commit transaction
            $db->commit();
            
            // Set success message
            setMessage('Book issued successfully', 'success');
            
            // Reset form
            $book_id = $member_id = $issue_date = $due_date = '';
            $success = true;
            
            // Redirect to avoid form resubmission
            redirect('issue_book.php');
            
        } catch (PDOException $e) {
            // Rollback transaction on error
            $db->rollBack();
            $errors[] = 'Error issuing book: ' . $e->getMessage();
        }
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Issue Book - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .content-container {
            max-width: 800px;
            margin: 20px auto;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="issue_book.php">Issue Book</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="return_book.php">Return Book</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_reservations.php">Manage Reservations</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="../logout.php" class="btn btn-outline-light btn-sm">
                        <i class="bi bi-box-arrow-right me-1"></i>Sign out
                    </a>
                </div>
            </div>
        </div>
    </nav>
    
    <div class="content-container">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Issue Book</h5>
            </div>
            <div class="card-body">
                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo h($_SESSION['message_type']); ?> alert-dismissible fade show" role="alert">
                        <?php echo h($_SESSION['message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>
                
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo h($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <form method="post" action="<?php echo h($_SERVER['PHP_SELF']); ?>">
                    <div class="mb-3">
                        <label for="book_id" class="form-label">Book</label>
                        <select class="form-select" id="book_id" name="book_id" required>
                            <option value="">Select a book</option>
                            <?php foreach ($books as $book): ?>
                                <option value="<?php echo h($book['id']); ?>" <?php echo $book_id == $book['id'] ? 'selected' : ''; ?>>
                                    <?php echo h($book['title']); ?> by <?php echo h($book['author']); ?> (ISBN: <?php echo h($book['isbn']); ?>) - Available: <?php echo h($book['available_quantity']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="member_id" class="form-label">Member</label>
                        <select class="form-select" id="member_id" name="member_id" required>
                            <option value="">Select a member</option>
                            <?php foreach ($members as $member): ?>
                                <option value="<?php echo h($member['id']); ?>" <?php echo $member_id == $member['id'] ? 'selected' : ''; ?>>
                                    <?php echo h($member['first_name'] . ' ' . $member['last_name']); ?> (<?php echo h($member['email']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="issue_date" class="form-label">Issue Date</label>
                                <input type="date" class="form-control" id="issue_date" name="issue_date" value="<?php echo h($issue_date ?: date('Y-m-d')); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="due_date" class="form-label">Due Date</label>
                                <input type="date" class="form-control" id="due_date" name="due_date" value="<?php echo h($due_date ?: date('Y-m-d', strtotime('+14 days'))); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Issue Book</button>
                        <a href="dashboard.php" class="btn btn-outline-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
