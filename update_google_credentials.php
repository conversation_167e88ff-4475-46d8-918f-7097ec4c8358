<?php
/**
 * Update Google OAuth Credentials
 * 
 * This script provides an easy way to update Google OAuth credentials.
 */

// Ensure clean session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if form is submitted
$message = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['client_id'], $_POST['client_secret'])) {
    $client_id = trim($_POST['client_id']);
    $client_secret = trim($_POST['client_secret']);
    
    // Validate inputs
    if (empty($client_id) || empty($client_secret)) {
        $message = 'Both Client ID and Client Secret are required.';
    } else {
        // Update the Google OAuth configuration file
        $config_file = __DIR__ . '/config/google_oauth.php';
        $config_content = file_get_contents($config_file);
        
        // Replace the client ID and client secret
        $config_content = preg_replace(
            "/define\('GOOGLE_CLIENT_ID', '.*?'\);/",
            "define('GOOGLE_CLIENT_ID', '$client_id');",
            $config_content
        );
        
        $config_content = preg_replace(
            "/define\('GOOGLE_CLIENT_SECRET', '.*?'\);/",
            "define('GOOGLE_CLIENT_SECRET', '$client_secret');",
            $config_content
        );
        
        // Save the updated configuration
        if (file_put_contents($config_file, $config_content)) {
            $success = true;
            $message = 'Google OAuth credentials have been updated successfully.';
        } else {
            $message = 'Failed to update Google OAuth configuration. Please check file permissions.';
        }
    }
}

// Get the current redirect URI
$redirect_uri = BASE_URL . 'google_callback.php';

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Google OAuth Credentials - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px;
        }
        .setup-container {
            max-width: 800px;
            margin: 40px auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            padding: 30px;
        }
        .code-block {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .alert {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <h1 class="mb-4"><i class="bi bi-google me-2"></i>Update Google OAuth Credentials</h1>
        
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $success ? 'success' : 'danger'; ?>" role="alert">
                <?php echo h($message); ?>
            </div>
        <?php endif; ?>
        
        <div class="mb-4">
            <h3>Enter Your OAuth Credentials</h3>
            <p>Enter the Client ID and Client Secret you obtained from the Google Cloud Console:</p>
            <form method="post" action="">
                <div class="mb-3">
                    <label for="client_id" class="form-label">Client ID</label>
                    <input type="text" class="form-control" id="client_id" name="client_id" placeholder="Enter your Google Client ID" required>
                    <div class="form-text">Example: 123456789012-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com</div>
                </div>
                <div class="mb-3">
                    <label for="client_secret" class="form-label">Client Secret</label>
                    <input type="text" class="form-control" id="client_secret" name="client_secret" placeholder="Enter your Google Client Secret" required>
                    <div class="form-text">Example: GOCSPX-abcdefghijklmnopqrstuvwxyz</div>
                </div>
                <button type="submit" class="btn btn-primary">Save Credentials</button>
            </form>
        </div>
        
        <div class="mb-4">
            <h3>Redirect URI</h3>
            <p>Make sure to add this redirect URI to your Google Cloud Console:</p>
            <div class="code-block">
                <?php echo h($redirect_uri); ?>
            </div>
        </div>
        
        <div class="mb-4">
            <h3>Next Steps</h3>
            <p>After saving your credentials, you can test the Google Sign-In functionality:</p>
            <a href="test_google_auth.php" class="btn btn-success">Test Google Authentication</a>
            <a href="login.php" class="btn btn-primary ms-2">Go to Login Page</a>
        </div>
        
        <div class="mt-4 text-center">
            <a href="index.php" class="btn btn-outline-secondary">Back to Home</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
